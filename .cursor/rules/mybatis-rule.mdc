---
description: 
globs: 
alwaysApply: true
---
MyBatis 生成规范
本规范旨在指导项目中 MyBatis 相关文件的生成、组织及命名方式，包括实体类（Entity）、Mapper 接口、Service 层及 XML 映射文件，确保代码一致性、可维护性和高可用性。本规范基于 MyBatis-Plus，结合 Spring Boot 和企业级开发实践。

一、整体目录结构
src/
└── main/
    ├── java/
    │   └── com/
    │       └── siteweb/
    │           └── tcs/
    │               └── [模块名称]/
    │                   ├── controller/    # 控制器层
    │                   ├── service/       # 服务层
    │                   │   ├── impl/      # 服务实现
    │                   ├── mapper/        # MyBatis Mapper 接口
    │                   ├── entity/        # 实体类
    │                   ├── dto/           # 数据传输对象
    │                   ├── vo/            # 视图对象
    │                   └── config/        # 配置类
    └── resources/
        ├── mapper/                        # MyBatis XML 映射文件
        │   └── [模块名称]/                # 按模块分组
        ├── static/                        # 静态资源
        ├── templates/                     # 模板文件
        └── application.yml                # 应用配置文件


二、命名规范
1. 实体类（Entity）

位置：src/main/java/com/siteweb/tcs/[模块名称]/entity/
命名规则：与数据库表名对应，采用驼峰命名法，表名前缀（如 tcs_）可省略
示例：表 tcs_user_info 对应实体类 UserInfo.java


规范：
使用 Lombok 注解（如 @Data）简化 getter/setter 代码
使用 MyBatis-Plus 注解（如 @TableName、@TableId）映射表和字段
主键字段使用 @TableId，推荐自增 ID（IdType.AUTO）
**注意**：MyBatis-Plus 不支持在同一个实体类中使用多个 `@TableId` 注解来映射联合主键。对于联合主键，建议将所有主键字段都使用 `@TableField` 进行映射，或者创建一个单独的复合主键类（不推荐，会增加复杂性）。在进行基于主键的查询或更新操作时，需要使用 `QueryWrapper` 或自定义 SQL 来处理多个主键条件。
支持逻辑删除（@TableLogic）和自动填充时间字段（@TableField）
实现 Serializable 接口，支持序列化
字段名与数据库列名一致时可省略 @TableField，否则明确映射
非数据库字段使用 @TableField(exist = false)



package com.siteweb.tcs.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;

@Data
@TableName("tcs_user_info")
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    @TableField("user_name")
    private String userName;

    @TableField("account_balance")
    private BigDecimal accountBalance;

    @TableField(exist = false)
    private String temporaryStatus;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableLogic
    @TableField("is_deleted")
    private Integer deleted; // 0: 未删除, 1: 已删除
}

2. Mapper 接口

位置：src/main/java/com/siteweb/tcs/[模块名称]/mapper/
命名规则：实体名称 + Mapper
示例：UserInfoMapper.java


规范：
继承 MyBatis-Plus 的 BaseMapper<T>，自动提供基础 CRUD 方法
使用 @Mapper 注解标记为 MyBatis Mapper
可选使用 @Repository 注解，标记为 Spring Bean
自定义方法使用 @Param 注解明确参数名称
方法命名遵循动词开头，清晰描述功能（如 selectByUserName、findActiveUsers）
复杂查询优先在 XML 中定义，简单查询可使用 @Select 注解



package com.siteweb.tcs.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.user.entity.UserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
@Repository
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    /**
     * 根据用户名查询用户信息
     * @param userName 用户名
     * @return UserInfo 对象，若无匹配则返回 null
     */
    UserInfo selectByUserName(@Param("userName") String userName);

    /**
     * 查询指定时间后创建的活跃用户
     * @param minCreateTime 最小创建时间
     * @return 活跃用户列表
     */
    List<UserInfo> findActiveUsers(@Param("minCreateTime") LocalDateTime minCreateTime);
}

3. Service 层

位置：
接口：src/main/java/com/siteweb/tcs/[模块名称]/service/
实现：src/main/java/com/siteweb/tcs/[模块名称]/service/impl/


命名规则：
接口：I + 实体名称 + Service（如 IUserInfoService.java）
实现：实体名称 + ServiceImpl（如 UserInfoServiceImpl.java）


规范：
接口定义业务方法，方法名清晰反映业务逻辑
实现类使用 @Service 注解，注入 Mapper
使用 @Transactional 注解管理事务（必要时）
尽量封装通用逻辑到 MyBatis-Plus 的 ServiceImpl 基类
异常处理统一，抛出自定义异常或使用全局异常处理器
方法返回 DTO 或 VO，避免直接返回 Entity



// 接口
package com.siteweb.tcs.user.service;

import com.siteweb.tcs.user.dto.UserInfoDTO;
import com.siteweb.tcs.user.entity.UserInfo;

import java.time.LocalDateTime;
import java.util.List;

public interface IUserInfoService {

    /**
     * 根据用户名查询用户信息
     * @param userName 用户名
     * @return 用户信息 DTO
     */
    UserInfoDTO getUserByName(String userName);

    /**
     * 查询活跃用户
     * @param minCreateTime 最小创建时间
     * @return 活跃用户 DTO 列表
     */
    List<UserInfoDTO> listActiveUsers(LocalDateTime minCreateTime);

    /**
     * 保存用户信息
     * @param userInfo 用户信息实体
     * @return 是否保存成功
     */
    boolean saveUser(UserInfo userInfo);
}

// 实现
package com.siteweb.tcs.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.user.dto.UserInfoDTO;
import com.siteweb.tcs.user.entity.UserInfo;
import com.siteweb.tcs.user.mapper.UserInfoMapper;
import com.siteweb.tcs.user.service.IUserInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements IUserInfoService {

    @Resource
    private UserInfoMapper userInfoMapper;

    @Override
    public UserInfoDTO getUserByName(String userName) {
        UserInfo userInfo = userInfoMapper.selectByUserName(userName);
        if (userInfo == null) {
            return null;
        }
        UserInfoDTO dto = new UserInfoDTO();
        BeanUtils.copyProperties(userInfo, dto);
        return dto;
    }

    @Override
    public List<UserInfoDTO> listActiveUsers(LocalDateTime minCreateTime) {
        List<UserInfo> userInfos = userInfoMapper.findActiveUsers(minCreateTime);
        return userInfos.stream().map(userInfo -> {
            UserInfoDTO dto = new UserInfoDTO();
            BeanUtils.copyProperties(userInfo, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public boolean saveUser(UserInfo userInfo) {
        return this.save(userInfo);
    }
}

4. XML 映射文件

位置：src/main/resources/mapper/[模块名称]/
命名规则：实体名称 + Mapper.xml
示例：UserInfoMapper.xml


规范：
namespace 与对应 Mapper 接口的全限定名一致
使用 resultMap 定义复杂映射，resultType 用于简单映射
定义 <sql> 片段复用公共列或条件
SQL 语句按 CRUD 顺序组织，复杂查询添加注释
使用动态 SQL 标签（如 <if>、<where>、<foreach>）处理动态条件
参数使用 #{} 防止 SQL 注入，必要时用 ${}（谨慎使用）



<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.user.mapper.UserInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.user.entity.UserInfo">
        <id column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="account_balance" property="accountBalance" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        user_id, user_name, account_balance, create_time, update_time, is_deleted
    </sql>

    <!-- 根据用户名查询 -->
    <select id="selectByUserName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_user_info
        WHERE user_name = #{userName}
        AND is_deleted = 0
    </select>

    <!-- 查询活跃用户 -->
    <select id="findActiveUsers" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_user_info
        WHERE create_time >= #{minCreateTime}
        AND is_deleted = 0
    </select>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.siteweb.tcs.user.entity.UserInfo" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO tcs_user_info (
            user_name, account_balance, create_time, update_time, is_deleted
        ) VALUES (
            #{userName}, #{accountBalance}, #{createTime}, #{updateTime}, #{deleted}
        )
    </insert>

    <!-- 更新用户 -->
    <update id="updateById" parameterType="com.siteweb.tcs.user.entity.UserInfo">
        UPDATE tcs_user_info
        <set>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="accountBalance != null">account_balance = #{accountBalance},</if>
            update_time = #{updateTime}
        </set>
        WHERE user_id = #{userId}
        AND is_deleted = 0
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteById">
        UPDATE tcs_user_info
        SET is_deleted = 1, update_time = NOW()
        WHERE user_id = #{id}
    </update>
</mapper>


三、MyBatis 配置规范
1. 主启动类配置

在主启动类中使用 @MapperScan 扫描所有模块的 Mapper 接口
推荐使用通配符扫描，减少配置复杂度

package com.siteweb.tcs;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@MapperScan("com.siteweb.tcs.*.mapper")
public class TcsApplication {
    public static void main(String[] args) {
        SpringApplication.run(TcsApplication.class, args);
    }
}

2. 模块化配置

对于多模块项目，可在模块的配置类中单独指定 Mapper 扫描路径
确保模块隔离，减少扫描范围

package com.siteweb.tcs.user.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@MapperScan("com.siteweb.tcs.user.mapper")
public class UserModuleConfig {
}

3. MyBatis-Plus 配置

在 application.yml 中配置 MyBatis-Plus，启用驼峰命名、逻辑删除等功能
指定 XML 映射文件位置和实体类包路径

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.siteweb.tcs.*.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0

4. 分页插件配置

配置 MyBatis-Plus 分页插件，支持 MySQL 等数据库
确保分页查询性能优化

package com.siteweb.tcs.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}

5. 自动填充配置

配置自动填充创建时间和更新时间，减少手动设置
实现 MetaObjectHandler 接口

package com.siteweb.tcs.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }
}


四、SQL 编写规范
1. 方法命名

查询：select*, get*, find*, query*
插入：insert*, save*, add*
更新：update*, modify*
删除：delete*, remove*
统计：count*, sum*
批量操作：batch*（如 batchInsert）

2. XML 文件组织

顺序：按 CRUD 顺序组织（查询、插入、更新、删除）
分组：相关查询放在一起，添加清晰注释
复用：使用 <sql> 片段定义公共列或条件
动态 SQL：使用 <if>、<where>、<set>、<foreach> 等标签
注释：复杂 SQL 添加功能说明、参数说明和返回值说明

<mapper namespace="com.siteweb.tcs.user.mapper.UserInfoMapper">
    <!-- 公共列 -->
    <sql id="Base_Column_List">
        user_id, user_name, account_balance, create_time, update_time, is_deleted
    </sql>

    <!-- 查询：根据条件动态查询用户 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_user_info
        <where>
            <if test="userName != null and userName != ''">
                AND user_name LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="minCreateTime != null">
                AND create_time >= #{minCreateTime}
            </if>
            AND is_deleted = 0
        </where>
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tcs_user_info (
            user_name, account_balance, create_time, update_time, is_deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userName}, #{item.accountBalance},
                #{item.createTime}, #{item.updateTime}, #{item.deleted}
            )
        </foreach>
    </insert>
</mapper>


五、最佳实践

优先使用 MyBatis-Plus：

利用 BaseMapper 和 ServiceImpl 减少样板代码
使用链式查询（QueryWrapper、LambdaQueryWrapper）简化条件构造


分页查询：

使用 MyBatis-Plus 分页插件，结合 IPage 接口
避免直接在 SQL 中写 LIMIT，交给插件处理


动态 SQL：

使用 <if>、<choose>、<where>、<set> 等标签减少硬编码
复杂条件使用 QueryWrapper 或 XML 动态 SQL


批量操作：

使用 <foreach> 标签实现批量插入、更新
批量操作考虑性能，分批提交（每批 500-1000 条）


缓存使用：

低频变更数据启用 MyBatis 二级缓存
高频查询结合 Redis 缓存，减少数据库压力


参数安全：

使用 #{} 防止 SQL 注入，谨慎使用 ${}
明确参数类型，复杂参数使用 DTO


结果映射：

复杂查询使用 resultMap，简单查询使用 resultType
关联查询使用 <association> 或 <collection>


事务管理：

Service 层使用 @Transactional 注解
明确事务边界，避免长事务


日志记录：

启用 MyBatis SQL 日志（log-impl: Slf4jImpl）
生产环境关闭详细日志，减少性能开销


代码复用：

公共 SQL 片段定义在 <sql> 中
通用 Mapper 方法抽取到基类接口




## 六、插件 MyBatis 配置

对于需要独立数据源和 MyBatis 配置的插件模块（例如 `tcs-south-seed`），需要单独进行配置。

### 1. 数据源配置 (`DataSourceConfig.java`)

插件需要定义自己的 `DataSource` Bean，通常使用 `@ConfigurationProperties` 从配置文件加载特定前缀的数据库连接信息。

```java
package com.siteweb.tcs.south.seed.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = {"com.siteweb.tcs.south.seed.dal.mapper"}, sqlSessionFactoryRef = "seedSqlSessionFactory")
public class DataSourceConfig {

    @Bean(name = "seedDataSource") // 定义 Bean 名称
    @ConfigurationProperties(prefix = "spring.datasource.seed") // 绑定配置文件属性
    public DataSource seedDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = "seedTransactionManager") // 定义事务管理器 Bean
    public DataSourceTransactionManager seedTransactionManager(@Qualifier("seedDataSource") DataSource dataSource) { // 注入插件数据源
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "seedSqlSessionFactory") // 定义 SqlSessionFactory Bean
    public SqlSessionFactory seedSqlSessionFactory(@Qualifier("seedDataSource") DataSource dataSource) throws Exception { // 注入插件数据源
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        // 指定插件独立的 Mapper XML 文件位置
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*.xml")); 
        return bean.getObject();
    }
}
```

**关键点:**

*   **`@Configuration`**: 标记此类为 Spring 配置类。
*   **`@MapperScan`**: 指定此插件的 Mapper 接口扫描路径 (`basePackages`) 和关联的 `SqlSessionFactory` (`sqlSessionFactoryRef`)。这确保了插件的 Mapper 接口能被正确扫描并与正确的 `SqlSessionFactory` 关联。
*   **`@Bean(name = "...")`**: 为插件的数据源、事务管理器和 `SqlSessionFactory` 定义具有明确名称的 Bean。使用 `@Qualifier` 确保在注入时引用正确的 Bean。
*   **`@ConfigurationProperties`**: 从 `application.yml` 或 `application.properties` 中加载以 `spring.datasource.seed` 为前缀的配置项来配置插件数据源。
*   **`setMapperLocations`**: 明确指定插件使用的 Mapper XML 文件的位置，与主应用或其他插件隔离。

### 2. 配置文件 (`application.yml`)

在应用的配置文件中，为插件的数据源添加独立的配置项。

```yaml
spring:
  datasource:
    # 主数据源配置 (示例)
    url: *************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

    # 插件数据源配置 (使用特定前缀 'seed')
    seed:
      url: *************************************************
      username: seed_user
      password: seed_password
      driver-class-name: com.mysql.cj.jdbc.Driver
      # HikariCP 连接池配置 (可选)
      hikari:
        connection-timeout: 30000
        maximum-pool-size: 10
```

**关键点:**

*   使用不同的前缀（如 `spring.datasource.seed`）来区分插件数据源和主数据源的配置。
*   确保插件数据源连接到正确的数据库实例和模式。

### 3. 使用插件 Mapper

在插件的服务层或其他需要访问插件数据库的地方，注入插件的 Mapper 接口。由于 `@MapperScan` 配置了正确的 `sqlSessionFactoryRef`，Spring 和 MyBatis 会自动将这些 Mapper 与插件的数据源关联起来。

```java
package com.siteweb.tcs.south.seed.service;

import com.siteweb.tcs.south.seed.dal.mapper.SeedDataMapper; // 插件 Mapper
import com.siteweb.tcs.south.seed.dal.entity.SeedData; // 插件 Entity
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SeedDataService {

    @Resource
    private SeedDataMapper seedDataMapper; // 注入插件 Mapper

    // 使用插件事务管理器 (如果需要跨方法事务)
    @Transactional(transactionManager = "seedTransactionManager")
    public void saveData(SeedData data) {
        seedDataMapper.insert(data); // 调用插件 Mapper 的方法
    }

    public List<SeedData> listData() {
        return seedDataMapper.selectList(null); // 调用插件 Mapper 的方法
    }
}
```

**关键点:**

*   直接注入插件的 Mapper 接口 (`SeedDataMapper`)。
*   如果需要声明式事务，使用 `@Transactional` 注解，并通过 `transactionManager` 属性指定插件的事务管理器 Bean 名称 (`seedTransactionManager`)。

通过这种方式，插件可以拥有完全独立的数据库连接、事务管理和 MyBatis 配置，与主应用程序或其他插件互不干扰，保证了模块化和隔离性。


七、常见问题与解决方案

SQL 注入风险：

问题：使用 ${} 拼接 SQL 可能导致注入
解决：优先使用 #{}，动态 SQL 使用 <if> 或 QueryWrapper


性能瓶颈：

问题：大表查询或批量操作性能低
解决：使用索引、分页、批量提交，必要时结合 Redis 缓存


Mapper 扫描失败：

问题：Mapper 接口未被 Spring 扫描
解决：检查 @MapperScan 配置，确保包路径正确


逻辑删除失效：

问题：自定义 SQL 未考虑逻辑删除字段
解决：自定义 SQL 添加 is_deleted = 0 条件，或使用 MyBatis-Plus 提供的逻辑删除功能


多模块 XML 冲突：

问题：多个模块 XML 文件名重复
解决：按模块分目录存放 XML（如 mapper/user/、mapper/device/）




八、附录：代码生成工具
推荐使用 MyBatis-Plus 提供的代码生成器，快速生成 Entity、Mapper、Service 和 Controller 代码。
package com.siteweb.tcs.generator;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.Collections;

public class CodeGenerator {

    public static void main(String[] args) {
        FastAutoGenerator.create("***********************************************", "root", "password")
                .globalConfig(builder -> {
                    builder.author("YourName")
                            .outputDir("src/main/java")
                            .disableOpenDir();
                })
                .packageConfig(builder -> {
                    builder.parent("com.siteweb.tcs")
                            .moduleName("user")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, "src/main/resources/mapper/user"));
                })
                .strategyConfig(builder -> {
                    builder.addInclude("tcs_user_info")
                            .entityBuilder().enableLombok().enableTableFieldAnnotation()
                            .mapperBuilder().enableBaseResultMap()
                            .serviceBuilder().formatServiceFileName("I%sService");
                })
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}

生成内容

Entity：生成带 Lombok 和 MyBatis-Plus 注解的实体类
Mapper：生成继承 BaseMapper 的接口和对应的 XML 文件
Service：生成 IService 接口和 ServiceImpl 实现
Controller：生成 RESTful 风格的控制器（可选）




