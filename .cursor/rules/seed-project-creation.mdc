---
description: 
globs: 
alwaysApply: false
---
# 软件 Seed 项目创建指导规范

本文档旨在提供一个标准化的指导规范，用于创建新的 TCS Seed 项目（南向插件模块）。遵循此规范有助于确保项目结构的一致性、配置的标准化以及与主应用的顺利集成。

## 一、项目概述

Seed 项目作为独立的南向插件模块，通常具备以下特点：

*   **独立性**：拥有自己的业务逻辑、数据源（可选）、Pekko Actor组件以及配置。
*   **可集成性**：能够方便地集成到主应用中，作为主应用功能的一部分。
*   **可重用性**：设计上考虑代码和组件的可重用性。

本规范基于 `tcs-south-seed` 项目的实践经验，同时遵循 `ai-tcs-plugin.md` 中的相关开发规范。

## 二、项目结构

基于 `tcs-south-seed` 的项目目录结构如下（以 `com.siteweb.tcs.south.your_seed_module` 为例）：

```
[your-seed-module-name]/
├── pom.xml                               # Maven 项目配置文件
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── siteweb/
│   │   │           └── tcs/
│   │   │               └── south/
│   │   │                   └── [your_seed_module]/  # 模块根包
│   │   │                       ├── YourSeedPlugin.java # 插件主类，继承 SouthPlugin
│   │   │                       ├── config/             # 配置类 (如 DataSourceConfig, FlywayConfig, GatewayCluster)
│   │   │                       ├── connector/          # 连接器相关 (如特定协议的实现)
│   │   │                       │   ├── ConnectorDataHolder.java # 连接器数据持有者
│   │   │                       │   ├── letter/         # pekko 消息类
│   │   │                       │   └── process/        # 数据处理
│   │   │                       │       └── YourFSUProxy.java # 设备代理Actor
│   │   │                       ├── web/                # Web 相关
│   │   │                       │   ├── controller/      # 控制器层
│   │   │                       │   ├── service/         # 服务层
│   │   │                       │   │   ├── impl/         # 服务层实现
│   │   │                       │   └── vo/              # 视图对象
│   │   │                       ├── exception/          # 自定义异常
│   │   │                       ├── dal/                # 数据访问层 (Data Access Layer，规范参考ai/xsx/mybatis_directory_guide.md)
│   │   │                       │   ├── mapper/         # MyBatis Mapper 接口
│   │   │                       │   ├── entity/         # 数据实体类
│   │   │                       │   └── dto/            # 数据传输对象
│   │   │                       └── util/               # 工具类 (可选)
│   │   ├── resources/
│   │   │   ├── db/
│   │   │   │   └── [plugin-id]/                 # 插件独立数据库相关
│   │   │   │       └── migration/        # Flyway 数据库迁移脚本
│   │   │   ├── i18n/                     # 国际化资源文件
│   │   │   ├── mapper/
│   │   │   │   └── [plugin-id] # MyBatis Mapper 配置文件
│   │   │   ├── META-INF/
│   │   │   │   └── com-siteweb-webroot/
│   │   │   │       └── plugins/
│   │   │   │           └── [plugin-id]/  # 插件前端静态资源
│   │   │   └── plugin.yml                # 插件配置文件
│   ├── web/                              # 前端代码 (生成规则参考ai/lqp/frontend-plugin-dev-rules.md)
│   │   ├── package.json
│   │   ├── public/
│   │   └── src/
│   └── docs/                             # 文档
└── README.md                             # 项目说明文件
```

**关键点**：

*   **模块化包结构**：在 `com.siteweb.tcs.south` 下创建模块专属的包名，例如 `com.siteweb.tcs.south.your_seed_module`。
*   **资源隔离**：插件的 MyBatis Mapper XML 文件、数据库迁移脚本、静态资源等应放置在插件内部的 `resources` 目录下，并进行适当的路径规划以避免与主应用或其他插件冲突。
*   **插件主类**：继承自 `SouthPlugin` 基类的入口类，负责插件的生命周期管理。

## 三、Maven 配置 (`pom.xml`)

Seed 项目的 `pom.xml` 文件需要关注以下几个方面，参考 `tcs-south-seed` 的实际配置：

1.  **父项目依赖**：
    ```xml
    <parent>
        <groupId>com.siteweb</groupId>
        <artifactId>thing-connect-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    ```

2.  **项目坐标**：
    ```xml
    <artifactId>tcs-south-your-seed-module</artifactId>
    ```

3.  **插件属性**：定义插件元数据，这些信息会被打包到 `MANIFEST.MF` 中。
    ```xml
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
        <plugin.id>south-your-seed-module</plugin.id>
        <plugin.name>你的插件名称</plugin.name>
        <plugin.class>com.siteweb.tcs.south.your_seed_module.YourSeedPlugin</plugin.class>
        <plugin.version>1.0.0</plugin.version>
        <plugin.provider>Siteweb</plugin.provider>
        <plugin.applicationName>你的应用名称</plugin.applicationName>
        <plugin.dependencies></plugin.dependencies> <!-- 依赖的其他插件ID，逗号分隔 -->
        <webroot.resource.path>META-INF/com-siteweb-webroot/plugins/${plugin.id}</webroot.resource.path>
    </properties>
    ```

4.  **核心依赖**：
    ```xml
    <dependencies>
        <!-- TCS 核心依赖 -->
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-middleware</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
    ```
    插件依赖 `tcs-common` 和 `tcs-middleware`，分别包含核心功能和基础组件。

5.  **构建配置**：
    ```xml
    <build>
        <plugins>
            <!-- 写入插件信息 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Plugin-Id>${plugin.id}</Plugin-Id>
                            <Plugin-Name>${plugin.name}</Plugin-Name>
                            <Plugin-Version>${plugin.version}</Plugin-Version>
                            <Plugin-Provider>${plugin.provider}</Plugin-Provider>
                            <Plugin-Class>${plugin.class}</Plugin-Class>
                            <Plugin-Dependencies>${plugin.dependencies}</Plugin-Dependencies>
                            <Plugin-BuildTime>${maven.build.timestamp}</Plugin-BuildTime>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <!-- 如果有前端代码，添加前端构建插件 -->
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>1.15.0</version>
                <configuration>
                    <workingDirectory>src/web</workingDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>install-node-and-npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>${node.version}</nodeVersion>
                            <npmVersion>${npm.version}</npmVersion>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm-install</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <arguments>install --registry=https://registry.npmmirror.com</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm-build</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>run build</arguments>
                            <environmentVariables>
                                <PROJECT_VERSION>${project.version}</PROJECT_VERSION>
                            </environmentVariables>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 插件打包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <descriptors>
                        <descriptor>../plugin.release.xml</descriptor>
                    </descriptors>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <!-- 如果有前端资源 -->
            <resource>
                <directory>src/web/dist</directory>
                <targetPath>${webroot.resource.path}</targetPath>
                <filtering>false</filtering>
            </resource>
            <!-- 后端资源 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
    ```

## 四、插件主类实现

插件主类需要继承 `SouthPlugin` 类，并实现相关的生命周期方法。以下是一个基本的实现结构，参考 `SouthSeedPlugin.java`：

```java
package com.siteweb.tcs.south.your_seed_module;

import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.runtime.SouthPlugin;
import com.siteweb.tcs.south.your_seed_module.connector.ConnectorDataHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;

/**
 * 插件主类
 * <p>
 * 插件的入口类，负责插件的生命周期管理
 * </p>
 */
@Slf4j
public class YourSeedPlugin extends SouthPlugin {

    @Autowired
    private ConnectorDataHolder dataHolder;

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Autowired
    private MessageSource messageSource;

    public YourSeedPlugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
            log.info("Starting YourSeedPlugin");

            // 设置插件ID和创建根Actor
            dataHolder.setPluginId(this.getPluginId());

            // 初始化其他组件

            log.info("YourSeedPlugin started successfully");
        } catch (Exception e) {
            log.error("Error starting YourSeedPlugin", e);
        }
    }

    @Override
    public void onStop() {
        log.info("Stopping YourSeedPlugin");
        // Actor系统会处理停止Actor
    }
}
```

## 五、独立数据源与 MyBatis 配置

> **注意：** MyBatis相关的目录结构、命名规范、编码规范等详细内容请参考 [MyBatis 生成规范](ai/xsx/mybatis_directory_guide.md)。

对于需要独立数据源的插件，请参考以下 `DataSourceConfig.java` 示例：

```java
package com.siteweb.tcs.south.your_seed_module.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.south.your_seed_module.dal.mapper"}, sqlSessionFactoryRef = "seedSqlSessionFactory")
public class DataSourceConfig {

    @Bean(name = "seedDataSourceProperties")
    @ConfigurationProperties(prefix = "plugin.datasource.seed")
    public DataSourceProperties seedDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "seedDataSource")
    public DataSource seedDataSource(@Qualifier("seedDataSourceProperties") DataSourceProperties dataSourceProperties) {
        return dataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "seedTransactionManager")
    public DataSourceTransactionManager seedTransactionManager(@Qualifier("seedDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "seedSqlSessionFactory")
    public SqlSessionFactory seedSqlSessionFactory(@Qualifier("seedDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/[plugin-id]/*.xml"));
        return bean.getObject();
    }
}
```

**配置注意点**：
- 请将上述代码中的 `[plugin-id]` 替换为实际的插件ID
- 使用 `DataSourceProperties` 来规范化数据源配置
- `EnableConfigurationProperties` 注解确保配置属性绑定正常工作
- 在Windows环境中的路径使用正斜杠（`/`），而不是反斜杠（`\`）
- 资源路径使用 `classpath:` 前缀，如 `classpath:mapper/*.xml`

### 示例Mapper XML文件

**重要规定**：为确保插件能够正常编译和启动，必须在 `resources/mapper/[plugin-id]` 目录下创建至少一个基本的Mapper XML文件，即使项目初期没有实际的数据库操作需求。这是因为MyBatis在启动时会扫描配置的Mapper XML路径，如果路径不存在或没有XML文件，可能导致应用启动失败。

以下是一个最小化的示例Mapper XML文件 (`ExampleMapper.xml`)：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.your_seed_module.dal.mapper.ExampleMapper">
    <!-- 这是一个示例Mapper文件，确保MyBatis配置能够正常加载 -->
    <!-- 实际开发中，请根据业务需求添加具体的SQL语句 -->

    <!-- 示例查询 -->
    <select id="selectExample" resultType="java.lang.String">
        SELECT 'Hello World' AS greeting
    </select>
</mapper>
```

对应的Mapper接口类 (`ExampleMapper.java`)：

```java
package com.siteweb.tcs.south.your_seed_module.dal.mapper;

import org.apache.ibatis.annotations.Mapper;

/**
 * 示例Mapper接口
 * 用于确保MyBatis配置能够正常加载
 */
@Mapper
public interface ExampleMapper {

    /**
     * 示例查询方法
     * @return 返回一个问候语字符串
     */
    String selectExample();
}
```

这个最小化的Mapper实现可以：
1. 确保MyBatis配置正确加载
2. 验证数据源连接是否正常
3. 为后续开发提供参考模板

## 六、数据库迁移 (Flyway)

对于需要使用Flyway进行数据库初始化和迁移的插件，参考以下配置：

```java
package com.siteweb.tcs.south.your_seed_module.config;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

/**
 * Flyway数据库迁移配置
 */
@Configuration
public class FlywayConfig {

    @Bean(name = "seedFlyway")
    @DependsOn("seedDataSource")
    public Flyway flyway(@Qualifier("seedDataSource") DataSource dataSource) {
        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/[plugin-id]/migration")
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("seed_schema_history")
            .load();

        flyway.migrate();

        return flyway;
    }
}
```

**配置说明**：
- 使用 `@DependsOn` 确保数据源已初始化
- `locations` 参数指定迁移脚本的位置，推荐使用 `classpath:db/[plugin-id]/migration` 格式
- `table` 参数定义Flyway使用的历史记录表名，建议包含插件标识，例如 `seed_schema_history`
- `baselineOnMigrate` 设为 `true` 允许在非空数据库上执行迁移
- `validateOnMigrate` 设为 `true` 确保迁移前验证现有迁移

## 七、Gateway集群配置

对于需要使用Pekko集群功能的插件，可以创建一个Gateway集群配置类，用于管理分片Actor。以下是一个示例：

```java
package com.siteweb.tcs.south.your_seed_module.config;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.common.system.DefaultShardingMessageExtractor;
import com.siteweb.tcs.south.your_seed_module.connector.process.YourFSUProxy;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * Gateway集群配置
 * <p>
 * 用于创建和管理分片Actor
 * </p>
 */
@Configuration
public class YourGatewayCluster {
    @Value("${your.gateway.sharding.name:YourGatewayProxy}")
    private String gatewayShardingName;

    /**
     * 创建Gateway分片器
     */
    @Bean("your-gateway-sharding")
    public ActorRef createGatewaySharding() throws IOException {
        var extractor = new DefaultShardingMessageExtractor();
        return ClusterContext.createSharding(gatewayShardingName, Props.create(YourFSUProxy.class), extractor);
    }
}
```

**配置说明**：
- 使用`@Configuration`注解标记该类为配置类
- 使用`@Value`注解从配置文件中获取分片名称
- 使用`ClusterContext.createSharding`方法创建分片Actor
- 分片Actor通常用于管理多个设备实例，每个设备实例由一个Actor负责

## 八、连接器数据持有者

为了在不同组件之间共享数据，插件通常需要实现一个连接器数据持有者类。以下是一个示例：

```java
package com.siteweb.tcs.south.your_seed_module.connector;

import lombok.Data;
import org.apache.pekko.actor.ActorRef;
import org.springframework.stereotype.Component;

/**
 * 连接器数据持有者
 * <p>
 * 用于在不同组件之间共享数据
 * </p>
 */
@Data
@Component
public class ConnectorDataHolder {

    /**
     * 插件ID
     */
    private String pluginId;
}
```

**实现说明**：
- 使用`@Component`注解将类注册为Spring组件
- 使用`@Data`注解自动生成getter、setter、equals、hashCode和toString方法
- 存储插件ID和根Actor引用，便于在不同组件之间共享
- 可以根据需要添加其他属性，如配置信息、连接状态等

## 九、设备代理Actor实现

对于需要管理设备的插件，通常需要实现一个设备代理Actor，用于处理设备的消息和状态。以下是一个示例：

```java
package com.siteweb.tcs.south.your_seed_module.connector.process;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

/**
 * 设备代理Actor
 * <p>
 * 负责处理设备的消息和状态
 * </p>
 */
@Slf4j
public class YourFSUProxy extends AbstractActor {

    /**
     * Returns the props for creating a {@link YourFSUProxy} Actor.
     *
     * @return a Props for creating a YourFSUProxy Actor
     */
    public static Props props() {
        return Props.create(YourFSUProxy.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .matchAny(message -> {
                    log.debug("YourFSUProxy received message: {}", message);
                    // 处理各种消息
                })
                .build();
    }
}
```

**实现说明**：
- 继承`AbstractActor`类，实现Pekko Actor的基本功能
- 提供静态`props()`方法，用于创建Actor的Props
- 实现`createReceive()`方法，定义Actor如何处理收到的消息
- 使用`matchAny`处理所有类型的消息，在实际应用中应根据消息类型进行分类处理

## 十、Controller 实现规范

### 1. Controller 基本规范

在实现插件的 Controller 时，需要遵循以下规范：

- 使用 `@RestController` 注解标记控制器类
- 使用 `@RequestMapping` 指定基础路径
- **所有的 Mapping 注解（@GetMapping、@PostMapping、@PutMapping、@DeleteMapping 等）必须包含 value 属性**
- 方法命名清晰反映功能，如 list、get、create、update、delete 等
- 返回统一的响应对象，如 ResponseEntity 或自定义的响应包装类
- 异常处理统一，使用全局异常处理器

> **重要：** 在控制器映射中，所有的 @Mapping 注解必须包含 value 属性。不要使用默认的空路径映射，这样可以提高代码的可读性和可维护性。

### 2. 测试接口实现

为了便于前后端联调和验证插件是否正常加载，每个插件应当实现一个简单的测试接口。这个接口将作为插件健康检查的基本端点，并且在部署后可用于快速确认插件的可用性。

在 `web/controller` 包下创建 `TestController.java`，实现一个基本的测试端点：

```java
package com.siteweb.tcs.south.your_seed_module.web.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试Controller
 * 用于前后端联调时测试插件是否正常加载
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/south/your-seed-module/test") // 必须指定 value
public class TestController {

    /**
     * 测试接口，用于验证插件是否正常加载
     * @return "Hello World"
     */
    @GetMapping(value = "/helloworld") // 必须指定 value
    public ResponseEntity<String> helloWorld() {
        log.info("Test endpoint accessed");
        return ResponseEntity.ok("Hello World");
    }
}
```

**实现说明**：
- 所有的 Mapping 注解都必须包含 value 属性，如 `@RequestMapping(value = "/api/south/your-seed-module/test")` 和 `@GetMapping(value = "/helloworld")`
- 测试端点使用 GET 方法，方便通过浏览器直接访问
- 返回简单的 "Hello World" 文本，确认控制器正常运行
- 记录访问日志，便于排查问题

这个简单的测试接口可以:
1. 验证插件是否成功加载并注册到Spring容器
2. 确认插件的Web路由是否正常工作
3. 为前端开发人员提供一个简单的API来测试连接

## 十一、插件集成配置

**重要：** 插件创建完成后，必须进行以下配置步骤，以确保插件能够被正确加载和使用。缺少这些配置将导致插件无法被识别和加载。

### 1. 在 tcs-core 的 pom.xml 中添加插件配置

在 `server/tcs-core/pom.xml` 文件中的 `<profiles>` 部分添加新插件的 profile 配置：

```xml
<profile>
    <id>your-seed-module</id>
    <dependencies>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-south-your-seed-module</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</profile>
```

**说明：**
- `<id>` 应与插件模块名称保持一致，例如 `south-ctcc`
- `<artifactId>` 应与插件的 Maven 模块名称一致，例如 `tcs-south-ctcc`
- 此配置告诉 tcs-core 在激活此 profile 时加载该插件

### 2. 在 application.yml 中添加插件路径

在 `server/tcs-core/src/main/resources/application.yml` 文件中的 `dev-path` 部分添加新插件的路径：

```yaml
plugin:
  pf4j:
    mode: development
    # 开发模式下的插件路径
    dev-path: |
      # 其他插件路径...
      ../tcs-south-your-seed-module/target/classes
```

**说明：**
- `dev-path` 指定了开发模式下插件的类路径
- 路径应为相对于 tcs-core 的路径，指向插件的 target/classes 目录
- 每个插件路径单独一行，保持缩进一致

### 3. 激活插件的 Profile

添加配置后，必须激活相应的 profile 才能加载插件。有两种方式可以激活插件的 profile：

#### 方式一：通过命令行参数

在启动应用时添加 `-P` 参数：

```bash
mvn spring-boot:run -Pdev,your-seed-module
```

**说明：**
- `-P` 参数后跟多个 profile 名称，用逗号分隔
- `dev` 是开发环境的基本 profile，必须包含
- `your-seed-module` 是你的插件 profile ID，与上面在 pom.xml 中定义的 ID 一致

#### 方式二：在 IDE 中配置

如果使用 IntelliJ IDEA，可以在运行配置中添加 Maven 命令行参数：

1. 编辑 tcs-core 的运行配置（Run/Debug Configurations）
2. 选择 Maven 选项卡
3. 在 "Command line" 字段中添加：`-Pdev,your-seed-module`
4. 应用并保存配置

### 4. 验证插件加载

完成上述配置并启动应用后，可以通过以下方式验证插件是否正确加载：

1. 检查启动日志，应该能看到类似以下的插件加载信息：
   ```
   Loading plugin your-seed-module
   Plugin your-seed-module loaded successfully
   ```

2. 访问插件的测试接口，如：`http://localhost:8080/api/south/your-seed-module/test/helloworld`

3. 在系统管理界面中查看已加载的插件列表

> **重要提示：**
> - 每次添加新插件或修改插件配置后，需要重启应用才能生效
> - 如果插件加载失败，请检查以上所有配置是否正确
> - 确保插件的 pom.xml 中的 plugin.id 与在 tcs-core 中配置的路径一致

### 5. 将插件添加到父工程

**关键步骤：** 必须将新创建的插件模块添加到父工程的 pom.xml 文件中，以便于整体构建和管理。

在 `server/pom.xml` 文件的 `<modules>` 部分添加新插件模块：

```xml
<modules>
    <!-- 其他模块... -->
    <module>tcs-south-your-seed-module</module>
</modules>
```

**说明：**
- `<module>` 值应为插件的文件夹名称，例如 `tcs-south-cmcc`
- 添加到 `<modules>` 列表中的任何位置均可，建议放在类似模块的附近
- 此配置使父工程能够识别并管理新插件模块
- 如果不添加此配置，新插件将不会被包含在整体构建中，也不会被 IDE 正确识别

> **重要提示：**
> - 添加模块后，可能需要在 IDE 中重新加载 Maven 项目以刷新项目结构
> - 如果使用命令行构建，确保在父工程目录下执行 `mvn clean install`
> - 确保模块名称与目录名称完全一致，避免大小写或拼写错误

## 十二、IDE 集成配置

创建新插件后，需要确保它能被 IntelliJ IDEA 正确识别为 Maven 模块，以便于代码导航、自动补全和编译。

### 1. 将插件添加为 Maven 模块

#### 方式一：通过 Maven 工具窗口

1. **打开 Maven 工具窗口**
   - 在 IntelliJ IDEA 右侧找到 Maven 工具窗口（通常在右侧工具栏）
   - 如果看不到 Maven 工具窗口，可以通过菜单 View > Tool Windows > Maven 打开

2. **刷新 Maven 项目**
   - 在 Maven 工具窗口中，点击顶部的"刷新"图标（Reload All Maven Projects）
   - 这将使 IDEA 重新读取所有 pom.xml 文件并识别新添加的模块

#### 方式二：通过 Project Structure 手动添加模块

1. **打开 Project Structure 对话框**
   - 点击菜单 File > Project Structure (快捷键: Ctrl+Alt+Shift+S 或 Mac 上的 Cmd+;)

2. **添加模块**
   - 在左侧面板选择"Modules"
   - 点击"+"按钮，选择"Import Module"
   - 浏览到插件目录，选择其 pom.xml 文件
   - 在导入向导中，保持默认设置，点击"Next"直到完成
   - 确保选择"Import Maven projects automatically"选项

### 2. 验证模块识别

完成上述步骤后，您可以通过以下方式验证模块是否正确加载：

1. **检查项目结构**
   - 在项目视图中，应该能看到插件作为一个独立的模块
   - 模块图标应该是 Maven 模块图标

2. **检查代码导航**
   - 尝试从其他模块引用插件中的类
   - 使用 Ctrl+点击（或 Cmd+点击）类名，应该能够跳转到源代码

3. **检查代码补全**
   - 在其他模块中尝试导入插件中的类
   - 代码补全应该能够提示相关类

4. **编译模块**
   - 右键点击插件模块
   - 选择"Build Module '插件名称'"
   - 应该能够成功编译

### 3. 常见问题解决

如果上述方法不起作用，可以尝试以下操作：

1. **清理 IDEA 缓存**
   - 关闭 IDEA
   - 删除项目目录下的 .idea 文件夹和 *.iml 文件
   - 重新启动 IDEA 并重新导入项目

2. **使用命令行**
   - 在命令行中执行 `mvn clean install -DskipTests`
   - 然后重新打开 IDEA

3. **检查模块依赖关系**
   - 确保插件的 pom.xml 中的 parent 配置正确
   - 确保所有必要的依赖都已正确声明

4. **检查文件路径**
   - 确保模块目录结构符合 Maven 标准
   - 确保没有文件路径或文件名中的特殊字符

> **提示：** 当你在 IDEA 中打开一个多模块的 Maven 项目时，应该选择根 pom.xml 文件打开，而不是单个模块的 pom.xml。这样 IDEA 才能正确识别所有模块的关系。

## 十三、编码规范


项目应遵循 `