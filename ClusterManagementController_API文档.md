# 集群管理控制器 API 文档

## 概述

`ClusterManagementController` 提供集群管理的 REST API 接口，支持四层结构的运行监控和管理：

**四层结构：**
1. **服务器列表** (ClusterMember) - 集群中的所有服务器节点
2. **分片类型** (ShardTypeInfo) - 每个服务器上的分片类型 (shardingnames)
3. **分片** (ShardInfo) - 每种类型下的具体分片 (shardings)  
4. **实体** (EntityInfo) - 每个分片中的业务实体 (entities)

**基础路径：** `/cluster`

## 通用响应格式

所有API接口都返回统一的响应格式：

```json
{
  "state": true,           // 执行结果：true-成功，false-失败
  "code": 0,              // 状态码：0-成功，-1-失败
  "timestamp": 1640995200000,  // 执行时间戳
  "data": {},             // 返回数据（成功时）
  "err_msg": null,        // 错误消息（失败时）
  "err_code": null        // 错误码（失败时）
}
```

## API 接口列表

### 1. 获取集群状态概览

**接口路径：** `GET /cluster/status`

**描述：** 获取集群整体状态信息

**响应数据结构：**
```json
{
  "state": true,
  "code": 0,
  "timestamp": 1640995200000,
  "data": {
    "clusterName": "TcsSystem",           // 集群名称
    "selfAddress": "pekko://TcsSystem@*************:25520",  // 当前节点地址
    "leader": "pekko://TcsSystem@*************:25520",       // 集群领导者地址
    "totalMembers": 3,                    // 集群总成员数
    "upMembers": 3,                       // 运行中的成员数
    "joiningMembers": 0,                  // 正在加入的成员数
    "leavingMembers": 0,                  // 正在离开的成员数
    "unreachableMembers": 0,              // 不可达成员数
    "isUp": true,                         // 当前节点是否正常运行
    "timestamp": "2024-01-01T12:00:00",   // 状态时间戳
    "roles": ["backend", "frontend"]      // 当前节点角色
  }
}
```

---

### 2. 获取完整集群树状结构 ⭐ 核心接口

**接口路径：** `GET /cluster/tree`

**描述：** 获取完整的四层集群树状结构，包含所有层级的详细信息

**响应数据结构：**
```json
{
  "state": true,
  "code": 0,
  "timestamp": 1640995200000,
  "data": [
    {
      "address": "pekko://TcsSystem@*************:25520",  // 服务器地址
      "status": "Up",                                       // 服务器状态
      "roles": ["backend", "frontend"],                     // 服务器角色
      "isLeader": true,                                     // 是否为领导者
      "isSelf": true,                                       // 是否为当前节点
      "hostname": "*************",                         // 主机名/IP
      "port": 25520,                                        // 端口号
      "shardTypes": [                                       // 分片类型列表（第二层）
        {
          "typeName": "DeviceActor",                        // 分片类型名称
          "totalShards": 100,                               // 该类型总分片数
          "totalEntities": 1500,                            // 该类型总实体数
          "shards": [                                       // 分片列表（第三层）
            {
              "typeName": "DeviceActor",                    // 分片类型名称
              "shardId": "1",                               // 分片ID
              "entityCount": 15,                            // 该分片中的实体数量
              "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/1",
              "entities": [                                 // 实体列表（第四层）
                {
                  "entityId": "device_001",                 // 实体ID
                  "shardId": "1",                          // 所属分片ID
                  "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/1/device_001"
                },
                {
                  "entityId": "device_002",
                  "shardId": "1",
                  "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/1/device_002"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

---

### 3. 获取集群成员列表（简化版）

**接口路径：** `GET /cluster/members`

**描述：** 获取集群成员基本信息，不包含分片详情

**响应数据结构：**
```json
{
  "state": true,
  "code": 0,
  "timestamp": 1640995200000,
  "data": [
    {
      "address": "pekko://TcsSystem@*************:25520",
      "status": "Up",
      "roles": ["backend", "frontend"],
      "isLeader": true,
      "isSelf": true,
      "hostname": "*************",
      "port": 25520,
      "shardTypes": null                    // 简化版本中为 null
    },
    {
      "address": "pekko://TcsSystem@*************:25520",
      "status": "Up",
      "roles": ["backend"],
      "isLeader": false,
      "isSelf": false,
      "hostname": "*************",
      "port": 25520,
      "shardTypes": null
    }
  ]
}
```

---

### 4. 获取指定成员的分片类型信息

**接口路径：** `GET /cluster/members/{address}/shard-types`

**路径参数：**
- `address` (string, required): 成员地址，如 "pekko://TcsSystem@*************:25520"

**描述：** 获取指定集群成员的分片类型信息（第二层：shardingnames）

**响应数据结构：**
```json
{
  "state": true,
  "code": 0,
  "timestamp": 1640995200000,
  "data": [
    {
      "typeName": "DeviceActor",            // 分片类型名称
      "totalShards": 100,                   // 该类型总分片数
      "totalEntities": 1500,                // 该类型总实体数
      "shards": [                           // 该类型下的所有分片
        {
          "typeName": "DeviceActor",
          "shardId": "1",
          "entityCount": 15,
          "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/1",
          "entities": [...]                 // 实体列表
        }
      ]
    },
    {
      "typeName": "AlarmActor",
      "totalShards": 50,
      "totalEntities": 800,
      "shards": [...]
    }
  ]
}
```

---

### 5. 获取所有分片类型列表

**接口路径：** `GET /cluster/shard-types`

**描述：** 获取集群中已注册的所有分片类型名称列表

**响应数据结构：**
```json
{
  "state": true,
  "code": 0,
  "timestamp": 1640995200000,
  "data": [
    "DeviceActor",
    "AlarmActor",
    "EventActor",
    "CommandActor"
  ]
}
```

---

### 6. 获取指定分片类型的详细信息

**接口路径：** `GET /cluster/shard-types/{typeName}`

**路径参数：**
- `typeName` (string, required): 分片类型名称，如 "DeviceActor"

**描述：** 获取指定分片类型的详细信息（第三层：shardings），包含该类型下所有分片和实体的完整信息

**响应数据结构：**
```json
{
  "state": true,
  "code": 0,
  "timestamp": 1640995200000,
  "data": {
    "typeName": "DeviceActor",              // 分片类型名称
    "totalShards": 100,                     // 该类型总分片数
    "totalEntities": 1500,                  // 该类型总实体数
    "shards": [                             // 该类型下的所有分片详情
      {
        "typeName": "DeviceActor",
        "shardId": "1",                     // 分片ID
        "entityCount": 15,                  // 该分片中的实体数量
        "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/1",
        "entities": [                       // 该分片中的所有实体
          {
            "entityId": "device_001",
            "shardId": "1",
            "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/1/device_001"
          },
          {
            "entityId": "device_002",
            "shardId": "1",
            "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/1/device_002"
          }
        ]
      },
      {
        "typeName": "DeviceActor",
        "shardId": "2",
        "entityCount": 12,
        "actorPath": "pekko://TcsSystem@*************:25520/system/sharding/DeviceActor/2",
        "entities": [...]
      }
    ]
  }
}
```

---

### 7. 集群健康检查

**接口路径：** `GET /cluster/health`

**描述：** 检查集群健康状态，用于监控和健康检查

**响应数据结构：**
```json
{
  "state": true,
  "code": 0,
  "timestamp": 1640995200000,
  "data": {
    "status": "healthy",                    // 健康状态："healthy" 或 "unhealthy"
    "upMembers": 3,                        // 运行中的成员数
    "totalMembers": 3,                     // 总成员数
    "unreachableMembers": 0,               // 不可达成员数
    "leader": "pekko://TcsSystem@*************:25520"  // 集群领导者地址
  }
}
```

**健康状态判断标准：**
- `healthy`: 有运行中的成员且没有不可达成员
- `unhealthy`: 没有运行中的成员或存在不可达成员

---

## 错误响应格式

当API调用失败时，返回以下格式：

```json
{
  "state": false,
  "code": -1,
  "timestamp": 1640995200000,
  "data": null,
  "err_msg": "获取集群状态失败: Connection timeout",
  "err_code": "-1"
}
```

## 数据类型说明

### ClusterStatus（集群状态）
| 字段 | 类型 | 描述 |
|------|------|------|
| clusterName | String | 集群名称 |
| selfAddress | String | 当前节点地址 |
| leader | String | 集群领导者地址 |
| totalMembers | Integer | 集群总成员数 |
| upMembers | Integer | 运行中的成员数 |
| joiningMembers | Integer | 正在加入的成员数 |
| leavingMembers | Integer | 正在离开的成员数 |
| unreachableMembers | Integer | 不可达成员数 |
| isUp | Boolean | 当前节点是否正常运行 |
| timestamp | LocalDateTime | 状态时间戳 |
| roles | Set<String> | 当前节点角色集合 |

### ClusterMember（集群成员）
| 字段 | 类型 | 描述 |
|------|------|------|
| address | String | 成员地址 |
| status | String | 成员状态 |
| roles | Set<String> | 成员角色集合 |
| isLeader | Boolean | 是否为领导者 |
| isSelf | Boolean | 是否为当前节点 |
| hostname | String | 主机名或IP |
| port | Integer | 端口号 |
| shardTypes | List<ShardTypeInfo> | 分片类型列表 |

### ShardTypeInfo（分片类型信息）
| 字段 | 类型 | 描述 |
|------|------|------|
| typeName | String | 分片类型名称 |
| totalShards | Integer | 该类型总分片数 |
| totalEntities | Integer | 该类型总实体数 |
| shards | List<ShardInfo> | 该类型下的所有分片 |

### ShardInfo（分片信息）
| 字段 | 类型 | 描述 |
|------|------|------|
| typeName | String | 分片类型名称 |
| shardId | String | 分片ID |
| entityCount | Integer | 该分片中的实体数量 |
| actorPath | String | Actor路径 |
| entities | List<EntityInfo> | 该分片中的所有实体 |

### EntityInfo（实体信息）
| 字段 | 类型 | 描述 |
|------|------|------|
| entityId | String | 实体ID |
| shardId | String | 所属分片ID |
| actorPath | String | 实体Actor路径 |

## 使用场景

### 1. 运行监控界面
```javascript
// 获取集群概览
const status = await fetch('/cluster/status');

// 获取完整树状结构用于展示
const tree = await fetch('/cluster/tree');
```

### 2. 健康检查和告警
```javascript
// 定期健康检查
const health = await fetch('/cluster/health');
if (health.data.status === 'unhealthy') {
  // 触发告警
}
```

### 3. 分片负载监控
```javascript
// 监控特定分片类型的负载
const deviceShards = await fetch('/cluster/shard-types/DeviceActor');
// 分析每个分片的实体数量分布
```

### 4. 集群拓扑展示
```javascript
// 获取成员列表用于拓扑图
const members = await fetch('/cluster/members');
// 渲染集群拓扑结构
```

## 注意事项

1. **性能考虑：** `/cluster/tree` 接口返回完整数据结构，数据量较大，建议在需要完整信息时使用
2. **实时性：** 集群状态信息具有实时性，建议根据业务需求控制调用频率
3. **错误处理：** 集群服务异常时可能导致API调用失败，请做好错误处理
4. **权限控制：** 生产环境中建议添加适当的权限控制机制

## 版本信息

- **控制器版本：** 1.0
- **API版本：** v1
- **更新时间：** 2024-01-01
- **维护者：** AI Assistant