# 移动标准化管理功能修改总结

## 修改概述
根据用户需求，对移动标准化管理功能进行了以下修改：

## 1. API接口扩展 (standard.ts)

### 新增字典项数据类型
```typescript
export interface DictionaryItem {
  dictionaryItemId: number;
  itemId: number;
  itemValue: string; // 显示值，对应数据库的ItemValue字段
  categoryId: number;
  parentCategoryId: number;
  parentItemId?: number;
  translateKey?: string;
  enable: number;
  isSystem?: number;
  isDefault?: number;
  description?: string;
  isExpired?: number;
  extendField?: string;
}
```

### 扩展数据类型
- **SignalStandardData**: 新增 `deviceTypeId`、`semaphoreTypeId`、`unit` 字段
- **AlarmStandardData**: 新增各种ID字段用于下拉选择，`alarmLogicClass` 改为可选类型

### 新增字典API接口
```typescript
export const dictionaryApi = {
  getDeviceType: () => Promise<ApiResponse<DictionaryItem[]>>,
  getSemaphoreType: () => Promise<ApiResponse<DictionaryItem[]>>,
  getAlarmTypesByDeviceType: (deviceTypeId: number) => Promise<ApiResponse<DictionaryItem[]>>,
  getEventSeverity: () => Promise<ApiResponse<DictionaryItem[]>>
}
```

## 2. 信号标准化对话框修改 (SignalStandardModal.vue)

### 主要修改内容：

#### 2.1 信号编码ID校验
- 添加6位数字校验规则
- 使用正则表达式 `/^\d{6}$/` 验证
- 设置输入框 `maxlength="6"`

#### 2.2 设备类型下拉选择
- 将输入框改为下拉选择框
- 调用 `dictionaryApi.getDeviceType()` 获取选项
- 选择后将 `itemId` 赋值给 `deviceTypeId` 字段

#### 2.3 信号类型下拉选择
- 将输入框改为下拉选择框
- 调用 `dictionaryApi.getSemaphoreType()` 获取选项
- 选择后将 `itemId` 赋值给 `semaphoreTypeId` 字段

#### 2.4 新增单位字段
- 添加单位输入框，支持用户输入信号单位

#### 2.5 含义字段格式约束
- 添加格式校验：`信号数值1=含义1;信号数值2=含义2;...`
- 使用正则表达式验证格式
- 字段可为空
- 在placeholder中提示正确格式

## 3. 告警标准化对话框修改 (AlarmStandardModal.vue)

### 主要修改内容：

#### 3.1 告警编码ID校验
- 添加6位数字校验规则
- 使用正则表达式 `/^\d{6}$/` 验证
- 设置输入框 `maxlength="6"`

#### 3.2 设备类型下拉选择
- 将输入框改为下拉选择框
- 调用 `dictionaryApi.getDeviceType()` 获取选项
- 选择后将 `itemId` 赋值给 `deviceTypeId` 字段

#### 3.3 告警逻辑类别联动选择
- 将输入框改为下拉选择框
- 根据选择的设备类型ID调用 `dictionaryApi.getAlarmTypesByDeviceType(deviceTypeId)` 获取选项
- 实现设备类型与告警逻辑类别的联动
- **移除告警逻辑类别名称字段**
- 告警逻辑类别在未选择设备类型时默认为空且禁用
- **告警编码ID设为必填项**

#### 3.4 告警级别下拉选择
将以下字段改为下拉选择：
- **通信楼宇告警级别**: 调用 `dictionaryApi.getEventSeverity()`
- **通信基站告警级别**: 调用 `dictionaryApi.getEventSeverity()`
- **传输节点告警级别**: 调用 `dictionaryApi.getEventSeverity()`
- **IDC告警级别**: 调用 `dictionaryApi.getEventSeverity()`

所有告警级别选择后都将 `itemId` 赋值给对应的ID字段。

## 4. 技术实现细节

### 4.1 数据加载时机
- 组件挂载时加载基础选项数据（设备类型、信号类型、告警级别）
- 设备类型选择变化时动态加载告警逻辑类别
- 编辑模式下根据现有数据预加载相关选项

### 4.2 表单验证
- 使用自定义验证函数进行复杂校验
- 6位编码ID校验
- 含义字段格式校验（可选）

### 4.3 数据绑定
- 使用双向绑定同时维护ID和名称字段
- 选择变化时自动更新对应的名称字段

### 4.4 用户体验优化
- 添加loading状态
- 错误处理和用户提示
- 清空联动字段逻辑
- 表单重置功能

## 5. API端点映射

| 功能 | API端点 | 对应Controller方法 |
|------|---------|-------------------|
| 获取设备类型 | `/api/thing/south-cmcc-plugin/dictionary/device-type` | `DictionaryController.getDeviceType()` |
| 获取信号类型 | `/api/thing/south-cmcc-plugin/dictionary/semaphore-type` | `DictionaryController.getSemaphoreType()` |
| 获取告警类型 | `/api/thing/south-cmcc-plugin/dictionary/alarm-type-by-device-type` | `DictionaryController.getAlarmTypesByDeviceType()` |
| 获取告警级别 | `/api/thing/south-cmcc-plugin/dictionary/event-severity` | `DictionaryController.getEventSeverity()` |

## 6. 修改文件清单

1. `tcs2/server/tcs-south-cmcc/src/main/web/src/api/standard.ts` - API接口扩展
2. `tcs2/server/tcs-south-cmcc/src/main/web/src/views/standard/components/SignalStandardModal.vue` - 信号标准化对话框
3. `tcs2/server/tcs-south-cmcc/src/main/web/src/views/standard/components/AlarmStandardModal.vue` - 告警标准化对话框

## 7. 重要修正说明

### 7.1 字段名称修正
根据后端 `DictionaryItem` 实体类的实际结构，进行了以下重要修正：
- **显示字段**: 下拉列表显示 `itemValue` 而不是 `itemName`
- **赋值字段**: 选择时仍然赋值 `itemId` 给对应的ID字段
- **数据结构**: 更新了 `DictionaryItem` 接口定义，与后端实体类保持一致

### 7.2 数据流说明
1. **下拉选项加载**: 从后端获取 `DictionaryItem[]` 数组
2. **选项显示**: 使用 `item.itemValue` 作为显示文本
3. **值绑定**: 使用 `item.itemId` 作为选择值
4. **名称回填**: 选择后将 `itemValue` 赋值给对应的名称字段

## 8. 功能验证

所有修改已完成，建议进行以下测试：

1. 信号标准化添加/编辑功能测试
2. 告警标准化添加/编辑功能测试
3. 下拉选项数据加载测试（验证显示 itemValue）
4. 表单验证功能测试
5. 联动选择功能测试
6. 数据保存测试（验证保存 itemId）

修改完成后，用户界面将提供更好的用户体验，包括数据验证、下拉选择和联动功能。下拉列表将正确显示 `itemValue` 字段的内容，同时保存 `itemId` 到数据库。 