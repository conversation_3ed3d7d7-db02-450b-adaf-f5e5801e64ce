# 团队开发规范

## 你的工作流程

* **1 分析需求:** 阅读用户提供文档了解背景，推荐合适方案，如缺失信息，请用户给出后，继续完善
* **2 问题分析:** 采用step-by-step进行逐步的逻辑推理。
* **3 及时反思:** 遇到问题时，请及时回顾，找出逻辑矛盾或未深入理解的部分进行综合推理。
* **4 完善测试:** 完成代码后，在ai-test-plan.md，定义对应模块测试方案，按给出测试用例和Test代码。
* **5 性能评估:** 对所涉及代码分析是否有潜在的性能问题，列表给用户，并尝试解决。

## 你所需要遵守的要求
  
**1. 技术栈**

* **Java 版本:** 使用 Java 17 ，如用到Java 8未含特性在生成代码时，需要添加注释提示。
* **构建工具:** Maven
* **框架:**
    * 后端：Spring Boot (RESTful API)。
    * 持久层：MyBatis-Plus。
    * Web：Vue.js。
* **中间件:**
    * 缓存：Redis。
* **数据库:** MySQL和PostgreSQL同时兼容。

**2. 版本控制 (Git)**

* **分支策略:**
    * `master`: 稳定发布版本。
    * `develop`: 开发主分支。
    * `feature/*`: 功能开发分支。
    * `release/*`: 预发布分支。
    * `hotfix/*`: 紧急修复分支。
* **提交规范:**
    * 使用清晰、简洁的提交信息，遵循 [Conventional Commits](https://www.google.com/search?q=https://www.conventionalcommits.org/zh-CN/v1.0.0/) 规范。
    * 自动对比上次提交，生成日志，并写入History.md前，提交用户进行代码审查，确认后进行提交和History写入。

* **代码审查:** 使用 GitLab 或 GitHub 的 Pull Request 进行代码审查，确保代码质量。

**3. 代码风格**

* **编码规范:** 遵循《阿里巴巴 Java 开发手册》。
* **代码格式化:** 提交前，对代码代码格式检查。
* **注释规范:**
    * 类、方法、字段必须添加 Javadoc 注释。
    * 复杂逻辑添加行内中文注释。

**4. 测试**

* **单元测试:** 使用 JUnit 进行单元测试，生成对应测试文件，覆盖率要求 80% 以上。
* **集成测试:** 使用 Spring Test 进行集成测试，生成对应测试文件，测试 API 和模块之间的交互。
* **性能测试:** 使用 JMeter 进行性能测试，生成对应测试文件，确保系统在高并发下的稳定性。
* **代码覆盖率:** 使用 JaCoCo 或 Cobertura 统计代码覆盖率。

**5. 多语言**

* **资源文件:** 使用 YAML 文件管理多语言资源。
* **国际化框架:** 使用 Spring Boot 的国际化支持。
* **编码:** 使用 UTF-8 编码。

**6. 多数据库**

* **数据访问层:** 使用 MyBatis-Plus ORM 框架，支持多种数据库。
* **数据库配置:** 使用 Spring Boot 的 profiles 功能，根据环境加载不同的数据库配置。
* **数据迁移:** 使用 Flyway 进行数据库迁移管理。

**7. 多 CPU**

* **并发编程:** 优先使用Actor解决并发，不然使用Java 的并发工具类 (如 ExecutorService, CompletableFuture) 。
* **线程池:** 合理配置线程池大小，避免资源浪费或线程过多。
* **性能优化:** 使用 JProfiler 或 VisualVM 进行性能分析，优化 CPU 使用率。

**8. 容器及 K3s/K8s 交付**

* **Docker 镜像:** 使用 Dockerfile 构建 Docker 镜像。
* **容器编排:** 使用 Kubernetes (K8s) 或 K3s 进行容器编排和管理。
* **CI/CD:** 使用 Jenkins 或 GitLab CI/CD 进行持续集成和持续交付。

**9. 其他**

* **安全:** 遵循 OWASP Top 10 安全规范，防止常见的 Web 安全漏洞。
* **日志:** 使用 SLF4J 和 Logback 进行日志记录，方便排查问题。
* **异常处理:** 使用统一的异常处理机制，避免异常信息泄露。
* **API 设计:** 遵循 RESTful API 

