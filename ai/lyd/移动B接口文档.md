# 中国移动动力环境集中监控系统规范
## - B 接口技术规范分册

##### 中国移动通信企业标准
##### Specification of supervision system for power and environment of CMCC
##### - Technical Specification for B Interface

### 中国移动通信有限公司网络部
##### 2016年-07月-18日 发布

### 目录
1.  范围.........................................................45
2.  规范性引用文件...............................................45
3.  术语定义.....................................................45
4.  接口网络结构.................................................56
    4.1. 接口方式..........................................................................................…67
    4.2. 接入双方要求....................................................................................67
5.  B 接口协议....................................................67
    5.1. 报文原则............................................................................................67
    5.2. WSDL 定义.........................................................................................67
    5.3. 基本定义............................................................................................78
    5.4. 基本报文格式定义........................................................................1113
    5.5. 报文类型定义................................................................................1113
    5.6. 数据流方式和格式定义................................................................1314
        5.6.1. FSU 向 SC 注册..............................................................................1314
        5.6.2. 上报告警信息................................................................................1516
        5.6.3. 请求监控点数据............................................................................1718
        5.6.4. 写监控点设置值............................................................................1921
        5.6.5. 请求监控点门限数据....................................................................2123
        5.6.6. 写监控点门限数据........................................................................2325
        5.6.7. 获取 FSU 注册信息.......................................................................2627
        5.6.8. 批量设置 FSU 注册信息...............................................................2729
        5.6.9. 获取 FSU 的 FTP 信息...................................................................2931
        5.6.10. 设置 FSU 的 FTP 信息...........................................................3032
        5.6.11. 时间同步.................................................................................3233
        5.6.12. 获取 FSU 状态信息...............................................................3335
        5.6.13. 更新 FSU 状态信息获取周期...............................................3436
        5.6.14. 重启 FSU................................................................................3638
        5.6.15. 上报监控点数据.....................................................................3739
        5.6.16. 请求动环设备配置数据.........................................................3941
        5.6.17. 上报动环设备的配置数据.....................................................4142
        5.6.18. 写动环设备的配置数据.........................................................4344
    5.7. FTP 接口能力.................................................................................4546
        5.7.1. 批量获取监控对象的配置数据....................................................4547
        5.7.2. 获取监控图像文件........................................................................4647
        5.7.3. 获取活动、历史告警同步文件....................................................4647
        5.7.4. 上传 FSU 相关文件.......................................................................4648
        5.7.5. 获取监控点性能数据文件............................................................4648
        5.7.6. 获取日志文件................................................................................4748
6.  编制历史...................................................4849

#### 前 言

为进一步规范中国移动动力环境集中监控系统（以下简称动环监控系统）建设，提升动环监控系统对动力专业运维管理的支撑能力，促进动环监控系统持续健康发展，中国移动通信有限公司制定了动力环境集中监控系统系列技术规范和测试规范。具体包括《总体技术规范分册》、《SC 技术规范分册》、《C接口技术规范分册》、《B 接口技术规范分册》、《FSU 技术规范分册》、《SC 测试规范分册》、《C 接口测试规范分册》、《B接口测试规范分册》、《FSU 测试规范分册》等。

本分册为《B 接口技术规范分册》，主要阐述了 B接口定义、接口网络结构、B接口协议要求等。

本规范由中国移动通信有限公司网络部提出并归口。

本规范版权由中国移动通信有限公司所有。未经本公司书面许可,任何单位与个人不得以任何形式摘抄、复制文档的部分或全部，并以任何形式传播。

本规范起草单位：中国移动通信有限公司
本规范主要起草人：穆赞、胡亚希、罗勇、钱钊钏、徐铎、杜翛、王宇剑、艾兴华

#### 1. 范围

本标准从 **B接口定义**、**接口网络结构**、**B接口协议要求** 等方面对动力环境集中监控系统 **B接口** 提出了要求，供中国移动通信集团内部各省公司使用。它适用于中国移动动力、环境集中监控系统的建设和监控系统、设备入网测试等工作。

#### 2. 规范性引用文件

下列文件对本文件的应用是必不可少的。凡是注日期的引用文件，仅注日期的版本适用于本文件。凡是不注日期的引用文件，其最新版本（包括所有的修改版）适用于本文件。



#### 3. 术语定义

*   **监控系统－Supervision System**
    监控系统指从数据采集设备到 SC 的整套软硬件系统，能对通信机房的动力设备及环境进行遥测、遥信、遥控和遥调，实时监视其运行参数，监测和处理故障，记录和处理相关数据，从而实现移动通信机房少人或无人值守和集中维护。
*   **监控中心－Supervision Center（SC）**
    面向多 FSU 管理的高级监控层次，即监控中心，将 FSU 的信息汇集、处理、共享，监控管理人员可在此对系统进行集中管理、控制，对监控信息进行使用、处置。
    为了适应维护管理体制，SC 可以按需进行分层级建设，例如地市级可以建设区域监控中心（Local Supervision Center，LSC），省级可以建设集中监控中心（Central Supervision Center，CSC）。
*   **现场监控单元－Field supervision unit（FSU)**
    动环监控系统的最小子系统，即现场监控单元，由若干监控模块和其它辅助设备组成，面向直接的设备数据采集、处理的监控层次，可以包含采样、数据处理、数据中继等功能。
*   **通信协议－Communication Protocol（CP）**
    规范两个实体之间进行标准通信的应用层规约。
*   **A 接口－A Interface**
    指现场监控单元（FSU）与监控对象（SO）之间的接口。
*   **B 接口－B Interface**
    指监控中心（SC）与现场监控单元（FSU）之间的接口。
*   **监控模块 Supervision Module(SM)**
    完成特定设备、环境量监控及管理功能，并提供相应监控信息的设备。
*   **监控对象－Supervision Object(SO)**
    被监控的各种电源、空调设备及机房环境。
*   **监控点－Supervision Point(SP)**
    指监控对象上某个特定的监控信号。

#### 4. 接口网络结构

图1 网络结构图

##### 4.1. 接口方式

**FSU** 与 **SC** 之间通过 **WebService** 和 **FTP** 方式互联，二者同时形成完整的 B接口协议标准。FSU 与 SC 之间的数据流交互采用基于 **Soap+XML+文本文件技术** 的接口。

##### 4.2. 接入双方要求

*   **SC 提供 WebService 服务**，用于接收 FSU 的注册、告警信息上报、监控点数据上报、动环设备配置数据上报。
*   **FSU 提供 WebService 服务**，用于响应 SC 主动请求的监控点数据、写监控点设置值、请求监控点门限数据、写监控点门限数据、获取 FSU 注册信息、设置 FSU 注册信息、获取 FSU 的 FTP 信息、设置 FSU 的 FTP 信息、时间同步、获取 FSU 状态信息（心跳机制）、更新 FSU 状态信息获取周期、重启 FSU、请求动环设备配置数据、写动环设备配置数据。
*   **FSU 提供 FTP 服务**，用于 SC 批量获取监控对象的配置数据、定期获取监控图像文件、获取活动、历史告警同步文件、获取监控点性能数据文件、上传 FSU 相关文件、获取日志文件。

#### 5. B 接口协议

##### 5.1. 报文原则

SC 与 FSU 之间的接口基于 **WebService 技术**，消息协议采用 **XML 格式**。

##### 5.2. WSDL 定义

SC 提供的 Webservice 接口的 WSDL 定义见附件 `SCService.wsdl`。
FSU 接口的 Webservice 接口的 WSDL 定义见附件 `FSUService.wsdl`。

##### 5.3. 基本定义

a) **告警消息**：监控对象及 FSU 上报的所有告警信息。
b) **FSUID**：用于中国移动全网唯一标示 FSU 设备的编号，具体定义详见《中国移动动环命名及编码指导意见》。
c) **动环设备ID**：即监控对象的编码，该编码在站点内唯一，可配置，后面简称“设备ID”，具体定义详见《中国移动动环命名及编码指导意见》。
d) **监控点ID**：同类型设备唯一，可配置，每类信号的编码详见《中国移动动环标准化字典表（2016年）》。
e) **数据类型的字节数定义**

表1 数据类型字节数定义

| 类型     | 字节数 |
| :------- | :----- |
| Long     | 4 字节 |
| Short    | 2 字节 |
| Char     | 1 字节 |
| Float    | 4 字节 |
| 枚举类型 | 4 字节 |

f) **工作过程定义**

SC、FSU 根据下图所示，建立连接：

图2 连接建立过程

工作过程如下：
1.  FSU 和 SC 之间传送 `LOGIN`, `LOGIN_ACK` 报文进行注册；报文使用的用户名和密码必须为 SC 提供给 FSU 的合法用户名和密码，由 SC 进行认证。
2.  如果认证通过即注册成功，则 B接口协议通过这个连接进行通讯。
3.  当 FSU 与 SC 之间的连接意外中断后，FSU 必须重新进行上述连接和注册过程。

g) **常量定义**

表2 常量定义

| 属性名称          | 属性描述           | 长度及类型    |
| :---------------- | :----------------- | :------------ |
| USER_LENGTH       | 用户名长度         | 20 字节       |
| PASSWORD_LEN      | 口令长度           | 20 字节       |
| DES_LENGTH        | 描述信息长度       | 120 字节      |
| VER_LENGTH        | 版本描述的长度     | 20 字节       |
| FSUID_LEN         | FSU ID 字符串长度  | 20 字节       |
| NMALARMID_LEN     | 网管告警编号       | 40 字节       |
| IP_LENGTH         | IP 串长度          | 15 字节       |
| DEVICEID_LEN      | 设备 ID 长度       | 26 字节       |
| ID_LENGTH         | 监控点/站点/机房 ID 长度 | 20 字节       |
| SERIALNO_LEN      | 告警序号长度       | 10 字节       |
| TIME_LEN          | 时间串长度         | 19 字节       |
| DEV_CONF_LEN      | 设备配置信息长度   | 6000 字节     |
| ALARMREMARK_LEN   | 告警预留字段       | 60 字节       |
| NAME_LENGTH       | 名字命名长度       | 80 字节       |
| FAILURE_CAUSE_LEN | 失败原因描述信息长度 | 40 字节       |

h) **枚举定义**

表3 枚举定义

| 属性名称 | 属性描述      | 枚举类型                                                                                                                                                                                                                                                        | 类型定义 |
| :------- | :------------ | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------- |
| EnumResult | 报文返回结果  | FAILURE＝0 失败<br>SUCCESS＝1 成功                                                                                                                                                                                                                          | EnumType |
| EnumType | 监控系统数据的种类 | DI＝4 数字输入量（包含多态数字输入量），遥信<br>AI＝3 模拟输入量，遥测<br>DO＝1 数字输出量，遥控<br>AO＝2 模拟输出量，遥调                                                                                                                                 |          |
| EnumState | 数据值的状态  | NOALARM＝0 正常数据<br>CRITICAL＝1 一级告警<br>MAJOR＝2 二级告警<br>MINOR＝3 三级告警<br>HINT＝4 四级告警<br>OPEVENT＝5 操作事件<br>INVALID＝6 无效数据                                                                                                       |          |
| EnumFlag | 告警标志      | BEGIN 开始<br>END 结束                                                                                                                                                                                                                                      |          |

i) **数据结构定义**

表4 数据结构定义

| 结构名称   | 结构描述     | 属性名称        | 属性数据类型       | 类型定义                                                                                                   |
| :--------- | :----------- | :-------------- | :----------------- | :--------------------------------------------------------------------------------------------------------- |
| TTime      | 时间的结构   | Year            | short              | 年                                                                                                         |
|            |              | Month           | Char               | 月                                                                                                         |
|            |              | Day             | Char               | 日                                                                                                         |
|            |              | Hour            | Char               | 时                                                                                                         |
|            |              | Minute          | Char               | 分                                                                                                         |
|            |              | Second          | Char               | 秒                                                                                                         |
| TSemaphore | 信号量的值的结构 | Type            | EnumType           | 数据类型                                                                                                   |
|            |              | ID              | Char[ID_LENGTH]    | 监控点 ID                                                                                                  |
|            |              | SignalNumber 注 1 | Char[SERIALNO_LEN] | 同设备同类监控点顺序号                                                                                     |
|            |              | MeasuredVal     | Float              | 实测值                                                                                                     |
|            |              | SetupVal        | Float              | 设置值                                                                                                     |
|            |              | Status          | EnumState          | 状态                                                                                                       |
|            |              | Time            | Char[TIME_LEN]     | 时间，格式 YYYY-MM-DD<SPA CE 键>hh:mm:ss （采用 24 小时的时间制式）                                        |
| TThreshold | 信号量的门限值的结构 | Type            | EnumType           | 数据类型                                                                                                   |
|            |              | ID              | Char[ID_LENGTH]    | 监控点 ID                                                                                                  |
|            |              | SignalNumber 注 1 | Char[SERIALNO_LEN] | 同设备同类监控点顺序号                                                                                     |
|            |              | Threshold       | Float              | 门限值                                                                                                     |
|            |              | AbsoluteVal     | Float              | 绝对阀值                                                                                                   |
|            |              | RelativeVal     | Float              | 百分比阀值                                                                                                 |
|            |              | Status          | EnumState          | 状态                                                                                                       |
| TAlarm     | 告警消息的结构 | SerialNo        | Char[SERIALNO_LEN] | 告警序号                                                                                                   |
|            |              | ID              | Char[ID_LENGTH]    | 监控点 ID                                                                                                  |
|            |              | DeviceID        | Char[DEVICEID_LEN] | 设备 ID                                                                                                    |
|            |              | NMAlarmID       | Char[NMALARMID_LEN] | 网管告警编号（告警标准化编号）                                                                             |
|            |              | AlarmTime       | Char [TIME_LEN]    | 告警时间，YYYY-MM-DD<SPA CE 键>hh:mm:ss （采用 24 小时的时间制式）                                        |
|            |              | AlarmLevel      | EnumState          | 告警级别                                                                                                   |
|            |              | AlarmFlag       | EnumFlag           | 告警标志                                                                                                   |
|            |              | AlarmDesc       | Char [DES_LENGTH]  | 告警的事件描述                                                                                             |
|            |              | EventValue      | Float              | 告警触发值                                                                                                 |
|            |              | SignalNumber 注 1 | Char[SERIALNO_LEN] | 同设备同类监控点顺序号                                                                                     |
|            |              | AlarmRemark     | Char[ALARMREMARK_LEN] | 预留字段                                                                                                   |
| TFSUStatus | FSU 状态参数 | CPUUsage        | Float              | CPU 使用率                                                                                                 |
|            |              | MEMUsage        | Float              | 内存使用率                                                                                                 |
|            |              | HardDiskUsage   | Float              | FSU 硬盘占用率（含 SD 卡等存储介质）                                                                       |
| TDevConf   | 监控对象配置信息 | DeviceID        | Char[DEVICEID_LEN] | 设备 ID                                                                                                    |
|            |              | DeviceName      | Char[NAME_LENGTH]  | 设备名称[定义参考中国移动动环命名及编码指导意见]                                                           |
|            |              | SiteName        | Char[NAME_LENGTH]  | 设备所在的站点名称[定义参考中国移动动环命名及编码指导意见 1.1]                                             |
|            |              | RoomName        | Char[NAME_LENGTH]  | 设备所在的机房名称[定义参考中国移动动环命名及编码指导意见 1.2]                                             |
|            |              | DeviceType      | EnumDeviceType     | 设备类型（按动环标准化定义）                                                                               |
|            |              | DeviceSubType   | EnumDeviceSubType  | 设备子类型（按动环标准化定义）                                                                             |
|            |              | Model           | Char [DES_LENGTH]  | 设备型号                                                                                                   |
|            |              | Brand           | Char [DES_LENGTH]  | 设备品牌                                                                                                   |
|            |              | RatedCapacity   | Float              | 额定容量                                                                                                   |
|            |              | Version         | Char [VER_LENGTH]  | 版本                                                                                                       |
|            |              | BeginRunTime    | Char [TIME_LEN]    | 启用时间                                                                                                   |
|            |              | DevDescribe     | Char [DES_LENGTH]  | 设备描述信息（包含设备的安装位置）                                                                         |
|            |              | Signals         | N\*TSignal         | 一个或多个监控点信号配置信息 注 2 。                                                                       |

**注 1**：该字段适用于同一个设备上同一个监控点有多个实例的场景。即对应于《中国移动动环标准化字典表（2016 年）》信号名中标注为 XXX 的内容。例如：信号 ID 为 007303，信号名称为单体 XXX 电压，当该序号为 020，则代表单体 020 电压。如果不适用上述场景，该字段取值置为“NULL”。
**注 2**：结构体 TSignal 定义具体见下表：

表5 TSignal 数据类型定义

| 类型名称 | 描述         | 属性名称        | 属性数据类型       | 类型定义                                                                                                                                                                                                                                                                     |
| :------- | :----------- | :-------------- | :----------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| TSignal  | 监控点信号配置信息 | Type            | EnumType           | 数据类型                                                                                                                                                                                                                                                     |
|          |              | ID              | Char[ID_LENGTH]    | 监控点 ID                                                                                                                                                                                                                                                    |
|          |              | SignalName      | Char[NAME_LENGTH]  | 信号名称                                                                                                                                                                                                                                                     |
|          |              | SignalNumber 注 1 | Char[SERIALNO_LEN] | 同设备同类监控点顺序号                                                                                                                                                                                                                                       |
|          |              | AlarmLevel      | EnumState          | 告警等级                                                                                                                                                                                                                                                     |
|          |              | Threshold       | Float              | 门限值                                                                                                                                                                                                                                                       |
|          |              | AbsoluteVal     | Float              | 绝对阀值                                                                                                                                                                                                                                                     |
|          |              | RelativeVal     | Float              | 百分比阀值                                                                                                                                                                                                                                                   |
|          |              | Describe        | Char [DES_LENGTH]  | 描述信息。状态信号为状态描述,，格式举例：0&正常;1&告警 。模拟信号为单位。                                                                                                                                                                              |
|          |              | NMAlarmID       | Char[NMALARMID_LEN] | 网管告警编号（参照《中国移动动环命名及编码指导意见》）                                                                                                                                                                                       |

##### 5.4. 基本报文格式定义

表6 基本报文格式定义

| 类型   | 一级节点 | 二级节点 | 定义   |
| :----- | :------- | :------- | :----- |
| 请求报文 | Request  | PK\_Type | 报文类型 |
|        |          | Info     | 报文内容 |
| 响应报文 | Response | PK\_Type | 报文类型 |
|        |          | Info     | 报文内容 |

完整的接口交互由请求报文和响应报文组成，每个请求报文必须有一个响应报文进行反馈。报文类型参见 5.5 报文类型定义，数据流方式和格式定义参见 5.6 数据流方式和格式定义。

##### 5.5. 报文类型定义

表7 报文类型定义

| 报文类型                 | 报文动作             | 数据流向   | 类型名称                     |
| :----------------------- | :------------------- | :--------- | :--------------------------- |
| FSU向SC注册                | 注册                 | SC<—FSU    | LOGIN                        |
|                          | 注册响应             | SC—>FSU    | LOGIN\_ACK                   |
| 上报告警信息             | 实时告警发送         | SC<—FSU    | SEND\_ALARM                  |
|                          | 实时告警发送确认     | SC—>FSU    | SEND\_ALARM\_ACK             |
| 请求监控点数据           | 监控点数据请求       | SC—>FSU    | GET\_DATA                    |
|                          | 请求监控点数据响应   | SC<—FSU    | GET\_DATA\_ACK               |
| 写监控点设置值           | 写监控点设置值请求   | SC—>FSU    | SET\_POINT                   |
|                          | 写监控点设置值响应   | SC<—FSU    | SET\_POINT\_ACK              |
| 请求监控点门限数据       | 监控点门限数据请求   | SC—>FSU    | GET\_THRESHOLD               |
|                          | 请求监控点门限数据响应 | SC<—FSU    | GET\_THRESHOLD\_ACK          |
| 写监控点门限数据         | 写监控点门限数据请求 | SC—>FSU    | SET\_THRESHOLD               |
|                          | 写监控点门限数据响应 | SC<—FSU    | SET\_THRESHOLD\_ACK          |
| 获取 FSU注册信息         | 获取 FSU 注册信息请求 | SC—>FSU    | GET\_LOGININFO               |
|                          | 获取 FSU 注册信息响应 | SC<—FSU    | GET\_LOGININFO\_ACK          |
| 设置 FSU注册信息         | 设置 FSU 注册信息请求 | SC—>FSU    | SET\_LOGININFO               |
|                          | 设置 FSU 注册信息响应 | SC<—FSU    | SET\_LOGININFO\_ACK          |
| 获取 FSU的FTP信息        | 获取 FSU 的 FTP 信息请求 | SC—>FSU    | GET\_FTP                     |
|                          | 获取 FSU 的 FTP 信息响应 | SC<—FSU    | GET\_FTP\_ACK                |
| 设置 FSU的FTP信息        | 设置 FSU 的 FTP 信息请求 | SC—>FSU    | SET\_FTP                     |
|                          | 设置 FSU 的 FTP 信息响应 | SC<—FSU    | SET\_FTP\_ACK                |
| 时间同步                 | 时间同步请求         | SC—>FSU    | TIME\_CHECK                  |
|                          | 时间同步响应         | SC<—FSU    | TIME\_CHECK\_ACK             |
| 获取 FSU的状态信息（心跳机制） | 获取 FSU 的状态参数请求 | SC—>FSU    | GET\_FSUINFO                 |
|                          | 获取 FSU 的状态参数响应 | SC<—FSU    | GET\_FSUINFO\_ACK            |
| 更新 FSU状态信息获取周期（心跳机制） | 更新 FSU 状态信息获取周期请求 | SC—>FSU    | UPDATE\_FSUINFO\_INTERVAL    |
|                          | 更新 FSU 状态信息获取周期响应 | SC<—FSU    | UPDATE\_FSUINFO\_INTERVAL\_ACK |
| 重启 FSU                 | 重启 FSU 请求        | SC—>FSU    | SET\_FSUREBOOT               |
|                          | 重启 FSU 响应        | SC<—FSU    | SET\_FSUREBOOT\_ACK          |
| 上报监控点数据           | 上报监控点数据请求   | SC<—FSU    | SEND\_DATA                   |
|                          | 上报监控点数据响应   | SC—>FSU    | SEND\_DATA\_ACK              |
| 请求动环设备配置数据     | 动环配置数据请求     | SC—>FSU    | GET\_DEV\_CONF               |
|                          | 动环配置数据确认     | SC<—FSU    | GET\_DEV\_CONF\_ACK          |
| 上报动环设备配置数据     | 上报动环设备配置变更数据请求 | SC<—FSU    | SEND\_DEV\_CONF\_DATA        |
|                          | 上报动环设备配置变更数据响应 | SC—>FSU    | SEND\_DEV\_CONF\_DATA\_ACK   |
| 写动环设备配置数据       | 写动环设备配置数据请求 | SC—>FSU    | SET\_DEV\_CONF\_DATA         |
|                          | 写动环设备配置数据响应 | SC<—FSU    | SET\_DEV\_CONF\_DATA \_ACK   |

##### 5.6. 数据流方式和格式定义

##### 5.6.1. FSU 向 SC 注册

a) **数据流方式**
FSU 向 SC 传送 FSUID、用户名、口令、内网IP、MAC地址和版本号。SC 验证用户名和口令是否正确，如果不正确，则向 FSU 返回注册失败的报文，并给出失败具体原因。
**注**：FSU 上报给 SC 的账户信息均具备对 FSU 管理的最高权限（可读可写）。注册失败时 FSU 和 SC 要分别记录日志。

图3 注册过程

b) **数据流格式定义**
动作：注册
发起：FSU

表8 FSU 向 SC 注册请求报文

| 发起客户端 | 字段    | 变量名称/报文定义 | 长度及类型          | 描述         |
| :--------- | :------ | :---------------- | :------------------ | :----------- |
|            | PK\_Type | Name              | Char\[LOGIN]        | 登录命令名   |
|            | Info    | UserName          | Char\[USER\_LENGTH] | 用户名       |
|            |         | PassWord          | Char\[PASSWORD\_LEN] | 口令（采用 MD5 进行加密） |
|            |         | FSUID             | Char\[FSUID\_LEN]   | FSU ID 号    |
|            |         | FSUIP             | Char\[IP\_LENGTH]   | FSU 的内网 IP |
|            |         | FSUMAC            | Char\[MAC\_LENGTH]  | FSU 的 MAC 地址 |
|            |         | FSUVER            | Char\[VER\_LENGTH]  | FSU 版本号   |

XML样例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>LOGIN</Name>
</PK_Type>
<Info>
<UserName/>
<PassWord/>
<FSUID/>
<FSUIP/>
<FSUMAC/>
<FSUVER/>
</Info>
</Request>
```
响应：SC

表9 SC 应答 FSU 注册报文

| 变量名称/报文定义 | 长度及类型            | 描述                                                                                                           |
| :---------------- | :-------------------- | :------------------------------------------------------------------------------------------------------------- |
| PK\_Type | Name              | Char\[LOGIN\_ACK]     | 登录命令相应                                                                                                   |
| Info              | Result                | EnumResult            | 返回注册结果。                                                                                                 |
|                   | FailureCause          | Char\[FAILURE\_CAUSE\_LEN] | 上报告警失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

XML样例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>LOGIN_ACK</Name>
</PK_Type>
<Info>
<Result/>
<FailureCause/>
</Info>
</Response>
```

##### 5.6.2. 上报告警信息

a) **数据流方式**
FSU 根据设备产生告警或者根据遥测量判断有告警需上报时，向 SC 上报告警信息，SC 返回确认信息。
如果因网络中断等原因导致告警数据上报失败，待网络恢复后，FSU 需要重新上报失败的告警。如果因服务未及时响应 (请求超时默认 30s) 导致告警上传失败，需要重新上报（最多尝试 3 次）。

图4 上报告警信息过程

b) **数据流格式定义**
发起：FSU

表10 上报告警信息报文

| 变量名称/报文定义 | 长度及类型        | 描述     |
| :---------------- | :---------------- | :------- |
| PK\_Type | Name              | Char\[SEND\_ALARM] | 告警上报 |
| Info              | FSUID             | Char\[FSUID\_LEN]  | FSUID    |
|                   | Values            | TAlarm            | 告警信息 |

XML样例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SEND_ALARM</Name>
</PK_Type>
<Info>
<FSUID/>
<Values>
<TAlarmList>
<TAlarm>
<SerialNo/>
<ID/>
<DeviceID/>
<NMAlarmID/>
<AlarmTime/>
<AlarmLevel/>
<AlarmFlag/>
<AlarmDesc/>
<EventValue/>
<SignalNumber/>
<AlarmRemark/>
</TAlarm>
<TAlarm>
<SerialNo/>
<ID/>
<DeviceID/>
<NMAlarmID/>
<AlarmTime/>
<AlarmLevel/>
<AlarmFlag/>
<AlarmDesc/>
<EventValue/>
<SignalNumber/>
<AlarmRemark/>
</TAlarm>
</TAlarmList>
</Values>
</Info>
</Request>
```

响应：SC

表11 上报告警信息应答报文

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                                           |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------------------- |
| PK\_Type | Name                       | Char\[SEND\_ALARM\_ACK]     | 告警信息                                                                                                       |
| Info              | Result                     | EnumResult                 | 返回设置结果                                                                                                   |
|                   | FailureCause               | Char\[FAILURE\_CAUSE\_LEN] | 上报告警失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

XML样例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SEND_ALARM_ACK</Name>
</PK_Type>
<Info>
<Result/>
<FailureCause/>
</Info>
</Response>
```

##### 5.6.3. 请求监控点数据

a) **数据流方式**
SC 向 FSU 发送所需数据的标识，FSU 向 SC 发送要求的监控点数据信息。

图5 监控点数据过程

b) **数据流格式定义**
发起：SC

表12 监控点数据报文

| 变量名称/报文定义 | 长度及类型             | 描述                                                                                                                                                                                                                                                                                                         |
| :---------------- | :--------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| PK\_Type | Name                   | Char\[GET\_DATA]       | 监控点数据                                                                                                                                                                                                                                                                   |
| Info              | FSUID                  | Char\[FSUID\_LEN]      | FSU ID 号                                                                                                                                                                                                                                                                    |
|                   | DeviceID               | Char\[DEVICEID\_LEN]   | 设备 ID。当为空，则返回该 FSU 所监控的所有设备的监控点的值；这种情况下，忽略 IDs 参数（即监控点 ID 列表）。                                                                                                                                                                  |
|                   | IDs                    | n\*ID\_LENGTH          | 相应的监控点 ID 号。当为空，则返回该设备的所有监控点的值。                                                                                                                                                                                                                   |

XML样例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>GET_DATA</Name>
</PK_Type>
<Info>
<FSUID/>
<DeviceList>
<Device ID="000000000001">
<ID/>
<ID/>
<ID/>
</Device>
<Device ID="000000000002">
<ID/>
<ID/>
<ID/>
</Device>
</DeviceList>
</Info>
</Request>
```

响应：FSU

表13 监控点数据应答报文

| 变量名称/报文定义 | 长度及类型                     | 描述                                                                                                                                                                                                                                                                                                                                                                                              |
| :---------------- | :----------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| PK\_Type | Name                           | Char\[GET\_DATA\_ACK]          | 监控点数据响应                                                                                                                                                                                                                                                        |
| Info              | FSUID                          | Char\[FSUID\_LEN]              | FSU ID 号                                                                                                                                                                                                                                                             |
|                   | Result                         | EnumResult                     | 请求数据成功与否的标志。                                                                                                                                                                                                                                              |
|                   | Values                         | Sizeof(TSemaphore)             | 对应 5.3 中的 TSemaphore 的数据结构定义。                                                                                                                                                                                                                           |
|                   | FailureCause                   | Char\[FAILURE\_CAUSE\_LEN]     | 请求监控点数据失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。                                                                                                                                                                      |

XML样例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>GET_DATA_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<Values>
<DeviceList>
<Device ID="000000000001">
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
</Device>
<Device ID="000000000002">
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
</Device>
</DeviceList>
</Values>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.4. 写监控点设置值

a) **数据流方式**
SC 向 FSU 发送监控点的标识 ID 和新设置值，FSU 设置监控点的新设置值并向 SC 返回设置结果。

**图6 写监控点的设置值过程**
（图片描述：此图展示了SC写监控点设置值的过程。SC发送 **SET_POINT** 消息给FSU，FSU设置后返回 **SET_POINT_ACK** 消息，表示设置成功与否。）

b) **数据流格式定义**

发起：**SC**

**表14 写监控点的设置值报文**

| 变量名称/报文定义 | 长度及类型                   | 描述                                       |
| :---------------- | :--------------------------- | :----------------------------------------- |
| PK_Type Name      | Char[SET_POINT]              | 写监控点的设置值                           |
| Info FSUID        | Char[FSUID_LEN]              | 单个 FSU ID 号                             |
| n*DeviceID        | n*Char[DEVICEID_LEN]         | n 个设备 ID 的列表                         |
| m*Value           | m*Sizeof(TSemaphore)         | m 个监控点的设置值，数据的值的类型由相应的数据结构决定 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SET_POINT</Name>
</PK_Type>
<Info>
<FSUID/>
<Value>
<DeviceList>
<Device ID="000000000001">
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
</Device>
<Device ID="000000000002">
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
</Device>
</DeviceList>
</Value>
</Info>
</Request>
```

响应：**FSU**

**表15 写监控点的设置值应答报文**

| 变量名称/报文定义 | 长度及类型                     | 描述                                                                                               |
| :---------------- | :----------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SET_POINT_ACK]            | 写监控点的设置值回应                                                                               |
| Info FSUID        | Char[FSUID_LEN]                | 单个 FSU ID 号                                                                                     |
| n*DeviceID        | n*Char[DEVICEID_LEN]           | n 个设备 ID 的列表                                                                                 |
| m*Id              | m*Sizeof(Long)                 | m 个控制或调节成功的 ID 的列表                                                                     |
| t*Id              | t*Sizeof(Long)                 | t 个控制或调节失败的 ID 的列表                                                                     |
| Result            | EnumResult                     | 写成功/失败（即控制的结果）                                                                        |
| FailureCause      | Char[FAILURE_CAUSE_LEN]        | 写监控点设置值失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SET_POINT_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
<DeviceList>
<Device ID="000000000001">
<SuccessList>
<Id/>
<Id/>
</SuccessList>
<FailList>
<Id/>
<Id/>
</FailList>
</Device>
<Device ID="000000000002">
<SuccessList>
<Id/>
<Id/>
</SuccessList>
<FailList>
<Id/>
<Id/>
</FailList>
</Device>
</DeviceList>
</Info>
</Response>
```

#### 5.6.5. 请求监控点门限数据

a) **数据流方式**
SC 向 FSU 发送所需数据的标识，FSU 向 SC 发送要求的监控点门限数据。

**图7 监控点门限数据**
（图片描述：此图展示了SC请求监控点门限数据的过程。SC发送 **GET_THRESHOLD** 消息给FSU，FSU返回 **GET_THRESHOLD_ACK** 消息，包含监控点门限数据。）

b) **数据流格式定义**

发起：**SC**

**表16 监控点门限数据报文**

| 变量名称/报文定义 | 长度及类型        | 描述                                                                                                       |
| :---------------- | :---------------- | :--------------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[GET_THRESHOLD] | 监控点门限数据                                                                                             |
| FSUID             | Char[FSUID_LEN]   | FSU ID 号                                                                                                  |
| DeviceID          | Char[DEVICEID_LEN] | 设备 ID。当为空，则返回该 FSU 所监控的所有设备的监控点门限数据，这种情况下，忽略 IDs 参数（即监控点 ID 列表）。 |
| IDs               | n*ID_LENGTH       | 相应的监控点 ID 号。当为空，则返回该设备的所有监控点的门限数据。                                           |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>GET_THRESHOLD</Name>
</PK_Type>
<Info>
<FSUID/>
<DeviceList>
<Device ID="000000000001">
<ID/>
<ID/>
<ID/>
</Device>
<Device ID="000000000002">
<ID/>
<ID/>
<ID/>
</Device>
</DeviceList>
</Info>
</Request>
```

响应：**FSU**

**表17 监控点门限数据应答报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[GET_THRESHOLD_ACK]    | 监控点门限数据响应                                                                                 |
| Info Result       | EnumResult                 | 请求数据成功与否的标志                                                                             |
| FSUID             | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| Values            | Sizeof(TThreshold)         | 对应 5.3 中的 TThreshold 的数据结构定义                                                            |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 请求监控点门限失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>GET_THRESHOLD_ACK</Name>
</PK_Type>
<Info>
<Result/>
<FSUID/>
<FailureCause/>
<Values>
<DeviceList>
<Device ID="000000000001">
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status=""/>
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status=""/>
</Device>
<Device ID="000000000002">
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status=""/>
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status=""/>
</Device>
</DeviceList>
</Values>
</Info>
</Response>
```

#### 5.6.6. 写监控点门限数据

a) **数据流方式**
SC 向 FSU 发送监控点的标识 ID 和新门限数据，FSU 设置监控点的新门限数据并向 SC 返回结果。如果写失败，则需自动重发一次。

**图8 写监控点门限数据**
（图片描述：此图展示了SC写监控点门限数据的过程。SC发送 **SET_THRESHOLD** 消息给FSU，FSU设置后返回 **SET_THRESHOLD_ACK** 消息，表示设置结果。）

b) **数据流格式定义**

发起：**SC**

**表18 写监控点门限数据报文**

| 变量名称/报文定义 | 长度及类型                   | 描述             |
| :---------------- | :--------------------------- | :--------------- |
| PK_Type Name      | Char[SET_THRESHOLD]          | 写监控点门限数据请求 |
| Info FSUID        | Char[FSUID_LEN]              | FSU ID 号        |
| n*DeviceID        | n*Char[DEVICEID_LEN]         | n 个设备 ID 的列表 |
| m*Value           | m*Sizeof(TThreshold)         | m 个监控点门限值，数据的值的类型由相应的数据结构决定 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SET_THRESHOLD</Name>
</PK_Type>
<Info>
<FSUID/>
<Value>
<DeviceList>
<Device ID="000000000001">
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status=""/>
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status=""/>
</Device>
<Device ID="000000000002">
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status="" />
<TThreshold Type="" ID="" SignalNumber="" Threshold=""
AbsoluteVal="" RelativeVal="" Status="" />
</Device>
</DeviceList>
</Value>
</Info>
</Request>
```

响应：**FSU**

**表19 写监控点门限数据应答报文**

| 变量名称/报文定义 | 长度及类型                     | 描述                                                                                               |
| :---------------- | :----------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SET_THRESHOLD_ACK]        | 写监控点门限数据请求回应                                                                           |
| Info FSUID        | Char[FSUID_LEN]                | FSU ID 号                                                                                          |
| n*DeviceID        | n*Char[DEVICEID_LEN]           | n 个设备 ID 的列表                                                                                 |
| m*Id              | m*Sizeof(Long)                 | m 个写成功的 ID 的列表                                                                             |
| t*Id              | t*Sizeof(Long)                 | t 个写失败的 ID 的列表                                                                             |
| Result            | EnumResult                     | 写成功/失败（即控制的结果）                                                                        |
| FailureCause      | Char[FAILURE_CAUSE_LEN]        | 写监控点门限失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SET_THRESHOLD_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
<DeviceList>
<Device ID="000000000001">
<SuccessList>
<Id/>
<Id/>
</SuccessList>
<FailList>
<Id/>
<Id/>
</FailList>
</Device>
<Device ID="000000000001">
<SuccessList>
<Id/>
<Id/>
</SuccessList>
<FailList>
<Id/>
<Id/>
</FailList>
</Device>
</DeviceList>
</Info>
</Response>
```

#### 5.6.7. 获取 FSU 注册信息

a) **数据流方式**
SC 向 FSU 发送获取 FSU 向 SC 注册信息，FSU 返回注册信息。

**图9 获取 FSU 注册信息过程**
（图片描述：此图展示了SC获取FSU注册信息的过程。SC发送 **GET_LOGININFO** 消息给FSU，FSU返回 **GET_LOGININFO_ACK** 消息，包含获取结果。）

b) **数据流格式定义**

发起：**SC**

**表20 获取 FSU 注册信息报文**

| 变量名称/报文定义 | 长度及类型        | 描述     |
| :---------------- | :---------------- | :------- |
| PK_Type Name      | Char[GET_LOGININFO] | 获取注册信息 |
| Info FSUID        | Char[FSUID_LEN]   | FSU ID 号  |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>GET_LOGININFO</Name>
</PK_Type>
<Info>
<FSUID/>
</Info>
</Request>
```

响应：**FSU**

**表21 获取 FSU 注册信息响应报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[GET_LOGININFO_ACK]    | 获注册信息响应                                                                                     |
| Info UserName     | Char[USER_LENGTH]          | 用户名                                                                                             |
| PassWord          | Char[PASSWORD_LEN]         | 口令                                                                                               |
| FSUID             | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| FSUIP             | IP_LENGTH                  | FSU 的内网 IP                                                                                      |
| FSUMAC            | Char[MAC_LENGTH]           | FSU 的 MAC 地址                                                                                    |
| FSUVER            | Char[VER_LENGTH]           | FSU 版本号                                                                                         |
| SiteID            | Char[ID_LENGTH]            | 所属站点编码                                                                                       |
| RoomID            | Char[n* ID_LENGTH]         | FSU 物理机房编码                                                                                   |
| Result            | EnumResult                 | 成功/失败                                                                                          |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 获取 FSU 注册信息失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>GET_LOGININFO_ACK</Name>
</PK_Type>
<Info>
<UserName>cmcc</UserName>
<PassWord>cmcc</PassWord>
<FSUID/>
<FSUIP/>
<FSUVER/>
<SiteID/>
<RoomID/>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.8. 批量设置 FSU 注册信息

a) **数据流方式**
SC 向 FSU 发送设置注册的数据信息，FSU 存储注册数据并返回设置结果。如果设置成功，更新后的用户名和密码在 SC 下次访问 FSU 时生效。如果设置失败，则需 SC 自动重发且仅允许重发一次。
**注**：“设置失败”包括 FSU 返回“Failure”消息以及 FSU 响应超时两种情况。其中 FSU 响应超时定义为：从 SC 下发 SET_LOGININFO 消息时刻起，SC 超过 5s 未收到 FSU 的响应。

**图10 设置 FSU 注册信息过程**
（图片描述：此图展示了SC设置FSU注册信息的过程。SC发送 **SET_LOGININFO** 消息给FSU，FSU设置后返回 **SET_LOGININFO_ACK** 消息，表示设置结果。）

b) **数据流格式定义**

发起：**SC**

**表22 设置 FSU 注册信息报文**

| 变量名称/报文定义 | 长度及类型             | 描述     |
| :---------------- | :--------------------- | :------- |
| PK_Type Name      | Char[SET_LOGININFO]    | 设置注册信息 |
| Info UserName     | Char[USER_LENGTH]      | 用户名   |
| PassWord          | Char[PASSWORD_LEN]     | 口令     |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SET_LOGININFO</Name>
</PK_Type>
<Info>
<UserName>cmcc</UserName>
<PassWord>cmcc</PassWord>
</Info>
</Request>
```

响应：**FSU**

**表23 设置注册信息响应报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SET_LOGININFO_ACK]    | 设置注册信息响应                                                                                   |
| Info FSUID        | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| Result            | EnumResult                 | 设置成功/失败                                                                                      |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 设置 FSU 注册信息失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SET_LOGININFO_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.9. 获取 FSU 的 FTP 信息

a) **数据流方式**
SC 向 FSU 发送获取 FTP 用户、密码信息，FSU 返回 FTP 信息。

**图11 获取 FSU 的 FTP信息过程**
（图片描述：此图展示了SC获取FSU FTP信息的过程。SC发送 **GET_FTP** 消息给FSU，FSU返回 **GET_FTP_ACK** 消息，包含获取结果。）

b) **数据流格式定义**

发起：**SC**

**表24 获取 FSU的 FTP 数据**

| 变量名称/报文定义 | 长度及类型      | 描述                   |
| :---------------- | :-------------- | :--------------------- |
| PK_Type Name      | Char[GET_FTP]   | 获取 FTP 用户、密码    |
| Info FSUID        | Char[FSUID_LEN] | FSU ID 号              |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>GET_FTP</Name>
</PK_Type>
<Info>
<FSUID/>
</Info>
</Request>
```

响应：**FSU**

**表25 获取 FSU的 FTP 信息响应报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[GET_FTP_ACK]          | 获取 FTP 用户、密码响应                                                                            |
| Info FSUID        | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| UserName          | Char[USER_LENGTH]          | 用户登录名                                                                                         |
| PassWord          | Char[PASSWORD_LEN]         | 密码                                                                                               |
| Result            | EnumResult                 | 成功/失败                                                                                          |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 获取 FSU 的 FTP 信息失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>GET_FTP_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<UserName/>
<PassWord/>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.10. 设置 FSU 的 FTP 信息

a) **数据流方式**
SC 向 FSU 发送设置 FTP 用户、密码数据的信息，FSU 存储和设置 FTP 信息，并返回设置结果。

**图12 设置 FSU 的 FTP信息过程**
（图片描述：此图展示了SC设置FSU FTP信息的过程。SC发送 **SET_FTP** 消息给FSU，FSU设置后返回 **SET_FTP_ACK** 消息，表示设置结果。）

b) **数据流格式定义**

发起：**SC**

**表26 设置 FSU的 FTP 信息**

| 变量名称/报文定义 | 长度及类型      | 描述                   |
| :---------------- | :-------------- | :--------------------- |
| PK_Type Name      | Char[SET_FTP]   | 设置 FTP 用户、密码    |
| Info FSUID        | Char[FSUID_LEN] | FSU ID 号              |
| UserName          | Char[USER_LENGTH] | 用户登录名             |
| PassWord          | Char[PASSWORD_LEN] | 密码                   |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SET_FTP</Name>
</PK_Type>
<Info>
<FSUID/>
<UserName/>
<PassWord/>
</Info>
</Request>
```

响应：**FSU**

**表27 设置 FSU的 FTP 信息响应报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SET_FTP_ACK]          | 设置 FTP 用户、密码响应                                                                            |
| Info FSUID        | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| Result            | EnumResult                 | 设置成功/失败                                                                                      |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 设置 FSU 的 FTP 信息失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SET_FTP_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.11. 时间同步

a) **数据流方式**
SC 向 FSU 发送标准时间信息，该信息在 FSU 向 SC 注册成功后发送，也可以进行手动发送。FSU 按参数更新时间并返回对时结果。

**图13 时间同步过程**
（图片描述：此图展示了SC与FSU的时间同步过程。SC发送 **TIME_CHECK** 消息给FSU，FSU更新时间后返回 **TIME_CHECK_ACK** 消息，表示对时结果。）

b) **数据流格式定义**

发起：**SC**

**表28 时间同步报文**

| 变量名称/报文定义 | 长度及类型      | 描述         |
| :---------------- | :-------------- | :----------- |
| PK_Type Name      | Char[TIME_CHECK] | 时间同步报文 |
| Info FSUID        | Char[FSUID_LEN] | FSU ID 号    |
| Time              | Sizeof(TTime)   | 本机时间     |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>TIME_CHECK</Name>
</PK_Type>
<Info>
<FSUID/>
<Time>
<Year/>
<Month/>
<Day/>
<Hour/>
<Minute/>
<Second/>
</Time>
</Info>
</Request>
```

响应：**FSU**

**表29 时间同步应答报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[TIME_CHECK_ACK]       | 时间同步回应                                                                                       |
| Info FSUID        | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| Result            | EnumResult                 | 同步成功/失败                                                                                      |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 时间同步失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>TIME_CHECK_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.12. 获取 FSU 状态信息

a) **数据流方式**
SC 向 FSU 定期发送获取 FSU 状态信息的请求，FSU 返回当前 FSU 状态参数。时间间隔可设置，默认为 10 分钟。

**图14 获取 FSU 状态信息过程**
（图片描述：此图展示了SC获取FSU状态信息的过程。SC发送 **GET_FSUINFO** 消息给FSU，FSU返回 **GET_FSUINFO_ACK** 消息，包含FSU状态参数。）

b) **数据流格式定义**

发起：**SC**

**表30 获取 FSU 状态信息信息**

| 变量名称/报文定义 | 长度及类型        | 描述             |
| :---------------- | :---------------- | :--------------- |
| PK_Type Name      | Char[GET_FSUINFO] | 获取 FSU 状态信息 |
| Info FSUID        | Char[FSUID_LEN]   | FSU ID 号        |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>GET_FSUINFO</Name>
</PK_Type>
<Info>
<FSUID/>
</Info>
</Request>
```

响应：**FSU**

**表31 获取 FSU 状态信息响应报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[GET_FSUINFO_ACK]      | 获取 FSU 状态信息响应                                                                              |
| Info FSUID        | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| TFSUStatus        | Sizeof(TFSUStatus)         | FSU 状态                                                                                           |
| Result            | EnumResult                 | 成功/失败                                                                                          |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 获取 FSU 状态信息失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |


#### 5.6.13. 更新 FSU 状态信息获取周期

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response期的请求，FSU 返回更新是否成功。当出现以下两种场景时，会触发该流程：
*   **场景一**：当某一个 FSU 向 SC 注册成功后，SC 向该 FSU 发送更新 FSU 状态获取周期的请求，FSU 返回更新是否成功。
*   **场景二**：当 SC 侧 FSU 状态获取周期发生变更后，SC 应主动向所有 FSU 发送更新 FSU 状态获取周期的请求，各 FSU 分别返回更新是否成功。

**图15 设置 FSU 状态信息获取周期**
（图片描述：此图展示了SC更新FSU状态信息获取周期的过程。SC发送 **UPDATE_FSUINFO_INTERVAL** 消息给FSU，FSU返回 **UPDATE_FSUINFO_INTERVAL_ACK** 消息，表示设置结果。）

b) **数据流格式定义**

发起：**SC**

**表32 设置 FSU 状态信息获取周期报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                           |
| :---------------- | :------------------------- | :----------------------------------------------------------------------------- |
| PK_Type Name      | Char[UPDATE_FSUINFO_INTERVAL] | 更新 FSU 状态信息获取周期请求                                                  |
| Info FSUID        | Char[FSUID_LEN]            | FSU 设备的 ID 号。当 SC 需向所有 FSU 发送更新状态获取周期请求时，FSUID 取值为“NULL”。 |
| Interval          | short                      | FSU 状态信息获取周期值，以秒（s）为单位                                        |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>UPDATE_FSUINFO_INTERVAL</Name>
</PK_Type>
<Info>
<FSUID/>
<Interval/>
</Info>
</Request>
```

响应：**FSU**

**表33 更新 FSU 状态信息获取周期响应报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[UPDATE_FSUINFO_INTERVAL_ACK] | 更新 FSU 状态信息获取周期响应                                                                      |
| Info FSUID        | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| Result            | EnumResult                 | 成功/失败                                                                                          |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 更新 FSU 状态信息获取周期失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>UPDATE_FSUINFO_INTERVAL_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.14. 重启 FSU

a) **数据流方式**
SC 向 FSU 发送重启要求，FSU 返回成功标志后重启。此报文用于 FSU 的升级等操作：SC 侧先通过 FTP 将升级文件上传到 FSU 的 `/upgrade/` 下，再发此报文使 FSU 重启后自动升级。

**图16 重启 FSU 过程**
（图片描述：此图展示了SC重启FSU的过程。SC发送 **SET_FSUREBOOT** 消息给FSU，FSU返回 **SET_FSUREBOOT_ACK** 消息，表示重启FSU响应。）

b) **数据流格式定义**

发起：**SC**

**表34 重启 FSU 报文**

| 变量名称/报文定义 | 长度及类型        | 描述           |
| :---------------- | :---------------- | :------------- |
| PK_Type Name      | Char[SET_FSUREBOOT] | 重启 FSU 信息 |
| Info FSUID        | Char[FSUID_LEN]   | FSU ID 号      |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SET_FSUREBOOT</Name>
</PK_Type>
<Info>
<FSUID/>
</Info>
</Request>
```

响应：**FSU**

**表35 重启 FSU 响应报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SET_FSUREBOOT_ACK]    | 重启 FSU 信息响应                                                                                  |
| Info FSUID        | Char[FSUID_LEN]            | FSU ID 号                                                                                          |
| Result            | EnumResult                 | 成功/失败                                                                                          |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 重启 FSU 失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SET_FSUREBOOT_ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.15. 上报监控点数据

a) **数据流方式**
FSU 根据监控点门限判断监控点数据超过门限（可设置）时，应向 SC 上报监控点信息，SC 返回确认信息。SC 对 FSU 写监控点数据后，FSU 需要上报被操作设备的监控点数据。

**图17 上报监控点数据**
（图片描述：此图展示了FSU上报监控点数据的过程。FSU发送 **SEND_DATA** 消息给SC，SC收到后返回 **SEND_DATA_ACK** 消息，表示接收到监控点数据。）

b) **数据流格式定义**

发起：**FSU**

**表36 上报监控点数据报文**

| 变量名称/报文定义 | 长度及类型               | 描述         |
| :---------------- | :----------------------- | :----------- |
| PK_Type Name      | Char[SEND_DATA]          | 上报监控点数据 |
| FSUID             | Char[FSUID_LEN]          | FSU ID 号    |
| Values            | Sizeof(TSemaphore)       | 对应 5.3 中的 TSemaphore 的数据结构定义 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SEND_DATA</Name>
</PK_Type>
<Info>
<FSUID/>
<Values>
<DeviceList>
<Device ID="000000000001">
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
</Device>
<Device ID="000000000002">
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
<TSemaphore Type="" ID="" SignalNumber="" MeasuredVal=""
SetupVal="" Status="" Time=""/>
</Device>
</DeviceList>
</Values>
</Info>
</Request>
```

响应：**SC**

**表37 上报监控点数据应答报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SEND_DATA_ACK]        | 监控点上报确认信息                                                                                 |
| Info Result       | EnumResult                 | 返回设置结果                                                                                       |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 接收监控点数据失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SEND_DATA_ACK</Name>
</PK_Type>
<Info>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.16. 请求动环设备配置数据

a) **数据流方式**
SC 向指定 FSU 发送所需配置数据的设备标识，FSU 向 SC 发送请求的动环设备当前配置信息。

**图18 请求动环设备配置数据过程**
（图片描述：此图展示了SC请求动环设备配置数据的过程。SC发送 **GET_DEV_CONF** 消息给FSU，FSU返回 **GET_DEV_CONF_ACK** 消息，包含动环设备配置数据。）

b) **数据流格式定义**

发起：**SC**

**查询 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>GET_DEV_CONF</Name>
</PK_Type>
<Info>
<FSUID/>
<DeviceID/>
</Info>
</Request>
```

响应：**FSU**

**表39 请求动环设备配置数据应答报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[GET_DEV_CONF_ACK]     | 动环设备配置数据确认信息                                                                           |
| Info Result       | EnumResult                 | 请求数据成功与否的标志                                                                             |
| Values            | Sizeof(TDevConf)           | 对应 5.3 中的 TDevConf 的数据结构定义                                                              |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 请求动环设备配置数据失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>GET_DEV_CONF_ACK</Name>
</PK_Type>
<Info>
<Result/>
<FailureCause/>
<Values>
<Device DeviceID="" DeviceName="" SiteName="" RoomName="" DeviceType=""
DeviceSubType="" Model="" Brand="" RatedCapacity="" Version="" BeginRunTime=""
DevDescribe="">
<Signals Count="">
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
</Signals>
</Device>
</Values>
</Info>
</Response>
```

#### 5.6.17. 上报动环设备的配置数据

a) **数据流方式**
FSU 上动环设备的配置信息发生变更或者 FSU 重启后，FSU 向 SC 上报变化的配置信息，SC 返回确认信息。

**图19 上报动环设备配置数据**
（图片描述：此图展示了FSU上报动环设备配置数据的过程。FSU发送 **SEND_DEV_CONF_DATA** 消息给SC，SC收到后返回 **SEND_DEV_CONF_DATA_ACK** 消息，表示接收配置变更数据。）

b) **数据流|
| Values            | Sizeof(TDevConf)         | 对应 5.3 中的 TDevConf 的数据结构定义 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SEND_DEV_CONF_DATA</Name>
</PK_Type>
<Info>
<FSUID/>
<Values>
<Device DeviceID="000000000001" DeviceName="" SiteName="" RoomName=""
DeviceType="" DeviceSubType="" Model="" Brand="" RatedCapacity="" Version=""
BeginRunTime="" DevDescribe="">
<Signals Count="">
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
</Signals>
</Device>
<Device DeviceID="000000000002" DeviceName="" SiteName="" RoomName=""
DeviceType="" DeviceSubType="" Model="" Brand="" RatedCapacity="" Version=""
BeginRunTime="" DevDescribe="">
<Signals Count="">
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
</Signals>
</Device>
</Values>
</Info>
</Request>
```

响应：**SC**

**表41 上报监控对象配置数据应答报文**

| 变量名称/报文定义 | 长度及类型                 | 描述                                                                                               |
| :---------------- | :------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SEND_DEV_CONF_DATA_ACK] | 动环设备配置数据上报确认信息                                                                       |
| Info Result       | EnumResult                 | 返回设置结果                                                                                       |
| FailureCause      | Char[FAILURE_CAUSE_LEN]    | 接收监控对象配置数据失败的原因（厂家自定义）。当 Result 取值为 1 时，FailureCause 取值为“NULL”。 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SEND_DEV_CONF_DATA_ACK</Name>
</PK_Type>
<Info>
<Result/>
<FailureCause/>
</Info>
</Response>
```

#### 5.6.18. 写

b) **数据流格式定义**

发起：**SC**

**表42 写动环设备配置数据报文**

| 变量名称/报文定义 | 长度及类型               | 描述                 |
| :---------------- | :----------------------- | :------------------- |
| PK_Type Name      | Char[SET_DEV_CONF_DATA]  | 写动环设备配置数据   |
| Info FSUID        | Char[FSUID_LEN]          | FSU ID 号            |
| Values            | Sizeof(TDevConf)         | 需要修改的监控对象的配置信息 |

**XML 样例**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Request>
<PK_Type>
<Name>SET_DEV_CONF_DATA</Name>
</PK_Type>
<Info>
<FSUID/>
<Values>
<Device DeviceID="000000000001" DeviceName="" SiteName SiteName="" RoomName=""
DeviceType="" DeviceSubType="" Model="" Brand="" RatedCapacity="" Version=""
BeginRunTime="" DevDescribe="">
<Signals Count="">
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Thresbhold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
</Signals>
</Device>
</Values>
</Info>
</Request>
```

响应：**FSU**

**表43 写动环设备配置数据应答报文**

| 变量名称/报文定义 | 长度及类型                   | 描述                                                                                               |
| :---------------- | :--------------------------- | :------------------------------------------------------------------------------------------------- |
| PK_Type Name      | Char[SET_DEV_CONF_DATA _ACK] | 写动环设备配置数据回应                                                                             |
| Info FSUID        | Char[FSUID_LEN]              | FSU ID 号                                                                                          |
| Success
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
<PK_Type>
<Name>SET_DEV_CONF_DATA _ACK</Name>
</PK_Type>
<Info>
<FSUID/>
<Result/>
<FailureCause/>
<SuccessList>
<Device ID="000000000001"/>
</SuccessList>
<FailList>
<Device ID="000000000002"/>
</FailList>
</Info>
</Response>
```

### 5.7. FTP 接口能力

FSU 应提供 FTP 接口，提供 FTP 存储文件能力**至少 8G**。通过 FSU 提供的 FTP 服务，SC 定期登录后取回或上传文件。**FSU 做服务端，SC 是客户端**。

#### 5.7.1. 批量获取监控对象的配置数据

FSU 将动环监控对象的配置数据以 **XML 格式**存储在**一级子目录 `\Config\` 下**并按需定期更新="" SiteName="" RoomName="" DeviceType=""
DeviceSubType="" Model="" Brand="" RatedCapacity="" Version="" BeginRunTime=""
DevDescribe="">
<Signals Count="">
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Threshold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
<Signal Type="" ID="" SignalName="" SignalNumber="" AlarmLevel=""
Threshold="" AbsoluteVal="" RelativeVal="" Describe="" NMAlarmID=""/>
</Signals>
</Device>
</Devices>
```

#### 5.7.2. 获取监控图像文件

FSU 存储的监控图像文件格式应为 **JPG、JPEG 之一**，每个图片文件的大小不应超过 **1M**。
FSU 应在根目录建立**一级子目录 `\PIC\`**，用以存放监控图像文件，并将此子目录设置为 FTP 默认的用户目录，SC 登录 FTP 后从此目录获取图像文件。FSU 自行维护 `\PIC\` 目录下的文件数目和磁盘空间，当达到存储上限时，建议采用**新文件覆盖最早文件的方式**进行滚动更新。

#### 5.7.3. 获取活动、历史告警同步文件

FSU 应**每天 0 点 0 分 0 秒**在根目录建立**一级子目录 `\Alarm\YYYYMMDD\`**，用于存储告警信息（包括活动和清除告警）。`YYYYMMDD` 为四位年、两位月、两位日，例如：20150102。并在当天文件夹下按告警事件发生顺序存入告警文件，告警文件格式：**`FSU上限时，建议删除离现在最久的文件夹和文件。

#### 5.7.4. 上传 FSU 相关文件

FSU 应该在根目录建立**一级子目录 `\upgrade\`**，用于存放 FSU 升级文件，升级文件包括但不限于动环设备采集解析规则文件、FSU 配置文件等。文件上传成功后，调用重启 FSU 完成 FSU 设备的升级。

#### 5.7.5. 获取监控点性能数据文件

FSU 在根目录建立**一级子目录 `\Measurement\`**，用于存放 FSU 管理范围内全部监控点的历史性能数据。要求 FSU 在每天的固定时间生成全部监控点性能数据文件，要求该文件格式为 **csv**。文件命名要求为 **`PM_FSUID_YYYYMMDDHHmm.csv`**，其中 `FSUID` 遵循《中国移动动环命名及编码指导意见》中相关要求；`YYYYMMDDHHmm` 指该性能数据文件生成时间，其中 `YYYY` 代表四位年，`MM` 代表两位月，`DD` 代表两位日，`HH` 代表两位小时，`mm` 代表两位分钟。

监控点历史性能数据文件中应至少包含以下信息：

| 属性名     | 序号 | 性能数据采集时间                   | DeviceID   | 监控点 ID | 监控点描述 | 监控点数据类型              | 监控点数据测量值 | 属性数据类型 |
| :--------- | :--- | :--------------------------------- | :--------- | :-------- | :--------- | :-------------------------- | :--------------- | :----------- |
| (未命名)   | (未命名) | 字符串，采用 YYYYMMDDHHMM 的格式，分别代表四位年、两位月、两位日、两位小时（24 小时制）和两位分钟 | 字符串     | 字符串    | 字符串     | 枚举型，取值范围为 {AI（遥测信号）,DI（遥信信号）} | 浮点型/枚举型    | 整型         |

#### 5.7.6. 获取日志文件

FSU 在上电启动开始就应该在根目录建立**一级子目录 `\logs\`**，用于存放 FSU 启动、重启和报文上报异常等信息。文件命名格式：**`FSUID_YYYYMMDD.log`**，`YYYYMMDD` 为四位年、两位月、两位日。日志内容包括但不限于以下信息：时间、操作者信息、操作内容、操作结果等。
FSU 自行维护 `\logs\` 目录下的文件夹数目和磁盘空间，当达到存储上限时，建议删除离现在最久的文件。
