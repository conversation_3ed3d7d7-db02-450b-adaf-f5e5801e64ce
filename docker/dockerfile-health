FROM openjdk:17-jdk-alpine
RUN apk update
RUN apk add -U tzdata
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN apk add --update font-adobe-100dpi ttf-dejavu fontconfig
ENV LANG C.UTF-8
VOLUME /tmp
WORKDIR /home
ADD output/tcs-health-3.2.4.jar /home/<USER>
COPY output/health/config /home/<USER>
EXPOSE 9800
ENTRYPOINT ["java", "-Xms4096m", "-Xmx16384m","-jar", "-Dspring.profiles.active=mysql", "-Dspring.config.location=/home/<USER>/ ","-Duser.timezone=Asia/Shanghai","/home/<USER>"]