FROM openjdk:17.0.2-slim-buster

# 安装必要的包
RUN apt-get update && \
    apt-get install -y \
    tzdata \
    libxrender1 \
    libxtst6 \
    libxi6 \
    libfreetype6 \
    fontconfig \
    fonts-dejavu \
    fonts-adobe-100dpi \
    fonts-wqy-microhei && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 设置语言环境
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8
ENV LANGUAGE C.UTF-8

VOLUME /tmp
WORKDIR /home

# 添加应用和配置文件
ADD output/tcs-health-3.2.4.jar /home/<USER>
COPY output/health/config /home/<USER>

# 暴露端口
EXPOSE 9800

# 启动命令
ENTRYPOINT ["java", "-Xms4096m", "-Xmx16384m", "-jar", "-Dspring.profiles.active=mysql", "-Dspring.config.location=/home/<USER>/ ", "-Duser.timezone=Asia/Shanghai", "/home/<USER>"]