"use client"

import React, { useState, useMemo } from 'react'
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Activity, Database, Network, Server, Users, ChevronDown, ChevronRight, Folder, FileText, Search, Filter, BarChart3, Eye, EyeOff, ExternalLink } from 'lucide-react'
import { Progress } from "@/components/ui/progress"

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'up':
      return 'bg-green-500'
    case 'down':
      return 'bg-red-500'
    case 'joining':
      return 'bg-yellow-500'
    default:
      return 'bg-gray-500'
  }
}

// 生成更多模拟数据来演示大数据量场景
const generateMockData = () => {
  const nodes = []
  for (let i = 1; i <= 5; i++) {
    const shards = []
    for (let j = 0; j < 10; j++) {
      const entities = []
      const entityCount = Math.floor(Math.random() * 50) + 10 // 10-60个实体
      for (let k = 0; k < entityCount; k++) {
        entities.push({
          entityId: `entity_${i}_${j}_${k.toString().padStart(3, '0')}`,
          shardId: j.toString(),
          actorPath: `pekko://TcsSystem@192.168.56.${i}:2551/system/sharding/cmcc-gateway/${j}/entity_${i}_${j}_${k.toString().padStart(3, '0')}`
        })
      }
      shards.push({
        typeName: "cmcc-gateway",
        shardId: j.toString(),
        entityCount: entities.length,
        actorPath: `pekko://TcsSystem@192.168.56.${i}:2551/system/sharding/cmcc-gateway/${j}`,
        entities
      })
    }
    
    nodes.push({
      address: `pekko://TcsSystem@192.168.56.${i}:2551`,
      status: "Up",
      roles: ["compute", "backend", "dc-default"],
      hostname: `192.168.56.${i}`,
      port: 2551,
      shardTypes: [{
        typeName: "cmcc-gateway",
        totalShards: 10,
        totalEntities: shards.reduce((sum, s) => sum + s.entityCount, 0),
        shards
      }],
      leader: i === 1,
      self: i === 1
    })
  }
  
  return {
    state: true,
    timestamp: Date.now(),
    data: nodes,
    code: 0,
    err_msg: null,
    err_code: null
  }
}

const clusterData = generateMockData()

export default function ClusterDashboard() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedNode, setSelectedNode] = useState<string>('all')
  const [selectedShard, setSelectedShard] = useState<string>('all')
  const [pageSize, setPageSize] = useState(20)
  const [currentPage, setCurrentPage] = useState(1)
  const [activeTab, setActiveTab] = useState('overview')

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  // 跳转到实体查询并过滤特定分片
  const navigateToShard = (nodeId: string, shardId: string) => {
    setSelectedNode(nodeId)
    setSelectedShard(shardId)
    setSearchTerm('')
    setCurrentPage(1)
    setActiveTab('entities')
  }

  const totalNodes = clusterData.data.length
  const upNodes = clusterData.data.filter(node => node.status === 'Up').length
  const totalEntities = clusterData.data.reduce((sum, node) => 
    sum + node.shardTypes.reduce((nodeSum, shardType) => nodeSum + shardType.totalEntities, 0), 0
  )
  const totalShards = clusterData.data.reduce((sum, node) => 
    sum + node.shardTypes.reduce((nodeSum, shardType) => nodeSum + shardType.totalShards, 0), 0
  )

  // 聚合统计数据
  const aggregatedStats = useMemo(() => {
    const nodeStats = clusterData.data.map(node => ({
      hostname: node.hostname,
      port: node.port,
      status: node.status,
      totalShards: node.shardTypes.reduce((sum, st) => sum + st.totalShards, 0),
      totalEntities: node.shardTypes.reduce((sum, st) => sum + st.totalEntities, 0),
      avgEntitiesPerShard: node.shardTypes.reduce((sum, st) => sum + st.totalEntities, 0) / 
                          node.shardTypes.reduce((sum, st) => sum + st.totalShards, 0),
      leader: node.leader,
      self: node.self
    }))

    const shardDistribution = clusterData.data.flatMap(node => 
      node.shardTypes.flatMap(st => 
        st.shards.map(shard => ({
          nodeId: `${node.hostname}:${node.port}`,
          shardId: shard.shardId,
          entityCount: shard.entityCount,
          typeName: shard.typeName
        }))
      )
    )

    return { nodeStats, shardDistribution }
  }, [])

  // 过滤和分页的实体数据
  const filteredEntities = useMemo(() => {
    let entities = clusterData.data.flatMap(node =>
      node.shardTypes.flatMap(st =>
        st.shards.flatMap(shard =>
          shard.entities.map(entity => ({
            ...entity,
            nodeId: `${node.hostname}:${node.port}`,
            typeName: st.typeName
          }))
        )
      )
    )

    if (searchTerm) {
      entities = entities.filter(entity => 
        entity.entityId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entity.nodeId.includes(searchTerm)
      )
    }

    if (selectedNode !== 'all') {
      entities = entities.filter(entity => entity.nodeId === selectedNode)
    }

    if (selectedShard !== 'all') {
      entities = entities.filter(entity => entity.shardId === selectedShard)
    }

    return entities
  }, [searchTerm, selectedNode, selectedShard])

  const paginatedEntities = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return filteredEntities.slice(startIndex, startIndex + pageSize)
  }, [filteredEntities, currentPage, pageSize])

  const totalPages = Math.ceil(filteredEntities.length / pageSize)

  // 获取可用的分片选项
  const availableShards = useMemo(() => {
    if (selectedNode === 'all') {
      return Array.from(new Set(
        clusterData.data.flatMap(node =>
          node.shardTypes.flatMap(st =>
            st.shards.map(shard => shard.shardId)
          )
        )
      )).sort((a, b) => {
        const numA = parseInt(a)
        const numB = parseInt(b)
        if (!isNaN(numA) && !isNaN(numB)) return numA - numB
        return a.localeCompare(b)
      })
    } else {
      const node = clusterData.data.find(n => `${n.hostname}:${n.port}` === selectedNode)
      return node ? node.shardTypes.flatMap(st => st.shards.map(s => s.shardId)) : []
    }
  }, [selectedNode])

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Pekko 运行监控</h1>
            <p className="text-muted-foreground mt-1">
              最后更新: {formatTimestamp(clusterData.timestamp)}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${clusterData.state ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm font-medium">
              {clusterData.state ? '集群正常' : '集群异常'}
            </span>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">节点总数</CardTitle>
              <Server className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalNodes}</div>
              <p className="text-xs text-muted-foreground">
                {upNodes} 个节点在线
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">分片总数</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalShards}</div>
              <p className="text-xs text-muted-foreground">
                平均每节点 {Math.round(totalShards / totalNodes)} 个
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">实体总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalEntities.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                平均每分片 {Math.round(totalEntities / totalShards)} 个
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">负载均衡</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">良好</div>
              <p className="text-xs text-muted-foreground">
                实体分布均匀
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">概览统计</TabsTrigger>
            <TabsTrigger value="nodes">节点详情</TabsTrigger>
            <TabsTrigger value="shards">分片分布</TabsTrigger>
            <TabsTrigger value="entities">实体查询</TabsTrigger>
            <TabsTrigger value="tree">树形视图</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 节点负载分布 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    节点负载分布
                  </CardTitle>
                  <CardDescription>各节点的实体数量分布</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {aggregatedStats.nodeStats.map((node, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(node.status)}`} />
                          <span className="font-mono">{node.hostname}:{node.port}</span>
                          {node.leader && <Badge variant="default" className="text-xs">Leader</Badge>}
                        </div>
                        <span className="font-medium">{node.totalEntities.toLocaleString()} 实体</span>
                      </div>
                      <Progress 
                        value={(node.totalEntities / Math.max(...aggregatedStats.nodeStats.map(n => n.totalEntities))) * 100} 
                        className="h-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{node.totalShards} 分片</span>
                        <span>平均 {Math.round(node.avgEntitiesPerShard)} 实体/分片</span>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* 分片类型统计 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    分片类型统计
                  </CardTitle>
                  <CardDescription>不同类型分片的分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">cmcc-gateway</span>
                        <Badge variant="secondary">{totalShards} 分片</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">总实体数:</span>
                          <span className="ml-2 font-medium">{totalEntities.toLocaleString()}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">分布节点:</span>
                          <span className="ml-2 font-medium">{totalNodes} 个</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">平均负载:</span>
                          <span className="ml-2 font-medium">{Math.round(totalEntities / totalShards)}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">负载方差:</span>
                          <span className="ml-2 font-medium text-green-600">低</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="nodes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5" />
                  集群节点详情
                </CardTitle>
                <CardDescription>
                  节点状态和负载信息汇总
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>节点地址</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>分片数</TableHead>
                      <TableHead>实体数</TableHead>
                      <TableHead>平均负载</TableHead>
                      <TableHead>标识</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {aggregatedStats.nodeStats.map((node, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-mono text-sm">
                          {node.hostname}:{node.port}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${getStatusColor(node.status)}`} />
                            {node.status}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{node.totalShards}</TableCell>
                        <TableCell className="font-medium">{node.totalEntities.toLocaleString()}</TableCell>
                        <TableCell>{Math.round(node.avgEntitiesPerShard)}</TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            {node.leader && (
                              <Badge variant="default" className="text-xs">Leader</Badge>
                            )}
                            {node.self && (
                              <Badge variant="outline" className="text-xs">Self</Badge>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="shards" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  分片分布热力图
                </CardTitle>
                <CardDescription>
                  显示各节点分片的实体数量分布，点击分片可查看详细实体列表
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {clusterData.data.map((node, nodeIndex) => (
                    <div key={nodeIndex} className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold flex items-center gap-2">
                          <Server className="h-4 w-4" />
                          {node.hostname}:{node.port}
                        </h4>
                        <div className="text-sm text-muted-foreground">
                          总计: {node.shardTypes[0].totalShards} 分片, {node.shardTypes[0].totalEntities.toLocaleString()} 实体 
                        </div>
                      </div>
                      <div className="grid grid-cols-10 gap-2">
                        {node.shardTypes[0].shards.map((shard, shardIndex) => {
                          const maxEntities = Math.max(...node.shardTypes[0].shards.map(s => s.entityCount))
                          const intensity = shard.entityCount / maxEntities
                          return (
                            <div
                              key={shardIndex}
                              className="aspect-square rounded border-2 border-muted flex flex-col items-center justify-center text-xs font-mono relative group cursor-pointer hover:border-primary transition-colors"
                              style={{
                                backgroundColor: `rgba(34, 197, 94, ${intensity * 0.8 + 0.1})`
                              }}
                              onClick={() => navigateToShard(`${node.hostname}:${node.port}`, shard.shardId)}
                            >
                              <div className="font-bold">{shard.shardId}</div>
                              <div className="text-[10px]">{shard.entityCount}</div>
                              <ExternalLink className="absolute top-1 right-1 h-2 w-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                              
                              {/* Tooltip */}
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                                分片 {shard.shardId}: {shard.entityCount} 个实体<br/>
                                <span className="text-xs opacity-75">点击查看实体列表</span>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="entities" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  实体查询与管理
                </CardTitle>
                <CardDescription>
                  搜索和浏览集群中的实体 (共 {filteredEntities.length.toLocaleString()} 个实体)
                  {selectedShard !== 'all' && (
                    <span className="ml-2 text-primary">
                      - 当前过滤: 分片 {selectedShard}
                    </span>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 搜索和过滤控件 */}
                <div className="flex gap-4 items-center flex-wrap">
                  <div className="flex-1 min-w-64 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索实体ID或节点地址..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={selectedNode} onValueChange={(value) => {
                    setSelectedNode(value)
                    setSelectedShard('all') // 重置分片选择
                    setCurrentPage(1)
                  }}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="选择节点" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有节点</SelectItem>
                      {clusterData.data.map((node, index) => (
                        <SelectItem key={index} value={`${node.hostname}:${node.port}`}>
                          {node.hostname}:{node.port}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={selectedShard} onValueChange={(value) => {
                    setSelectedShard(value)
                    setCurrentPage(1)
                  }}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="选择分片" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有分片</SelectItem>
                      {availableShards.map((shardId) => (
                        <SelectItem key={shardId} value={shardId}>
                          分片 {shardId}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10 条/页</SelectItem>
                      <SelectItem value="20">20 条/页</SelectItem>
                      <SelectItem value="50">50 条/页</SelectItem>
                      <SelectItem value="100">100 条/页</SelectItem>
                    </SelectContent>
                  </Select>
                  {(selectedNode !== 'all' || selectedShard !== 'all' || searchTerm) && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        setSelectedNode('all')
                        setSelectedShard('all')
                        setSearchTerm('')
                        setCurrentPage(1)
                      }}
                    >
                      清除过滤
                    </Button>
                  )}
                </div>

                {/* 实体表格 */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>实体ID</TableHead>
                      <TableHead>分片ID</TableHead>
                      <TableHead>节点</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>Actor路径</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedEntities.map((entity, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-mono text-sm">
                          {entity.entityId}
                        </TableCell>
                        <TableCell className="font-mono">
                          <Badge variant="outline" className="text-xs">
                            {entity.shardId}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {entity.nodeId}
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className="text-xs">
                            {entity.typeName}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-mono text-xs text-muted-foreground max-w-96 truncate">
                          {entity.actorPath}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* 分页控件 */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    显示 {((currentPage - 1) * pageSize + 1).toLocaleString()} - {Math.min(currentPage * pageSize, filteredEntities.length).toLocaleString()} 条，
                    共 {filteredEntities.length.toLocaleString()} 条记录
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      上一页
                    </Button>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tree" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5" />
                  集群树形视图
                </CardTitle>
                <CardDescription>
                  以树形结构展示集群的层次关系，点击分片可查看详细实体列表
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OptimizedTreeView data={clusterData} onShardClick={navigateToShard} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function OptimizedTreeView({ 
  data, 
  onShardClick 
}: { 
  data: typeof clusterData
  onShardClick: (nodeId: string, shardId: string) => void
}) {
  return (
    <div className="space-y-2">
      <TreeNode
        icon={<Network className="h-4 w-4" />}
        label={`Pekko 集群 (${data.data.length} 个节点, ${data.data.reduce((sum, node) => sum + node.shardTypes.reduce((nodeSum, shardType) => nodeSum + shardType.totalEntities, 0), 0).toLocaleString()} 个实体)`}
        defaultExpanded={true}
      >
        {data.data.map((node, nodeIndex) => (
          <TreeNode
            key={nodeIndex}
            icon={<Server className="h-4 w-4" />}
            label={
              <div className="flex items-center gap-2">
                <span>{node.hostname}:{node.port}</span>
                <div className={`w-2 h-2 rounded-full ${getStatusColor(node.status)}`} />
                <span className="text-xs text-muted-foreground">
                  ({node.shardTypes[0].totalShards} 分片, {node.shardTypes[0].totalEntities.toLocaleString()} 实体)
                </span>
                {node.leader && (
                  <Badge variant="default" className="text-xs h-4">Leader</Badge>
                )}
              </div>
            }
            defaultExpanded={false}
          >
            {/* 分片类型汇总 */}
            {node.shardTypes.map((shardType, stIndex) => (
              <TreeNode
                key={stIndex}
                icon={<Database className="h-4 w-4" />}
                label={
                  <div className="flex items-center gap-2">
                    <span>{shardType.typeName}</span>
                    <span className="text-xs text-muted-foreground">
                      ({shardType.totalShards} 分片, {shardType.totalEntities.toLocaleString()} 实体)
                    </span>
                  </div>
                }
                defaultExpanded={false}
              >
                {/* 分片方块网格展示 */}
                <div className="ml-6 space-y-3">
                  <div className="grid grid-cols-10 gap-2">
                    {shardType.shards.map((shard, shardIndex) => {
                      const maxEntities = Math.max(...shardType.shards.map(s => s.entityCount))
                      const intensity = shard.entityCount / maxEntities
                      return (
                        <div
                          key={shardIndex}
                          className="aspect-square rounded border-2 border-muted flex flex-col items-center justify-center text-xs font-mono relative group cursor-pointer hover:border-primary transition-colors"
                          style={{
                            backgroundColor: `rgba(34, 197, 94, ${intensity * 0.8 + 0.1})`
                          }}
                          onClick={(e) => {
                            e.stopPropagation()
                            onShardClick(`${node.hostname}:${node.port}`, shard.shardId)
                          }}
                        >
                          <div className="font-bold">{shard.shardId}</div>
                          <div className="text-[10px]">{shard.entityCount}</div>
                          <ExternalLink className="absolute top-1 right-1 h-2 w-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                          
                          {/* Tooltip */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                            分片 {shard.shardId}: {shard.entityCount} 个实体<br/>
                            <span className="text-xs opacity-75">点击查看实体列表</span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                  <div className="text-xs text-muted-foreground italic">
                    点击分片方块可查看详细实体列表
                  </div>
                </div>
              </TreeNode>
            ))}
          </TreeNode>
        ))}
      </TreeNode>
    </div>
  )
}

function TreeNode({ 
  icon, 
  label, 
  children, 
  defaultExpanded = false 
}: { 
  icon: React.ReactNode
  label: React.ReactNode
  children: React.ReactNode
  defaultExpanded?: boolean 
}) {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded)

  return (
    <div className="space-y-1">
      <div
        className="flex items-center gap-2 py-1 px-2 rounded hover:bg-muted cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? (
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        ) : (
          <ChevronRight className="h-4 w-4 text-muted-foreground" />
        )}
        {icon}
        <div className="flex-1">{label}</div>
      </div>
      {isExpanded && (
        <div className="ml-6 space-y-1 border-l border-muted pl-4">
          {children}
        </div>
      )}
    </div>
  )
}
