Q/CT XXXX—2021





















![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.001.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.002.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.003.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.004.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.005.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.006.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.007.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.008.png)![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.009.png)
<a name="sectionmark0"></a><a name="_hlk18680602"></a>I

**目   次**

[**前     言**	III](#_toc23112235)

[1	范围	1](#_toc23112236)

[2	规范性引用文件	1](#_toc23112237)

[3	术语和定义	1](#_toc23112238)

[4	接口	3](#_toc23112250)

[5	B接口定义	4](#_toc23112251)

[5.1 接口方式	4](#_toc23112252)

[5.2 报文原则	4](#_toc23112253)

[5.3 WSDL定义	4](#_toc23112254)

[5.4 报文格式定义	4](#_toc23112255)

[5.5 报文内容定义	4](#_toc23112256)

[6	B接口报文	9](#_toc23112257)

[6.1 LOGIN与LOGIN_ACK	10](#_toc23112258)

[6.2 SUREADY与SUREADY_ACK	13](#_toc23112259)

[6.3 SET_SCIP与SET_SCIP_ACK	14](#_toc23112260)

[6.4 GET_SUFTP与GET_SUFTP_ACK	16](#_toc23112261)

[6.5 SET_SUFTP与SET_SUFTP_ACK	18](#_toc23112262)

[6.6 ASK_SCHEMECONFIG与ASK_SCHEMECONFIG _ACK	19](#_toc23112263)

[6.7 GET_SCHEMECONFIG与GET_SCHEMECONFIG _ACK	21](#_toc23112264)

[6.8 SET_SCHEMECONFIG与SET_SCHEMECONFIG_ACK	23](#_toc23112265)

[6.9 ASK_FACTORYCONFIG与ASK_FACTORYCONFIG_ACK	24](#_toc23112266)

[6.10 SEND_FACTORYCONFIG与SEND_FACTORYCONFIG_ACK	26](#_toc23112267)

[6.11 GET_FACTORYCONFIG与GET_FACTORYCONFIG_ACK	28](#_toc23112268)

[6.12 SET_FACTORYCONFIG与SET_FACTORYCONFIG_ACK	30](#_toc23112269)

[6.13 GET_SPCONFIGOPTION 与GET_SPCONFIGOPTION_ACK	32](#_toc23112270)

[6.14 SET_SPCONFIGOPTION与SET_SPCONFIGOPTION_ACK	34](#_toc23112271)

[6.15 GET_DATA与GET_DATA_ACK	36](#_toc23112272)

[6.16 ASK_TODAYHISDATA 与ASK_TODAYHISDATA_ACK	39](#_toc23112273)

[6.17 SEND_ALARM与SEND_ALARM_ACK	40](#_toc23112274)

[6.18 GET_ACTIVEALARM与GET_ACTIVEALARM_ACK	42](#_toc23112275)

[6.19 SET_RMCTRLCMD与SET_RMCTRLCMD_ACK	44](#_toc23112276)

[6.20 SET_TIME与SET_TIME_ACK	47](#_toc23112277)

[6.21 GET_SUINFO与GET_SUINFO_ACK	49](#_toc23112278)

[6.22 SU_REBOOT与SU_REBOOT_ACK	51](#_toc23112279)

[6.23 GET_SORESINFO与GET_SORESINFO_ACK	52](#_toc23112280)

[6.24 GET_SMARTDOOR与GET_SMARTDOOR_ACK	55](#_toc23112281)

[6.25 SET_SMARTDOOR与SET_SMARTDOOR_ACK	57](#_toc23112282)

[6.26 SEND_SMARTDOOR与SEND_SMARTDOOR_ACK	60](#_toc23112283)

[6.27 SEND_DOOREVENT与SEND_DOOREVENT_ACK	61](#_toc23112284)

[7	FSU接入SC流程	64](#_toc23112285)

[7.1 自顶向下接入模式	65](#_toc23112286)

[7.2 自底向上接入模式	66](#_toc23112287)

[7.3 FSU注册流程	67](#_toc23112288)

[8	测点标准化	68](#_toc23112289)

[8.1 标准化配置文件	68](#_toc23112290)

[8.2 厂家配置文件	69](#_toc23112291)

[8.3 监控点SP配置方案	70](#_toc23112292)

[8.4 修改FSU监控点配置模板选项	70](#_toc23112293)

[9	重要运行机制	70](#_toc23112294)

[9.1 安全机制	70](#_toc23112295)

[9.2 告警发送与接收机制	74](#_toc23112296)

[9.3 实时监测数据发送与接收机制	77](#_toc23112297)

[9.4 心跳机制	78](#_toc23112298)

[9.5 时间同步机制	79](#_toc23112299)

[9.6 远程控制指令下发机制	79](#_toc23112300)

[9.7 SO资源信息上报机制	80](#_toc23112301)

[9.8 SO串口透传机制（可选）	80](#_toc23112302)

[附录A （规范性附录） SCService.wsdl 文件内容	83](#_toc23112303)

[附录B （规范性附录） SUService.wsdl文件内容	84](#_toc23112304)

[附录C （资料性附录） 编码规则指引	85](#_toc23112305)

[附录D （资料性附录） 新装FSU流程参考说明	87](#_toc23112306)

[附录E （资料性附录） 标准化配置文件说明	89](#_toc23112307)

[附录F （资料性附录） 监测值历史记录	93](#_toc23112308)

[附录G （资料性附录） 蓄电池放电曲线	96](#_toc23112309)

[附录H （资料性附录） FSU基本技术要求	98](#_toc23112310)

[附件1中国电信动环系统标准化监控信号测点全表	99](#_toc23112311)






**<a name="_toc23112235"></a>前     言**

为规范中国电信动力环境集中监控系统（以下简称动环监控系统）的建设，实现现场监控单元（FSU）的标准化设计，特制定动环监控系统统一互联协议重要组成部分：现场监控单元（FSU）与监控中心（SC）互联B接口技术规范。

本规范明确了中国电信动环监控系统现场监控单元（FSU）与监控中心（SC）互联所遵循的B接口定义、相关机制与流程、互联协议、报文格式等技术要求，作为中国电信动环监控系统标准化建设和设备采购的依据。

本规范由中国电信集团有限公司提出并归口。

本规范由中国电信集团有限公司云网运营部组织制定起草。

主要起草人：杜民、赖世能、宗凌、孙文波、赖勇辉、杨飞、罗万彬、王烨、卞礼军、黄艺云、李学楠、邬智蔚。


97

**中国电信动环监控系统B接口技术规范**
1. # <a name="_toc23112236"></a>**范围**
本规范规定了动环监控系统现场监控单元（FSU）与省动环监控系统（SC）之间B接口的数据传输规范。

本规范适用于中国电信动环监控系统现场监控单元（FSU）与省动环监控系统（SC）之间的接口协议。
1. # <a name="_toc23112237"></a>**规范性引用文件**
<a name="_toc280599742"></a>下列文件对于本文件的应用是必不可少的。凡是注日期的引用文件，仅注日期的版本适用于本文件。凡是不注日期的引用文件，其最新版本（包括所有的修改单）适用于本文件。

YD/T 1363.2-2014 通信局（站）电源、空调及环境集中监控管理系统 第2部分：互联协议
1. # <a name="_toc23112238"></a>**术语和定义**
下列术语和定义适用于本规范。
1. ## <a name="_toc18511149"></a><a name="_toc18511648"></a><a name="_toc18682139"></a><a name="_toc19650183"></a><a name="_toc20131846"></a><a name="_toc20235606"></a><a name="_toc23094933"></a><a name="_toc23112239"></a><a name="_toc53822015"></a><a name="_toc47506238"></a><a name="_toc421621419"></a><a name="_toc16259741"></a>**通信协议 Communication Protocol**
规范两个实体（设备或软件）之间进行标准通信的应用层的规约。
1. ## <a name="_toc19650184"></a><a name="_toc20131847"></a><a name="_toc20235607"></a><a name="_toc23094934"></a><a name="_toc23112240"></a><a name="_toc16259742"></a><a name="_toc421621420"></a>**现场监控单元 Field Supervision Unit（FSU)**
监控系统的最小子系统，<a name="_toc53822016"></a><a name="_toc47506239"></a>对动力设备及环境的数据进行采集，并具有统计、分析、管理、数据中继和传输等功能。
1. ## <a name="_toc19650185"></a><a name="_toc20131848"></a><a name="_toc20235608"></a><a name="_toc23094935"></a><a name="_toc23112241"></a><a name="_toc16259743"></a><a name="_toc421621422"></a>**监控对象Supervision Object(SO)**
被监控的通信局站各类基础设施，包括但不限于高低压配电系统、柴油发电机组、通信直流电源、UPS、各类空调设备以及机房环境。
1. ## <a name="_toc19650186"></a><a name="_toc20131849"></a><a name="_toc20235609"></a><a name="_toc23094936"></a><a name="_toc23112242"></a><a name="_toc421621423"></a><a name="_toc16259744"></a>**监控点Supervision Point(SP)**
监控对象上某个特定的监控信号，可分为模拟量监测点（AI）、数字量监测点（DI）、远程控制点（DO）、遥调测点（AO）、软件定义监控点（阈值监测点）等5种类型。
1. ## <a name="_toc19650187"></a><a name="_toc20131850"></a><a name="_toc20235610"></a><a name="_toc23094937"></a><a name="_toc23112243"></a><a name="_toc16259747"></a>**Webservice服务**
WebService是一种跨编程语言和跨操作系统平台的远程调用技术，通过Internet进行基于Http协议的网络应用间的交互。
1. ## <a name="_toc19650188"></a><a name="_toc20131851"></a><a name="_toc20235611"></a><a name="_toc23094938"></a><a name="_toc23112244"></a><a name="_toc16259748"></a>**XML**
Extensible Markup Language，国际通用标记语言，是Internet环境中跨平台、依赖于内容的传输数据技术。
1. ## <a name="_toc20131852"></a><a name="_toc20235612"></a><a name="_toc23094939"></a><a name="_toc23112245"></a>**WSDL**
Web Services Description Language，Web服务描述语言，是用来描述WebService的，它用XML的格式描述了WebService有哪些方法、参数类型、访问路径等等。
1. ## <a name="_toc20131853"></a><a name="_toc20235613"></a><a name="_toc23094940"></a><a name="_toc23112246"></a>**标准化配置文件**
用于FSU监控测点配置的设置文件，FSU与SC之间FTP传送的标准化配置文件由3个xml格式文件组成：《标准化监控点全表》、《标准化监控点配置方案全表》、《FSU监控点配置方案表》。
1. ## <a name="_toc20235614"></a><a name="_toc23094941"></a><a name="_toc23112247"></a>**标准化监控测点全表**
根据需要，对监控的电源、空调等设备的重要测点信号进行标准化命名，形成统一管理的模板。
1. ## <a name="_toc20131854"></a><a name="_toc20235615"></a><a name="_toc23094942"></a><a name="_toc23112248"></a>**监控点配置方案**
系统给每个标准监控测点SP设置若干配置模板，每个配置模板设置了一套完整的标准监控点SP配置参数。监控点SP的配置方案参数包含以下内容：阈值生成信号的告警级别、告警门限值、告警回差设置、告警产生延时设置、告警结束延时设置，DI遥信信号与AI遥测信号的存储周期、变化绝对阀值设置、百分比阀值设置。
1. ## <a name="_toc20235616"></a><a name="_toc23094943"></a><a name="_toc23112249"></a>**厂家配置文件**
除标准化配置文件以外，FSU正常工作需要的配置文件，包括网络参数、端口参数、协议转换等内容。
1. # <a name="_toc18510959"></a><a name="_toc18511024"></a><a name="_toc18511152"></a><a name="_toc18511651"></a><a name="_toc18510960"></a><a name="_toc18511025"></a><a name="_toc18511153"></a><a name="_toc18511652"></a><a name="_toc18510961"></a><a name="_toc18511026"></a><a name="_toc18511154"></a><a name="_toc18511653"></a><a name="_toc18510962"></a><a name="_toc18511027"></a><a name="_toc18511155"></a><a name="_toc18511654"></a><a name="_toc18510963"></a><a name="_toc18511028"></a><a name="_toc18511156"></a><a name="_toc18511655"></a><a name="_toc18510964"></a><a name="_toc18511029"></a><a name="_toc18511157"></a><a name="_toc18511656"></a><a name="_toc18510965"></a><a name="_toc18511030"></a><a name="_toc18511158"></a><a name="_toc18511657"></a><a name="_toc18510966"></a><a name="_toc18511031"></a><a name="_toc18511159"></a><a name="_toc18511658"></a><a name="_toc18510967"></a><a name="_toc18511032"></a><a name="_toc18511160"></a><a name="_toc18511659"></a><a name="_toc18510968"></a><a name="_toc18511033"></a><a name="_toc18511161"></a><a name="_toc18511660"></a><a name="_toc18510969"></a><a name="_toc18511034"></a><a name="_toc18511162"></a><a name="_toc18511661"></a><a name="_toc18510970"></a><a name="_toc18511035"></a><a name="_toc18511163"></a><a name="_toc18511662"></a><a name="_toc18510971"></a><a name="_toc18511036"></a><a name="_toc18511164"></a><a name="_toc18511663"></a><a name="_toc18510972"></a><a name="_toc18511037"></a><a name="_toc18511165"></a><a name="_toc18511664"></a><a name="_toc18510973"></a><a name="_toc18511038"></a><a name="_toc18511166"></a><a name="_toc18511665"></a><a name="_toc18510974"></a><a name="_toc18511039"></a><a name="_toc18511167"></a><a name="_toc18511666"></a><a name="_toc18510975"></a><a name="_toc18511040"></a><a name="_toc18511168"></a><a name="_toc18511667"></a><a name="_toc23112250"></a>**接口**
依据管理功能的不同将整个动环监控系统划分为几个网络管理层，各个管理层之间存在着相互通信，而且整个动环监控系统存在与其他网管之间的相互通信，这样为保证网络内部不同级别的管理层之间、监控系统与其他综合网管之间的正常通信，将不同管理层之间定义不同的接口，接口定义如图1所示。

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.010.png)

1. 动环监控系统A、B、C、D接口定位图

——A接口：现场监控单元（FSU）与监控对象（SO）之间的接口；

——B接口：现场监控单元（FSU）、本地动环监控系统前置机与监控中心（SC）之间的通信接口；

——C接口：大型数据中心等特定子区域动环监控系统与监控中心（SC）之间的通信接口；

——D接口：监控中心（SC）与其它网管系统（MBOSS）之间的通信接口。

本接口规范指的是B接口。
1. # <a name="_toc363243725"></a><a name="_toc17190509"></a><a name="_toc23112251"></a>**B接口定义**
   1. ## <a name="_toc17190510"></a><a name="_toc23112252"></a><a name="_toc17123214"></a>**接口方式**
FSU与SC之间通过WebService服务（B接口协议文本交互）和FTP服务（B接口文件交换）进行信息通信，二者协同配合完成B接口信息传递。
1. ## <a name="_toc166680552"></a><a name="_toc228250486"></a><a name="_toc23112253"></a>**报文原则**
SC与FSU之间的B接口协议报文交互基于WebService技术，消息协议报文采用XML格式，报文发起方作为客户端，响应方作为服务端，SC与FSU可以互为客户端与服务端，即有些报文SC为客户端，FSU为服务端；另外一些报文FSU为客户端，SC为服务端。
1. ## <a name="_toc23112254"></a>**WSDL定义**
SC提供的Webservice接口的WSDL文件（文件名：SCService.wsdl）定义见附录A。

FSU接口的Webservice接口的WSDL文件（文件名：SUService.wsdl）定义见附录B。
1. ## <a name="_toc228250487"></a><a name="_toc23112255"></a>**报文格式定义**
1  基本报文格式定义

<table><tr><th colspan="1">类型</th><th colspan="1">一级节点</th><th colspan="1">二级节点</th><th colspan="1">定义</th></tr>
<tr><td colspan="1" rowspan="2">请求报文</td><td colspan="1" rowspan="2">Request</td><td colspan="1">PK_Type</td><td colspan="1">报文类型</td></tr>
<tr><td colspan="1">Info</td><td colspan="1">报文内容</td></tr>
<tr><td colspan="1" rowspan="2">响应报文</td><td colspan="1" rowspan="2">Response</td><td colspan="1">PK_Type</td><td colspan="1">报文类型</td></tr>
<tr><td colspan="1">Info</td><td colspan="1">报文内容</td></tr>
</table>
1. ## <a name="_toc17190512"></a><a name="_toc23112256"></a>**报文内容定义**
   1. ### **数据类型字节数定义**
1  数据类型字节数定义

|类型|字节数|
| :-: | :-: |
|Long|4字节|
|Short|2字节|
|Char|1字节|
|Float|4字节|
|枚举类型|4字节|
1. ### <a name="_toc228250491"></a><a name="_toc228250490"></a>**常量定义（可扩展）**
1  常量定义

|名称|含义|字节数|
| :-: | :-: | :-: |
|NAME\_LENGTH|名字命名长度|40字节|
|USER\_LENGTH|用户名长度|20字节|
|PASSWORD\_LEN|口令长度|20字节|
|DES\_LENGTH|告警描述信息长度|200字节|
|SUID\_LEN|SU编码长度|17字节|
|DEVICEID\_LEN|设备编码长度|7字节|
|SPID\_LENGTH|监控点ID长度|12字节|
|IP\_LENGTH|IP串长度|15字节|
|SERIALNO\_LEN|告警序号（特征码）长度|10字节|
|TIME\_LEN|时间串长度|19字节|
|FILENAME\_LEN|文件名长度|80字节|
|FAILURE\_CAUSE\_LEN|失败原因描述信息长度|40字节|
1. ### **枚举定义（可扩展）**
报文传送过程中，枚举变量可以用数字，也可以用字符串，优先选用数字。

1  枚举定义

<table><tr><th colspan="1">属性名称</th><th colspan="1">属性描述</th><th colspan="1" valign="top">枚举类型</th><th colspan="1" valign="top">意义</th></tr>
<tr><td colspan="1" rowspan="2">EnumResult</td><td colspan="1" rowspan="2">报文返回结果</td><td colspan="1">FAILURE＝0</td><td colspan="1">失败</td></tr>
<tr><td colspan="1">SUCCESS＝1</td><td colspan="1">成功</td></tr>
<tr><td colspan="1" rowspan="24">EnumFailureCode</td><td colspan="1" rowspan="24">失败原因</td><td colspan="1">USERNAME_ERROR=1</td><td colspan="1">用户名错</td></tr>
<tr><td colspan="1">PASSWORD_ERROR=2</td><td colspan="1">密码错</td></tr>
<tr><td colspan="1">SUID_ERROR=3</td><td colspan="1">错误的SUID</td></tr>
<tr><td colspan="1">DEVICEID_ERROR=4</td><td colspan="1">错误的广义设备ID</td></tr>
<tr><td colspan="1">SPID_ERROR=5</td><td colspan="1">错误的SPID</td></tr>
<tr><td colspan="1">IP_ERROR=6</td><td colspan="1">IP错误</td></tr>
<tr><td colspan="1">NOFILE_ERROR=7</td><td colspan="1">没有文件</td></tr>
<tr><td colspan="1">CONFIG_CHECK_ERROR=8</td><td colspan="1">配置验证失败</td></tr>
<tr><td colspan="1">CONFIG_ERROR=9</td><td colspan="1">配置方案选项值超出范围</td></tr>
<tr><td colspan="1">DATA_FORMAT_ERROR=10</td><td colspan="1">数据格式错</td></tr>
<tr><td colspan="1">CTRL_TIMEOUT=11</td><td colspan="1">控制超时</td></tr>
<tr><td colspan="1">CTRL_PARA_ERROR=12</td><td colspan="1">控制参数错</td></tr>
<tr><td colspan="1">IP_OUTOFACL_ERROR=13</td><td colspan="1">IP不在ACL范围</td></tr>
<tr><td colspan="1">NOFILEDIR_ERROR=14</td><td colspan="1">文件目录不存在</td></tr>
<tr><td colspan="1">AlarmTime_NULL=15</td><td colspan="1">告警开始时间为空或者告警开始时间的年份错误</td></tr>
<tr><td colspan="1">AlarmTrigger_NULL=16</td><td colspan="1">告警触发值为空</td></tr>
<tr><td colspan="1">AlarmLevel_NULL=17</td><td colspan="1">告警级别为空或者不规范</td></tr>
<tr><td colspan="1">AlarmFlag_NULL=18</td><td colspan="1">告警标识为空或不规范</td></tr>
<tr><td colspan="1">AlarmNum_ERROR=19</td><td colspan="1">一条报文包含超过约定数量的告警信息或者报文中的告警信息为空</td></tr>
<tr><td colspan="1">AlarmSN_ERROR=20</td><td colspan="1">告警序号为空或者其长度不等于10位数字或者大于4294967295（此数据为十进制整数的最大值，若大于会溢出）</td></tr>
<tr><td colspan="1">Alarm_ERROR=21</td><td colspan="1">告警标识为END，但告警结束时间为空</td></tr>
<tr><td colspan="1">Alarm Sending Failed＝22</td><td colspan="1">FSU向SC发送告警失败</td></tr>
<tr><td colspan="1">Alarm_Failed＝23</td><td colspan="1">SU存在发送失败的告警</td></tr>
<tr><td colspan="1">OTHER_ERROR＝9999</td><td colspan="1">其他错误</td></tr>
<tr><td colspan="1" rowspan="5">EnumType</td><td colspan="1" rowspan="5">监控点种类</td><td colspan="1">TI＝1</td><td colspan="1">阈值生成信号（由遥测信号根据配置方案生成）</td></tr>
<tr><td colspan="1">DI＝2</td><td colspan="1">遥信信号（数字输入量，包含多态数字输入量）</td></tr>
<tr><td colspan="1">AI＝3</td><td colspan="1">遥测信号（模拟输入量）</td></tr>
<tr><td colspan="1">DO＝4</td><td colspan="1">遥控信号（数字输出量）</td></tr>
<tr><td colspan="1">AO＝5</td><td colspan="1">遥调信号（模拟输出量）</td></tr>
<tr><td colspan="1" rowspan="5">EnumAlarmLevel</td><td colspan="1" rowspan="5">告警事件等级</td><td colspan="1">NOALARM＝0</td><td colspan="1">无告警</td></tr>
<tr><td colspan="1">CRITICAL＝1</td><td colspan="1">一级告警</td></tr>
<tr><td colspan="1">MAJOR＝2</td><td colspan="1">二级告警</td></tr>
<tr><td colspan="1">MINOR＝3</td><td colspan="1">三级告警</td></tr>
<tr><td colspan="1">HINT＝4</td><td colspan="1">四级告警</td></tr>
<tr><td colspan="1" rowspan="7">EnumState</td><td colspan="1" rowspan="7">监控点数据代表的事件状态</td><td colspan="1">NOALARM＝0</td><td colspan="1">正常数据</td></tr>
<tr><td colspan="1">CRITICAL＝1</td><td colspan="1">一级告警</td></tr>
<tr><td colspan="1">MAJOR＝2</td><td colspan="1">二级告警</td></tr>
<tr><td colspan="1">MINOR＝3</td><td colspan="1">三级告警</td></tr>
<tr><td colspan="1">HINT＝4</td><td colspan="1">四级告警</td></tr>
<tr><td colspan="1">OPEVENT＝5</td><td colspan="1">操作事件</td></tr>
<tr><td colspan="1">INVALID＝6</td><td colspan="1">无效数据</td></tr>
<tr><td colspan="1" rowspan="2">EnumFlag</td><td colspan="1" rowspan="2">告警事件起始/结束标志</td><td colspan="1">BEGIN=0</td><td colspan="1">开始</td></tr>
<tr><td colspan="1">END=1</td><td colspan="1">结束</td></tr>
<tr><td colspan="1" rowspan="4">EnumConfigOption</td><td colspan="1" rowspan="4">监控点配置模板选项</td><td colspan="1">ONE=1</td><td colspan="1">配置方案1</td></tr>
<tr><td colspan="1">TWO=2</td><td colspan="1">配置方案2</td></tr>
<tr><td colspan="1">THREE=3</td><td colspan="1">配置方案3</td></tr>
<tr><td colspan="1">FOUR=4</td><td colspan="1">配置方案4</td></tr>
<tr><td colspan="1" rowspan="9">EnumDeviceMeanings</td><td colspan="1" rowspan="9">遥信、遥控、遥调信号意义枚举定义</td><td colspan="1">Meanings=0</td><td colspan="1">信号为遥信量，表示“正常”</td></tr>
<tr><td colspan="1">Meanings=1</td><td colspan="1">信号为遥信量，表示“告警”</td></tr>
<tr><td colspan="1">Meanings=2</td><td colspan="1">信号为遥控量，表示“通”的操作</td></tr>
<tr><td colspan="1">Meanings=3</td><td colspan="1">信号为遥控量，表示“断”的操作</td></tr>
<tr><td colspan="1">Meanings=4</td><td colspan="1">信号为遥控量，表示“运行”的操作</td></tr>
<tr><td colspan="1">Meanings=5</td><td colspan="1">信号为遥控量，表示“停止”的操作</td></tr>
<tr><td colspan="1">Meanings=6</td><td colspan="1">信号为遥调量，表示“赋值”的操作</td></tr>
<tr><td colspan="1">Meanings=7</td><td colspan="1">信号为遥调量，表示“增加”的操作</td></tr>
<tr><td colspan="1">Meanings=8</td><td colspan="1">信号为遥调量，表示“减少”的操作</td></tr>
</table>

1. ### <a name="_toc228250493"></a>**数据结构定义（可自行扩展）**
1  数据结构定义

<table><tr><th colspan="1">结构名称</th><th colspan="1">结构描述</th><th colspan="1" valign="top">属性名称</th><th colspan="1" valign="top">属性类型</th><th colspan="1" valign="top">类型定义</th></tr>
<tr><td colspan="1" rowspan="6">TTime</td><td colspan="1" rowspan="6">时间的结构</td><td colspan="1">Year</td><td colspan="1">short</td><td colspan="1">年</td></tr>
<tr><td colspan="1">Month</td><td colspan="1">short</td><td colspan="1">月</td></tr>
<tr><td colspan="1">Day</td><td colspan="1">short</td><td colspan="1">日</td></tr>
<tr><td colspan="1">Hour</td><td colspan="1">short</td><td colspan="1">时</td></tr>
<tr><td colspan="1">Minute</td><td colspan="1">short</td><td colspan="1">分</td></tr>
<tr><td colspan="1">Second</td><td colspan="1">short</td><td colspan="1">秒</td></tr>
<tr><td colspan="1" rowspan="11">TAlarm</td><td colspan="1" rowspan="11">当前告警的数据结构</td><td colspan="1">SerialNo</td><td colspan="1">char[SERIALNO_LEN]</td><td colspan="1">SU告警序号</td></tr>
<tr><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">SU编码</td></tr>
<tr><td colspan="1">DeviceID</td><td colspan="1">char[DEVICEID_LEN]</td><td colspan="1">设备编码</td></tr>
<tr><td colspan="1">SPID</td><td colspan="1">char[SPID_LENGTH]</td><td colspan="1">监控点ID</td></tr>
<tr><td colspan="1">StartTime</td><td colspan="1">char [TIME_LEN]</td><td colspan="1">告警开始时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</td></tr>
<tr><td colspan="1">EndTime</td><td colspan="1">char [TIME_LEN]</td><td colspan="1">告警结束时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</td></tr>
<tr><td colspan="1">TriggerVal</td><td colspan="1">Float</td><td colspan="1">告警触发值</td></tr>
<tr><td colspan="1">AlarmLevel</td><td colspan="1">EnumAlarmLevel</td><td colspan="1">告警级别</td></tr>
<tr><td colspan="1">AlarmFlag</td><td colspan="1">EnumFlag</td><td colspan="1">告警标志</td></tr>
<tr><td colspan="1">AlarmDesc</td><td colspan="1">char [DES_LENGTH]</td><td colspan="1">告警事件标准文本</td></tr>
<tr><td colspan="1">AlarmFriDesc</td><td colspan="1">char [DES_LENGTH]</td><td colspan="1">告警详细描述</td></tr>
<tr><td colspan="1" rowspan="6">TSemaphore</td><td colspan="1" rowspan="6">实时数据/控制命令的值的结构</td><td colspan="1">Type</td><td colspan="1">EnumType</td><td colspan="1">数据类型</td></tr>
<tr><td colspan="1">SPID</td><td colspan="1">char[SPID_LENGTH]</td><td colspan="1">监控点ID</td></tr>
<tr><td colspan="1">MeasuredVal</td><td colspan="1">float</td><td colspan="1"><p>实测值/控制值</p><p></p></td></tr>
<tr><td colspan="1">Meanings</td><td colspan="1">EnumDeviceMeanings</td><td colspan="1">遥信、遥控、遥调信号意义，遥测、阈值信号忽略本项</td></tr>
<tr><td colspan="1">ReportTime</td><td colspan="1">char [TIME_LEN]</td><td colspan="1">实时数据时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</td></tr>
<tr><td colspan="1">Status</td><td colspan="1">EnumState</td><td colspan="1">状态</td></tr>
<tr><td colspan="1" rowspan="6">SOResource</td><td colspan="1" rowspan="6">监控对象资源信息</td><td colspan="1">SOName</td><td colspan="1">char [30]</td><td colspan="1">设备名称</td></tr>
<tr><td colspan="1">SO_Manufac</td><td colspan="1">char [30]</td><td colspan="1">设备制造商名称</td></tr>
<tr><td colspan="1">SO_Model</td><td colspan="1">char [30]</td><td colspan="1">设备型号</td></tr>
<tr><td colspan="1">SO_OutputCapacity</td><td colspan="1">float</td><td colspan="1">设备容量</td></tr>
<tr><td colspan="1">SO_OutputUnit</td><td colspan="1">char [10]</td><td colspan="1">设备容量的单位</td></tr>
<tr><td colspan="1">SO_StartTime</td><td colspan="1">char [10]</td><td colspan="1">设备投产日期，YYYY-MM-DD</td></tr>
<tr><td colspan="1" rowspan="4">SmartDoorValue</td><td colspan="1" rowspan="4">智能门禁配置信息</td><td colspan="1">DeviceID</td><td colspan="1">char[DEVICEID_LEN]</td><td colspan="1">智能门禁设备编码</td></tr>
<tr><td colspan="1">DoorType</td><td colspan="1">char[60]</td><td colspan="1">智能门禁类型</td></tr>
<tr><td colspan="1">CardType</td><td colspan="1">short</td><td colspan="1">智能门禁卡读卡字节数</td></tr>
<tr><td colspan="1">SmartDoorDes</td><td colspan="1">char [DES_LENGTH]</td><td colspan="1">智能门禁设备详细描述</td></tr>
<tr><td colspan="1" rowspan="5">SDoorAuthData</td><td colspan="1" rowspan="5">智能门禁授权信息</td><td colspan="1">CardNumber</td><td colspan="1">long</td><td colspan="1">卡序号，在一个智能门禁控制设备内唯一</td></tr>
<tr><td colspan="1">CardCode</td><td colspan="1">char[16]</td><td colspan="1">智能卡卡号，在一个智能门禁控制设备内唯一</td></tr>
<tr><td colspan="1">StartDate</td><td colspan="1">char [TIME_LEN]</td><td colspan="1">授权开始时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</td></tr>
<tr><td colspan="1">EndDate</td><td colspan="1">char [TIME_LEN]</td><td colspan="1">授权结束时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</td></tr>
<tr><td colspan="1">OtherData</td><td colspan="1">char [DES_LENGTH]</td><td colspan="1">当采用指纹、身份证验证时的其他识别信息</td></tr>
<tr><td colspan="1" rowspan="7">SDoorEvent</td><td colspan="1" rowspan="7">智能门禁事件信息</td><td colspan="1">SerialNo</td><td colspan="1">char[SERIALNO_LEN]</td><td colspan="1">智能门禁事件序号</td></tr>
<tr><td colspan="1">DeviceID</td><td colspan="1">char[DEVICEID_LEN]</td><td colspan="1">智能门禁设备编码</td></tr>
<tr><td colspan="1">EventType</td><td colspan="1">short</td><td colspan="1">智能门禁事件类型</td></tr>
<tr><td colspan="1">EventTime</td><td colspan="1">char [TIME_LEN]</td><td colspan="1">事件发生时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</td></tr>
<tr><td colspan="1">CardNumber</td><td colspan="1">long</td><td colspan="1">卡序号，在一个智能门禁控制设备内唯一</td></tr>
<tr><td colspan="1">CardCode</td><td colspan="1">char[16]</td><td colspan="1">智能卡卡号，在一个智能门禁控制设备内唯一</td></tr>
<tr><td colspan="1">EventDes</td><td colspan="1">char [DES_LENGTH]</td><td colspan="1">智能门禁事件详细描述</td></tr>
<tr><td colspan="1" rowspan="3">TSUStatus</td><td colspan="1" rowspan="3">SU状态参数</td><td colspan="1">CPUUsage</td><td colspan="1">float</td><td colspan="1">CPU使用率</td></tr>
<tr><td colspan="1">MEMUsage</td><td colspan="1">float</td><td colspan="1">内存使用率</td></tr>
<tr><td colspan="1">SUDateTime</td><td colspan="1">char [TIME_LEN]</td><td colspan="1">SU本地时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）</td></tr>
</table>
1. # <a name="_toc17190514"></a><a name="_toc23112257"></a>**B接口报文**
B接口报文是监控中心SC应用软件和全网FSU都能识别并正确响应的命令，中国电信动环监控系统利用B接口报文和FTP服务实现了告警监控、运行数据采集、遥控遥调、运行历史记录分析与采集等基本动环监控功能，表6为B接口报文命令全集。

1  中国电信动环监控系统统一B接口报文命令集

<table>   <tr><th colspan="1"><p>报文</p><p>类型</p></th><th colspan="1">报文意义</th><th colspan="1"><p>数据流</p><p>方向</p></th><th colspan="1">报文命令名称</th><th colspan="1"><p>命令</p><p>代号</p></th></tr>
   <tr><td colspan="1" rowspan="10">网络联接参数设置</td><td colspan="1">注册请求</td><td colspan="1">SCßFSU</td><td colspan="1">LOGIN</td><td colspan="1">101</td></tr>
   <tr><td colspan="1">注册请求报文响应</td><td colspan="1">SCàFSU</td><td colspan="1">LOGIN_ACK</td><td colspan="1">102</td></tr>
   <tr><td colspan="1">FSU注册状态验证</td><td colspan="1">SCßFSU</td><td colspan="1">SUREADY</td><td colspan="1">103</td></tr>
   <tr><td colspan="1">FSU注册状态验证响应报文</td><td colspan="1">SCàFSU</td><td colspan="1">SUREADY_ACK</td><td colspan="1">104</td></tr>
   <tr><td colspan="1">SC向FSU下发网络联接参数</td><td colspan="1">SCàFSU</td><td colspan="1">SET_SCIP</td><td colspan="1">105</td></tr>
   <tr><td colspan="1">SC向FSU下发网络联接参数响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">SET_SCIP_ACK</td><td colspan="1">106</td></tr>
   <tr><td colspan="1">SC获取FSU的FTP参数</td><td colspan="1">SCàFSU</td><td colspan="1">GET_SUFTP</td><td colspan="1">107</td></tr>
   <tr><td colspan="1">SC获取FSU FTP参数的响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">GET_SUFTP_ACK</td><td colspan="1">108</td></tr>
   <tr><td colspan="1">SC设置FSU FTP参数报文</td><td colspan="1">SCàFSU</td><td colspan="1">SET_SUFTP</td><td colspan="1">109</td></tr>
   <tr><td colspan="1">SC设置FSU FTP参数的响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">SET_SUFTP_ACK</td><td colspan="1">110</td></tr>
   <tr><td colspan="1" rowspan="6">标准化配置文件</td><td colspan="1">FSU请求标准化配置文件报文</td><td colspan="1">FSUàSC</td><td colspan="1">ASK_SCHEMECONFIG</td><td colspan="1">201</td></tr>
   <tr><td colspan="1">FSU请求标准化配置文件响应报文</td><td colspan="1">FSUßSC</td><td colspan="1">ASK_SCHEMECONFIG_ACK</td><td colspan="1">202</td></tr>
   <tr><td colspan="1">SC请求FSU上传其标准化配置文件报文</td><td colspan="1">SCàFSU</td><td colspan="1">GET_SCHEMECONFIG</td><td colspan="1">203</td></tr>
   <tr><td colspan="1">SC请求FSU上传其标准化配置文件报文响应</td><td colspan="1">SCßFSU</td><td colspan="1">GET_SCHEMECONFIG_ACK</td><td colspan="1">204</td></tr>
   <tr><td colspan="1">SC下发标准化配置文件报文</td><td colspan="1">SCàSU</td><td colspan="1">SET_SCHEMECONFIG</td><td colspan="1">205</td></tr>
   <tr><td colspan="1">SC标准化配置文件的响应报文</td><td colspan="1">SCßSU</td><td colspan="1">SET_SCHEMECONFIG_ACK</td><td colspan="1">206</td></tr>
   <tr><td colspan="1" rowspan="8">厂家配置文件</td><td colspan="1">FSU向SC请求厂家配置文件的报文</td><td colspan="1">FSUàSC</td><td colspan="1">ASK_FACTORYCONFIG</td><td colspan="1">301</td></tr>
   <tr><td colspan="1">FSU向SC请求厂家配置文件的响应报文</td><td colspan="1">FSUßSC</td><td colspan="1">ASK_FACTORYCONFIG_ACK</td><td colspan="1">302</td></tr>
   <tr><td colspan="1">FSU请求上传自己厂家配置文件报文</td><td colspan="1">FSUàSC</td><td colspan="1">SEND_FACTORYCONFIG</td><td colspan="1">303</td></tr>
   <tr><td colspan="1">FSU请求上传自己厂家配置文件响应报文</td><td colspan="1">FSUßSC</td><td colspan="1">SEND_ FACTORYCONFIG_ACK</td><td colspan="1">304</td></tr>
   <tr><td colspan="1">SC请求FSU上传其厂家配置文件报文</td><td colspan="1">SCàFSU</td><td colspan="1">GET_FACTORYCONFIG</td><td colspan="1">305</td></tr>
   <tr><td colspan="1">SC请求FSU上传其厂家配置文件响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">GET_FACTORYCONFIG_ACK</td><td colspan="1">306</td></tr>
   <tr><td colspan="1">SC下发FSU厂家配置文件报文</td><td colspan="1">SCàFSU</td><td colspan="1">SET_FACTORYCONFIG</td><td colspan="1">307</td></tr>
   <tr><td colspan="1">SC下发FSU厂家配置文件响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">SET_FACTORYCONFIG_ACK</td><td colspan="1">308</td></tr>
   <tr><td colspan="1" rowspan="4">FSU监控点标准化配置模板选型</td><td colspan="1">SC获取FSU监控点配置模板选型报文</td><td colspan="1">SCàFSU</td><td colspan="1">GET_SPCONFIGOPTION</td><td colspan="1">401</td></tr>
   <tr><td colspan="1">SC获取FSU监控点配置模板选型响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">GET_SPCONFIGOPTION_ACK</td><td colspan="1">402</td></tr>
   <tr><td colspan="1">SC设置监控点配置模板选型报文</td><td colspan="1">SCàFSU</td><td colspan="1">SET_SPCONFIGOPTION</td><td colspan="1">403</td></tr>
   <tr><td colspan="1">SC设置监控点配置模板选型响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">SET_SPCONFIGOPTION_ACK</td><td colspan="1">404</td></tr>
   <tr><td colspan="1" rowspan="4">实时监测数据与当天历史监测数据</td><td colspan="1">SC获取FSU监控点实时监测数据报文</td><td colspan="1">SCàFSU</td><td colspan="1">GET_DATA</td><td colspan="1">501</td></tr>
   <tr><td colspan="1">SC获取FSU监控点实时监测数据响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">GET_DATA_ACK</td><td colspan="1">502</td></tr>
   <tr><td colspan="1">SC获取FSU监控点当天监测历史数据报文</td><td colspan="1">SCàFSU</td><td colspan="1">ASK_TODAYHISDATA</td><td colspan="1">503</td></tr>
   <tr><td colspan="1">SC获取FSU监控点当天监测历史数据响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">ASK_TODAYHISDATA_ACK</td><td colspan="1">504</td></tr>
   <tr><td colspan="1" rowspan="4">告警信息</td><td colspan="1">FSU 告警信息主动上送报文</td><td colspan="1">SCßFSU</td><td colspan="1">SEND_ALARM</td><td colspan="1">601</td></tr>
   <tr><td colspan="1">FSU 告警信息主动上送响应报文</td><td colspan="1">SCàFSU</td><td colspan="1">SEND_ALARM_ACK</td><td colspan="1">602</td></tr>
   <tr><td colspan="1">SC获取FSU当前所有活动告警报文</td><td colspan="1">SCàFSU</td><td colspan="1">GET_ACTIVEALARM</td><td colspan="1">603</td></tr>
   <tr><td colspan="1">SC获取FSU当前所有活动告警响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">GET_ACTIVEALARM_ACK</td><td colspan="1">604</td></tr>
   <tr><td colspan="1" rowspan="2">控制（遥调遥控）命令</td><td colspan="1">SC下发遥调遥控控制命令报文</td><td colspan="1">SCàFSU</td><td colspan="1">SET_RMCTRLCMD</td><td colspan="1">701</td></tr>
   <tr><td colspan="1">SC下发遥调遥控控制命令响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">SET_RMCTRLCMD_ACK</td><td colspan="1">702</td></tr>
   <tr><td colspan="1" rowspan="6">系统或辅助命令</td><td colspan="1">发送时钟消息</td><td colspan="1">SCàFSU</td><td colspan="1">SET_TIME</td><td colspan="1">901</td></tr>
   <tr><td colspan="1">发送时钟消息响应</td><td colspan="1">SCßFSU</td><td colspan="1">SET_TIME_ACK</td><td colspan="1">902</td></tr>
   <tr><td colspan="1">获取SU的运行状态参数报文</td><td colspan="1">SCàFSU</td><td colspan="1">GET_SUINFO</td><td colspan="1">1001</td></tr>
   <tr><td colspan="1">获取SU的运行状态参数响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">GET_SUINFO_ACK</td><td colspan="1">1002</td></tr>
   <tr><td colspan="1">重启SU报文</td><td colspan="1">SCàFSU</td><td colspan="1">SET_SUREBOOT</td><td colspan="1">1101</td></tr>
   <tr><td colspan="1">重启SU响应</td><td colspan="1">SCßFSU</td><td colspan="1">SET_SUREBOOT_ACK</td><td colspan="1">1102</td></tr>
   <tr><td colspan="1" rowspan="8">*智能门禁</td><td colspan="1">SC获取FSU智能门禁配置报文</td><td colspan="1">SCàFSU</td><td colspan="1">GET_SmartDOOR</td><td colspan="1">1201</td></tr>
   <tr><td colspan="1">SC获取FSU智能门禁配置响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">GET_SmartDOOR_ACK</td><td colspan="1">1202</td></tr>
   <tr><td colspan="1">SC设置智能门禁配置报文</td><td colspan="1">SCàFSU</td><td colspan="1">SET_SmartDOOR</td><td colspan="1">1203</td></tr>
   <tr><td colspan="1">SC设置智能门禁配置响应报文</td><td colspan="1">SCßFSU</td><td colspan="1">SET_SmartDOOR_ACK</td><td colspan="1">1204</td></tr>
   <tr><td colspan="1">FSU发送智能门禁授权设置结果报文</td><td colspan="1">SCßFSU</td><td colspan="1">SEND_SmartDOOR</td><td colspan="1">1205</td></tr>
   <tr><td colspan="1">FSU发送智能门禁授权设置结果响应报文</td><td colspan="1">SCàFSU</td><td colspan="1">SEND_SmartDOOR_ACK</td><td colspan="1">1206</td></tr>
   <tr><td colspan="1">FSU发送智能门禁事件报文</td><td colspan="1">SCßFSU</td><td colspan="1">SEND_DOOREvent</td><td colspan="1">1207</td></tr>
   <tr><td colspan="1">FSU发送智能门禁事件响应报文</td><td colspan="1">SCàFSU</td><td colspan="1">SEND_DOOREvent_ACK</td><td colspan="1">1208</td></tr>
</table>

   智能门禁B接口报文命令为可选项，智能门禁未纳入动环监控系统的SC及FSU可不支持智能门禁B接口报文命令。
   1. ## <a name="_toc523924133"></a><a name="_toc17190515"></a><a name="_toc23112258"></a>**LOGIN与LOGIN\_ACK**
数据流说明：

FSU向SC传送SUID、用户名、口令等SU注册信息，SC验证FSU注册信息是否正确，如果正确，返回SC采集机IP地址、端口等响应信息；如果不正确，则向FSU返回注册失败报文，并给出失败原因。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.011.png)

1. LOGIN与LOGIN\_ACK报文流程
1  LOGIN报文

<table>   <tr><th colspan="1" valign="top">发起</th><th colspan="3" valign="top">SU</th></tr>
   <tr><td colspan="1" valign="top">字段</td><td colspan="1" valign="top">变量名称/报文定义</td><td colspan="1" valign="top">长度及类型</td><td colspan="1" valign="top">描述</td></tr>
   <tr><td colspan="1" rowspan="2" valign="top">PK_Type</td><td colspan="1" valign="top">LOGIN</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">FSU向SC注册请求</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">101</td></tr>
   <tr><td colspan="1" rowspan="9" valign="top">Info</td><td colspan="1" valign="top">UserName</td><td colspan="1" valign="top">USER_LENGTH</td><td colspan="1" valign="top">SC注册机认证用用户名，</td></tr>
   <tr><td colspan="1" valign="top">Password</td><td colspan="1" valign="top">PASSWORD_LEN</td><td colspan="1" valign="top">SC注册机认证用口令</td></tr>
   <tr><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">SUIP</td><td colspan="1" valign="top">Char[IP_LENGTH]</td><td colspan="1" valign="top">分配给FSU的IP地址</td></tr>
   <tr><td colspan="1" valign="top">SUPort</td><td colspan="1" valign="top">Sizeof(long)</td><td colspan="1" valign="top">FSU WebService 的端口号</td></tr>
   <tr><td colspan="1" valign="top">SUVendor</td><td colspan="1" valign="top">char[VENDOR_LENGTH]</td><td colspan="1" valign="top">FSU的制造商名称</td></tr>
   <tr><td colspan="1" valign="top">SUModel</td><td colspan="1" valign="top">char[MODEL_LENGTH]</td><td colspan="1" valign="top">FSU的型号</td></tr>
   <tr><td colspan="1" valign="top">SUHardVer</td><td colspan="1" valign="top">char[VER_LEN]</td><td colspan="1" valign="top">FSU的硬件版本号</td></tr>
   <tr><td colspan="1" valign="top">SUSoftVer</td><td colspan="1" valign="top">char[VER_LEN]</td><td colspan="1" valign="top">FSU的软件版本号</td></tr>
</table>

   **XML样例：**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>LOGIN</Name>

   `		`<Code>101</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<UserName>chinatelecom</UserName>

   <Password>chinatelecom</Password>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<SUIP>************</SUIP>

   `		`<SUPort>8080</SUPort>

   `		`<SUVendor>艾默生</SUVendor>

   `		`<SUModel>IDU</SUModel>

   `		`<SUHardVer>2.1</SUHardVer>

   `		`<SUSoftVer>3.0</SUSoftVer>

   `	`</Info>

   </Request>

1  LOGIN\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">LOGIN_ACK</td><td colspan="1"></td><td colspan="1">SC对FSU注册请求的响应</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">102</td></tr>
   <tr><td colspan="1" rowspan="7">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">SCIP</td><td colspan="1">IP_LENGTH</td><td colspan="1">返回SC采集机IP地址</td></tr>
   <tr><td colspan="1">SCPort</td><td colspan="1">Sizeof(long)</td><td colspan="1">SC采集机的WebService 端口号</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，SC找不到SUID，返回SUID_ERROR</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*WhiteIP</td><td colspan="1">n*char[IP_LENGTH]</td><td colspan="1">n个IP列表（FSU的IP地址白名单，覆盖FSU原来的白名单）</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   <PK\_Type>

   `		`<Name>LOGIN\_ACK</Name>

   `		`<Code>102</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<SCIP>************</SCIP>

   `		`<SCPort>8080</SCPort>

   `		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`		`<ACL>

`			`<WhiteIP>************</WhiteIP>

`			`<WhiteIP>************</WhiteIP>

`		`</ACL>

`	`</Info>

</Response>
1. ## <a name="_toc523924134"></a><a name="_toc17190516"></a><a name="_toc23112259"></a>**SUREADY与SUREADY\_ACK**
数据流说明：

FSU通过向SC发送SUREADY报文可验证自身是否已在SC注册。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.012.png)

1. SUREADY与SUREADY\_ACK报文流程
1  SUREADY报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SUREADY</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">FSU注册状态验证</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">103</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SUREADY</Name>

   `		`<Code>103</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `	`</Info>

   </Request>

1  SUREADY\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SUREADY_ACK</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">FSU注册状态验证的响应报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">104</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">Result</td><td colspan="1" valign="top">EnumResult</td><td colspan="1" valign="top">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1" valign="top">FailureCode</td><td colspan="1" valign="top">EnumFailureCode</td><td colspan="1" valign="top">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1" valign="top">FailureCause</td><td colspan="1" valign="top">char[FAILURE_CAUSE_LEN]</td><td colspan="1" valign="top">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   <PK\_Type>

   `		`<Name>SUREADY\_ACK</Name>

   `		`<Code>104</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924149"></a><a name="_toc17190517"></a><a name="_toc23112260"></a>**SET\_SCIP与SET\_SCIP\_ACK**
数据流说明：

SC向FSU下发SC采集机的IP地址、Webservice端口及白名单等配置数据。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.013.png)

1. SET\_SCIP与SET\_SCIP\_ACK报文流程
1  SET\_SCIP报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_SCIP</td><td colspan="1"></td><td colspan="1" valign="top">SC向FSU下发SC采集机的IP地址和Webservice端口</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1" valign="top">105</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1">SCIP</td><td colspan="1">char[IP_LENGTH]</td><td colspan="1" valign="top">SC采集机的IP地址</td></tr>
   <tr><td colspan="1">SCPort</td><td colspan="1">short</td><td colspan="1" valign="top">SC采集机的Webservice端口</td></tr>
   <tr><td colspan="1">SCLoginIP</td><td colspan="1">char[IP_LENGTH]</td><td colspan="1" valign="top">SC注册机的IP地址</td></tr>
   <tr><td colspan="1">SCLoginPort</td><td colspan="1">short</td><td colspan="1" valign="top">SC注册机的端口</td></tr>
   <tr><td colspan="1">n*WhiteIP</td><td colspan="1">n*char[IP_LENGTH]</td><td colspan="1" valign="top">n个IP地址列表（SU的上位机IP地址白名单）</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SET\_SCIP</Name>

   `		`<Code>105</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<SCIP>************</SCIP>

   `		`<SCPort>8080</SCPort>

<SCLoginIP>***********</SCLoginIP>

`		`<SCLoginPort>8080</SCLoginPort>

`		`<ACL>

`			`<WhiteIP>************</WhiteIP>

`			`<WhiteIP>************</WhiteIP>

`		`</ACL>

`	`</Info>

</Request>

1  SET\_SCIP\_ACK报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_SCIP_ACK</td><td colspan="1"></td><td colspan="1">SC向FSU下发网络联接参数响应报文</td></tr>
<tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">106</td></tr>
<tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编号</td></tr>
<tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
<tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
<tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Response>

<PK\_Type>

`		`<Name>SET\_SCIP\_ACK</Name>

`		`<Code>106</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924150"></a><a name="_toc17190518"></a><a name="_toc23112261"></a>**GET\_SUFTP与GET\_SUFTP\_ACK**
数据流说明：

SC获取FSU的FTP服务用户名、密码等参数的报文。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.014.png)

1. GET\_SUFTP与GET\_SUFTP\_ACK报文流程

1  GET\_FTP报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SUFTP</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">获取FSU FTP服务的相关参数</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">107</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   <PK\_Type>

   `		`<Name>GET\_SUFTP</Name>

   `		`<Code>107</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `	`</Info>

   </Request>

1  GET\_FTP\_ACK报文

<table>   <tr><th colspan="1" valign="top">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2" valign="top">PK_Type</td><td colspan="1" valign="top">GET_SUFTP_ACK</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">获取FSU的FTP服务参数的响应报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">108</td></tr>
   <tr><td colspan="1" rowspan="7" valign="top">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">UserName</td><td colspan="1" valign="top">USER_LENGTH</td><td colspan="1" valign="top">FSU FTP服务用户登录名</td></tr>
   <tr><td colspan="1" valign="top">Password</td><td colspan="1" valign="top">PASSWORD_LEN</td><td colspan="1" valign="top">FSU FTP服务对应UserName用户的密码</td></tr>
   <tr><td colspan="1" valign="top">FTPPort</td><td colspan="1" valign="top">char[5]</td><td colspan="1" valign="top">FSU FTP服务端口</td></tr>
   <tr><td colspan="1" valign="top">Result</td><td colspan="1" valign="top">EnumResult</td><td colspan="1" valign="top">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1" valign="top">FailureCode</td><td colspan="1" valign="top">EnumFailureCode</td><td colspan="1" valign="top">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1" valign="top">FailureCause</td><td colspan="1" valign="top">char[FAILURE_CAUSE_LEN]</td><td colspan="1" valign="top">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   <PK\_Type>

   `		`<Name>GET\_SUFTP\_ACK</Name>

   `		`<Code>108</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<UserName>scuser</UserName>

   `		`<Password>telecom</Password>

   `		`<FTPPort>336</FTPPort>

   `		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924151"></a><a name="_toc17190519"></a><a name="_toc23112262"></a>**SET\_SUFTP与SET\_SUFTP\_ACK**
数据流说明：

SC向SU下发设置其FTP服务用户名、密码、端口等参数的报文。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.015.png)

1. SET\_SUFTP与SET\_SUFTP\_ACK报文流程

1  SET\_SCFTP报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SET_SUFTP</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">SC设置FSU FTP参数报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">109</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">UserName</td><td colspan="1" valign="top">USER_LENGTH</td><td colspan="1" valign="top">FSU FTP服务用户登录名</td></tr>
   <tr><td colspan="1" valign="top">Password</td><td colspan="1" valign="top">PASSWORD_LEN</td><td colspan="1" valign="top">FSU FTP服务对应UserName用户的密码</td></tr>
   <tr><td colspan="1" valign="top">FTPPort</td><td colspan="1" valign="top">char[5]</td><td colspan="1" valign="top">FSU FTP服务端口</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   <PK\_Type>

   `		`<Name>SET\_SCFTP</Name>

   `		`<Code>109</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<UserName>scuser</UserName>

   `		`<Password>telecom</Password>

   `		`<FTPPort>336</FTPPort>

   `	`</Info>

   </Request>

1  SET\_SUFTP\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_SUFTP_ACK</td><td colspan="1" valign="top">Sizeof(long)</td><td colspan="1" valign="top">SC设置FSU FTP参数报文响应报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">110</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">Result</td><td colspan="1" valign="top">EnumResult</td><td colspan="1" valign="top">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1" valign="top">FailureCode</td><td colspan="1" valign="top">EnumFailureCode</td><td colspan="1" valign="top">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1" valign="top">FailureCause</td><td colspan="1" valign="top">char[FAILURE_CAUSE_LEN]</td><td colspan="1" valign="top">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   <PK\_Type>

   `		`<Name>SET\_SUFTP\_ACK</Name>

   `		`<Code>110</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<Result>SUCCESS</Result>

<FailureCode/>

`		`<FailureCause/>

`	`</Info>

</Response>

1. ## <a name="_toc523924135"></a><a name="_toc17190520"></a><a name="_toc23112263"></a>**ASK\_SCHEMECONFIG与ASK\_SCHEMECONFIG \_ACK**
数据流说明：

FSU向SC发起标准化配置文件请求报文，SC验证SUID正确后，SC判断是否已具备该FSU标准化配置文件，如果具备，则向该FSU返回成功ACK报文，否则返回带有NOFILE\_ERROR失败码的ACK报文。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.016.png)

1. ASK\_SCHEMECONFIG与ASK\_SCHEMECONFIG\_ACK报文流程
1  ASK\_SCHEMECONFIG报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">ASK_SCHEMECONFIG</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">FSU请求标准化配置文件报文</td></tr>
<tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">201</td></tr>
<tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name>ASK\_SCHEMECONFIG</Name>

`		`<Code>201</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>00-EF-10-A0-22-98</SUID>

`	`</Info>

</Request>

1  ASK\_SCHEMECONFIG\_ACK报文

<table><tr><th colspan="1">字段</th><th colspan="1">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">ASK_SCHEMECONFIG_ACK</td><td colspan="1"></td><td colspan="1">FSU请求标准化配置文件的响应报文</td></tr>
<tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">202</td></tr>
<tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
<tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
<tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
<tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Response>

`	`<PK\_Type>

`		`<Name>ASK\_SCHEMECONFIG\_ACK</Name>

`		`<Code>202</Code>

`	`</PK\_Type>

`	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>FAILURE</Result>

<FailureCode>NOFILE\_ERROR</FailureCode>

`		`<FailureCause>找不到FSU监控点配置方案表</FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924137"></a><a name="_toc17190521"></a><a name="_toc23112264"></a>**GET\_SCHEMECONFIG与GET\_SCHEMECONFIG \_ACK**
数据流说明：

本报文主要用于SC要求FSU上传其标准化配置文件。

FSU在接收到GET\_SCHEMECONFIG报文后，将自身标准化配置文件写入根目录/SCHEMECONFIG/UPLOAD/XXX/目录（XXX表示SUID），然后返回成功的GET\_SCHEMECONFIG\_ACK报文。SC收到成功的GET\_SCHEMECONFIG\_ACK报文后，采用FTP服务登录FSU，读取该FSU放置好的标准化配置文件。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.017.png)

1. GET\_SCHEMECONFIG与GET\_SCHEMECONFIG\_ACK报文流程
1  GET\_SCHEMECONFIG报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SCHEMECONFIG</td><td colspan="1"></td><td colspan="1">SC请求FSU上传其标准化配置文件报文</td></tr>
<tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">203</td></tr>
<tr><td colspan="1">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name>GET\_SCHEMECONFIG</Name>

`		`<Code>203</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>00-EF-10-A0-22-98</SUID>

`	`</Info>

</Request>

1  GET\_SCHEMECONFIG\_ACK报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SCHEMECONFIG_ACK</td><td colspan="1"></td><td colspan="1">SC请求FSU上传标准化配置文件的响应报文</td></tr>
<tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">204</td></tr>
<tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">SU编码</td></tr>
<tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
<tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
<tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Response>

`	`<PK\_Type>

`		`<Name>GET\_SCHEMECONFIG\_ACK</Name>

`		`<Code>204</Code>

`	`</PK\_Type>

<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924136"></a><a name="_toc17190522"></a><a name="_toc23112265"></a>**SET\_SCHEMECONFIG与SET\_SCHEMECONFIG\_ACK**
数据流说明：

SC利用FTP将该FSU标准化配置文件写入该FSU的指定目录（如：根目录/SCHEMECONFIG/DOWNLOAD/XXX目录（XXX为SUID）），然后向FSU发送SET\_SCHEMECONFIG报文。FSU收到SET\_SCHEMECONFIG报文后，首先验证SUID是否正确，如果正确，读取自己根目录/SCHEMECONFIG/DOWNLOAD/XXX目录中的标准化配置文件，然后向SC返回成功的SET\_SCHEMECONFIG\_ACK应答报文。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.018.png)

1. SET\_SCHEMECONFIG与SET\_SCHEMECONFIG\_ACK报文流程

1  SET\_SCHEMECONFIG报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_SCHEMECONFIG</td><td colspan="1"></td><td colspan="1">SC下发标准化配置文件通知报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">205</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SET\_SCHEMECONFIG</Name>

   `		`<Code>205</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `	`</Info>

   </Request>

1  SET\_SCHEMECONFIG\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_SCHEMECONFIG_ACK</td><td colspan="1"></td><td colspan="1">SC下发标准化配置文件通知的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">206</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SET\_SCHEMECONFIG\_ACK</Name>

   `		`<Code>206</Code>

   `	`</PK\_Type>

	

   <Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924138"></a><a name="_toc17190523"></a><a name="_toc23112266"></a>**ASK\_FACTORYCONFIG与ASK\_FACTORYCONFIG\_ACK**
数据流说明：

ASK\_FACTORYCONFIG报文是一个查询报文，FSU询问SC是否保存了该FSU的厂家配置文件，SC收到ASK\_FACTORYCONFIG报文后，判断是否保存了该FSU的厂家配置文件，如果有则返回成功的ACK报文。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.019.png)

1. ASK\_FACTORYCONFIG与ASK\_FACTORYCONFIG\_ACK报文流程

1  ASK\_FACTORYCONFIG报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">ASK_FACTORYCONFIG</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">FSU向SC请求厂家配置文件的报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">301</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>ASK\_FACTORYCONFIG</Name>

   `		`<Code>301</Code>

   `	`</PK\_Type>

   <Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `	`</Info>

   </Request>

1  ASK\_FACTORYCONFIG\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">ASK_FACTORYCONFIG_ACK</td><td colspan="1"></td><td colspan="1">FSU向SC请求自身厂家配置的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">302</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>ASK\_FACTORYCONFIG\_ACK</Name>

   `		`<Code>302</Code>

   `	`</PK\_Type>

   <Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924140"></a><a name="_toc17190524"></a><a name="_toc23112267"></a>**SEND\_FACTORYCONFIG与SEND\_FACTORYCONFIG\_ACK**
数据流说明：

FSU将厂家配置文件写入根目录/FACTORYCONFIG/UPLOAD/XXX/目录（XXX表示SUID），然后向SC发送SEND\_FACTORYCONFIG报文通知SC来读取。SC收到SEND\_FACTORYCONFIG报文后，首先检验SUID，记录厂家配置文件名称，并返回SEND\_FACTORYCONFIG\_ACK报文。然后SC利用FTP登录到该FSU，根据之前报文中的厂家配置文件名称，从该FSU的根目录/FACTORYCONFIG/UPLOAD/XXX中将该FSU厂家配置文件读取到SC。。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.020.png)

1. SEND\_FACTORYCONFIG与SEND\_FACTORYCONFIG\_ACK报文流程

1  SEND\_FACTORYCONFIG报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SEND_FACTORYCONFIG</td><td colspan="1"></td><td colspan="1">FSU请求上传自己的厂家配置文件报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">303</td></tr>
   <tr><td colspan="1" rowspan="2">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">n*ConfigFile</td><td colspan="1">n* char[FILENAME_LEN]</td><td colspan="1">n个厂家配置文件名的列表，带完整路径的文件名</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SEND\_FACTORYCONFIG</Name>

   `		`<Code>303</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<FileList>

   `			`<ConfigFile FileName=”.\fschme\factoryconfig1.xml”/>

   `			`<ConfigFile FileName=”.\fschme\factoryconfig2.xml”/>

   `		`</FileList>

   `	`</Info>

   </Request>


1  SEND\_FACTORYCONFIG\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SEND_FACTORYCONFIG_ACK</td><td colspan="1"></td><td colspan="1">FSU请求上传自己的厂家配置文件的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">304</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SEND\_FACTORYCONFIG\_ACK</Name>

   `		`<Code>304</Code>

   `	`</PK\_Type>

   `	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924141"></a><a name="_toc17190525"></a><a name="_toc23112268"></a>**GET\_FACTORYCONFIG与GET\_FACTORYCONFIG\_ACK**
数据流说明：

SC如需获取某个FSU的厂家配置文件，则首先向该FSU发送GET\_FACTORYCONFIG命令报文；FSU收到报文后，将在用的厂家配置文件写入自己的根目录/FACTORYCONFIG /UPLOAD/XXX目录（XXX为SUID），然后FSU向SC返回成功的GET\_FACTORYCONFIG\_ACK报文；最后SC利用FTP读取该FSU厂家配置文件。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.021.png)

1. GET\_FACTORYCONFIG与GET\_FACTORYCONFIG\_ACK报文流程

1  GET\_FACTORYCONFIG报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">GET_FACTORYCONFIG</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">SC请求FSU上传其厂家配置文件报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">305</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>GET\_FACTORYCONFIG</Name>

   `		`<Code>305</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `	`</Info>

   </Request>

1  GET\_FACTORYCONFIG\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_FACTORYCONFIG_ACK</td><td colspan="1"></td><td colspan="1">SC请求FSU上传其厂家配置文件的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">306</td></tr>
   <tr><td colspan="1" rowspan="5">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*ConfigFile</td><td colspan="1">n* char[FILENAME_LEN]</td><td colspan="1">n个配置文件的列表，带完整路径的文件名</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>GET\_FACTORYCONFIG\_ACK</Name>

   `		`<Code>306</Code>

   `	`</PK\_Type>

   `	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`		`<FileList>

`			`<ConfigFile FileName=”.\fschm\factoryconfig1.xml”/>

`			`<ConfigFile FileName=”\fschm\factoryconfig2.xml”/>

`		`</FileList>

`	`</Info>

</Response>

1. ## <a name="_toc523924139"></a><a name="_toc17190526"></a><a name="_toc23112269"></a>**SET\_FACTORYCONFIG与SET\_FACTORYCONFIG\_ACK**
数据流说明：

SC将厂家配置文件通过FTP写入FSU的根目录/FACTORYCONFIG/DOWNLOAD/XXX/目录（XXX为SUID），然后向该FSU发起SET\_FACTORYCONFIG报文通知FSU读取厂家配置。FSU接收到SET\_FACTORYCONFIG报文后，到自己的指定目录位置读取SC下发的厂家配置文件，并返回应答包。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.022.png)

1. SET\_FACTORYCONFIG与SET\_FACTORYCONFIG\_ACK报文流程

1  SET\_FACTORYCONFIG报文

<table>   <tr><th colspan="1">字段</th><th colspan="1">变量名称/报文定义</th><th colspan="1">长度及类型</th><th colspan="1">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_FACTORYCONFIG</td><td colspan="1"></td><td colspan="1">SC向某个FSU下发该FSU全套厂家配置文件的通知报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">307</td></tr>
   <tr><td colspan="1" rowspan="2">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">n*ConfigFile</td><td colspan="1">n* char[FILENAME_LEN]</td><td colspan="1">n个配置文件的列表，带完整路径的文件名</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SET\_FACTORYCONFIG</Name>

   `		`<Code>307</Code>

   `	`</PK\_Type>

	

   <Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<FileList>

   `			`<ConfigFile FileName=”.\fschme\factoryconfig1.xml”/>

   `			`<ConfigFile FileName=”.\fschme\factoryconfig2.xml”/>

   `		`</FileList>

   `	`</Info>

   </Request>

1  SET\_FACTORYCONFIG\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_FACTORYCONFIG_ACK</td><td colspan="1">Sizeof(long)</td><td colspan="1">SC下发某个FSU全套厂家配置文件的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">308</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">m*ConfigFile</td><td colspan="1">m* char[FILENAME_LEN]</td><td colspan="1">m个读取成功的厂家配置文件的列表，带完整路径的文件名</td></tr>
   <tr><td colspan="1">n*ConfigFile</td><td colspan="1">n* char[FILENAME_LEN]</td><td colspan="1">n个读取失败的厂家配置文件的列表，带完整路径的文件名</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SET\_FACTORYCONFIG\_ACK</Name>

   `		`<Code>308</Code>

   `	`</PK\_Type>

   `	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`		`<FileList>

`			`<SuccessFileList>

`				`<ConfigFile FileName=”.\fschme\factoryconfig1.xml”/>

`				`<ConfigFile FileName=”.\fschme\factoryconfig2.xml”/>

`			`</SuccessFileList>

`			`<FailFileList>

`			`</FailFileList>

`		`</FileList>

`	`</Info>

</Response>

1. ## <a name="_toc523924142"></a><a name="_toc17190527"></a><a name="_toc23112270"></a>**GET\_SPCONFIGOPTION 与GET\_SPCONFIGOPTION\_ACK**
数据流说明：

SC向某个FSU请求其上传某个或某些监控点的配置模板选型，监控点的具体配置内容可以根据SPID与配置模板选型查找标准化配置文件。FSU收到报文后，在ACK报文中根据要求上报其某个或某些监控点的配置模板选型。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.023.png)

1. GET\_SPCONFIGOPTION与GET\_SPCONFIGOPTION\_ACK报文流程

1  GET\_SPCONFIGOPTION报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SPCONFIGOPTION</td><td colspan="1"></td><td colspan="1">SC获取FSU监控点配置模板选项报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">401</td></tr>
   <tr><td colspan="1" rowspan="3">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n*char[DEVICEID_LEN]</td><td colspan="1">广义设备ID。当为全9时，则返回该SU所监控的所有监控点的配置，此时忽略SPIDs参数（即监控点SPID列表）。</td></tr>
   <tr><td colspan="1">m*SPID</td><td colspan="1">m*char[SPID_LENGTH]</td><td colspan="1">相应的监控点ID号列表，当除首位外，其余位为全9时，则返回该动力设备/动力系统/机房的所有监控点的配置（此时DeviceID必须有意义，为某一特定动力设备或动力系统或机房的编码）。</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>GET\_SPCONFIGOPTION</Name>

   `		`<Code>401</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<DeviceList>

   `			`<DeviceID="2010001">

   `				`<SPID>************</SPID>

   `				`<SPID>************</SPID>

   `			`</Device>

   `			`<DeviceID="1063001">

   `				`<SPID>130630220010</SPID>				

   `			`</Device>

   `		`</DeviceList>

   `	`</Info>

   </Request>

1  GET\_SPCONFIGOPTION\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SPCONFIGOPTION_ACK</td><td colspan="1"></td><td colspan="1">SC获取FSU监控点配置模板选项响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">402</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n*char[DEVICEID_LEN]</td><td colspan="1">广义设备ID列表</td></tr>
   <tr><td colspan="1">m*TConfigOption</td><td colspan="1">m*Sizeof(TConfigOption)</td><td colspan="1">对应TConfigOption数据结构定义</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>GET\_SPCONFIGOPTION\_ACK</Name>

   `		`<Code>402</Code>

   `	`</PK\_Type>

   `	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`		`<Values>

`			`<DeviceList>

`				`<DeviceID=”2010001”>

`					`<TConfigOption Type="3" SPID="************" OptionID="1"/>

`					`<TConfigOption Type="3" SPID="************" OptionID="2"/>

`				`</Device>

`				`<DeviceID=”1063001”>

`					`<TConfigOption Type="3" SPID="130630220010" OptionID="1"/>					</Device>

`			`</DeviceList>

`		`</Values>

`	`</Info>

</Response>

1. ## <a name="_toc523924143"></a><a name="_toc17190528"></a><a name="_toc23112271"></a>**SET\_SPCONFIGOPTION与SET\_SPCONFIGOPTION\_ACK**
数据流说明：

SC通过本报文设置FSU某个或某些监控点配置模板选型的选择。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.024.png)

1. SET\_SPCONFIGOPTION与SET\_SPCONFIGOPTION\_ACK报文流程
1  SET\_SPCONFIGOPTION报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SET_SPCONFIGOPTION</td><td colspan="1"></td><td colspan="1" valign="top">SC设置监控点配置模板选型报文</td></tr>
<tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">403</td></tr>
<tr><td colspan="1" rowspan="3">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
<tr><td colspan="1" valign="top">n*Device</td><td colspan="1" valign="top">n*char[DEVICEID_LEN]</td><td colspan="1" valign="top">广义设备ID的列表</td></tr>
<tr><td colspan="1" valign="top">m*TConfigOption</td><td colspan="1">m*Sizeof(TConfigOption)</td><td colspan="1" valign="top">监控点配置列表</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name>SET\_SPCONFIGOPTION</Name>

`		`<Code>403</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>00-EF-10-A0-22-98</SUID>

`		`<Values>

`			`<DeviceList>

`				`<DeviceID=”2010001”>

`					`<TConfigOption Type="3" SPID="************" OptionID="2"/>

`					`<TConfigOption Type="3" SPID="************" OptionID="1"/>

`				`</Device>

`				`<DeviceID=”1063001”>

`					`<TConfigOption Type="3" SPID="130630220010" OptionID="2"/>

`				`</Device>

`			`</DeviceList>

`		`</Values>

`	`</Info>

</Request>

1  SET\_SPCONFIGOPTION\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_SPCONFIGOPTION_ACK</td><td colspan="1"></td><td colspan="1">SC设置监控点配置模板选项响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">404</td></tr>
   <tr><td colspan="1" rowspan="7">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n*char[DEVICEID_LEN]</td><td colspan="1">广义设备ID的列表</td></tr>
   <tr><td colspan="1">m*SPID</td><td colspan="1">m* char[SPID_LENGTH]</td><td colspan="1">设置成功的SPID的列表</td></tr>
   <tr><td colspan="1">t*SPID</td><td colspan="1">t* char[SPID_LENGTH]</td><td colspan="1">设置失败的SPID的列表</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SET\_SPCONFIGOPTION\_ACK</Name>

   `		`<Code>404</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `		`<Result>FAILURE</Result>

<FailureCode>CONFIG\_OPTION\_ERROR</FailureCode>

`		`<FailureCause>监控点配置模板选项设置失败</FailureCause>

`		`<DeviceList>

`			`<DeviceID=”2010001”>

`				`<SuccessList>

`					`<SPID>************</SPID>

`				`</SuccessList>

`				`<FailList>

`					`<SPID>************</SPID>

`				`</FailList>

`			`</Device>

`			`<DeviceID=”1063001”>

`				`<SuccessList>

`					`<SPID>130630220010</SPID>

`				`</SuccessList>

`				`<FailList>

`				`</FailList>

`			`</Device>

`		`</DeviceList>

`	`</Info>

</Response>

1. ## <a name="_toc523924144"></a><a name="_toc17190529"></a><a name="_toc23112272"></a>**GET\_DATA与GET\_DATA\_ACK**
数据流说明：

SC向FSU发送本报文提出一个或多个监控点实时监测数据上报的请求，FSU接收到报文后,将相应的监控点采集的最新监测数据在ACK报文中向SC发送。

如果FSU当前实时监测数据采集失败，涉及的监控点实时监测数据状态值标记为无效（Status="6"），并将MeasuredVal字段返回空（即MeasuredVal=""），此时Meanings返回同样为空（即Meanings=""）。

如果FSU出现不存在的SPID时，SC的ACK报文，结果返回“0”，失败码为“5”，失败原因为“SPID\_ERROR”，只返回失败的SPID，不返回存在的SPID。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.025.png)

1. GET\_DATA与GET\_DATA\_ACK报文流程
1  GET\_DATA报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_DATA</td><td colspan="1"></td><td colspan="1">SC请求FSU某些或全部监控点实时监测数据报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">501</td></tr>
   <tr><td colspan="1" rowspan="3">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n*char[DEVICEID_LEN]</td><td colspan="1">广义设备ID列表。当为全9时，则返回该SU所监控的所有监控点的当前运行状态值；这种情况下，忽略SPIDs参数（即监控点SPID列表）。</td></tr>
   <tr><td colspan="1">m*SPID</td><td colspan="1">m* char[SPID_LENGTH]</td><td colspan="1">监控点ID号列表。当除首位外，其余位为全9时，则返回该广义设备所有监控点的当前运行状态值（此时DeviceID必须有意义，为某一特定动力设备或动力系统或机房的编码）。</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>GET\_DATA</Name>

   `		`<Code>501</Code>

   `	`</PK\_Type>

   `	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<DeviceList>

`			`<DeviceID=”1063001”>

`				`<SPID>130630220010</SPID>

`				`<SPID>120630170010</SPID>

`			`</Device>

`			`<DeviceID=”2010001”>

`				`<SPID>************</SPID>

`			`</Device>

`		`</DeviceList>

`	`</Info>

</Request>

1  GET\_DATA\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_DATA_ACK</td><td colspan="1"></td><td colspan="1">SC请求监控点数据响应</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">502</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n*char[DEVICEID_LEN]</td><td colspan="1">广义设备ID列表</td></tr>
   <tr><td colspan="1">TSemaphores</td><td colspan="1">m*Sizeof(TSemaphore)</td><td colspan="1">对应监控点实时运行状态值，参见TSemaphore的数据结构定义</td></tr>
</table>


   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>GET\_DATA\_ACK</Name>

   `		`<Code>502</Code>

   `	`</PK\_Type>

   `	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`		`<Values>

`			`<DeviceList>

`				`<DeviceID="1063001">

`					`<TSemaphore Type="3" SPID="130630220010" MeasuredVal="0.1" Meanings="" ReportTime="2016-09-10 11:19:31" Status="NOALARM"/>

`					`<TSemaphore Type="2" SPID="120630170010" MeasuredVal="0" Meanings="0" ReportTime="2016-09-10 11:19:31" Status="NOALARM"/>

`				`</Device>

`				`<DeviceID="2010001">

`					`<TSemaphore Type="3" SPID="************" MeasuredVal="" Meanings="" ReportTime="2016-09-10 11:19:31" Status="6"/>

`				`</Device>

`			`</DeviceList>

`		`</Values>

`	`</Info>

</Response>

1. ## <a name="_toc523924145"></a><a name="_toc17190530"></a><a name="_toc23112273"></a>**ASK\_TODAYHISDATA 与ASK\_TODAYHISDATA\_ACK**
数据流说明：

SC应用软件如果需要当天的运行历史记录，可以发送ASK\_TODAYHISDATA报文给FSU，FSU收到ASK\_TODAYHISDATA报文后，将截止当前时间最新的历史运行记录文件（该SU所有监控点的当天历史运行记录）写入FSU的根目录/TODAYHISDATA/UPLOAD/XXX/目录（XXX表示SUID），然后向SC返回ASK\_TODAYHISDATA\_ACK报文。SC收到成功的ASK\_TODAYHISDATA\_ACK报文后，采用FTP服务登录该FSU，读取该FSU放置好的当日历史运行记录文件。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.026.png)

1. ASK\_FACTORYCONFIG与ASK\_FACTORYCONFIG\_ACK报文流程

1  ASK\_TODAYHISDATA报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">ASK_ TODAYHISDATA</td><td colspan="1"></td><td colspan="1">SC向FSU请求当日历史运行记录文件的报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">503</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>ASK\_TODAYHISDATA</Name>

   `		`<Code>503</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>00-EF-10-A0-22-98</SUID>

   `	`</Info>

   </Request>

1  ASK\_TODAYHISDATA\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">ASK_TODAYHISDATA_ACK</td><td colspan="1"></td><td colspan="1">SC向FSU请求当日历史运行记录文件的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">504</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>ASK\_TODAYHISDATA\_ACK</Name>

   `		`<Code>504</Code>

   `	`</PK\_Type>

   `	`<Info>

<SUID>00-EF-10-A0-22-98</SUID>

`		`<Result>SUCCESS</Result>

<FailureCode></FailureCode>

`		`<FailureCause></FailureCause>

`	`</Info>

</Response>

1. ## <a name="_toc523924146"></a><a name="_toc17190531"></a><a name="_toc23112274"></a>**SEND\_ALARM与SEND\_ALARM\_ACK**
数据流说明：

FSU根据实时监测数据、标准化配置文件判断被监控对象是否发生告警，如有告警条件满足则自动生成告警事件，并通过本报文向SC主动发送告警。如果监控点配置的告警级别为零，即为不告警，此时该信号任何状态不产生告警。

告警信息报文中一般包含了多条告警信息，如果其中一条及以上告警信息上送失败或被SC验证错误，SC返回的失败的ACK报文，失败码呈现最后一个有错误的告警信息的错误代码，且ACK报文FailureCause格式为“SerialNo:失败原因描述；SerialNo:失败原因描述”，同时其他正确的告警信息应加入当前告警列表，FSU不应重复发送。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.027.png)

1. SEND\_ALARM与SEND\_ALARM\_ACK报文流程

1  SEND\_ALARM报文

   |字段|变量名称/报文定义|长度及类型|描述|
   | :-: | :-: | :-: | :-: |
   |PK\_Type|SEND\_ALARM||告警信息上报|
   ||Code|Sizeof(short)|601|
   |Info|TAlarms|n\*Sizeof(TAlarm)|告警信息，TAlarm参见数据结构定义|

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SEND\_ALARM</Name>

   `		`<Code>601</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<Values>

   `			`<TAlarmList>

   `				`<TAlarm>

   `				`<SerialNo>0012345678</SerialNo>

   `				`<SUID>44-45-53-54-00-00</SUID>

<DeviceID>2010001</DeviceID>

<SPID>************</SPID>

`				`<StartTime>2015-06-10 11:19:31</StartTime>

`				`<EndTime></EndTime>

`				`<TriggerVal>420.1</TriggerVal>

`				`<AlarmLevel>MAJOR</AlarmLevel>

`				`<AlarmFlag>BEGIN</AlarmFlag>

`					`<AlarmDesc>线电压Uab过高 (420.1V)</AlarmDesc>				

`				`</TAlarm>

`			`<TAlarmList>

`		`</Values>

`	`</Info>

</Request>

1  SEND\_ALARM\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SEND_ALARM_ACK</td><td colspan="1"></td><td colspan="1">告警信息上报响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">602</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">SU编号</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义，上报的告警信息都成功才返回成功。</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*FailureSerialNo</td><td colspan="1">n*char[SERIALNO_LEN]</td><td colspan="1">未成功接收的告警信息的告警序号列表</td></tr>
   <tr><td colspan="1">SCSerialNo</td><td colspan="1">char[SERIALNO_LEN]</td><td colspan="1">监控中心返回对成功接收的告警信息生成的告警特征码，成功接收任一个告警信息也会返回告警特征码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   <PK\_Type>

   `		`<Name>SEND\_ALARM\_ACK</Name>

   `		`<Code>602</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID/>

<Result/>SUCCESS</Result>

<FailureCode></FailureCode>

<FailureSerialNoList>

<FailureSerialNo>1234567890</FailureSerialNo>

<FailureSerialNo>1234567891</FailureSerialNo>

`		`</FailureSerialNoList>

<FailureCause></FailureCause>

<SCSerialNo>0000001108</SCSerialNo>

`	`</Info>

</Response>

1. ## <a name="_toc523924147"></a><a name="_toc17190532"></a><a name="_toc23112275"></a>**GET\_ACTIVEALARM与GET\_ACTIVEALARM\_ACK**
数据流说明：

该报文用于实现FSU和SC的告警同步，SC通过本报文获取某个FSU的当前所有活动告警以便进行告警同步。

注意SC通过GET\_ACTIVEALARM报文获取的告警是不返回SC告警特征码的，以示与正常FSU上报的告警信息的区别。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.028.png)

1. GET\_ACTIVEALARM与GET\_ACTIVEALARM\_ACK报文流程

1  GET\_ACTIVEALARM报文

|字段|变量名称/报文定义|长度及类型|描述|
| :-: | :-: | :-: | :-: |
|PK\_Type|GET\_ACTIVEALARM||SC获取FSU当前所有告警信息请求报文|
||Code|Sizeof(short)|603|
|Info|SUID|char[SUID\_LEN]|FSU编码|

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name>GET\_ACTIVEALARM</Name>

`		`<Code>603</Code>

`	`</PK\_Type>

`	`<Info>

<SUID>44-45-53-54-00-00</SUID>

`	`</Info>

</Request>

1  GET\_ACTIVEALARM\_AC报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_ACTIVEALARM_ACK</td><td colspan="1"></td><td colspan="1">SC获取FSU当前所有告警信息请求响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">604</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义，上报的告警信息都成功才返回成功。</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*TAlarm</td><td colspan="1">n*Sizeof(TAlarm)</td><td colspan="1">SU当前所有告警信息</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   <PK\_Type>

   `		`<Name>GET\_ACTIVEALARM\_ACK</Name>

   `		`<Code>604</Code>

   `	`</PK\_Type>

   `	`<Info>

<Result>SUCCESS</Result>

<FailureCode></FailureCode>

<FailureCause></FailureCause>

<Values>

`			`<TAlarmList>

`				`<TAlarm>

`				`<SerialNo>0012345678</SerialNo>

`				`<SUID>44-45-53-54-00-00</SUID>

<DeviceID>2010001</DeviceID>

<SPID>************</SPID>

`				`<StartTime>2015-06-10 11:19:31</StartTime>

`				`<EndTime></EndTime>

`				`<TriggerVal>420.1</TriggerVal>

`				`<AlarmLevel>MAJOR</AlarmLevel>

`				`<AlarmFlag>BEGIN</AlarmFlag>

`					`<AlarmDesc>线电压Uab过高 (420.1V)</AlarmDesc>								</TAlarm>

`			`<TAlarmList>

</Values>

`	`</Info>

</Response>

1. ## <a name="_toc523924148"></a><a name="_toc17190533"></a><a name="_toc23112276"></a>**SET\_RMCTRLCMD与SET\_RMCTRLCMD\_ACK**
数据流说明：

SC向FSU发送遥控、遥调标准控制命令，该控制命令是标准化配置文件定义的标准控制指令，不是监控对象（动力设备）能直接通过智能口接收、执行的控制命令字符串。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.029.png)

1. SET\_RMCTRLCMD与SET\_RMCTRLCMD\_ACK报文流程

关于控制结果的返回说明：

1）SU检测请求的IP，如果IP不在ACL范围内，SU日志记录错误代码为IP\_OUTOFACL\_ERROR，但不做响应。

2）当出现既有成功，也有失败的结果时，以失败结果返回接口信息。出现不同的失败码时，只需要返回最后一个失败码。失败原因，以“SPID：失败原因”依次列出，中间以“；”相隔。

1  SET\_RMCTRLCMD报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_RMCTRLCMD</td><td colspan="1"></td><td colspan="1">SC向FSU下发遥控遥调标准控制命令报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">701</td></tr>
   <tr><td colspan="1" rowspan="3">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n* char[DEVICEID_LEN]</td><td colspan="1">n个广义设备ID的列表</td></tr>
   <tr><td colspan="1">m*TSemaphore</td><td colspan="1">m*Sizeof(TSemaphore)</td><td colspan="1">m个监控点的设置值，数据的值的类型由相应的数据结构决定</td></tr>
</table>

   **XML样例**（说明：第1条指令“远程控制开关电源模块关机”，第2条指令“远程遥调开关电源均充电压为57V”，第3条指令“远程控制精密空调开机”，第4条指令“远程遥调精密空调设定温度下降2℃”）

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SET\_RMCTRLCMD</Name>

   `		`<Code>701</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<Values>

   `			`<DeviceList>

   `				`<Device ID="060001">

   `					`<TSemaphore Type="4" SPID="140600120010" MeasuredVal="" Meanings="5" ReportTime="2018-09-02 14:02:35" Status="5"/>

   `					`<TSemaphore Type="5" SPID="150600410010" MeasuredVal="57" Meanings="6" ReportTime="2018-09-02 14:02:35" Status="5"/>

   `				`</Device>

   `				`<Device ID="080002">

   `					`<TSemaphore Type="4" SPID="140800280010" MeasuredVal="" Meanings="4" ReportTime="2018-09-02 14:02:35" Status="5"/>

   `					`<TSemaphore Type="5" SPID="150800310010" MeasuredVal="2" Meanings="8" ReportTime="2018-09-02 14:02:35" Status="5"/>

   `				`</Device>

   `			`</DeviceList>

   `		`</Values>

   `	`</Info>

   </Request>

1  SET\_RMCTRLCM\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SET_RMCTRLCM_ACK</td><td colspan="1"></td><td colspan="1" valign="top">SC向FSU下发遥控遥调标准控制命令的响应报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">702</td></tr>
   <tr><td colspan="1" rowspan="7">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">Result</td><td colspan="1" valign="top">EnumResult</td><td colspan="1" valign="top">成功/失败标志，见EnumResult枚举定义，控制命令全部成功执行才返回成功状态。</td></tr>
   <tr><td colspan="1" valign="top">FailureCode</td><td colspan="1" valign="top">EnumFailureCode</td><td colspan="1" valign="top">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1" valign="top">FailureCause</td><td colspan="1" valign="top">char[FAILURE_CAUSE_LEN]</td><td colspan="1" valign="top">失败原因</td></tr>
   <tr><td colspan="1" valign="top">n*Device</td><td colspan="1" valign="top">n* char[DEVICEID_LEN]</td><td colspan="1" valign="top">n个设备ID列表</td></tr>
   <tr><td colspan="1" valign="top">m*SPID</td><td colspan="1">m* char[SPID_LENGTH]</td><td colspan="1" valign="top">m个控制或调节成功的SPID列表</td></tr>
   <tr><td colspan="1" valign="top">t* SPID</td><td colspan="1">t* char[SPID_LENGTH]</td><td colspan="1" valign="top">t个控制或调节失败的SPID列表</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SET\_RMCTRLCM\_ACK</Name>

   `		`<Code>702</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `	`<Result>FAILURE</Result>

<FailureCode>12</FailureCode>

`		`<FailureCause>140800280010：CONTROL\_TIMEOUT；150800310010：CONTROL\_PARAMETER\_ERROR</FailureCause>

`		`<DeviceList>

`			`< DeviceID="060001">

`				`<SuccessList>

`					`<SPID>140600120010</SPID>

`					`<SPID>150600410010</SPID>

`				`</SuccessList>

`				`<FailList>

`				`</FailList>

`			`</Device>

`			`< DeviceID="080002">

`				`<SuccessList>

`				`</SuccessList>

`				`<FailList>

`					`<SPID>140800280010</SPID>

`					`<SPID>150800310010</SPID>

`				`</FailList>

`			`</Device>

`		`</DeviceList>

`	`</Info>

</Response>

1. ## <a name="_toc523924152"></a><a name="_toc17190534"></a><a name="_toc23112277"></a>**SET\_TIME与SET\_TIME\_ACK**
数据流说明：

SC向FSU下发时钟设置参数，FSU接收成功后，应立即将自身的时钟调整为SC下发的时间参数，然后再返回成功的ACK报文。此报文一般用于全网时间同步。













![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.030.png)

1. SET\_TIME与SET\_TIME\_ACK报文流程

1  SET\_TIME报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SET_TIME</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">SC向FSU发送时间参数</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">901</td></tr>
   <tr><td colspan="1" rowspan="2">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">TTime</td><td colspan="1" valign="top">Sizeof(TTime)</td><td colspan="1" valign="top">时间参数</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SET\_TIME</Name>

   `		`<Code>901</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<Time>

   `			`<Year>2018<Year/>

   `			`<Month>09</Month>

   `			`<Day>02</Day>

   `			`<Hour>18</Hour>

   `			`<Minute>15</Minute>

   `			`<Second>30</Second>

   `		`</Time>

   `	`</Info>

   </Request>

1  SET\_TIME\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SET_TIME_ACK</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">SC向FSU发送时间参数响应报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">902</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">Result</td><td colspan="1" valign="top">EnumResult</td><td colspan="1" valign="top">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1" valign="top">FailureCode</td><td colspan="1" valign="top">EnumFailureCode</td><td colspan="1" valign="top">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1" valign="top">FailureCause</td><td colspan="1" valign="top">char[FAILURE_CAUSE_LEN]</td><td colspan="1" valign="top">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   <PK\_Type>

   `		`<Name>SET\_TIME\_ACK</Name>

   `		`<Code>902</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<Result>SUCCESS</Result>

<FailureCode/>

`		`<FailureCause/>

`	`</Info>

</Response>

1. ## <a name="_toc523924153"></a><a name="_toc17190535"></a><a name="_toc23112278"></a>**GET\_SUINFO与GET\_SUINFO\_ACK**
数据流说明：

SC通过本报文获取FSU当前状态，此命令可作为监控系统的心跳命令，由SC定时从各FSU获取，以防FSU吊死。

当FSU主动推送告警信息的SEND\_ALARM报文未收到SC返回的SEND\_ALARM\_ACK报文时，FSU在GET\_SUINFO\_ACK报文中应返回错误码Alarm Sending Failed（22），提醒SC存在告警发送不成功的异常情况，直至收到SC的SEND\_ALARM\_ACK 报文为止。

当FSU主动推送告警信息的SEND\_ALARM报文收到SC返回含有失败码的SEND\_ALARM\_ACK报文时，FSU在GET\_SUINFO\_ACK报文中应返回错误码Alarm\_Failed（23），提醒SC存在部分告警信息错误的异常情况，SC可据此判断该FSU发生“告警发送异常告警”，告警清除的条件是该FSU中不再存在有“发送次数”标识的告警信息。

除以上2种情况外，FSU应返回成功的GET\_SUINFO\_ACK报文。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.031.png)

1. GET\_SUINFO与GET\_SUINFO\_ACK报文流程
1  GET\_SUINFO报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SUINFO</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">获取FSU的当前运行状态信息</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">1001</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>GET\_SUINFO</Name>

   `		`<Code>1001</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `	`</Info>

   </Request>

1  GET\_SUINFO\_ACK报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SUINFO_ACK</td><td colspan="1"></td><td colspan="1" valign="top">获取FSU的状态信息响应</td></tr>
<tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1" valign="top">1002</td></tr>
<tr><td colspan="1" rowspan="5">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
<tr><td colspan="1">TSUStatus</td><td colspan="1">Sizeof(TSUStatus)</td><td colspan="1" valign="top">FSU状态</td></tr>
<tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1" valign="top">成功/失败标志，见EnumResult枚举定义</td></tr>
<tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1" valign="top">失败码，见EnumFailureCode枚举值</td></tr>
<tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1" valign="top">失败原因</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Response>

`	`<PK\_Type>

`		`<Name>GET\_SUINFO\_ACK</Name>

`		`<Code>310</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>44-45-53-54-00-00</SUID>

`		`<TSUStatus>

`			`<SUTime>2019/01/24 08:15:30</SUTime>

<CPUUsage>60%</CPUUsage>

`			`<MEMUsage>50%</MEMUsage>

`		`</TSUStatus>

`	`<Result/>FAILURE<Result/>

<FailureCode/>23<FailureCode/>

`		`<FailureCause/>Alarm Failed<FailureCause/>

`	`</Info>

</Response>

1. ## <a name="_toc523924154"></a><a name="_toc17190536"></a><a name="_toc23112279"></a>**SU\_REBOOT与SU\_REBOOT\_ACK**
数据流说明：

SC通过本报文可以远程重启FSU，FSU收到报文在重启前返回成功的ACK报文。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.032.png)

1. SU\_REBOOT与SU\_REBOOT\_ACK报文流程

1  SU\_REBOOT报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SU_REBOOT</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">SC重启SU报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">1101</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">SU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SET\_SUREBOOT</Name>

   `		`<Code>1101</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `	`</Info>

   </Request>

1  重启SU响应报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SET_SUREBOOT_ACK</td><td colspan="1">Sizeof(long)</td><td colspan="1">重启SU响应</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1102</td></tr>
   <tr><td colspan="1" rowspan="4">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">SU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SU\_REBOOT\_ACK</Name>

   `		`<Code>1102</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<Result>SUCCESS</Result>

<FailureCode/>

`		`<FailureCause/>

`	`</Info>

</Response>

1. ## <a name="_toc523924155"></a><a name="_toc17190537"></a><a name="_toc23112280"></a>**GET\_SORESINFO与GET\_SORESINFO\_ACK**
数据流说明：

SC通过本报文要求FSU上报其监控对象SO的资源信息，SU接收命令后，建议通过读取监控对象的智能口获取其资源信息，或者返回FSU存储的监控对象SO的资源信息。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.033.png)

1. GET\_SORESINFO与GET\_SORESINFO\_ACK报文流程

1  GET\_SORESINFO报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SORESINFO</td><td colspan="1"></td><td colspan="1">SC要求FSU上报其监控对象资源信息报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1103</td></tr>
   <tr><td colspan="1" rowspan="2">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">m*DeviceID</td><td colspan="1">m*char[DEVICEID_LEN]</td><td colspan="1">设备ID列表</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>GET\_SORESINFO</Name>

   `		`<Code>1103</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<DeviceList>

   `			`<DeviceID>1060001</DeviceID>

   `		`</DeviceList>

   `	`</Info>

   </Request>

1  GET\_SORESINFO\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SORESINFO _ACK</td><td colspan="1">Sizeof(long)</td><td colspan="1">SC要求FSU上报其监控对象资源信息的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1104</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n* char[DEVICEID_LEN]</td><td colspan="1">n个设备ID列表</td></tr>
   <tr><td colspan="1">SOResource</td><td colspan="1">Sizeof(SOResource)</td><td colspan="1">设备资源信息数据结构</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>GET\_SORESINFO\_ACK</Name>

   `		`<Code>1104</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<Result>SUCCESS</Result>

<FailureCode/>

`		`<FailureCause/>

<Values>

<DeviceID=”1060001”>

`				`<SOResource>

`				`<SOName>48V直流电源</SOName>

<SO\_Manufac>艾默生</SO\_Manufac>

`				`<SO\_Model>PS48100</SO\_Model>

<SO\_OutputCapacity>600</SO\_OutputCapacity>

<SO\_OutputUnit>A</SO\_OutputUnit>

`				`<SO\_StartTime>2015-06-10</SO\_StartTime>

`				`</SOResource>

`			`</Device>

</Values>

`	`</Info>

</Response>

1. ## <a name="_toc17190538"></a><a name="_toc23112281"></a>**GET\_SMARTDOOR与GET\_SMARTDOOR\_ACK**
数据流说明：

SC通过本报文要求FSU上报其智能门禁的配置数据。

DoorType：智能门禁类型，是以下类型组合，多个类型之间用空格隔开

——CARD：智能卡

——IDENTITYCARD：身份证

——FINGERPRINT：指纹

CardType：门禁设备智能卡读卡字节数，智能卡号为4字节16进制，当设置字节不足4字节时，卡号高字节补0。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.034.png)

1. GET\_SMARTDOOR与GET\_SMARTDOOR\_ACK报文流程

1  GET\_SMARTDOOR报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">GET_SMARTDOOR</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">SC要求FSU上报智能门禁配置数据报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">1201</td></tr>
   <tr><td colspan="1">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>GET\_SMARTDOOR</Name>

   `		`<Code>1201</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `	`</Info>

   </Request>

1  GET\_SMARTDOOR\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">GET_SMARTDOOR _ACK</td><td colspan="1">Sizeof(long)</td><td colspan="1">SC要求FSU上报智能门禁配置数据的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1202</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
   <tr><td colspan="1">n*Device</td><td colspan="1">n*char[DEVICEID_LEN]</td><td colspan="1">智能门禁控制设备ID列表</td></tr>
   <tr><td colspan="1">m*SmartDoorValue</td><td colspan="1">m* Sizeof(SmartDoorValue)</td><td colspan="1">智能门禁设备配置参数</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>GET\_SMARTDOOR\_ACK</Name>

   `		`<Code>1202</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<Values>

   `			`<DeviceList>

   `				`<DeviceID="1151001">

   `				`<SmartDoorValueDeviceID="1152001" DoorType=" CARD"CardType="4"SmartDoorDes="一号楼.5层.西门"/>

   `				`<SmartDoorValueDeviceID="1152002" DoorType=" CARDIDENTITYCARD"CardType="4"SmartDoorDes="一号楼.3层.东门"/>

   `				`</Device>

   `				`<DeviceID="1151002">

   `				`<SmartDoorValueDeviceID="1152001" DoorType=" CARD"CardType="4"SmartDoorDes="二号楼.1层.西门"/>

   `				`</Device>

   `			`</DeviceList>

   `		`</Values>

   `		`<Result>SUCCESS</Result>

<FailureCode/>

`		`<FailureCause/>

`	`</Info>

</Response>

1. ## <a name="_toc17190539"></a><a name="_toc23112282"></a>**SET\_SMARTDOOR与SET\_SMARTDOOR\_ACK**
数据流说明：

SC通过本报文对FSU联接的智能门禁进行遥控、授权设置。

ActionType为遥控动作类型，字符串，在以下选项中选取：

——OPEN：遥控开门，需指定操作的智能门禁控制设备和智能门禁设备；

——CLOSE：遥控关门，需指定操作的智能门禁控制设备和智能门禁设备；

——RESET：将智能门禁控制设备恢复到初始状态，删除所有用户，删除所有缓存时间，需指定操作的智能门禁控制设备；

——ADDUSER：添加一个用户，需指定操作的智能门禁控制设备及其所管理的所有智能门禁设备；

——REMOVEUSER：删除一个用户，需指定操作的智能门禁控制设备。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.035.png)

1. SET\_SMARTDOOR与SET\_SMARTDOOR\_ACK报文流程

1  SET\_SMARTDOOR报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SET_SMARTDOOR</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">SC设置FSU智能门禁配置数据报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">1203</td></tr>
   <tr><td colspan="1" rowspan="5">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">ActionType</td><td colspan="1" valign="top">char(30)</td><td colspan="1" valign="top">动作类型</td></tr>
   <tr><td colspan="1" valign="top">ActionID</td><td colspan="1" valign="top">long</td><td colspan="1" valign="top">遥控、授权命令序号</td></tr>
   <tr><td colspan="1" valign="top">Device</td><td colspan="1" valign="top">char[DEVICEID_LEN]</td><td colspan="1" valign="top">智能门禁控制设备编码</td></tr>
   <tr><td colspan="1" valign="top">m*SDoorAuthData</td><td colspan="1" valign="top">m*Sizeof(SDoorAuthData)</td><td colspan="1" valign="top">智能门禁设备遥控、授权命令参数列表</td></tr>
</table>

   **XML样例1**（遥控打开“一号楼.5层.西门”）

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Request>

   `	`<PK\_Type>

   `		`<Name>SET\_SMARTDOOR</Name>

   `		`<Code>1203</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<ActionType>OPEN</ActionType>

   `		`<ActionID>104456</ActionID>

   `		`<DeviceID="1151001">

`	`<SDoorAuthDataDeviceID="1152001" CardNumber=""CardCode=""StartDate=""EndDate="" OtherData=""/>

`	`</Info>

</Request>

**XML样例2**（复位智能门禁控制设备，DeviceID="1151002"）

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name>SET\_SMARTDOOR</Name>

`		`<Code>1203</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>44-45-53-54-00-00</SUID>

`		`<ActionType>RESET</ActionType>

`		`<ActionID>104456</ActionID>

`		`<DeviceID="1151002">

`	`<SDoorAuthDataDeviceID="" CardNumber=""CardCode=""StartDate=""EndDate="" OtherData=""/>

`	`</Info>

</Request>

**XML样例3**（在“一号楼.5层.西门”上添加智能卡，序号79，卡号A17E1987）

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name>SET\_SMARTDOOR</Name>

`		`<Code>1203</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>44-45-53-54-00-00</SUID>

`		`<ActionType>ADDUSER</ActionType>

`		`<ActionID>104456</ActionID>

`		`<DeviceID="1151001">

`	`<SDoorAuthDataDeviceID="1152001" CardNumber="79"CardCode="A17E1987"StartDate="2019-06-10 11:19:31"EndDate="2019-08-10 11:19:31" OtherData=""/>

`	`</Info>

</Request>

1  SET\_SMARTDOOR\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1" valign="top">SET_SMARTDOOR _ACK</td><td colspan="1" valign="top">Sizeof(long)</td><td colspan="1" valign="top">SC设置FSU智能门禁配置数据的响应报文</td></tr>
   <tr><td colspan="1" valign="top">Code</td><td colspan="1" valign="top">Sizeof(short)</td><td colspan="1" valign="top">1204</td></tr>
   <tr><td colspan="1" rowspan="6">Info</td><td colspan="1" valign="top">SUID</td><td colspan="1" valign="top">char[SUID_LEN]</td><td colspan="1" valign="top">FSU编码</td></tr>
   <tr><td colspan="1" valign="top">ActionID</td><td colspan="1" valign="top">long</td><td colspan="1" valign="top">遥控、授权命令序号</td></tr>
   <tr><td colspan="1" valign="top">Device</td><td colspan="1" valign="top">char[DEVICEID_LEN]</td><td colspan="1" valign="top">智能门禁控制设备编码</td></tr>
   <tr><td colspan="1" valign="top">Result</td><td colspan="1">EnumResult</td><td colspan="1" valign="top">成功/失败标志，见EnumResult枚举定义</td></tr>
   <tr><td colspan="1" valign="top">FailureCode</td><td colspan="1" valign="top">EnumFailureCode</td><td colspan="1" valign="top">失败码，见EnumFailureCode枚举值</td></tr>
   <tr><td colspan="1" valign="top">FailureCause</td><td colspan="1" valign="top">char[FAILURE_CAUSE_LEN]</td><td colspan="1" valign="top">失败原因</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SET\_SMARTDOOR\_ACK</Name>

   `		`<Code>1204</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<SUID>44-45-53-54-00-00</SUID>

   `		`<ActionID>104456</ActionID>

   `		`<DeviceID="1151001">

   `		`<Result>SUCCESS</Result>

<FailureCode/>

`		`<FailureCause/>

`	`</Info>

</Response>

1. ## <a name="_toc17190540"></a><a name="_toc23112283"></a>**SEND\_SMARTDOOR与SEND\_SMARTDOOR\_ACK**
数据流说明：

FSU应在收到授权命令30分钟内上报设置结果，FSU客户端向SC服务端发送智能门禁授权设置结果请求，SC服务端接收到设置结果后返回成功标志。












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.036.png)

1. SEND\_SmartDOOR与SEND\_SmartDOOR\_ACK报文流程

1  SEND\_SmartDOOR报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SEND_SMARTDOOR</td><td colspan="1"></td><td colspan="1">SC设置FSU智能门禁配置数据报文</td></tr>
<tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1205</td></tr>
<tr><td colspan="1" rowspan="6">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
<tr><td colspan="1">ActionID</td><td colspan="1">long</td><td colspan="1">遥控、授权命令序号</td></tr>
<tr><td colspan="1">Device</td><td colspan="1">char[DEVICEID_LEN]</td><td colspan="1">智能门禁控制设备编码</td></tr>
<tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
<tr><td colspan="1">FailureCode</td><td colspan="1">EnumFailureCode</td><td colspan="1">失败码，见EnumFailureCode枚举值</td></tr>
<tr><td colspan="1">FailureCause</td><td colspan="1">char[FAILURE_CAUSE_LEN]</td><td colspan="1">失败原因</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name>SEND\_SMARTDOOR</Name>

`		`<Code>1205</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>44-45-53-54-00-00</SUID>

`		`<ActionID>104456</ActionID>

`		`<DeviceID="1151001">

`		`<Result>SUCCESS</Result>

<FailureCode/>

`		`<FailureCause/>

`	`</Info>

</Request>

1  SEND\_SMARTDOOR\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SEND_SMARTDOOR _ACK</td><td colspan="1">Sizeof(long)</td><td colspan="1">SC设置FSU智能门禁配置数据的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1206</td></tr>
   <tr><td colspan="1" rowspan="2">Info</td><td colspan="1">ActionID</td><td colspan="1">long</td><td colspan="1">遥控、授权命令序号</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SEND\_SMARTDOOR\_ACK</Name>

   `		`<Code>1206</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<ActionID>104456</ActionID>

   `		`<Result>SUCCESS</Result>

   `	`</Info>

   </Response>

   1. ## <a name="_toc17190541"></a><a name="_toc23112284"></a>**SEND\_DOOREVENT与SEND\_DOOREVENT\_ACK**
数据流说明:

FSU客户端向SC服务端主动发送智能门禁事件信息。如果因网络中断等原因导致事件上报失败，待网络连接恢复后，FSU应自动重新上报失败的事件。如果因SC未及时响应或SC返回失败的ACK报文，FSU也应自动重新上报（每次间隔30s以上，最多尝试3次）。

门禁事件序号SerialNo以10位数字表示，如0012345678(十进制)，不足10位前面补0，最大不能超过一个无符号长整型所表示的数字，即数字在0~4294967295之间，SerialNo在FSU的门禁事件中保证唯一性。

智能门禁事件类型EventType有以下选项：

- 0:其他
- 1:刷卡进门成功
- 2:刷卡出门成功
- 3:按钮出门
- 4:遥控开门
- 5:遥控关门
- 6:密码开门
- 9:解除警报
- 10:进门被拒
- 11:出门被拒
- 12:未知卡
- 13:超时未关门
- 14:非法开门












![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.037.png)

1. SEND\_DOOREVENT与SEND\_DOOREVENT\_ACK报文流程

1  SEND\_DOOREvent报文

<table><tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
<tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SEND_DOOREVENT</td><td colspan="1"></td><td colspan="1">SC设置FSU智能门禁配置数据报文</td></tr>
<tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1207</td></tr>
<tr><td colspan="1" rowspan="3">Info</td><td colspan="1">SUID</td><td colspan="1">char[SUID_LEN]</td><td colspan="1">FSU编码</td></tr>
<tr><td colspan="1">Device</td><td colspan="1">char[DEVICEID_LEN]</td><td colspan="1">智能门禁控制设备编码</td></tr>
<tr><td colspan="1">m*SDoorEvent</td><td colspan="1">m*Sizeof(SDoorEvent)</td><td colspan="1">智能门禁事件列表</td></tr>
</table>

**XML样例**

<?xml version=“1.0” encoding=“UTF-8”?>

<Request>

`	`<PK\_Type>

`		`<Name> SEND\_DOOREVENT </Name>

`		`<Code>1207</Code>

`	`</PK\_Type>

`	`<Info>

`		`<SUID>44-45-53-54-00-00</SUID>

`		`<DeviceID="1151001">

`	`<SDoorEventList>

`		`<SDoorEvent>

`			`<SerialNo>1024897</SerialNo>

`			`<DeviceID>1152001</DeviceID>

`			`<EventTime>2019-06-10 11:19:31</EventTime>

`			`<EventType>1</EventType>

`			`<CardNumber>79</CardNumber>

`			`<CardCode>A17E1987</CardCode>

`			`<EventDesc>A17E1987刷卡成功</EventDesc>

`		`</SDoorEvent>

`		`</SDoorEventList>

`	`</Info>

</Request>

1  SEND\_DOOREVENT\_ACK报文

<table>   <tr><th colspan="1">字段</th><th colspan="1" valign="top">变量名称/报文定义</th><th colspan="1" valign="top">长度及类型</th><th colspan="1" valign="top">描述</th></tr>
   <tr><td colspan="1" rowspan="2">PK_Type</td><td colspan="1">SEND_DOOREvent _ACK</td><td colspan="1">Sizeof(long)</td><td colspan="1">SC设置FSU智能门禁配置数据的响应报文</td></tr>
   <tr><td colspan="1">Code</td><td colspan="1">Sizeof(short)</td><td colspan="1">1208</td></tr>
   <tr><td colspan="1" rowspan="2">Info</td><td colspan="1">Device</td><td colspan="1">char[DEVICEID_LEN]</td><td colspan="1">智能门禁控制设备编码</td></tr>
   <tr><td colspan="1">Result</td><td colspan="1">EnumResult</td><td colspan="1">成功/失败标志，见EnumResult枚举定义</td></tr>
</table>

   **XML样例**

   <?xml version=“1.0” encoding=“UTF-8”?>

   <Response>

   `	`<PK\_Type>

   `		`<Name>SEND\_DOOREVENT\_ACK</Name>

   `		`<Code>1208</Code>

   `	`</PK\_Type>

   `	`<Info>

   `		`<DeviceID="1151001">

   `		`<Result>SUCCESS</Result>

   `	`</Info>

   </Response>
1. # <a name="_toc17190542"></a><a name="_toc23112285"></a>**FSU接入SC流程**
新安装的FSU接入SC的流程称为FSU接入SC流程，接入流程可参考附录D。

新安装的FSU，首先由现场工程安装人员通过调测工具或FSU配置界面，在FSU配置初始信息，FSU初始配置信息一般包括以下信息：

1) 与SC联网的注册参数：
1) SC服务器信息：FSU向SC平台注册时连接的SC服务器IP地址、端口号、用户名、密码。
1) FSU提供Webservice服务及FTP服务所需提供的信息：FSU IP地址、端口号、用户名、密码。
1) FSU资源信息：FSU编码，FSU制造商名称、型号、软硬件版本号等。
1) FSU接入的标准监控点信息：包括所接入的监控点编码、监控点配置等信息，由用户参照附件1《中国电信动环监控系统标准监测信号全表》自行定义，本协议中上述信息存储为标准化配置文件。
1) FSU采集监控对象SO信息所需的配置信息：包括FSU南向采集端口信息、SO A接口协议解析库等，本协议中上述信息存储为厂家配置文件。

上述信息的具体规则、内容各省可根据实际需求进行确定，本规范规定的内容可供参考，具体可见附录C。

FSU采集SO的运行数据，并将采集到的监测数据进行标准化等相应处理后，通过本技术规范定义的B接口协议报文上报监控中心SC。

工程施工时，首先在监控中心SC应用软件中对准备安装的FSU设备及其监测点SP进行数据配置（简称建站），包括该FSU的标准监控点配置文件（以下简称“标准化配置文件”）。

工程人员安装调测完FSU后，可利用FSU调测工具生成该FSU的厂家配置文件，例如动力设备智能接口解码协议、数据采集通道配置等。

用于FSU与SC交互传输的标准化配置文件、厂家配置文件应采用XML格式。

根据FSU对标准监控点配置文件、厂家配置文件的配置方式不同，FSU接入SC的流程可分为两种模式：

——自顶向下接入；

——自底向上接入。
1. ## <a name="_toc17190543"></a><a name="_toc23112286"></a>**自顶向下接入模式**
自顶向下接入模式下，被监控的设备资源信息及标准化配置文件由SC配置生成，FSU如需重新加载配置，首先通过注册后，请求SC将标准化配置文件下发，FSU根据下发的标准化配置文件进行相应的设置，并生成厂家配置文件。

接入流程如下：

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.038.png)

1. 自顶向下FSU接入SC模式流程示意图
   1. ## <a name="_toc17190544"></a><a name="_toc23112287"></a>**自底向上接入模式**
自底向上接入模式下，FSU标准化配置文件、厂家配置文件以FSU中的版本为准，FSU中加载配置时利用自身保存的标准化配置文件、厂家配置文件，SC保存的该FSU标准化配置文件、厂家配置文件作为备份档案使用。

接入流程如下：

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.039.png)

1. 自底向上FSU接入SC模式框架图
   1. ## <a name="_toc17190545"></a><a name="_toc23112288"></a>**FSU注册流程**
FSU可向SC发起注册流程以便与SC建立TCP/IP连接关系，注册参数由工程安装人员或维护人员利用FSU配置工具提前在FSU设置完成。

FSU注册报文得到SC确认后，SC分配采集服务器的IP地址给FSU，FSU今后的工作报文将发向采集服务器。B接口是非实时的持续连接，基于HTTP协议，B接口报文在广域网内通过TCP/IP选择路由向目的IP发送报文，报文源IP与报文目的IP之间不建立固定连接。

如果SC收到FSU注册报文后，在系统中找不到相应的FSU建站数据，说明该FSU还未在SC完成建站，SC在注册ACK报文中将返回失败。因此在FSU注册前，必须在SC系统提前做好FSU的建站配置。

FSU应具备IP地址过滤功能（白名单），注册时SC返回的LOGIN\_ACK报文，其ACL中的IP地址白名单是该FSU可以响应的源IP地址集，该IP地址集以外的IP地址发来的任何报文，FSU均不能响应。

FSU若是采用动态IP地址，每次IP地址变更，FSU应重新发起注册流程。
1. # <a name="_toc23112289"></a><a name="_toc17190546"></a>**测点标准化**
   1. ## <a name="_toc17190547"></a><a name="_toc23112290"></a>**标准化配置文件**
用于FSU与SC之间FTP传送的标准化配置文件由3个xml格式文件组成：《标准化监控点全表》、《标准化监控点配置方案全表》、《FSU监控点配置方案表》。

《标准化监控点全表》、《标准化监控点配置方案全表》是各省分公司根据需求编制的《动环监控系统标准监测信号全表》的xml格式版本，具体格式与内容可参照附录E编制。

如果是FSU向SC发起标准化配置文件传送请求，首先通过ASK\_SCHEMECONFIG报文向SC提出标准化配置文件下发请求，SC判断是否已具备该FSU标准化配置文件，如果具备，则向该FSU返回成功ACK报文，否则返回带有NOFILE\_ERROR失败码的ACK报文。之后，SC利用FTP将该FSU标准化配置文件写入该FSU的根目录/SCHEMECONFIG/DOWNLOAD/XXX目录（XXX为SUID），然后向FSU发送SET\_SCHEMECONFIG报文。FSU收到SET\_SCHEMECONFIG报文后，读取自己根目录/SCHEMECONFIG/DOWNLOAD/XXX目录中的标准化配置文件，然后向SC返回SET\_SCHEMECONFIG\_ACK应答报文。FSU读取标准化配置文件后，应立即加载到FSU中并生效。

SC如需获取某个FSU的标准化配置文件，则首先向该FSU发送GET\_SCHEMECONFIG命令报文；FSU收到报文后，将在用的标准化配置文件写入自己的根目录/SCHEMECONFIG/UPLOAD/XXX目录（XXX为SUID），然后FSU向SC返回成功的GET\_SCHEMECONFIG\_ACK报文；最后SC利用FTP读取该FSU标准化配置文件。

上传下载目录的维护由FSU执行，因为SC的FTP用户权限只有文件读写权限，不具备文件修改和删除权限。

为了防止FSU采用动态IP时，SC错误的请求另一个FSU的标准化配置文件，SUID要写入标准化配置文件中。
1. ## <a name="_toc17190548"></a><a name="_toc23112291"></a>**厂家配置文件**
FSU正常工作所需的配置文件，除去标准化配置文件外，其余配置文件均称为厂家配置文件。

FSU的厂家配置文件如需上传SC保存，首先FSU要将自身厂家配置文件写入根目录/FACTORYCONFIG/UPLOAD/目录，完成写入后向SC发送SEND\_FACTORYCONFIG报文；SC收到SEND\_FACTORYCONFIG报文后，利用FTP将FSU全套厂家配置文件读取并保存到SC的档案，完成后向FSU发送成功的SEND\_FACTORYCONFIG\_ACK报文，成功则报文中的Result为成功标志，过程中如果出错，则报文中的FailureCode为相应错误代码，并在FailureCause中说明出错原因。

SC如需获取某个FSU的厂家配置文件，则首先向该FSU发送GET\_FACTORYCONFIG命令报文，FSU收到报文后，将在用的全套厂家配置文件写入自己的根目录/FACTORYCONFIG/UPLOAD目录，然后FSU向SC返回GET\_FACTORYCONFIG\_ACK成功报文，最后SC利用FTP读取该FSU全套厂家配置文件。

如果是FSU向SC发起厂家配置文件传送请求，首先通过ASK\_FACTORYCONFIG报文向SC提出厂家配置文件下发请求，SC判断是否已具备该FSU厂家配置文件，如果具备，则向该FSU返回成功ACK报文，否则返回带有NOFILE\_ERROR失败码的ACK报文。之后，SC利用FTP将该FSU全套厂家配置文件写入该FSU的根目录/FACTORYCONFIG/DOWNLOAD目录，然后向FSU发送SET\_FACTORYCONFIG报文；FSU收到SET\_FACTORYCONFIG报文后，读取自己根目录/FACTORYCONFIG/DOWNLOAD目录中的厂家配置文件，然后向SC返回SET\_FACTORYCONFIG\_ACK应答报文。FSU读取厂家配置文件后，应立即加载到FSU中并生效。

由于是异步操作，在动态IP的情况下，SC有可能FTP登录到错误的FSU，为了确保是正确的FSU，厂家配置文件内应包含SUID信息。
1. ## <a name="_toc23112292"></a>**监控点SP配置方案**
监控点SP的配置方案包含以下内容：阈值生成信号的告警级别、告警门限值、告警回差设置、告警产生延时设置、告警结束延时设置，DI遥信信号与AI遥测信号的存储周期、变化绝对阀值设置、百分比阀值设置。

系统给每个标准监控点SP设置若干配置模板，每个配置模板设置了一套完整的标准监控点SP配置参数。标准监控点SP及其配置模板由电信各省公司或专业公司在其《动环监控系统标准监测信号全表》中明确规定。真实监控点的配置方案在系统标准监控点配置模板中选择其一。

标准监控点配置模板由电信各省公司或专业公司集中编制与管理，一般用户没有配置修改权限。检查监控点配置时，SC只需对标准监控点进行配置模板内容检查，对真实监控点只需检查其选用的模板是否正确。整个系统监控点配置参数的检查工作量小，配置参数不容易出错，配置错误问题也易于发现和修改，适用于省集中维护的发展趋势。

本技术规范B接口报文样例的标准监控点SP配置方案采用以下方式：每个标准监控点有4个配置方案模板（模板数量可扩展），可选择其中一个生效，详细内容参考《中国电信动环系统标准化监控信号测点全表》。
1. ## <a name="_toc17190549"></a><a name="_toc23112293"></a>**修改FSU监控点配置模板选项**
系统不能单独修改FSU监控点的某个配置参数，只能在电信各省公司或专业公司编制的《动环监控系统标准监测信号全表》规定的配置模板中修改其选择项，例如原来选择配置模板1的可以修改成选择配置模板2，修改功能利用SET\_SPCONFIGOPTION报文来完成。

SC如需获取FSU某个或某些监控点的配置模板选项，则向其发送GET\_SPCONFIGOPTION报文，FSU返回 GET\_SPCONFIGOPTION\_ACK报文，将SC指定的监控点配置模板选项信息返回SC。GET\_SPCONFIGOPTION报文中的DeviceID为全9时，则返回该FSU所有监控点的配置模板选项，这种情况下，忽略SPIDs参数（即监控点SPID列表）。当SPID为全9时，则返回某DeviceID的所有监控点的配置模板选项信息。
1. # <a name="_toc23112294"></a><a name="_toc17190554"></a>**重要运行机制**
   1. ## <a name="_toc23112295"></a>**安全机制**
FSU与SC通过广域网通信，为了增强通信的安全性，可采用以下安全机制：

- 访问控制列表：FSU只接受访问控制列表中允许的SC主机发起的连接，以避免未经授权的访问。
- 时间戳与签名：B接口协议文本的通信数据包中包括时间戳字段，避免回放攻击。通信数据包中包括签名字段，对通信双方的身份进行认证，并防止通信数据包被篡改。

  如采用内网组网，可以选用以上安全机制的一种，如采用公网组网，应同时选用以上两种方式。
  1. ### <a name="_toc17190555"></a>**访问控制列表**
访问控制列表仅包含白名单，以下描述以IPV4为主，IPV6表示方法相同。

FSU只接受白名单中主机的访问。

如果白名单为空，FSU自动添加默认白名单 0.0.0.0，接受任何主机访问。

某个FSU的访问控制列表（白名单）无法增、删、改单个IP地址，采取全覆盖方式进行修改。如果成功，FSU向SC返回成功的SET\_ SCIP\_ACK 报文，过程中如果出错，则SET\_SCIP\_ACK报文中的FailureCode为相应错误代码，并在FailureCause中须说明具体出错原因。

SC可通过LOGIN\_ACK及SET\_SCIP指令覆盖FSU中的访问控制列表（白名单）。
1. ### <a name="_toc17190556"></a>**时间戳与签名**
通信数据包中的时间戳记录报文发送的时间，时间戳格式为YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式），通信数据包的有效时间为时间戳向后10分钟内。

签名由报文内容、时间戳和FSU 密码组合生成，对发送与接收双方的身份进行认证，并防止通信报文被篡改。通信报文添加时间戳与签名的流程如下：

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.040.png)

1. 通信报文添加时间戳与签名的流程图

示例如下：

- SC 中添加FSU B，PASSWORD 为87772555E1C16715EBA5C85341684C58。SC通过电子邮件方式将SC接入信息（含FSU PASSWORD）分发至FSU B。
- FSU B 生成请求数据报文（省略具体内容）

  *<Request>*

*<PK\_Type>*

*</PK\_Type>*

*<Info>*

*</Info>*

*</Request>*

- FSU B 添加时间戳

  *<Request TimeStamp=”2018-01-01 00:00:00”>*

  *<PK\_Type>*

  *</PK\_Type>*

  *<Info>*

  *</Info>*

*</Request>*

- FSU B 使用FSU PASSWORD 进行签名，签名算法使用 md5

  签名= md5(内容+时间戳+PASSWORD) = md5(‘*<PK\_Type></PK\_Type><Info></Info>2018-01-01 00:00:00*87772555E1C16715EBA5C85341684C58’) = ‘ED4FB4C4F7275E93938C8BDA5E46D9BC’

  将签名添加到请求数据报文中

  *<Request TimeStamp=”2018-01-01 00:00:00” Sign=”* ED4FB4C4F7275E93938C8BDA5E46D9BC*”>*

  *<PK\_Type>*

  *</PK\_Type>*

  *<Info>*

  *</Info>*

  *</Request>*

- FSU B  发送数据请求报文
- SC接收到数据请求报文

  *<Request TimeStamp=”2018-01-01 00:00:00” Sign=” ED4FB4C4F7275E93938C8BDA5E46D9BC”>*

  *<PK\_Type>*

  *</PK\_Type>*

  *<Info>*

  *</Info>*

  *</Request>*

- SC使用记录的FSU PASSWORD 计算签名

  签名= md5(内容+时间戳+PASSWORD) = md5(‘*<PK\_Type></PK\_Type><Info></Info>2018-01-01 00:00:00*87772555E1C16715EBA5C85341684C58’) = ‘ED4FB4C4F7275E93938C8BDA5E46D9BC’

- SC检查时间戳
- SC处理请求报文
- SC发送应答报文，

  *<Response TimeStamp=”2018-01-01 00:00:01” Sign=” 9ADAF21D7365FDC5088519BC90FC1E9F”>*

  *<PK\_Type>*

  *</PK\_Type>*

  *<Info>*

  *</Info>*

  *</Response>*

使用签名，需要注意以下事项：

- 内容包括PK\_Type和 Info 两个节点
- 计算 md5 前，应将字符串转换为 UTF-8 编码的字节数组
- SC中SU PASSWORD 不能重复，长度最少8个字符。
- SU PASSWORD 不应在协议报文中传输。
- 签名验证不通过的报文，应当丢弃。
- 时间戳超时的报文，应当丢弃。
  1. ## <a name="_toc17190550"></a><a name="_toc23112296"></a><a name="_toc17190558"></a>**告警发送与接收机制**
动环监控系统监测对象产生的告警都是由“阈值生成信号”这种特定类型的监控点产生的，监测对象产生的告警必须对应某个“阈值生成信号”监控点，具体对应关系参见《中国电信动环监控系统标准监测信号表》。

监测对象告警的判断由FSU完成，告警一旦发生或消除，FSU应主动向SC上报告警事件，告警不采取SC轮询方式获取。监测对象告警发生事件，告警报文只有开始时间，没有结束时间；告警消除事件，告警报文必须既有告警开始时间，又有告警结束时间，且与告警发生的告警报文采用同一个告警序号。

如果FSU与SC之间发生通信中断，FSU必须存储通信中断期间的所有告警事件，通信一旦恢复，存储的未上报告警事件，FSU应主动上报SC。

SC也可以通过B接口报文获取某个FSU当前所有未消除的当前告警，以便进行告警核对。
1. ### <a name="_toc17190551"></a>**告警事件描述**
告警事件描述采用XML文件，本规范告警事件XML格式如下：

<TAlarm>

`	`<SerialNo>告警序号</SerialNo>	

`	`<SUID>FSU编号</SUID>

<DeviceID>广义设备编码</DeviceID>

<SPID>监控点ID</SPID>

`	`<StartTime>告警开始时间</StartTime>

`	`<EndTime>告警结束时间</EndTime>

`	`<TriggerVal>告警触发值</TriggerVal>

<AlarmLevel>告警级别</AlarmLevel>

`	`<AlarmFlag>告警标志</AlarmFlag>

`	`<AlarmDesc>告警标准文本</AlarmDesc>

<AlarmFriDesc>告警详细描述</AlarmFriDesc>

</TAlarm>

相关解释：

1) SerialNo：告警序号，本规范以10位数字表示，如0012345678(十进制)，不足10位前面补0，最大不能超过一个无符号长整型所表示的数字，即数字在0~4294967295之间。在某个FSU,告警序号由程序自动顺序增加，但告警结束时的告警序号与告警产生时的告警序号必须相同。当告警序号达到最大值时，从0开始循环。
1) SUID：FSU编码。
1) DeviceID：广义设备编码。
1) SPID：监控点编码。只有“阈值”、“遥信”2种类型的监控点能产生告警事件。
1) StartTime：告警开始时间（不加上告警开始延时时间），格式：YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）；
1) EndTime：告警结束时间（不加上告警结束延时时间），格式：YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）。在FSU通信中断期间产生的历史告警，可以只送一条既包含告警开始时间，又包含告警结束时间的告警报文，表示告警已消除，告警触发值为告警开始事件的触发值。
1) 告警触发值TriggerVal：告警事件触发值。
1) 告警级别AlarmLevel：见本文枚举定义，CRITICAL/MAJOR/MINOR/HINT。
1) 告警标志AlarmFlag：BEGIN/END，表征告警事件是开始还是结束。
1) 告警标准文本AlarmDesc：由《中国电信动环监控系统标准监测信号表》中对应该监控点的{“标准监控点名称”+“DI测点状态为0时的意义”+（+“告警事件触发值”+）}字符串合并构成。
1) 告警详细描述AlarmFriDesc：200字节以内的告警详细描述。
1) 所有文本描述中不能包含”<”、”>”字符。

   告警事件样例：

<TAlarm>

`	`<SerialNo>0012345678</SerialNo>	

`		`<SUID>44-45-53-54-00-00</SUID>

<DeviceID>2010001</DeviceID>

<SPID>************</SPID>

`	`<StartTime>2015-06-1011:19:31</StartTime>

`	`<EndTime></EndTime>

`		`<TriggerVal>420.1V</TriggerVal>

`	`<AlarmLevel>MAJOR</AlarmLevel>

`	`<AlarmFlag>BEGIN</AlarmFlag>

`	`<AlarmDesc>低压配电系统线电压Uab过高(420.1V)</AlarmDesc>

<AlarmFriDesc>市电Uab高过正常范围</AlarmFriDesc>

</TAlarm>
1. ### <a name="_toc17190552"></a>**FSU主动上报告警/SC获取FSU告警流程说明**
一旦FSU判断发生告警，FSU应主动、立即向SC发送SEND\_ALARM报文报告告警信息。SC收到告警信息后，向FSU发送SEND\_ALARM\_ACK报文，SC返回报文中的告警SerialNo须与发送报文中的告警SerialNo一致,同时SC将返回一特征码SCSerialNo给FSU。SC成功接收的告警不应重复上报。

FSU发送告警报文时，如果没有收到SC发送的SEND\_ALARM\_ACK报文，FSU每次间隔30s钟重复发送该报文，直至收到SC的SEND\_ALARM\_ACK报文为止，在此期间，如果连续3次未收到SC的SEND\_ALARM\_ACK报文则在心跳的响应报文GET\_SUINFO\_ACK设置错误码“22”。

FSU收到SC返回的含有失败码FailureCode的SEND\_ALARM\_ACK报文（对应SC判断接收到的部分或所有告警信息出错的场景），FSU应对FailureCause进行解析，对未成功发送的某条或数条告警打上“发送次数”标识。如果FSU能对发送出错的告警信息进行智能处理，则每条含有“发送次数”标识的告警信息最多重复发送3次，每次至少间隔1分钟，3次仍未成功上报的告警信息，FSU就不再发送。

FSU存在打上“发送次数”标识的告警信息时，FSU在心跳响应中应返回错误码为“23”的GET\_SUINFO\_ACK报文。SC收到含有“23”错误码的心跳返回报文，则判断该FSU发生“告警发送异常告警”。告警清除的条件是该FSU经人工干预后不再含有打上“发送次数”标识的告警信息。

所有发生过的告警信息，FSU须保留7天以上备查。

FSU在超过30分钟未收到SC的任何指令时，可以向SC主动推送告警，如参考模板里告警信号为“PSC应用服务器异常告警”。
1. ### <a name="_toc17190553"></a>**告警主动清除操作流程说明**
因为施工或者测试，有可能导致SC与FSU告警不一致，可以采用如下方式处理：

场景一：SC堆积了部分无法再通过FSU上送清除信息而消除的告警信息，需要进行人工清除告警操作。对于需要人工清除的告警信息（例如工程割接产生或误告警产生的告警），SU应在重启时具备告警复位功能，清理SU本身的当前告警列表，对所有监测点重新采集一次，根据最新采集结果判断告警并上报。

场景二：SC可对某FSU进行告警自动比对，手工从FSU获取其全量的活动告警信息，SC与该SU有关的当前告警进行比对。如果有差异，SC以SU的告警为基准进行同步。
1. ## <a name="_toc23112297"></a>**实时监测数据发送与接收机制**
SC可通过B接口协议报文查询模拟量监测点（AI）、数字量监测点（DI）的实时运行数据，远程控制点（DO）、遥调测点（AO）可通过B接口协议报文在SC实现远程控制和远程参数设置。

本规范对实时运行数据的采集支持SC轮询方式，采用GET\_DATA 与GET\_DATA\_ACK报文，支持单个监控点的实时运行数据上报，也支持某个局站、某个动力设备、某套动力系统、某个机房的所有AI/DI监测点的实时运行数据上报。

由于省级动环监控系统纳入监控的局站以及监测点数量众多，本规范采取以下实时运行数据上报管控机制：FSU不主动上报自身监控点的实时运行数据，只在接收到SC的GET\_DATA指令后，才按报文要求上报特定监测点的实时运行数据。

FSU不主动向SC发送实时监测数据，只有SC向其发送GET\_DATA报文时才响应1次，即FSU每收到1条GET\_DATA报文按要求响应1次，返回SC指定的监测点实时监测数据。SC获取FSU实时数据时，若FSU无法获得某些监测点监测数据，则这些监测点的值为空，并且状态为“无效数据”，此时不触发告警。即SU串口或采集通道监测数据采集失败，涉及的SPID测点的实时监测数据状态值标记为无效（Status="6"），并将MeasuredVal字段返回空（即MeasuredVal=""），此时Meanings同样为空（即Meanings=""）。
1. ## <a name="_toc17190559"></a><a name="_toc23112298"></a>**心跳机制**
SC应定期通过GET\_SUINFO报文获取FSU当前的运行状态，作为判断FSU是否正常的判断依据，此为心跳机制。

SC对某个FSU的心跳轮询间隔建议不超过10分钟。

SC向FSU下发心跳监测命令，并根据FSU返回的结果进行判断：

**1)	SC在15S内收到GET\_SUINFO\_ACK报文**

如果返回的是该FSU的CPU、内存等信息，则SC判断FSU为状态正常。如果返回的SUID与请求的SUID不一致，则SC据此判断FSU产生“PSC采集机异常告警”。

FSU主动告警推送未收到SC的ACK则在心跳返回报文中将错误码设置为“22”，直至收到SC的SEND\_ALARM\_ACK报文为止。

若FSU在告警推送过程收到SC含有错误码的SEND\_ALARM\_ACK报文，则在心跳返回报文中将错误码设置为“23”，SC据此判断FSU产生“告警发送异常告警”，该告警清除的条件是FSU中没有打上“发送次数”标识的告警信息，并且在心跳GET\_SUINFO\_ACK中没有错误码“23”。

**2)	SC在15S内没有收到GET\_SUINFO\_ACK报文**

等待5min，SC向FSU再次下发心跳监测命令。

若SC在15S内有收到GET\_SUINFO\_ACK报文，SC后续处理流程与前述1）一致。

若SC在15S内没有收到GET\_SUINFO\_ACK报文，继续等待5min，SC向FSU第三次下发心跳监测命令。

若SC在15S内有收到GET\_SUINFO\_ACK报文，SC后续处理手段与前述1）一致。

若SC在连续3次下发心跳后均未收到GET\_SUINFO\_ACK报文，SC产生告警如“监控单元通信中断”告警。

FSU返回的心跳命令ACK包中，需返回FSU当前时间。可选定每天某一时刻后的第一次心跳，SC对FSU返回的FSU本机时间与SC当前时间进行比对，若SC当前时间与FSU本机时间相差30S或FSU返回的报文中缺乏FSU本机时间，则该FSU的时钟同步状态更新为”异常”,并标明失败原因。

**3）FSU心跳时间同步要求**

FSU返回的心跳命令ACK包中，需返回FSU当前时间。如可约定每天6点后的第一次心跳，SC对FSU返回的FSU本机时间与SC当前时间进行比对，若SC当前时间与FSU本机时间相差30S或FSU返回的报文中缺乏FSU本机时间，则该FSU的时钟同步状态更新为”异常”,并标明失败原因。

**4）FSU无须自监测自身是否吊死，由SC通过心跳负责监测FSU是否保持正常运行。**

FSU如通过注册报文实现自监测是否吊死功能，向SC发起注册请求的周期应超过SC心跳周期2倍以上（不含2倍）。FSU在SC心跳周期2倍以上时间（大于15分钟）内未收到SC的任何报文（包括心跳监测命令），FSU可发起注册请求。

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.041.png)

1. 系统心跳监测流程图

FSU无须自监测自身是否通信正常，由SC通过心跳机制监测FSU是否保持正常向上通信。
1. ## <a name="_toc17190560"></a><a name="_toc23112299"></a>**时间同步机制**
SC应定期通过SET\_TIME报文向所有下挂的FSU下发SC的当前时间，FSU接收到报文后，应自动将FSU的时间设置为所接收到的SET\_TIME报文里面的时间。

时间同步对于省级动环监控系统是非常重要的一项功能，FSU应具备接收时间同步报文且按报文设置自身时间的功能。FSU本身的时间误差应≤ 1.0 s/天。
1. ## <a name="_toc17190561"></a><a name="_toc23112300"></a>**远程控制指令下发机制**
SC通过SET\_RMCTRLCMD报文向FSU下发远程控制指令、远程参数设置指令。注意SC向FSU下发的控制命令是标准指令，参见《中国电信动环系统标准化监控信号测点全表》及SET\_RMCTRLCMD报文规范，这些指令不是监控对象SO能理解并执行的控制指令，需要FSU转换成SO能理解并执行的控制信号或命令。
1. ## <a name="_toc17190562"></a><a name="_toc23112301"></a>**SO资源信息上报机制**
今后集团公司将推动所采购的电源空调设备能在A接口中主动上报自身的资源信息，FSU应自动采集所监控的智能电源空调设备上报的资源信息，并保存在FSU中。

SC通过GET\_SORESINFO报文向FSU下发SO资源信息查询指令，FSU接收到报文后，应将FSU存储的所有SO资源信息上报SC。
1. ## <a name="_toc17190563"></a><a name="_toc23112302"></a>**SO串口透传机制（可选）**
FSU具有多个串行通信端口（RS232/RS485），用于与外部被监控的电源空调设备通信。FSU的串行端口可以配置为两种工作模式：

- 协议解析模式：串行通信端口上的通信内容由FSU中的协议解析库控制，这种模式为本规范推荐的模式。
- 串口转发模式：串行通信端口上的通信内容由外部其他客户端控制（类似于串口控制）。

概念结构如下：

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.042.png)

1. 串口透传机制示意图

串口转发库的功能是在其他客户端和被监控的其他设备之间建立起透明虚拟通道，实现双向数据转发，串口转发库不对串口通信的数据进行解析与修改，结构如下：

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.043.png)

1. 串口转发库功能示意图

典型B接口数据流为：

B接口服务器→FSU（网络端口）→FSU（协议解析库）→FSU（串行通信端口1）→监控对象1。

典型的串口转发数据流为：

其他客户端→FSU（网络端口）→FSU（串口转发库）→FSU（串行通信端口N）→其他设备。



<a name="_toc228250494"></a>**<a name="_toc23112303"></a>**附录A\
（规范性附录）\
SCService.wsdl 文件内容
==============================================================
<?xml version="1.0" encoding="UTF-8"?>

<wsdl:definitionstargetNamespace="http://SCService.chinatelecom.com" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://SCService.chinatelecom.com" xmlns:intf="http://SCService.chinatelecom.com" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">

<wsdl:message name="invokeRequest">

<wsdl:part name="xmlData" type="soapenc:string"/>

</wsdl:message>

<wsdl:message name="invokeResponse">

<wsdl:part name="invokeReturn" type="soapenc:string"/>

</wsdl:message>

<wsdl:portType name="SCService">

<wsdl:operation name="invoke" parameterOrder="xmlData">

<wsdl:input message="impl:invokeRequest" name="invokeRequest"/>

<wsdl:output message="impl:invokeResponse" name="invokeResponse"/>

</wsdl:operation>

</wsdl:portType>

<wsdl:binding name="SCServiceSoapBinding" type="impl:SCService">

<wsdlsoap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>

<wsdl:operation name="invoke">

<wsdlsoap:operation soapAction=""/>

<wsdl:input name="invokeRequest">

<wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://SCService.chinatelecom.com" use="encoded"/>

</wsdl:input>

<wsdl:output name="invokeResponse">

<wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://SCService.chinatelecom.com" use="encoded"/>

</wsdl:output>

</wsdl:operation>

</wsdl:binding>

<wsdl:service name="SCServiceService">

<wsdl:port binding="impl:SCServiceSoapBinding" name="SCService">

<wsdlsoap:address location="http://127.0.0.1:8080/services/SCService"/>

</wsdl:port>

</wsdl:service>

</wsdl:definitions>


<a name="_toc23112304"></a>附录B\
（规范性附录）\
SUService.wsdl文件内容
==============================
<?xml version="1.0" encoding="UTF-8"?>

<wsdl:definitions targetNamespace="http://SUService.chinatelecom.com" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://SUService.chinatelecom.com" xmlns:intf="http://SUService.chinatelecom.com" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">

<wsdl:message name="invokeRequest">

<wsdl:part name="xmlData" type="soapenc:string"/>

</wsdl:message>

<wsdl:message name="invokeResponse">

<wsdl:part name="invokeReturn" type="soapenc:string"/>

</wsdl:message>

<wsdl:portType name="SUService">

<wsdl:operation name="invoke" parameterOrder="xmlData">

<wsdl:input message="impl:invokeRequest" name="invokeRequest"/>

<wsdl:output message="impl:invokeResponse" name="invokeResponse"/>

</wsdl:operation>

</wsdl:portType>

<wsdl:binding name="SUServiceSoapBinding" type="impl:SUService">

<wsdlsoap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>

<wsdl:operation name="invoke">

<wsdlsoap:operation soapAction=""/>

<wsdl:input name="invokeRequest">

<wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://SUService.chinatelecom.com" use="encoded"/>

</wsdl:input>

<wsdl:output name="invokeResponse">

<wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://SUService.chinatelecom.com" use="encoded"/>

</wsdl:output>

</wsdl:operation>

</wsdl:binding>

<wsdl:service name="SUServiceService">

<wsdl:port binding="impl:SUServiceSoapBinding" name="SUService">

<wsdlsoap:address location="http://127.0.0.1:8080/services/SUService"/>

</wsdl:port>

</wsdl:service>

</wsdl:definitions>


**<a name="_toc23112305"></a>**附录C\
（资料性附录）<a name="_toc17239924"></a><a name="_toc17239993"></a><a name="_toc17239925"></a><a name="_toc17239994"></a><a name="_toc17239926"></a><a name="_toc17239995"></a><a name="_toc17239927"></a><a name="_toc17239996"></a><a name="_toc17239928"></a><a name="_toc17239997"></a><a name="_toc17239929"></a><a name="_toc17239998"></a><a name="_toc17239930"></a><a name="_toc17239999"></a><a name="_toc17239931"></a><a name="_toc17240000"></a><a name="_toc17239932"></a><a name="_toc17240001"></a><a name="_toc17239933"></a><a name="_toc17240002"></a><a name="_toc17239934"></a><a name="_toc17240003"></a><a name="_toc17239935"></a><a name="_toc17240004"></a><a name="_toc17239936"></a><a name="_toc17240005"></a><a name="_toc17239937"></a><a name="_toc17240006"></a><a name="_toc17239938"></a><a name="_toc17240007"></a><a name="_toc17239939"></a><a name="_toc17240008"></a><a name="_toc17190513"></a>\
编码规则指引
==================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================================
B接口协议报文内容涉及以下各种编码，这些编码的规则各省可自行制定，本技术规范样例采用以下编码规则，各省也可参考。

1) SUID：FSU的编码，在整个动环监控系统中保证唯一性。
1) PDeviceID：动力设备编码，6位，动力设备类型编码3位+局站内同设备类型的设备顺序号3位。
1) PSystemID：动力系统编码，6位，动力系统类型编码3位+局站内系统顺序号3位。
1) TcRoomID：机房编码，6位，机房类型编码3位+局站内机房顺序号3位。
1) 广义设备ID （DeviceID）：共7位。第1位是Type码，1-设备，2-系统，3-机房，4-局站或网管系统。后6位为“动力设备编码PDeviceID”（6位）、“动力系统编码PSystemID”（6位）、“机房编码TcRoomID”（6位）三选一，根据Type码来判定后6位编码属于哪种编码。

DeviceID在某个通信局站内保证唯一性。

1) SPID：标准监控点SP的编码。

   表C.1SPID编码规则

|**SPID共12位，具体定义如下：【123456789ABC】**|
| :- |
|1：信号归属类型，1-动力设备，2-动力系统，3-机房，4-局站或网管系统|
|2：信号类型，1-阈值生成信号，2-遥信信号（DI），3-遥测信号（AI），4-遥控信号（DO），5-遥调信号（AO）。|
|<p>3-5：3位，广义设备类型，“动力设备类型编码”（3位）、“动力系统类型编码”（3位）、“机房类型编码”（3位）中三选一，根据SPID首位“信号归属类型”的值来判定。</p><p>6-8：3位，广义设备类型中信号流水号。</p><p>9-B：3位，同类监控信号的顺序号，如整流模块序号、单体电池序号等。</p><p>C-C：1位，预留扩展，暂固定为0。</p>|

某种动力设备或动力系统，其标准监控点集合应由电信各省公司或专业公司在其《动环监控系统标准监测信号全表》（参照《中国电信动环监控系统标准监测信号全表》编制）中明确规定，每个监控点的编码不随安装位置、所归属动力设备的品牌型号而改变。这种编码规则有利于规范监控点，杜绝无效无用监测点，同时也有利于监控集成厂家对监测点进行模板化处理。

监控中心SC应绑定FSU（SUID为其唯一标识）到某个局站，一个局站下可以有一个或多个FSU，但不允许一个FSU绑定多个局站，即FSU不允许监测跨局站的动力设备或机房环境。本技术规范中，监控中心SC利用广义设备ID （DeviceID）与标准监控点编码（SPID）的组合可在一个局站内部定位唯一的监控点SP，所以B接口报文通过SUID、DeviceID和SPID的组合在全系统范围内唯一定位具体监控点SP。

**<a name="_toc23112306"></a>**附录D\
（资料性附录）\
新装FSU流程参考说明
==================================
新安装的FSU，首先必须由现场工程安装人员通过调测工具，在FSU生成初始配置信息，包括以下信息：

1) LOGIN报文所需的SC服务器IP地址、用户名、密码，以及分配给FSU的IP地址、FSUWebService的端口号。
1) LOGIN报文所需的FSU编码，以及FSU的制造商名称、型号、软硬件版本等。
1) FSU接入的标准监控点信息，包括所接入的监控点编码、监控点配置等信息，《中国电信动环监控B接口技术应用指引-标准监测信号全表分册》提供了一套完整的动环监控系统标准监测信号表。这些信息由中国电信省级公司以标准化配置文件方式提供。
1) FSU采集监控对象SO信息所需的配置信息，包括采集端口信息、SO A接口协议解析库等，这些信息由FSU供应商以厂家配置文件方式提供。

FSU在LOGIN\_ACK报文中可以获得SC采集机IP地址、SC采集机Webservice服务端口号、FSU IP地址过滤白名单。

上述过程称为注册流程，在标准化配置文件、厂家配置文件齐全的情况下，FSU可正常工作。FSU在掉电重启、一般故障重启时也采用注册流程。

FSU保存采集到的监测数据，并将监测数据进行标准化等相应处理后，通过B接口协议报文上报监控中心SC。因为监控中心SC会绑定FSU（SUID为唯一标识）到某个局站，一个局站下可以有一个或多个SU，但不允许一个FSU归属多个局站，同时DeviceID+SPID在一个局站内部是唯一的，所以B接口报文通过SUID、广义设备ID （DeviceID）和标准监控点编码SPID的组合来唯一定位具体监控点。

工程施工时，首先在监控中心SC对准备安装的FSU设备进行初始化数据配置（简称建站），包括生成该FSU负责监控的监控点信息配置表（含SUID、DeviceID及SPID信息的《FSU监控点配置方案表》），该表与《中国电信动环监控系统标准监测信号全表》组成FSU的标准化配置文件（也称电信配置文件）。

工程人员通过FSU调测工具生成FSU的其他配置文件，例如动力设备智能接口解码协议、数据采集通道配置等，组成FSU的厂家配置文件。

工程施工阶段，FSU标准化配置文件、厂家配置文件均由FSU现场安装调测人员在FSU上配置，然后进行系统调测。

用于FSU与SC交互传输的标准化配置文件、厂家配置文件要求采用XML格式，详细介绍见后文。

下图是FSU接入SC的详细流程图（含应用软件流程）。

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.044.png)

图D.1 FSU接入SC流程图


<a name="_toc23112307"></a>附录E\
（资料性附录）<a name="_toc17190466"></a>\
标准化配置文件说明
==================================
1. <a name="_toc17139725"></a><a name="_toc17190467"></a>标准化配置文件

本技术指引中，标准化配置文件由3个xml格式文件组成：《标准化监控点全表》、《标准化监控点配置方案全表》，《FSU监控点配置方案表》。

监控点的配置方案包含以下内容：阈值生成信号的告警级别、告警门限值、告警回差设置、告警产生延时设置、告警结束延时设置，DI遥信信号与AI遥测信号的存储周期、变化绝对阀值设置、百分比阀值设置。本规范的监控点配置方案采用以下方式：每个标准监控点有4个配置方案模板，可选择其中一个生效。系统不支持对具体监控点进行个性化配置。

《FSU监控点配置方案表》文件名为SUConfigInstance.xml，这是一个FSU特定监控点的信息及配置模板选项表，SUConfigInstance.xml文件内容格式如下：

<?xml version="1.0" encoding="UTF-8"?>

<SU ID="00-EF-10-A0-22-98">

<DeviceList>

<Device DeviceID="2020001" DeviceName="1#低压配电系统" Description="ABB低压柜系统" DeviceHLType="2">

<Signal SPID="230200050010" SPName="线电压Uab" SPType="3" AlarmMeanings="" NormalMeanings="" Unit="V"OptionID="1"/>

</Device>

<Device DeviceID="1020001" DeviceName="1#低压配电屏" Description="" DeviceHLType="1">

<Signal SPID="130200280010" SPName="线电压Uab" SPType="3" AlarmMeanings="" NormalMeanings="" Unit="V"OptionID="2"/>

</Device>

<Device DeviceID="3120001" DeviceName="天河路335号电信枢纽机房" Description="" DeviceHLType="3">

<Signal SPID="331200010010" SPName="环境温度" SPType="3" AlarmMeanings="" NormalMeanings="" Unit="℃"OptionID="1"/>

</Device>

</DeviceList>

</SU>

《标准化监控点全表》文件名为StdSPDic.xml，文件内容格式如下：

<?xml version="1.0" encoding="UTF-8"?>

<StdSPDic Name="广东电信标准化监控点全表" Version="1.0">

<DeviceTypeList>

<DeviceType TypeID="020" DeviceHLType="2"DeviceTypeName="低压配电系统">

<Signal SPID="230200050010" SPName="线电压Uab" SPType="3" AlarmMeanings="" NormalMeanings="" Unit="V"/>

</DeviceType>

<DeviceType TypeID="020" DeviceHLType="1" DeviceTypeName="低压配电屏">

<Signal SPID="130200280010" SPName="线电压Uab" SPType="3" AlarmMeanings="" NormalMeanings="" Unit="V"/>

</DeviceType>

<DeviceType TypeID="120" DeviceHLType="3" DeviceTypeName="机房">

<Signal SPID="331200010010" SPName="环境温度" SPType="3" AlarmMeanings="" NormalMeanings="" Unit="℃"/>

</DeviceType>

</DeviceTypeList>

</StdSPDic>

《标准化监控点配置方案全表》文件名为StdSPConfigOptionDic.xml，文件内容格式如下：

<?xml version="1.0" encoding="UTF-8"?>

<StdSPConfigOptionDic Name="广东电信标准化监控点配置方案全表" Version="1.0">

<DeviceTypeList>

<DeviceType TypeID="020" DeviceHLType="2"DeviceTypeName="低压配电系统">

<Signal SPID="230200050010" SPName="线电压Uab">

<OptionList>

<Option OptionID="1" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="60" AbsoluteVal="10" RelativeVal="10" Hysteresis=""/>

<Option OptionID="2" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="60" AbsoluteVal="10" RelativeVal="10" Hysteresis=""/>

<Option OptionID="3" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="360" AbsoluteVal="5" RelativeVal="5" Hysteresis=""/>

<Option OptionID="4" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="720" AbsoluteVal="5" RelativeVal="5" Hysteresis=""/>

</OptionList>

`		`</Signal>

</DeviceType>

<DeviceType TypeID="120" DeviceHLType="3"DeviceTypeName="机房">

<Signal SPID="331200010010" SPName="环境温度">

<OptionList>

<Option OptionID="1" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="60" AbsoluteVal="10" RelativeVal="10" Hysteresis="" />

<Option OptionID="2" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="1440" AbsoluteVal="10" RelativeVal="10" Hysteresis="" />

<Option OptionID="3" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="60" AbsoluteVal="5" RelativeVal="5" Hysteresis=""/>

<Option OptionID="4" AlarmLevel="" AlarmThreshold="" StartDelay="" EndDelay="" Period="60" AbsoluteVal="5" RelativeVal="5" Hysteresis="" />

</OptionList>

`		`</Signal>

</DeviceType>

</DeviceTypeList>

</StdSPConfigOptionDic>

1. <a name="_toc17190468"></a>FSU从SC获取标准化配置文件流程图

![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.045.png)

图E.1 FSU从SC获取标准化配置文件流程图

1. <a name="_toc17190469"></a>厂家配置文件核对与下发

SC利用GET\_FACTORYCONFIG报文与FTP可读取SU厂家配置文件，并与保存在SC的档案进行核对，如果发现配置文件不一致，SC应记录日志，并提供手动同步厂家配置功能。

SC根据FSU的相关请求，可以利用SET\_FACTORYCONFIG报文向FSU传送正确的厂家配置文件，覆盖原有的厂家配置文件。
**<a name="_toc17190470"></a><a name="_toc23112308"></a>**附录F\
（资料性附录）\
监测值历史记录
=============================================================
FSU存储的监控点监测值历史记录以文件形式保存，不主动向SC发送。

本规范中，监测值历史记录文件是SC通过FTP的方式，登录到FSU读取。SC采集机可根据运行状况，选择闲时进行监测值历史记录文件采集，读取成功后在FSU删除该文件。FSU自身存储的监测值历史记录文件位置应与上报SC的监测值历史记录文件FTP目录不同。

遥测量（AI）监控点根据其配置方案生成监测值历史记录，有3种模式：存储周期、变化绝对值、变化百分比。

存储周期模式以每天凌晨零点为基准，按照设定的时间间隔（以分钟为单位），周期性的生成该监控点监测值历史记录，每条历史记录包括该监控点本周期内的监测值最大值及其发生时间、监测值最小值及其发生时间、周期结束时的监测值瞬时值及其发生时间。

变化绝对值模式只记录该监控点触发条件满足时的监测值瞬时值、触发条件及其发生时间，触发条件包含在监控点配置方案中，触发规则是相对上个监测值，当前监测值变化绝对值超过配置方案中的设定值即满足触发条件。

变化百分比模式类似变化绝对值模式，也只记录触发条件满足时的该监控点监测值瞬时值、触发条件及其发生时间，但它的触发规则是相对上个监测值，当前监测值变化百分比超过配置方案中的设定值即满足触发条件。

遥信量（DI）如果配置方案中设定了存储周期，也可生成监测值历史记录，存储周期以每天凌晨零点为基准，按照设定的时间间隔（以分钟为单位），周期性的生成历史记录，记录该DI监控点周期结束时的瞬时状态值及其发生时间。

遥控、遥调事件发生后，应生成历史记录，记录该遥控、遥调事件的控制指令、控制参数及发生时间。

FSU应在每天0点30分前生成前一天的监测值历史记录文件，存储在FSU的根目录/History/XXX/YYYYMMDD/，XXX表示SUID，YYYYMMDD为四位年、两位月、两位日，例如20180620，表示这个目录存储的是2018年6月20日这天的监测值历史记录文件。

对于AI类型监控点按照存储周期形成的监测值历史记录，FSU按照历史记录产生的时间顺序存入监测值历史记录文件，文件名格式为HistoryData.xml或HistoryDataXX.xml，XX代表序号，当文件大小超过1M时，需要分成几个文件。

HistoryData.xml和HistoryDataXX.xml文件内容格式如下：

<?xml version="1.0" encoding="UTF-8"?>

<SUID="">

<DeviceList>

<Device DeviceID="" DeviceName="">

<Signal SPID="" MaxVal="" MaxValTime="" MinVal="" MinValTime=""PeriodStart="2016-09-28 00:00:00" PeriodEnd="2016-09-28 08:00:00"/>

<Signal SPID="" MaxVal="" MaxValTime="" MinVal="" MinValTime=""PeriodStart="2016-09-28 00:00:00" PeriodEnd="2016-09-28 08:00:00"/>

</Device>

</DeviceList>

</SU>

对于遥测量发生超设定阈值变化（百分比阈值或变化绝对阈值），或遥信量、阈值2种类型的监控点设置有存储周期，也会生成历史记录，历史记录文件名格式为HisData.xml或HisDataXX.xml，XX代表序号，当文件大小超过1M时，需要分成几个文件。

注意，AI遥测量存储周期结束时记录的监测值瞬时值也记录在HisData.xml。

HisData.xml和HisDataXX.xml文件内容格式如下：

<?xml version="1.0" encoding="UTF-8"?>

<SUID="">

<DeviceList>

<Device DeviceID="" DeviceName="">

<Signal SPID="" TriggerVal="" Meanings="" RecordTime=""/>

<Signal SPID="" TriggerVal="" Meanings="" RecordTime=""/>

</Device>

</DeviceList>

</SU>

当天AI类型监控点按照存储周期形成的监测值历史记录，放置在FSU的/History/XXX/YYYYMMDD/目录下的临时文件中，记录当天零点到当前时间的监测值历史记录，临时文件名格式为TempHistoryData.xml或TempHistoryDataXX.xml，XX代表序号，当文件大小超过1M时，需要分成几个文件。

当天AI类型监控点按照变化绝对值模式、变化百分比模式形成的监测值历史记录，以及DI、DO、AO形成的历史记录，也放置在FSU的/History/XXX/YYYYMMDD/目录下的临时文件中，记录当天零点到当前时间的上述监控量瞬时值历史记录，临时文件名格式为TempHistData.xml或TempHisDataXX.xml，XX代表序号，当文件大小超过1M时，需要分成几个文件。

TempHistoryData.xml和TempHistoryDataXX.xml文件的内容格式与HistoryData.xml相同。TempHistData.xml和TempHisDataXX.xml文件的内容格式与HisData.xml相同。

FSU应能保留7天以上的监测值历史记录文件备查。

**<a name="_toc23112309"></a>**附录G\
（资料性附录）<a name="_toc17190471"></a>\
蓄电池放电曲线
==================================
FSU如能自动判断蓄电池放电，并记录蓄电池放电曲线文件。

1. <a name="_toc17190472"></a>放电记录逻辑说明

1、触发条件：实时监控蓄电池组总电压，以48V蓄电池组为例，当蓄电池组总电压低于51.5V（2V电池电压转换值2.146V）时，FSU开始进入记录准备状态，蓄电池放电记录原始文件第一个点为51V（2V电池电压转换值2.125V）。其他类型蓄电池组触发条件如下：

240V直流系统蓄电池组：触发记录为51.5\*5=257.5V，第一个记录点为51\*5=255V。

UPS系统蓄电池组：按2V单体电池对应的电压进行换算，根据电池串接数量计算出触发记录和第一个点：触发记录2.146V，第一个记录点2.125V。

2、记录频率：蓄电池组电压每变化0.02V记录一个点（电压、电流），电流变化不作为触发记录条件。当充电电压大于53V（2V电池电压转换值2.208V）后，增加存储条件，每1分钟记录1次电压、电流（说明：当电压恒定时，记录电流的变化值）。

3、放电曲线记录结束条件，符合以下条件之一结束：

1）当蓄电池组总电压大于53V（2V电池电压转换值2.208V）且充电电流小于10A时结束记录，充电电流触发值也可以根据蓄电池的容量及传感器精度进行配置，建议按容量的1%以下进行设置。

2）当蓄电池组总电压大于53V（2V电池电压转换值2.208V）后记录时间超过10小时，结束记录，避免电流测量误差造成无法停止记录。

4、数据说明：数字精度，电压保留小数点两位，电流保留小数点1位，电流放电时为负数，充电为正数。

5、每次放电应为一个完整的文件，不能将一次放电过程分为多个文件储存。

1. <a name="_toc17190473"></a>数据记录文件内容格式说明

蓄电池放电曲线数据记录文件存放于FSU的根目录/History/XXX/YYYYMMDD/目录（可自行约定），放电曲线的文件内容格式建议采用XML文件格式，文件名HisBatCurveYYYYMMDDHHMMSS.xml，例如，2018年8月28日14点25分18秒开始的蓄电池放电记录，文件名是HisBatCurve20180828142518.xml。同一组电池，一天内有两次放电，则根据放电开始时间有两个蓄电池放电记录文件。

本规范中电池放电曲线记录电池总电压与放电电流，文件内容格式如下文所述。其中OffsetSeconds表示从上个记录点计算的时间偏移秒数，即本次记录时刻减去上次记录时刻的差值，如样例中第一条记录是起始记录点，时间偏移0秒，第2条跟第1条相比偏移9秒，第3条跟第2条相比11秒。由于相互间有依赖关系，因此要求顺序记录。DeviceList标签，表示可能同一时刻有多条放电曲线。

<?xml version="1.0" encoding="UTF-8"?>

<SU ID="00-EF-10-A0-22-98">

`	`<StartTime>2016-09-28 14:25:18</StartTime>

<DeviceList>

<Device DeviceID="1061001" DeviceName="1#48V蓄电池组">

`	`<Signal SPIDs="130610010010, 130610020010"SPNames="48V蓄电池组电压,48V电池组电流">

<RecordList>

<RecordVoltageVal="51.4"CurrentVal="-51.6"OffsetSeconds="0"/>

<Record VoltageVal="51.1" CurrentVal="-52.1"OffsetSeconds="9"/>

<Record VoltageVal="50.8" CurrentVal="-52.5"OffsetSeconds="11"/>

<Record VoltageVal="50.5" CurrentVal="-53.0"OffsetSeconds="9"/>

</RecordList>

</Signal>

</Device>

</DeviceList>

</SU>
**<a name="_toc17190474"></a><a name="_toc23112310"></a>**附录H\
（资料性附录）\
FSU基本技术要求
=============================================================
1. 满足《中国电信动环监控系统B接口技术规范》的所有技术要求，能够完成对中国电信网上在用的电源空调设备智能口实施通信协议与数据解析。
1. 可根据《标准化配置文件》对采集到的动环监测点信息进行过滤、整理、存储，并依据《标准化配置文件》规定的告警条件产生告警并主动上报SC。当出现未成功上送的告警信息时，应即时按照《中国电信动环监控系统B接口技术规范》的技术要求通过心跳返回信息向SC反馈。
1. 具备监控点监测值历史记录生成功能，历史记录存储能同时支持3种方式：设定存储周期、根据变化绝对阀值、根据变化百分比阀值。历史记录生成的周期、条件应符合《中国电信动环系统标准化监控信号测点全表》的要求，数据格式应按照本技术规范执行。
1. 具备存储所监测信号7天告警信息与监测值历史记录的能力，储存方式根据《中国电信动环系统标准化监控信号测点全表》执行。
1. 具备接收并快速响应SC各类报文命令，并及时返回执行结果或执行相关命令的功能，响应时间≤10秒（从SU接收到命令到返回响应报文或开始对监控对象输出遥控遥调命令的时间）。
1. 具有专用的本地调试接口（如485或RJ45），可进行现场维护操作和参数配置，RJ45端口同样接受IP地址过滤白名单限制。
1. 具有底端逻辑控制功能，可通过FSU的通信接口或DO\AO接口对监控对象下达DO控制命令或AO调整命令。
1. 具备断电后来电重启动且不丢失配置数据和历史数据（已存储的告警信息与监测值历史记录）的功能，重启后已成功上报的告警信息不应再次上报。
1. 具备温度补偿功能的内置硬件时钟电路，FSU断电后应维持内部时钟正确工作时间不少于3天，在正常工作温度范围内，时钟准确度≤1秒/天。
1. 可自行判断蓄电池放电状态及充电结束，并完整保存蓄电池充放电曲线，判断条件、数据文件格式符合本技术指引要求。

# **<a name="_toc23112311"></a>**附件![](Aspose.Words.c58cdfb8-7b09-49f9-ba34-60bcc242838f.046.png):中国电信动环系统标准化监控信号测点全表
