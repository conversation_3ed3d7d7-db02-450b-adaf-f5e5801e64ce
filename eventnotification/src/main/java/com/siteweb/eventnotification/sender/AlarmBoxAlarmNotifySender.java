package com.siteweb.eventnotification.sender;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.eventnotification.dto.*;
import com.siteweb.utility.constans.SystemConfigEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;

/**
 * <AUTHOR> zhou
 * @description AlarmBoxAlarmNotifySender
 * @createTime 2022-04-25 14:47:32
 */
@Service
public class AlarmBoxAlarmNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {

    private final Logger log = LoggerFactory.getLogger(AlarmBoxAlarmNotifySender.class);

    @Autowired
    RestTemplate restTemplate;

    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("告警箱通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -2;
        }
        String notifyGatewayUrl = getNotifyGatewayUrl(SystemConfigEnum.ALARM_BOX_MESSAGE_API_URL, elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getElementId());
        if (CharSequenceUtil.isBlank(notifyGatewayUrl)) {
            log.error("通知网关服务发送告警箱通知URL未配置");
            return -3;
        }
        AlarmBoxMsgRequestDTO alarmBoxMsgRequestDTO = new AlarmBoxMsgRequestDTO();
        alarmBoxMsgRequestDTO.setContent(String.format("EquipmentName:%s,SequenceId:%s,SerialNo:%d,OperationType:%d,EventName:%s,EventSeverityId:%d,StartTime:%s,EndTime:",
                alarmNotifyExecutorDTO.getEquipmentName(), alarmNotifyExecutorDTO.getSequenceId(), alarmNotifyExecutorDTO.getSerialNo(),
                alarmNotifyExecutorDTO.getOperationType(), alarmNotifyExecutorDTO.getEventName(), alarmNotifyExecutorDTO.getEventSeverityId(),
                DateUtil.format(alarmNotifyExecutorDTO.getStartTime(), DatePattern.NORM_DATETIME_PATTERN)));
        alarmBoxMsgRequestDTO.setDelimiter(":,");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<AlarmBoxMsgRequestDTO> httpEntity = new HttpEntity<>(alarmBoxMsgRequestDTO, httpHeaders);
        AlarmBoxMsgResponseDTO alarmBoxMsgResponseDTO = null;
        try {
            alarmBoxMsgResponseDTO = restTemplate.postForObject(notifyGatewayUrl, httpEntity, AlarmBoxMsgResponseDTO.class);
            log.info("发送告警箱通知：{}", alarmBoxMsgRequestDTO.getContent());
        } catch (RestClientException e) {
            log.error("告警箱通知数据发送失败：{} {}", alarmBoxMsgRequestDTO.getContent(), e.getMessage());
        }
        if (alarmBoxMsgResponseDTO != null && alarmBoxMsgResponseDTO.getError_code() == 0) {
            result = 1;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
