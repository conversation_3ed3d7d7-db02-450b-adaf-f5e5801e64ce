<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 https://maven.apache.org/xsd/assembly-1.1.3.xsd">
    <id>release</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <includes>
                <include>${project.build.finalName}.jar</include>
            </includes>
            <outputDirectory>/</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>/</outputDirectory>
        </fileSet>
    </fileSets>
    <dependencySets>
        <dependencySet>
            <outputDirectory>/lib</outputDirectory>
            <includes>
                <include>com.siteweb:tcs-plugin-common</include>
                <include>com.siteweb:tcs-common</include>
                <include>com.siteweb:tcs-hub</include>
                <include>com.siteweb:tsc-cmcc-common</include>
                <include>com.siteweb:stream-core</include>
                <include>com.siteweb:stream-common</include>
                <include>com.siteweb:tcs-middleware-common</include>
            </includes>
        </dependencySet>
    </dependencySets>
</assembly>
