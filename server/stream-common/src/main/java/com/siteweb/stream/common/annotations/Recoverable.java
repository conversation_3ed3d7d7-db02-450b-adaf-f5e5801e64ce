package com.siteweb.stream.common.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于标记 ShapeActor内的字段，表示该字段是可恢复的 <br/>
 * 当Actor出现异常重启后该字段会自动恢复
 *
 * <AUTHOR> (2025-02-17)
 **/
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Recoverable {


}