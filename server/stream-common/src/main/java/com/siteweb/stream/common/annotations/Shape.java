package com.siteweb.stream.common.annotations;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 流引擎Shape处理单元定义
 *
 * <AUTHOR> (2025-02-11)
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Shape {

    /**
     * 图形组件标识符（不可重复的）
     */
    String type();


    /**
     * Shape的Properties I18n 路径前缀
     * @return
     */
    String i18nPrefix() default "streams.shapes";

    /***
     * 是否为过期的图形组件。
     */
    boolean deprecated() default false;
}
