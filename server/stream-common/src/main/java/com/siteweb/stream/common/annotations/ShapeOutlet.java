package com.siteweb.stream.common.annotations;

import java.lang.annotation.*;

/**
 * 定义Shape出口
 *
 * <AUTHOR> (2025-02-12)
 **/
@Repeatable(ShapeOutlets.class)
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ShapeOutlet {

    /***
     * 输出端身份标识符，单个Shape内应保持唯一
     */
    short id();

    /**
     * 定义出口的数据类型
     */
    Class<?> type();

    /**
     * 是否为动态的
     * 标记为动态时可由前端自定义， 如何与前端联动端口数量
     */
    boolean dynamic() default false;


    /**
     * 单个Outlet的最大扇出数量
     */
    int maxFan() default 255;

    /**
     * 代码内注释，没其他用
     */
    String  desc () default "";

}
