package com.siteweb.stream.common.annotations;

import com.siteweb.stream.common.enums.StreamResourceEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName: StreamResourceDescription
 * @descriptions: 流资源描述
 * @author: xsx
 * @date: 2/20/2025 1:23 PM
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface StreamResourceDescription {
    StreamResourceEnum streamResourceType();
}
