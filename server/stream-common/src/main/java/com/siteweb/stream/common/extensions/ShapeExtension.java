package com.siteweb.stream.common.extensions;

import com.siteweb.stream.common.extensions.impl.ShapeExtensionImpl;
import org.apache.pekko.actor.AbstractExtensionId;
import org.apache.pekko.actor.ExtendedActorSystem;
import org.apache.pekko.actor.ExtensionIdProvider;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR> (2025-02-22)
 **/
public class ShapeExtension extends AbstractExtensionId<ShapeExtensionImpl> implements ExtensionIdProvider {
    // This will be the identifier of our CountExtension
    public static final ShapeExtension INSTANCE = new ShapeExtension();

    private ShapeExtension() {
    }

    // The lookup method is required by ExtensionIdProvider,
    // so we return ourselves here, this allows us
    // to configure our extension to be loaded when
    // the ActorSystem starts up
    public ShapeExtension lookup() {
        return ShapeExtension.INSTANCE; // The public static final
    }

    // This method will be called by Akka
    // to instantiate our Extension
    public ShapeExtensionImpl createExtension(ExtendedActorSystem system) {
        var impl = new ShapeExtensionImpl();


        ApplicationContext applicationContext;
//        system.eventStream().subscribe()
//        applicationContext.getAutowireCapableBeanFactory().autowireBean(actor);
        return impl;
    }

}
