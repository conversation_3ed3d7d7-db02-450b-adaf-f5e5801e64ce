package com.siteweb.stream.common.extensions.impl;

import com.siteweb.stream.common.runtime.AbstractShape;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.Extension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (2025-02-22)
 **/
@Slf4j
public class ShapeExtensionImpl implements Extension {
    private final Map<String, AbstractShape> tracerMap = new HashMap<>();


    public void register(String globalInstanceId, AbstractShape shape) {
        tracerMap.put(globalInstanceId, shape);
        log.info("Shape Register, Global ID = {}, Object = {}", globalInstanceId, System.identityHashCode(shape));
    }

    public void unRegister(String globalInstanceId) {
        tracerMap.remove(globalInstanceId);
    }


    public AbstractShape get(String globalInstanceId) {
        return tracerMap.get(globalInstanceId);
    }


    public List<AbstractShape> getGraphAllShape(Long graphId) {
        var prefix = graphId + "@";
       return tracerMap.keySet().stream().filter(e->e.startsWith(prefix)).map(tracerMap::get).toList();
    }

}
