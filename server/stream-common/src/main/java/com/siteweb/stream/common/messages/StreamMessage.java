package com.siteweb.stream.common.messages;

import com.siteweb.tcs.common.ISerializableMessage;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 流消息基类
 */
@Data
public abstract class StreamMessage implements ISerializableMessage {
    public StreamMessage() {
        timestamp = LocalDateTime.now();
        msgId = UUID.randomUUID();
    }

    /**
     * 消息ID
     */
    // @JsonIgnore
    protected UUID msgId;


    /**
     * 消息生成时间
     */
    // @JsonIgnore
    protected LocalDateTime timestamp;

}
