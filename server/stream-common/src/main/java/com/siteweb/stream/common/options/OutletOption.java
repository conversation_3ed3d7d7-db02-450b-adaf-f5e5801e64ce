package com.siteweb.stream.common.options;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (2025-02-18)
 **/
@Data
@NoArgsConstructor
public class OutletOption {

    public OutletOption(int _outletId, int _index) {
        this((short) _outletId, (short) _index);

    }

    public OutletOption(short _outletId, short _index) {
        this.outletId = _outletId;
        this.index = _index;
    }


    /**
     * 所属出口ID
     */
    private short outletId;
    /**
     * 如果是静态出口，则始终为0 <br/>
     * 如果是动态出口，则为生成索引。
     */
    private short index;
}
