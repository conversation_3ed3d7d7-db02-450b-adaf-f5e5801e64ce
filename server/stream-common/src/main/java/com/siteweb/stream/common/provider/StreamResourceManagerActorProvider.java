package com.siteweb.stream.common.provider;

import org.apache.pekko.actor.*;

/**
 * @ClassName: StreamResourceManagerActorProvider
 * @descriptions:
 * @author: xsx
 * @date: 2/23/2025 2:54 PM
 **/
public class StreamResourceManagerActorProvider  extends AbstractExtensionId<StreamResourceManagerActorProvider.Instance> implements ExtensionIdProvider{
    // 单例实例
    private static final StreamResourceManagerActorProvider INSTANCE = new StreamResourceManagerActorProvider();

    @Override
    public Instance createExtension(ExtendedActorSystem system) {
        return new Instance();
    }

    @Override
    public ExtensionId<? extends Extension> lookup() {
        return INSTANCE;
    }

    /**
     * **获取扩展实例**
     * @param system ActorSystem
     * @return Instance 扩展实例
     */
    public static Instance getInstance(ActorSystem system) {
        return INSTANCE.get(system);
    }

    public static class Instance implements Extension {
        private ActorRef streamResourceManagerActorRef;

        public void setStreamResourceManagerActorRef(ActorRef streamResourceManagerActorRef){
            this.streamResourceManagerActorRef = streamResourceManagerActorRef;
        }

        public ActorRef getStreamResourceManagerActorRef(){
            return streamResourceManagerActorRef;
        }
    }
}
