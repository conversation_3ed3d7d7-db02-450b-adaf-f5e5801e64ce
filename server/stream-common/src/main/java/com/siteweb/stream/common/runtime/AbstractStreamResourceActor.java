package com.siteweb.stream.common.runtime;

import com.siteweb.stream.common.runtime.events.StreamResourceLifeCycleRequestEvent;
import com.siteweb.stream.common.stream.StreamResourceDescriptor;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @ClassName: AbstractStreamResourceActor
 * @descriptions:
 * @author: xsx
 * @date: 2/19/2025 6:10 PM
 **/
public abstract class AbstractStreamResourceActor extends AbstractActor implements StreamResource {

    public AbstractStreamResourceActor(StreamResourceDescriptor streamResourceDescriptor){}


    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(StreamResourceLifeCycleRequestEvent.class,this::handleEvent).build();
    }

    private void handleEvent(StreamResourceLifeCycleRequestEvent requestEvent) {
        switch (requestEvent.getOperationType()){
            case DELETE -> this.closeStreamResource();
            case UPDATE -> this.updateStreamResource((StreamResourceDescriptor) requestEvent.getT());
        }
    }

    @Override
    public void close() throws Exception {

    }
}
