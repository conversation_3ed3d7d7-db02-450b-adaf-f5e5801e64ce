package com.siteweb.stream.common.runtime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-03-03)
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnumDescriptor {
    private String key;
    private String desc;
    private List<EnumElement> items = new ArrayList<>();


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EnumElement {
        private String desc;
        private String value;
    }


}




