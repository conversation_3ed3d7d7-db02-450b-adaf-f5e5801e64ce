package com.siteweb.stream.common.runtime;

import com.siteweb.tcs.common.expression.enums.OperationType;
import com.siteweb.stream.common.runtime.events.StreamResourceLifeCycleRequestEvent;
import com.siteweb.stream.common.runtime.events.StreamResourceLifeCycleResponseEvent;
import com.siteweb.stream.common.stream.StreamResourceDescriptor;
import com.siteweb.stream.common.stream.StreamResourceFactory;
import lombok.Getter;
import lombok.Setter;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @ClassName: StreamResourceManagerActor
 * @descriptions:
 * @author: xsx
 * @date: 2/19/2025 6:08 PM
 **/
public class StreamResourceManagerActor extends AbstractActor {

    private Map<Long,StreamResourceHandle> streamResourceIdRefMap = new HashMap<>();

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(StreamResourceLifeCycleRequestEvent.class,this::handleLifeCycleEvent)
                .build();
    }

    private void handleLifeCycleEvent(StreamResourceLifeCycleRequestEvent streamResourceLifeCycleEvent){
        try {
            switch (streamResourceLifeCycleEvent.getOperationType()){
                case CREATE -> handleCreateStreamResource(streamResourceLifeCycleEvent);
                case DELETE -> handleDeleteStreamResource(streamResourceLifeCycleEvent);
                case UPDATE -> handleUpdateStreamResource(streamResourceLifeCycleEvent);
                case QUERY -> handleQueryStreamResource(streamResourceLifeCycleEvent);
            }
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    private void handleCreateStreamResource(StreamResourceLifeCycleRequestEvent<StreamResourceDescriptor> streamResourceLifeCycleEvent){
        StreamResourceDescriptor streamResourceDescriptor = streamResourceLifeCycleEvent.getT();
        //存在就结束掉
        if(streamResourceIdRefMap.containsKey(streamResourceDescriptor.getStreamResourceDescriptorId())){
            return;
        }
        Class<? extends StreamResource> resourceClass = StreamResourceFactory.getInstance().getStreamResourceClassByType(streamResourceDescriptor.getResourceType());
        ActorRef streamResourceActorRef = getContext().actorOf(Props.create(resourceClass, streamResourceDescriptor), streamResourceDescriptor.getStreamResourcePathName());
        StreamResourceHandle resourceHandle = new StreamResourceHandle();
        resourceHandle.setStreamResourceActorRef(streamResourceActorRef);
        streamResourceIdRefMap.put(streamResourceDescriptor.getStreamResourceDescriptorId(),resourceHandle);
    }

    private void handleDeleteStreamResource(StreamResourceLifeCycleRequestEvent<Long> streamResourceLifeCycleEvent){
        Long resourceId = streamResourceLifeCycleEvent.getT();
        StreamResourceLifeCycleResponseEvent responseEvent = new StreamResourceLifeCycleResponseEvent();
        responseEvent.setOperationType(OperationType.DELETE);
        if(streamResourceIdRefMap.containsKey(resourceId)){
            StreamResourceHandle resourceHandle = streamResourceIdRefMap.get(resourceId);
            resourceHandle.decrementRefCount();
            if(resourceHandle.isNotUse()){
                resourceHandle.getStreamResourceActorRef().forward(streamResourceLifeCycleEvent,getContext());
            }
            responseEvent.setIsSuccess(true);
        }else {
            responseEvent.setIsSuccess(false);
        }
        getSender().tell(responseEvent,getSelf());
    }

    private void handleQueryStreamResource(StreamResourceLifeCycleRequestEvent<Long> streamResourceLifeCycleEvent){
        Long streamResourceId = streamResourceLifeCycleEvent.getT();
        StreamResourceLifeCycleResponseEvent responseEvent = new StreamResourceLifeCycleResponseEvent();
        responseEvent.setOperationType(OperationType.QUERY);
        if(streamResourceIdRefMap.containsKey(streamResourceId)){
            StreamResourceHandle resourceHandle = streamResourceIdRefMap.get(streamResourceId);
            responseEvent.setIsSuccess(true);
            responseEvent.setStreamResourceId(streamResourceId);
            responseEvent.setT(resourceHandle.getStreamResourceActorRef());
            resourceHandle.incrementRefCount();
        }else {
            responseEvent.setIsSuccess(false);
            responseEvent.setT("can not found the resource");
        }
        getSender().tell(responseEvent,getSelf());
    }

    private void handleUpdateStreamResource(StreamResourceLifeCycleRequestEvent<StreamResourceDescriptor> streamResourceLifeCycleEvent){
        StreamResourceDescriptor streamResourceDescriptor = streamResourceLifeCycleEvent.getT();
        StreamResourceLifeCycleResponseEvent responseEvent = new StreamResourceLifeCycleResponseEvent();
        responseEvent.setOperationType(OperationType.UPDATE);
        if(streamResourceIdRefMap.containsKey(streamResourceDescriptor.getStreamResourceDescriptorId())){
            StreamResourceHandle resourceHandle = streamResourceIdRefMap.get(streamResourceDescriptor.getStreamResourceDescriptorId());
            resourceHandle.getStreamResourceActorRef().forward(streamResourceDescriptor,getContext());
            responseEvent.setIsSuccess(true);
        }else {
            responseEvent.setIsSuccess(false);
            responseEvent.setT("can not found the resource");
        }
        getSender().tell(responseEvent,getSelf());
    }


    private class StreamResourceHandle{
        @Getter
        @Setter
        private ActorRef streamResourceActorRef;
        private AtomicInteger refCount = new AtomicInteger(0);

        public void incrementRefCount(){
            refCount.incrementAndGet();
        }

        public void decrementRefCount(){
            refCount.decrementAndGet();
        }

        public boolean isNotUse(){
            return refCount.get() == 0;
        }
    }
}
