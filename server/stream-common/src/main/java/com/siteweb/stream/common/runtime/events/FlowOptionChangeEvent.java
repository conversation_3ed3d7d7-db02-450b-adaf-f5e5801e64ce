package com.siteweb.stream.common.runtime.events;

import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.stream.StreamFlowOption;
import com.siteweb.stream.common.stream.StreamShapeOption;
import lombok.Data;

import java.util.Map;

/**
 * @ClassName: FlowOptionChangeEvent
 * @descriptions:
 * @author: xsx
 * @date: 3/20/2025 8:58 AM
 **/
@Data
public class FlowOptionChangeEvent extends StreamMessage {
    /**
     * 流实例id
     */
    private Long streamFlowId;
    /**
     * 流配置参数
     */
    private StreamFlowOption streamFlowOption;
    /**
     * 节点配置参数
     */
    private Map<Long,StreamShapeOption> streamShapeOptionMap;
}
