package com.siteweb.stream.common.stream;

import lombok.Data;

/**
 * 动态出口记录
 *
 * <AUTHOR> (2025-02-13)
 **/
@Data
public class DynamicOutletOption {

    /**
     * 记录所属 dynamic 的 Outlet Id
     */
    private final short outletId;
    /**
     * 实例的索引，生成后不可更改。
     */
    private final short index;
    /**
     * 最终生成的 Outlet实例ID
     * InstanceId = ((outletId & 0xFFFF) << 16) | (index & 0xFFFF);
     */
//    private final int InstanceId;

    /**
     * 出口名称
     */
    private final String name;



    public DynamicOutletOption(int _outletId, int _index) {
        this((short) _outletId, (short) _index);
    }


    public DynamicOutletOption(short _outletId, short _index) {
        this.outletId = _outletId;
        this.index = _index;
        this.name = "";
    }

}
