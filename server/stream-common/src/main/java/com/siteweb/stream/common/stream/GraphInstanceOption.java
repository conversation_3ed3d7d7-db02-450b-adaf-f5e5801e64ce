package com.siteweb.stream.common.stream;

import com.siteweb.stream.common.util.Parameters;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class GraphInstanceOption {

    private long graphId;
    private long graphInstanceId;

    private Parameters parameters;

    private Map<Long,FlowInstanceOption> flowInstanceOptionMap;

    public void setProperty(String name, Object value) {
        parameters.setProperty(name, value);
    }

    public FlowInstanceOption getFlowInstanceOptionById(long streamFlowId) throws Exception {
        containFlowInstance(streamFlowId);
        return flowInstanceOptionMap.get(streamFlowId);
    }

    public Object getOptionByKey(String optionKey){
        if(!parameters.getProperties().containsKey(optionKey)){
            return null;
        }
        return parameters.getProperty(optionKey);
    }

    public boolean containFlowInstance(Long flowInstanceId) throws Exception {
        if(flowInstanceOptionMap.containsKey(flowInstanceId)){
            return true;
        }
        throw new Exception("not found flow instance");
    }

    public Object getFlowOptionByKey(Long flowInstanceId, String optionKey) throws Exception {
        containFlowInstance(flowInstanceId);
        FlowInstanceOption flowInstanceOption = flowInstanceOptionMap.get(flowInstanceId);
        Object propertyValue = flowInstanceOption.getParameters().getProperty(optionKey);
        return propertyValue;
    }

    public Object getNodeOptionByKey(Long flowInstanceId, Long nodeInstanceId, String optionKey) throws Exception {
        containFlowInstance(flowInstanceId);
        FlowInstanceOption flowInstanceOption = flowInstanceOptionMap.get(flowInstanceId);
        Object propertyValue = flowInstanceOption.getNodeOptionByKey(nodeInstanceId,optionKey);
        return propertyValue;
    }
}
