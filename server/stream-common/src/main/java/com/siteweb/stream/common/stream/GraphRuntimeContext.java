package com.siteweb.stream.common.stream;

import com.siteweb.stream.common.enums.RunMode;
import com.siteweb.stream.common.runtime.StreamTraceContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.pekko.actor.ActorRef;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: GraphRuntimeContext
 * @descriptions: 图运行上下文对象
 * @author: xsx
 * @date: 2/14/2025 6:33 PM
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GraphRuntimeContext extends RuntimeContext {

    /**
     * 图唯一ID
     */
    private final Long graphId;

    /**
     * 图的调试跟踪器
     */
    private final StreamTraceContext traceContext;


    private final StreamGraphOption graphOptions;

    /**
     * 运行模式
     */
    private RunMode runMode = RunMode.DEBUG;

    private Map<Object,Object> commonCache = new ConcurrentHashMap<>();

}
