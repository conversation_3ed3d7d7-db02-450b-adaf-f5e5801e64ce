package com.siteweb.stream.common.stream;

import lombok.Data;

import java.util.List;

@Data
public class StreamPluginInfo {
    private long pluginId;
    private String pluginName;
    private String pluginIcon;
    private String pluginPath;
    private String supportDomainTypes;
    private String dependPlugins;
    private String dependMinTCSVersion;
    private String supportLanguages; //TODO : multi language
    private String pluginVersion;
    private String pluginAuthor;
    private String pluginDescription;
    private List<StreamShapeInfo> streamShapeInfos;
}
