package com.siteweb.stream.common.stream;

import cn.hutool.json.JSONObject;
import com.siteweb.stream.common.enums.StreamResourceEnum;
import lombok.Data;

/**
 * @ClassName: StreamResourceDescriptor
 * @descriptions: 流资源描述对象
 * @author: xsx
 * @date: 2/15/2025 10:32 AM
 **/
@Data
public class StreamResourceDescriptor {
    private long streamResourceDescriptorId;
    private String streamResourceName;
    private StreamResourceEnum resourceType;
    //资源配置参数，mysql连接、redis连接、modbus连接
    private JSONObject params;

    public String getStreamResourcePathName(){
        return String.format("%s~%s",resourceType,streamResourceDescriptorId);
    }
}
