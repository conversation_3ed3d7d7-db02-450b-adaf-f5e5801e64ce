package com.siteweb.stream.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR> (2025-02-27)
 **/
public class ClassHierarchySerializer extends JsonSerializer<Class<?>> {

    private final  Class<?> objectType = Object.class;


    @Override
    public void serialize(Class<?> clazz, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 序列化类继承链
        gen.writeStartArray();
        while (clazz != null && !clazz.equals(objectType)) {
            gen.writeString(clazz.getName());
            clazz = clazz.getSuperclass();

        }
        gen.writeEndArray();
    }
}
