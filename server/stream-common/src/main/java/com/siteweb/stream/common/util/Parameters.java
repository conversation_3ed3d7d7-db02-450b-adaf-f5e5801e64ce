package com.siteweb.stream.common.util;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class Parameters {
    private final Map<String, Object> properties = new HashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getProperties() {
        return this.properties;
    }

    @JsonAnySetter
    public void properties(String name, Object value) {
        this.properties.put(name, value);
    }



    /**
     * 检测属性Key是否存在
     * @param key
     * @return
     */
    public Boolean containsKey(String key) {
        return this.properties.containsKey(key);
    }

    /**
     * 获取属性值
     * @param name
     * @return
     */
    public Object getProperty(String name) {
        return this.properties.get(name);
    }

    /**
     * 设置属性值
     * @param name
     * @param value
     */
    public void setProperty(String name, Object value) {
        this.properties.put(name, value);
    }

    /**
     * 获取泛型的属性值，当属性值类型与T不一致时返回null
     * @param name 属性名
     * @param clazz 要转换的类型
     */
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String name, Class<T> clazz) {
        var v = this.properties.get(name);
        if (clazz.isInstance(v)) {
            return (T) v;
        }
        return null;
    }


}
