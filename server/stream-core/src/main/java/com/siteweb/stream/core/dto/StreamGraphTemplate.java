package com.siteweb.stream.core.dto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.stream.core.entity.StreamGraph;
import com.siteweb.stream.core.manager.StreamLibraryManager;
import lombok.Data;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.InvalidParameterException;

/**
 * <AUTHOR> (2025-07-07)
 **/

@Data
public class StreamGraphTemplate {

    /**
     * Json文本模板
     */
    private String jsonTemplate;

    public StreamGraph toStreamGraph(String fsuId) {
        var json = jsonTemplate.replace("{FSU:ID}", fsuId);
        try {
            var objectMapper = StreamLibraryManager.INSTANCE.getObjectMapper();
            return objectMapper.readValue(json, StreamGraph.class);
        } catch (JsonProcessingException e) {
            throw new InvalidParameterException();
        }
    }

    public static StreamGraphTemplate fromPath(Path filepath) throws IOException {
        var template = new StreamGraphTemplate();
        template.setJsonTemplate(Files.readString(filepath, StandardCharsets.UTF_8));
        return template;
    }

    public static StreamGraphTemplate fromInputStream(InputStream stream) throws IOException {
        var template = new StreamGraphTemplate();
        var bytes = stream.readAllBytes();
        template.setJsonTemplate(new String(bytes, StandardCharsets.UTF_8));
        return template;
    }


}
