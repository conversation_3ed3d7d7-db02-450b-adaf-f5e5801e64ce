package com.siteweb.stream.core.dto;

import com.siteweb.stream.common.stream.StreamShapeInfo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> (2025-06-04)
 **/
@Data
public class StreamLibraryDTO {
    private String libraryId;
    private String libraryName;
    private String libraryVersion;
    private String libraryPackage;
    private String libraryProvider;
    private String buildTime;
    private Boolean enable;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private List<StreamShapeInfo> shapes;

}
