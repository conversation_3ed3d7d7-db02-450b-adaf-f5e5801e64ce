package com.siteweb.stream.core.entity;

import com.siteweb.stream.common.stream.StreamFlowOption;
import com.siteweb.stream.common.stream.StreamLink;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: StreamFlowInfo
 * @descriptions: actor模型下流配置类 数据库持久化实体
 * @author: xsx
 * @date: 2/14/2025 10:26 AM
 **/
@Data
public class StreamFlow {

    //流实体id
    private Long streamFlowId;


    //实体流名称
    private String streamFlowName;



    private StreamFlowOption streamFlowOption;


    //实体流节点列表
    private List<StreamNode> nodes;


    //流连线列表
    private List<StreamLink> links;
}
