package com.siteweb.stream.core.manager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.stream.common.stream.GraphInstanceStateProbe;
import com.siteweb.stream.common.stream.StreamGraphOption;
import com.siteweb.stream.core.entity.StreamGraph;
import com.siteweb.stream.core.runtime.StreamGraphInstance;
import org.apache.pekko.actor.ActorContext;

import java.util.HashMap;
import java.util.Map;
/**
 * @ClassName: StreamGraphInstanceManager
 * @descriptions: actor模型
 * @author: xsx
 * @date: 2/14/2025 1:28 PM
 **/
public class StreamGraphInstanceManager {
    //图实例缓存，
    // key -> 图实例的id
    // value -> 图实例源节点的actor ref
    private Map<Long, StreamGraphInstance> streamGraphInstanceMap = new HashMap<>();

    public GraphInstanceStateProbe getStreamGraphInstanceState(long streamGraphInstanceId) {
        return null;
    }

    public StreamGraphInstance getStreamGraphInstance(long streamGraphInstanceId) {
        return null;
    }


    // 静态内部类，只有在调用 getInstance 时才会加载
    private static class SingletonHolder {
        private static final StreamGraphInstanceManager INSTANCE = new StreamGraphInstanceManager();
    }
    // 提供全局访问点
    public static StreamGraphInstanceManager getInstance() {
        return StreamGraphInstanceManager.SingletonHolder.INSTANCE;
    }
    private StreamGraphInstanceManager(){
    }

    /**
     * 创建图
     * @param actorContext
     * @param streamGraph
     * @param streamGraphOption
     */
    public StreamGraphInstance createGraph(ActorContext actorContext, StreamGraph streamGraph, StreamGraphOption streamGraphOption){
        StreamGraphInstance streamGraphInstance = new StreamGraphInstance(actorContext, streamGraph, streamGraphOption);
        streamGraphInstanceMap.put(streamGraph.getStreamGraphId(),streamGraphInstance);
        return streamGraphInstance;
    }

    public boolean graphInstanceExist(Long streamGraphId){
        return streamGraphInstanceMap.containsKey(streamGraphId);
    }

    /**
     * 启动图
     * @param streamGraphId
     */
    public void startGraph(Long streamGraphId){
        if(!streamGraphInstanceMap.containsKey(streamGraphId)){
            return;
        }
        streamGraphInstanceMap.get(streamGraphId).start();
    }

    /**
     * 停止图
     * @param streamGraphId
     * @return
     */
    public boolean stopGraph(Long streamGraphId){
        if(!streamGraphInstanceMap.containsKey(streamGraphId)){
        }
        StreamGraphInstance streamGraphInstance = streamGraphInstanceMap.get(streamGraphId);
        if (ObjectUtil.isNotNull(streamGraphInstance)){
            streamGraphInstance.stop();
        }
        return true;
    }

    /**
     * 销毁图
     * @return
     */
    public boolean destroyGraph(Long streamGraphId){
        if(streamGraphInstanceMap.containsKey(streamGraphId)){
            streamGraphInstanceMap.get(streamGraphId).destroyGraph();
            streamGraphInstanceMap.remove(streamGraphId);
        }
        return true;
    }


    public StreamGraphInstance getInstance(Long streamGraphId){
        return streamGraphInstanceMap.get(streamGraphId);
    }

}