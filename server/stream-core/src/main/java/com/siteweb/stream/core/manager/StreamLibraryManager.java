package com.siteweb.stream.core.manager;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.stream.common.exception.StreamException;
import com.siteweb.stream.common.runtime.EnumDescriptor;
import com.siteweb.stream.common.stream.*;
import com.siteweb.stream.common.util.DynamicOutletUtils;
import com.siteweb.stream.core.dto.StreamLibraryDTO;
import com.siteweb.stream.core.entity.StreamLibrary;
import com.siteweb.stream.core.entity.StreamNode;
import com.siteweb.stream.core.runtime.StreamModuleInstance;
import com.siteweb.stream.core.runtime.StreamShapeInstance;
import com.siteweb.tcs.common.system.ClusterContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流处理库运行时管理器
 *
 * <AUTHOR> (2025-05-27)
 **/
@Slf4j
public class StreamLibraryManager {

    public static StreamLibraryManager INSTANCE = new StreamLibraryManager();

    @Getter
    private ObjectMapper objectMapper = new ObjectMapper();
    /**
     * 已加载的所有StreamModule
     */
    private final Map<String, StreamModuleInstance> streamModuleHashMap = new HashMap<>();

    private final Map<String, StreamShapeDescriptor> streamShapeDescriptors = new HashMap<>();


    /**
     * 从jar包加载StreamLibrary
     *
     * @param streamLibrary jar file
     */
    public void loadStreamLibrary(StreamLibrary streamLibrary) throws Exception {
        var shaCode = fileSha256(streamLibrary.getJarFile());
        if (!shaCode.equals(streamLibrary.getJarCode())) {
            log.error("The Jar file of the graph module[{}] was tampered with and could not be loaded. It has been skipped.", streamLibrary.getLibraryId());
            throw StreamException.STREAM_MODULE_JAR_FILE_HAS_BEEN_TEMPERED.toException();
        }
        var jarLoader = new StreamModuleInstance(streamLibrary);
        if (streamModuleHashMap.containsKey(jarLoader.getLibraryId())) {
            log.warn("The Stream Module has been loaded and cannot be reloaded");
            return;
        }
        jarLoader.load();
        jarLoader.setEnabled(streamLibrary.getEnable());
        streamModuleHashMap.put(jarLoader.getLibraryId(), jarLoader);
        var shapes = jarLoader.getDescriptors().values();
        for (StreamShapeDescriptor descriptor : shapes) {
            if (streamShapeDescriptors.containsKey(descriptor.getType())) {
                log.error("Scan for duplicate Stream Module shapes");
                continue;
            }
            streamShapeDescriptors.put(descriptor.getType(), descriptor);
        }
        refreshObjectMapper();
    }


    public void loadDefaultLibrary() {
        var streamLibrary = new StreamLibrary();
        streamLibrary.setLibraryId("defaults");
        streamLibrary.setLibraryName("Default");
        streamLibrary.setLibraryPackage("com.siteweb.stream");
        streamLibrary.setLibraryVersion("1.0.0");
        streamLibrary.setEnable(true);
        try {
            var jarLoader = new StreamModuleInstance(streamLibrary, ClusterContext.getContext().getClassLoader());
            jarLoader.load();
            var shapes = jarLoader.getDescriptors().values();
            for (StreamShapeDescriptor descriptor : shapes) {
                if (streamShapeDescriptors.containsKey(descriptor.getType())) {
                    log.error("Scan for duplicate Stream Module shapes");
                    continue;
                }
                streamShapeDescriptors.put(descriptor.getType(), descriptor);
            }
            streamModuleHashMap.put(jarLoader.getLibraryId(), jarLoader);
            refreshObjectMapper();
        } catch (Exception e) {
            log.error("默认ShapeLibrary 加载异常");
        }
    }


    public void loadFromPlugin(String pluginId, String pluginName, String packageName, ClassLoader classLoader) {
        var streamLibrary = new StreamLibrary();
        streamLibrary.setLibraryId(pluginId);
        streamLibrary.setLibraryName(pluginName);
        streamLibrary.setLibraryPackage(packageName);
        streamLibrary.setLibraryVersion("1.0.0");
        streamLibrary.setEnable(true);
        try {
            var jarLoader = new StreamModuleInstance(streamLibrary, classLoader);
            jarLoader.load();
            var shapes = jarLoader.getDescriptors().values();
            for (StreamShapeDescriptor descriptor : shapes) {
                if (streamShapeDescriptors.containsKey(descriptor.getType())) {
                    log.error("Scan for duplicate Stream Module shapes");
                    continue;
                }
                streamShapeDescriptors.put(descriptor.getType(), descriptor);
            }
            streamModuleHashMap.put(jarLoader.getLibraryId(), jarLoader);
            refreshObjectMapper();
        } catch (Exception e) {
            log.error("默认ShapeLibrary 加载异常");
        }
    }


    /**
     * 卸载给定的StreamLibrary
     * 必须已经明确解除Module所有类型强引用。
     *
     * @param libraryId
     * @return 是否卸载成功
     */
    public boolean unloadStreamLibrary(String libraryId) {
        var library = this.streamModuleHashMap.get(libraryId);
        if (library == null) return true;
        var shapes = library.getDescriptors().values();
        if (library.unload()) {
            for (StreamShapeDescriptor descriptor : shapes) {
                var ext = streamShapeDescriptors.get(descriptor.getType());
                if (ext != null && ext.getType().equals(descriptor.getType()) && ext.getModuleId().equals(descriptor.getModuleId())) {
                    streamShapeDescriptors.remove(descriptor.getType());
                }
            }
            this.streamModuleHashMap.remove(libraryId);
            return true;
        }
        return false;
    }


    public static String fileSha256(String filePath) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        try (InputStream is = Files.newInputStream(Paths.get(filePath));
             DigestInputStream dis = new DigestInputStream(is, md)) {
            // 自动读取并更新 digest
            byte[] buffer = new byte[8192];
            while (dis.read(buffer) != -1) {
            }
        }
        // 转 16 进制字符串
        StringBuilder sb = new StringBuilder();
        for (byte b : md.digest()) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }


    /**
     * Stream Library 加载/卸载后 刷新ObjectMapper，不然OptionType被引用无法卸载。
     */
    private void refreshObjectMapper() {
        var objectMapper = new ObjectMapper();
        for (var library : streamModuleHashMap.values()) {
            objectMapper.registerModule(library.getTypeModule());
        }
        JacksonTypeHandler.setObjectMapper(objectMapper);
        this.objectMapper = objectMapper;
    }


    /**
     * 根据提供的数据创建 Shape实例
     *
     * @param actorContext actor context()
     * @param node         StreamNode 记录
     * @return
     */
    public StreamShapeInstance createShapeInstance(StreamNode node, ActorContext actorContext, GraphRuntimeContext graphRuntimeContext, FlowRuntimeContext flowRuntimeContext) throws Exception {
        StreamShapeDescriptor descriptor = streamShapeDescriptors.get(node.getShapeType());
        if (descriptor != null) {
            Map<Integer, ShapeOutletInstance> outlets = new HashMap<>();
            List<ActorRef> inlets = new ArrayList<>();
            // build outlet instances
            buildOutlets(descriptor, outlets, node.getOption().getDynamicOutlets());
            var context = new ShapeRuntimeContext(node.getStreamNodeId(), inlets, outlets, new HashMap<>());
            context.setGlobalContext(GlobalRuntimeContext.INSTANCE);
            context.setGraphContext(graphRuntimeContext);
            context.setFlowContext(flowRuntimeContext);
            Props props = Props.create(descriptor.getClazz(), context);
            var actor = actorContext.actorOf(props, descriptor.getClazz().getName() + node.getStreamNodeId());
            var instance = new StreamShapeInstance(actor);
            instance.setContext(context);
            instance.setShapeType(node.getShapeType());
            instance.setInstanceId(node.getStreamNodeId());
            instance.setInstanceName(node.getOption().getName());
            instance.options(node.getOption());
            return instance;
        } else {
            log.error("Stream Shape not found: Shape ID={} when flow shape was created", node.getShapeType());
            throw new Exception(String.format("Stream Shape not found: Shape ID=%s when flow shape was created", node.getShapeType()));
        }
    }


    /**
     * 构建Outlets 实例
     *
     * @param descriptor
     * @param outlets
     */
    private void buildOutlets(StreamShapeDescriptor descriptor, Map<Integer, ShapeOutletInstance> outlets, int[] dynamicOutlets) throws Exception {
        var outletMetas = descriptor.getOutlets();
        // 构建静态Outlets实例
        for (var outlet : descriptor.getOutlets()) {
            if (!outlet.isDynamic()) {
                ShapeOutletInstance outletInstance = new ShapeOutletInstance(outlet.getLetId(), (short) 0);
                outletInstance.setOutDataType(outlet.getDataType());
                outlets.put(outletInstance.getInstanceId(), outletInstance);
            }
        }

        // 构建动态Outlets实例
        if (dynamicOutlets != null) {
            for (int dynamicOutlet : dynamicOutlets) {
                var res = DynamicOutletUtils.split(dynamicOutlet);
                var outletId = res[0];
                var dynamicIndex = res[1];
                var meta = outletMetas.stream().filter(e -> e.isDynamic() && e.getLetId() == outletId).findFirst();
                if (meta.isPresent()) {
                    var outlet = meta.get();
                    ShapeOutletInstance outletInstance = new ShapeOutletInstance(outletId, dynamicIndex);
                    outletInstance.setOutDataType(outlet.getDataType());
                    outlets.put(outletInstance.getInstanceId(), outletInstance);
                } else {
                    log.error("dynamic outlet not found, outletId {}", outletId);
                    throw new Exception(String.format("dynamic outlet not found, outletId = %d", dynamicIndex));
                }
            }
        }

    }


    public Boolean moduleIsLoaded(String libraryId) {
        return streamModuleHashMap.containsKey(libraryId);
    }

    public void enable(String libraryId) {
        var lib = streamModuleHashMap.get(libraryId);
        if (lib != null) lib.setEnabled(true);
    }

    public void disable(String libraryId) {
        var lib = streamModuleHashMap.get(libraryId);
        if (lib != null) lib.setEnabled(false);
    }


    public List<StreamShapeInfo> getLibraryShapes(String libraryId) {
        var library = streamModuleHashMap.get(libraryId);
        if (library == null) return List.of();
        return library.getShapes(null);
    }


    public List<StreamShapeInfo> getShapes(String i18n) {
        var modules = streamModuleHashMap.values();
        List<StreamShapeInfo> result = new ArrayList<>();
        for (var module : modules) {
            var shapes = module.getShapes(i18n);
            result.addAll(shapes);
        }
        return result;
    }

    public List<EnumDescriptor> getEnums(String i18n) {
        var modules = streamModuleHashMap.values();
        List<EnumDescriptor> result = new ArrayList<>();
        for (var module : modules) {
            var enumDescriptors = module.getEnums(i18n);
            result.addAll(enumDescriptors);
        }
        return result;
    }

    public List<StreamLibraryDTO> getLibraryList() {
        var list = new ArrayList<StreamLibraryDTO>();
        var modules = streamModuleHashMap.values();
        for (var module : modules) {
            var mod = new StreamLibraryDTO();


            list.add(mod);
        }
        return list;
    }


}
