package com.siteweb.stream.core.provider;


import com.siteweb.stream.core.entity.StreamGraph;
import com.siteweb.stream.core.mapper.StreamGraphMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class StreamGraphProvider {


    @Autowired
    private StreamGraphMapper streamGraphMapper;

    //
//    public StreamGraph[] getStreamGraphList() {
//        return new StreamGraph[]{
//                southDeviceManagerMocker.MockModbusStreamGraph()
//        };
//    }
    public StreamGraph findGraph(Long streamGraphId) {
        return streamGraphMapper.selectById(streamGraphId);
    }

    public void createGraph(StreamGraph streamGraphRequest) {
        streamGraphMapper.insert(streamGraphRequest);
    }

    public void updateGraph(StreamGraph streamGraph) {
        streamGraphMapper.updateById(streamGraph);
    }

    public void deleteGraph(long streamGraphId) {
        streamGraphMapper.deleteById(streamGraphId);
    }

}
