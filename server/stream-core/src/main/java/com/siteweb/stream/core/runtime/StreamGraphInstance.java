package com.siteweb.stream.core.runtime;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.stream.common.messages.BusinessMessage;
import com.siteweb.stream.common.runtime.events.*;
import com.siteweb.stream.common.stream.GraphRuntimeContext;
import com.siteweb.stream.common.stream.StreamGraphOption;
import com.siteweb.stream.common.util.ActorUtil;
import com.siteweb.stream.core.entity.StreamGraph;
import lombok.Data;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.PoisonPill;
import org.apache.pekko.actor.Props;

import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;

/**
 * @ClassName: StreamGraphInstance
 * @descriptions:
 * @author: xsx
 * @date: 2/14/2025 6:10 PM
 **/
@Data
public class StreamGraphInstance {
    private StreamGraph streamGraphInfo;
    private ActorRef graphActorRef;
    private GraphRuntimeContext graphRuntimeContext;
    private StreamGraphOption streamGraphOption;

    // 用于图上所有Node 的数据跟踪
    private final StreamGraphTracer graphTracer;
    // 图的运行上下文
    private final GraphRuntimeContext context;

    public StreamGraphInstance(ActorContext actorContext, StreamGraph streamGraph,
            StreamGraphOption streamGraphOption) {
        this.streamGraphInfo = streamGraph;
        this.streamGraphOption = streamGraphOption;
        GraphOptionChangeEvent graphOptionChangeEvent = new GraphOptionChangeEvent();
        graphOptionChangeEvent.setStreamGraphId(streamGraph.getStreamGraphId());
        graphOptionChangeEvent.setStreamGraphOption(streamGraph.getGraphOption());
        graphTracer = new StreamGraphTracer(actorContext.system());

        context = new GraphRuntimeContext(streamGraph.getStreamGraphId(), graphTracer, streamGraph.getGraphOption());
        graphActorRef = actorContext.actorOf(Props.create(StreamGraphActor.class, streamGraph, context));
        graphActorRef.tell(graphOptionChangeEvent, ActorRef.noSender());
    }

    public void start() {
        GraphStartEvent graphInitEvent = new GraphStartEvent(streamGraphInfo.getStreamGraphId());
        graphActorRef.tell(graphInitEvent, ActorRef.noSender());
    }

    public void stop() {
        GraphTerminateEvent graphTerminateEvent = new GraphTerminateEvent(streamGraphInfo.getStreamGraphId());
        graphActorRef.tell(graphTerminateEvent, ActorRef.noSender());
    }

    public void restart() {
        stop();
        start();
    }

    public void startFlow(long streamFlowId) {
        FlowStartEvent flowStartEvent = new FlowStartEvent(streamGraphInfo.getStreamGraphId(), streamFlowId);
        graphActorRef.tell(flowStartEvent, ActorRef.noSender());
    }

    public void stopFlow(long streamFlowId) {
        FlowTerminateEvent flowTerminateEvent = new FlowTerminateEvent(streamGraphInfo.getStreamGraphId(),
                streamFlowId);
        graphActorRef.tell(flowTerminateEvent, ActorRef.noSender());
    }

    public void restartFlow(long streamFlowId) {
        stopFlow(streamFlowId);
        startFlow(streamFlowId);
    }

    // 更新图配置参数
    public void updateGraphInstanceOption(GraphOptionChangeEvent streamOptionChangeMessage) {
        this.streamGraphOption = streamOptionChangeMessage.getStreamGraphOption();
        graphActorRef.tell(streamOptionChangeMessage, ActorRef.noSender());
    }

    public void tellBusinessMessage(BusinessMessage businessMessage) {
        if (ObjectUtil.isEmpty(businessMessage))
            return;
        graphActorRef.tell(businessMessage, ActorRef.noSender());
    }

    public <T> T askBusinessMessage(BusinessMessage businessMessage) throws ExecutionException, InterruptedException {
        CompletionStage<Object> completionStage = ActorUtil.askActorAsync(graphActorRef, businessMessage, 1000l);
        Object o = completionStage.toCompletableFuture().get();
        return (T) o;
    }

    /**
     * 销毁图，下面的所有流以及节点都会被销毁
     */
    public void destroyGraph() {
        graphActorRef.tell(PoisonPill.getInstance(), ActorRef.noSender());
    }
}
