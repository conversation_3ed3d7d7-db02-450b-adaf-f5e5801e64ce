package com.siteweb.stream.core.runtime;

import com.siteweb.stream.common.runtime.events.GraphStartEvent;
import com.siteweb.stream.common.runtime.events.GraphTerminateEvent;
import com.siteweb.stream.common.stream.GraphRuntimeContext;
import com.siteweb.stream.core.runtime.commands.CreateGraphCommand;
import com.siteweb.stream.core.runtime.commands.StartFlowCommand;
import com.siteweb.stream.core.runtime.commands.StartGraphCommand;
import com.siteweb.stream.core.runtime.commands.StopGraphCommand;
import com.siteweb.stream.core.runtime.responses.GraphCreatedResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.HashMap;
import java.util.Map;

/**
 * Stream 模块的根 Actor，负责管理所有图 Actor
 * 作为 TCS Actor 系统中 Stream 模块的入口点
 */
@Slf4j
public class StreamRootActor extends AbstractActor {

    // 存储所有图 Actor 的引用
    private final Map<Long, ActorRef> graphActors = new HashMap<>();

    /**
     * 创建 Props
     */
    public static Props props() {
        return Props.create(StreamRootActor.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(CreateGraphCommand.class, this::createGraph)
                .match(StartGraphCommand.class, this::startGraph)
                .match(StopGraphCommand.class, this::stopGraph)
                .match(StartFlowCommand.class, this::startFlow)
                .build();
    }

    /**
     * 创建图 Actor
     */
    private void createGraph(CreateGraphCommand cmd) {
        log.info("Creating graph actor for graph ID: {}", cmd.getStreamGraph().getStreamGraphId());

        // 创建图的运行时上下文
        GraphRuntimeContext context = new GraphRuntimeContext(
                cmd.getStreamGraph().getStreamGraphId(),
                new StreamGraphTracer(getContext().system()), cmd.getStreamGraph().getGraphOption());

        // 创建图 Actor
        ActorRef graphActor = getContext().actorOf(
                Props.create(StreamGraphActor.class, cmd.getStreamGraph(), context),
                "graph-" + cmd.getStreamGraph().getStreamGraphId());

        // 存储图 Actor 引用
        graphActors.put(cmd.getStreamGraph().getStreamGraphId(), graphActor);

        // 回复创建成功
        getSender().tell(
                new GraphCreatedResponse(cmd.getStreamGraph().getStreamGraphId(), graphActor),
                getSelf());
    }

    /**
     * 启动图
     */
    private void startGraph(StartGraphCommand cmd) {
        ActorRef graphActor = graphActors.get(cmd.getGraphId());
        if (graphActor != null) {
            log.info("Starting graph with ID: {}", cmd.getGraphId());
            graphActor.tell(new GraphStartEvent(cmd.getGraphId()), getSelf());
        } else {
            log.warn("Cannot start graph with ID: {}. Graph actor not found.", cmd.getGraphId());
        }
    }

    /**
     * 停止图
     */
    private void stopGraph(StopGraphCommand cmd) {
        ActorRef graphActor = graphActors.get(cmd.getGraphId());
        if (graphActor != null) {
            log.info("Stopping graph with ID: {}", cmd.getGraphId());
            graphActor.tell(new GraphTerminateEvent(cmd.getGraphId()), getSelf());
        } else {
            log.warn("Cannot stop graph with ID: {}. Graph actor not found.", cmd.getGraphId());
        }
    }

    /**
     * 启动流
     */
    private void startFlow(StartFlowCommand cmd) {
        ActorRef graphActor = graphActors.get(cmd.getGraphId());
        if (graphActor != null) {
            log.info("Starting flow with ID: {} in graph: {}", cmd.getFlowId(), cmd.getGraphId());
            graphActor.tell(
                    new com.siteweb.stream.common.runtime.events.FlowStartEvent(cmd.getGraphId(), cmd.getFlowId()),
                    getSelf());
        } else {
            log.warn("Cannot start flow with ID: {} in graph: {}. Graph actor not found.",
                    cmd.getFlowId(), cmd.getGraphId());
        }
    }

    @Override
    public void preStart() {
        log.info("StreamRootActor started");
    }

    @Override
    public void postStop() {
        log.info("StreamRootActor stopped");
    }
}
