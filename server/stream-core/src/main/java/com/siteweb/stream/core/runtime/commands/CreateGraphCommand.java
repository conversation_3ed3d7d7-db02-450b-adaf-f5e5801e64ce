package com.siteweb.stream.core.runtime.commands;

import com.siteweb.stream.common.stream.StreamGraphOption;
import com.siteweb.stream.core.entity.StreamGraph;
import lombok.Data;

/**
 * 创建图命令
 */
@Data
public class CreateGraphCommand {
    private final StreamGraph streamGraph;
    private final StreamGraphOption streamGraphOption;
    
    public CreateGraphCommand(StreamGraph streamGraph, StreamGraphOption streamGraphOption) {
        this.streamGraph = streamGraph;
        this.streamGraphOption = streamGraphOption;
    }
}
