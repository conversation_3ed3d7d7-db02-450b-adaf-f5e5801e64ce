<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.stream.core.mapper.StreamGraphMapper">


    <resultMap id="BaseResultMap" type="com.siteweb.stream.core.entity.StreamGraph">
        <result column="graph_option" property="graphOption"
                typeHandler="com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler"/>
        <result column="flows" property="flows" typeHandler="com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler"/>
    </resultMap>




    <insert id="createStreamGraph" useGeneratedKeys="true" keyColumn="stream_graph_id" keyProperty="streamGraphId" databaseId="postgres">
        INSERT INTO tcs_streams(stream_graph_name, graph_option, flows, create_time, update_time, created_by, updated_by)
        VALUES (
            #{graph.streamGraphName},
            #{graph.graphOption, jdbcType=OTHER, typeHandler=com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler}::json,
            #{graph.flows, jdbcType=OTHER, typeHandler=com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler}::json,
            #{graph.createTime},
            #{graph.updateTime},
            #{graph.createdBy},
            #{graph.updatedBy}
        );
    </insert>



    <insert id="createStreamGraph" useGeneratedKeys="true" keyColumn="stream_graph_id" keyProperty="streamGraphId" databaseId="mysql">
        INSERT INTO tcs_streams(stream_graph_name, graph_option, flows, create_time, update_time, created_by, updated_by)
        VALUES (
            #{graph.streamGraphName},
            #{graph.graphOption},
            #{graph.flows},
            #{graph.createTime},
            #{graph.updateTime},
            #{graph.createdBy},
            #{graph.updatedBy}
        );
    </insert>

    <update id="updateStreamGraph" databaseId="postgres">
        UPDATE public.tcs_streams
        SET stream_graph_name=#{graph.streamGraphName},
        <choose>
            <when test="_databaseId == 'postgres'">
                graph_option=#{graph.graphOption, jdbcType=OTHER, typeHandler=com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler}::json,
                flows=#{graph.flows, jdbcType=OTHER, typeHandler=com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler}::json,
            </when>
            <otherwise>
                graph_option=#{graph.graphOption},
                flows=#{graph.flows},
            </otherwise>
        </choose>
        create_time=#{graph.createTime},
        update_time=#{graph.updateTime},
        created_by=#{graph.createdBy},
        updated_by=#{graph.updatedBy}
        WHERE stream_graph_id=#{graph.streamGraphId}
    </update>


    <select id="findStreamGraph" resultMap="BaseResultMap">
        SELECT stream_graph_id, stream_graph_name, graph_option, flows, create_time, update_time, created_by, updated_by
        FROM tcs_streams WHERE stream_graph_id=#{streamGraphId}
    </select>

</mapper>