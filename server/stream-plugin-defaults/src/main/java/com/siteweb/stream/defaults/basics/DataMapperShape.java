package com.siteweb.stream.defaults.basics;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.common.types.Any;
import com.siteweb.stream.common.types.Inferred;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据映射组件， 将未知的数据结构映射为已知的数据结构
 *
 * <AUTHOR> (2025-02-20)
 **/
@Slf4j
@EditorHidden
@Shape(type = "data-mapper")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeInlet(id = 0x01, type = Any.class)
@ShapeOutlet(id = 0x01, type = Inferred.class)
public class DataMapperShape extends AbstractShape {
    public DataMapperShape(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {



    }
}
