package com.siteweb.stream.defaults.basics;

import com.googlecode.aviator.Expression;
import com.siteweb.stream.common.annotations.*;
import com.siteweb.tcs.common.expression.AssignExpression;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.common.types.Any;
import com.siteweb.stream.common.types.Inferred;
import com.siteweb.tcs.common.util.AviatorHelper;
import com.siteweb.stream.defaults.options.PropertyInjectOption;
import com.siteweb.stream.defaults.options.defaults.PropertyInjectDefaultOption;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 消息的属性赋值，属性注入
 * <AUTHOR> (2025-02-28)
 **/
@Slf4j
@EditorHidden
@Shape(type = "property-inject")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeDefaultOptions(PropertyInjectDefaultOption.class)
@ShapeInlet(id = 0x01, type = Any.class)
@ShapeOutlet(id = 0x01, type = Inferred.class)
public class PropertyInjectShape extends AbstractShape {

    @Recoverable
    private PropertyInjectOption option;

    /**
     * 运行上下文，不需要保留，Actor重启后重新构建
     */
    private final Map<String, Object> evalContext;

    private final List<Expression> expressionList = new ArrayList<>();

    public PropertyInjectShape(ShapeRuntimeContext context) {
        super(context);
        evalContext = context.createEvalContext();
    }

    @Override
    protected final void onOptionReset(StreamShapeOption options) {
        if (options instanceof PropertyInjectOption injectOption) {
            option = injectOption;
            for (AssignExpression exp : option.getAssignExpressions()) {
                var expression = AviatorHelper.compile(exp.toExpression());
                expressionList.add(expression);
            }
        }
    }

    @Override
    protected final void processMessage(StreamMessage message) {
        evalContext.put("msg", message);
        for (Expression exp : expressionList) {
            exp.execute(evalContext);
        }
        context.getOutLet((short) 0x01).broadcast(message);
        evalContext.remove("msg");
    }
}