package com.siteweb.stream.defaults.common;

import com.googlecode.aviator.Expression;
import com.siteweb.stream.common.util.DynamicOutletUtils;
import lombok.Data;

import java.util.Map;

/**
 * Switch 运行时的分支对象
 *
 * <AUTHOR> (2025-02-19)
 **/
@Data
public class SwitchRuntimeBranch {

    /**
     * 字符串表达式
     */
    private String condition;
    /**
     * 分支表达式
     */
    private Expression expression;
    /**
     * 出口id
     */
    private short outletId;
    /**
     * 动态出口索引
     */
    private short index;

    public SwitchRuntimeBranch(String _condition, Expression exp, int outletInstanceId) {
        expression = exp;
        condition = _condition;
        var res = DynamicOutletUtils.split(outletInstanceId);
        outletId = res[0];
        index = res[1];
    }

    /**
     * 匹配分支条件是否成立
     *
     * @param evalContext
     * @return
     */
    public boolean match(Map<String, Object> evalContext) {
        return (Boolean) expression.execute(evalContext);
    }


}
