package com.siteweb.stream.defaults.internal;

import com.siteweb.stream.common.annotations.EditorHidden;
import com.siteweb.stream.common.annotations.Shape;
import com.siteweb.stream.common.annotations.ShapeInlet;
import com.siteweb.stream.common.annotations.ShapeOutlet;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.common.types.Any;
import com.siteweb.stream.common.types.Inferred;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> (2025-03-03)
 **/




@Slf4j
@Shape(type = "internal-flow")
@EditorHidden
@ShapeInlet(id = 0x01, type = Any.class)
@ShapeOutlet(id = 0x01, type = Inferred.class, dynamic = true)
public class FlowShape extends AbstractShape {
    public FlowShape(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {

    }


//    public static StreamNode node(){
//
//    }


}
