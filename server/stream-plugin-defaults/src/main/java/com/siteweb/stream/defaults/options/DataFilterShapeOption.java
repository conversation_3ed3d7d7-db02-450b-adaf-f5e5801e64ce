package com.siteweb.stream.defaults.options;

import com.siteweb.tcs.common.expression.enums.ConditionOperator;
import com.siteweb.tcs.common.expression.ValueExpression;
import com.siteweb.stream.common.stream.StreamShapeOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (2025-02-22)
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class DataFilterShapeOption extends StreamShapeOption {

    /**
     * 属性名
     */
    private ValueExpression property;

    /**
     * 操作符
     */
    private ConditionOperator operator;

    /**
     * 属性
     */
    private ValueExpression expression;



    private boolean  cacheLastMsg;


}
