package com.siteweb.stream.defaults.options;

import com.siteweb.stream.common.stream.StreamShapeOption;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (2025-02-19)
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class TimerShapeOption extends StreamShapeOption {

    /**
     * 首次触发延迟
     */
    private Integer firstDelay;

    /**
     * 触发间隔
     */
    private Integer interval;


    /**
     * 默认状态。是否创建后自动启动
     */
    private boolean defaultState;

}
