package com.siteweb.stream.defaults.options.defaults;

import com.siteweb.tcs.common.expression.enums.ValueScope;
import com.siteweb.tcs.common.expression.ValueExpression;
import com.siteweb.stream.common.stream.AbstractShapeDefaultOption;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.defaults.options.DataTracerShapeOption;
import org.slf4j.event.Level;

/**
 * <AUTHOR> (2025-02-22)
 **/
public class DataTracerDefaultOption implements AbstractShapeDefaultOption {


    @Override
    public StreamShapeOption option() {
        DataTracerShapeOption option = new DataTracerShapeOption();
        option.setProperty(new ValueExpression(ValueScope.MESSAGE, "payload"));
        option.setOutput(DataTracerShapeOption.OutputTarget.DEBUGGER);
        option.setFormat("{}");
        option.setLogLevel(Level.INFO);
        option.setEnabled(true);
        return option;
    }

}
