# ==================================================
# Comment Component
# ==================================================
comment.name=注释
comment.alias=文本注释
comment.groups=通用
comment.tags=注释,注解
# ==================================================
# Switch Component
# ==================================================
data-switch.name=数据条件分支
data-switch.alias=条件分支
data-switch.groups=数据逻辑,数据分支
data-switch.tags=Switch,分支,判断,条件
data-switch.inlet1.name=In
data-switch.outlet1.name=Out-{index}
# ==================================================
# Timer Component
# ==================================================
fixed-timer.name=固定间隔定时器
fixed-timer.alias=定时器
fixed-timer.groups=定时器
fixed-timer.tags=定时器,Time,Timer
fixed-timer.outlet1.name=Out
# ==================================================
# Tracer Component
# ==================================================
data-tracer.name=数据跟踪器
data-tracer.alias=跟踪器
data-tracer.groups=调试
data-tracer.tags=调试,跟踪
data-tracer.inlet1.name=输入
# ==================================================
# DataMapper Component
# ==================================================
data-mapper.name=数据映射
data-mapper.alias=映射
data-mapper.groups=数据转换
data-mapper.tags=Mapper,映射,转换
data-mapper.inlet1.name=输入
data-mapper.outlet1.name=输出
# ==================================================
# DataProcess Component
# ==================================================
data-process.name=数据处理
data-process.alias=处理
data-process.groups=数据
data-process.tags=处理,转换
data-process.inlet1.name=数据输入
data-process.outlet1.name=数据输出
