[{"type": "input", "field": "host", "title": "主机地址", "info": "TCP服务器绑定的网络地址", "value": "0.0.0.0"}, {"type": "inputNumber", "field": "port", "title": "端口", "info": "TCP服务器监听的端口号", "value": 8080, "props": {"min": 1, "max": 65535}}, {"type": "switch", "field": "enableSticky", "title": "启用粘包处理", "info": "是否启用TCP粘包处理功能", "value": true}, {"type": "radio", "field": "stickyStrategy", "title": "粘包处理策略", "info": "选择TCP粘包处理的策略", "value": 1, "options": [{"label": "固定长度", "value": 1}, {"label": "分隔符", "value": 2}, {"label": "自定义长度字段", "value": 3}]}, {"type": "inputNumber", "field": "fixedLength", "title": "固定长度", "info": "固定长度策略的包长度", "value": 1024, "props": {"min": 1}, "show": "${enableSticky && stickyStrategy === 1}"}, {"type": "input", "field": "delimiter", "title": "分隔符", "info": "分隔符策略的分隔符（十六进制字符串，如：0D0A）", "value": "0D0A", "validate": [{"pattern": "^[0-9A-Fa-f]+$", "message": "请输入有效的十六进制字符串"}], "show": "${enableSticky && stickyStrategy === 2}"}, {"type": "inputNumber", "field": "lengthFieldOffset", "title": "长度字段偏移量", "info": "自定义长度字段策略中长度字段的偏移量", "value": 0, "props": {"min": 0}, "show": "${enableSticky && stickyStrategy === 3}"}, {"type": "inputNumber", "field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "长度字段长度", "info": "自定义长度字段策略中长度字段的长度", "value": 4, "props": {"min": 1, "max": 8}, "show": "${enableSticky && stickyStrategy === 3}"}, {"type": "inputNumber", "field": "lengthAdjustment", "title": "长度调整值", "info": "自定义长度字段策略中的长度调整值", "value": 0, "show": "${enableSticky && stickyStrategy === 3}"}, {"type": "inputNumber", "field": "skipBytes", "title": "跳过字节数", "info": "自定义长度字段策略中需要跳过的字节数", "value": 0, "props": {"min": 0}, "show": "${enableSticky && stickyStrategy === 3}"}, {"type": "inputNumber", "field": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "最大帧长度", "info": "TCP数据包的最大长度", "value": 65535, "props": {"min": 1}}, {"type": "switch", "field": "defaultStatus", "title": "默认状态", "info": "是否在创建后自动启动服务器", "value": true}]