package com.siteweb.stream.service.controller;

import com.siteweb.stream.service.service.StreamLibraryService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (2025-02-18)
 **/

@RestController
@RequestMapping("/streams/shapes")
public class ShapesController {

    @Autowired
    private StreamLibraryService streamLibraryService;


    @GetMapping(value = "/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllInfo() {
        return ResponseHelper.successful(streamLibraryService.getShapes("zh-CN"));
    }

    @GetMapping(value = "/enums", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnums() {
        return ResponseHelper.successful(streamLibraryService.getEnums("zh-CN"));
    }


}
