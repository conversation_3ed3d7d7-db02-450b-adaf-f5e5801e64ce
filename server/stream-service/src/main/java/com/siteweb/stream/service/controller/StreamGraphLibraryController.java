package com.siteweb.stream.service.controller;


import com.siteweb.stream.core.dto.StreamLibraryDTO;
import com.siteweb.stream.core.entity.StreamLibrary;
import com.siteweb.stream.service.service.StreamLibraryService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/streams/library")
public class StreamGraphLibraryController {
    private static final Logger logger = LoggerFactory.getLogger(StreamGraphLibraryController.class);
    @Autowired
    private StreamLibraryService streamLibraryService;


    @PostMapping("/upload")
    public ResponseEntity<ResponseResult> uploadLibrary(@RequestParam("file") MultipartFile file) {
        try {
            streamLibraryService.uploadLibrary(file);
            logger.info("Stream Library[{}] loaded successfully.", file.getName());
            return ResponseHelper.successful(true);
        } catch (Exception ex) {
            logger.error("Stream Library[{}] loaded Failed.", file.getName(), ex);
            return ResponseHelper.failed(ex.toString());
        }
    }

    @PutMapping("/unload")
    public ResponseEntity<ResponseResult> unloadLibrary(@RequestParam String libraryId) {
        try {
            streamLibraryService.unloadLibrary(libraryId);
            logger.info("The unloading of the Stream Library[{}] successfully.", libraryId);
            return ResponseHelper.successful(true);
        } catch (Exception ex) {
            logger.error("The unloading of the Stream Library[{}] failed.", libraryId, ex);
            return ResponseHelper.failed(ex.toString());
        }
    }


    @PutMapping("/enable")
    public ResponseEntity<ResponseResult> enableLibrary(@RequestParam String libraryId) {
        try {
            streamLibraryService.enableLibrary(libraryId);
            logger.info("The unloading of the Stream Library[{}] successfully.", libraryId);
            return ResponseHelper.successful(true);
        } catch (Exception ex) {
            logger.error("The unloading of the Stream Library[{}] failed.", libraryId, ex);
            return ResponseHelper.successful(false);
        }
    }

    @PutMapping("/disable")
    public ResponseEntity<ResponseResult> disableLibrary(@RequestParam String libraryId) {
        try {
            streamLibraryService.disableLibrary(libraryId);
            logger.info("The unloading of the Stream Library[{}] successfully.", libraryId);
            return ResponseHelper.successful(true);
        } catch (Exception ex) {
            logger.error("The unloading of the Stream Library[{}] failed.", libraryId, ex);
            return ResponseHelper.successful(false);
        }
    }


    @GetMapping("/list")
    public ResponseEntity<ResponseResult> getAllLibraryInfos() {
        return ResponseHelper.successful(streamLibraryService.getLibraryList());
    }

    @GetMapping("/{libraryId}")
    public ResponseEntity<ResponseResult> getLibraryById(@PathVariable String libraryId) {
        try {
            StreamLibraryDTO libraryInfo = streamLibraryService.getLibraryById(libraryId);
            if (libraryInfo != null) {
                logger.info("Successfully retrieved library info for libraryId: {}", libraryId);
                return ResponseHelper.successful(libraryInfo);
            } else {
                logger.warn("Library not found with libraryId: {}", libraryId);
                return ResponseHelper.failed("Library not found with id: " + libraryId);
            }
        } catch (Exception ex) {
            logger.error("Failed to retrieve library info for libraryId: {}", libraryId, ex);
            return ResponseHelper.failed("Failed to retrieve library: " + ex.getMessage());
        }
    }
//
}
