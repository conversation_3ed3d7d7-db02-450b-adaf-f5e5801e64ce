package com.siteweb.stream.service.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.stream.common.provider.StreamResourceManagerActorProvider;
import com.siteweb.stream.common.runtime.events.StreamResourceLifeCycleRequestEvent;
import com.siteweb.stream.common.stream.StreamResourceDescriptor;
import com.siteweb.tcs.common.expression.enums.OperationType;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: StreamResourceController
 * @descriptions: 流资源控制器
 * @author: xsx
 * @date: 3/20/2025 10:43 AM
 **/
@Slf4j
@RestController
@RequestMapping("/stream-resource")
public class StreamResourceController {

    @PostMapping
    public ResponseEntity<ResponseResult> createStreamResource(@RequestBody StreamResourceDescriptor streamResourceDescriptor) {
        //todo 这里先使用nextLong 后续生产环境要变成雪花算法
        streamResourceDescriptor.setStreamResourceDescriptorId(RandomUtil.randomLong());
        //先存json文件
        File file = FileUtil.file("C:/stream-test/stream-resource.json");
        boolean exist = file.exists();
        if (exist) {
            List<StreamResourceDescriptor> streamResourceDescriptorList = null;
            String dataStr = FileUtil.readString(file, "UTF-8");
            if (StringUtils.isBlank(dataStr)) {
                streamResourceDescriptorList = new ArrayList<>();
                streamResourceDescriptorList.add(streamResourceDescriptor);
            } else {
                streamResourceDescriptorList = JSONUtil.toList(dataStr, StreamResourceDescriptor.class);
                streamResourceDescriptorList.add(streamResourceDescriptor);
            }
            FileUtil.writeString(JSONUtil.toJsonStr(streamResourceDescriptorList), file, "UTF-8");
        } else {
            List<StreamResourceDescriptor> streamResourceDescriptorList = new ArrayList<>();
            streamResourceDescriptorList.add(streamResourceDescriptor);
            FileUtil.writeString(JSONUtil.toJsonStr(streamResourceDescriptorList), file, "UTF-8");
        }
        //通知StreamResourceManager
        // TODO 上下文只能从Actor内部获取 具体创建逻辑需在Actor内部进行
        ActorContext actorContext = null;
        ActorRef resourceManagerActorRef = StreamResourceManagerActorProvider.getInstance(actorContext.system()).getStreamResourceManagerActorRef();
        StreamResourceLifeCycleRequestEvent resourceLifeCycleRequestEvent = new StreamResourceLifeCycleRequestEvent();
        resourceLifeCycleRequestEvent.setOperationType(OperationType.CREATE);
        resourceLifeCycleRequestEvent.setT(streamResourceDescriptor);
        resourceManagerActorRef.tell(resourceLifeCycleRequestEvent, ActorRef.noSender());
        return ResponseHelper.successful();
    }

    @DeleteMapping("/{streamResourceId}")
    public ResponseEntity<ResponseResult> deleteStreamResource(@PathVariable(value = "streamResourceId") Long streamResourceId) {
        //todo 这里先从json里面查找删除
        File file = FileUtil.file("C:/stream-test/stream-resource.json");
        boolean exist = file.exists();
        if (exist) {
            List<StreamResourceDescriptor> streamResourceDescriptorList = null;
            String dataStr = FileUtil.readString(file, "UTF-8");
            if (StringUtils.isNotBlank(dataStr)) {
                streamResourceDescriptorList = JSONUtil.toList(dataStr, StreamResourceDescriptor.class);
                streamResourceDescriptorList.removeIf(e -> streamResourceId.longValue() == e.getStreamResourceDescriptorId());
                FileUtil.writeString(JSONUtil.toJsonStr(streamResourceDescriptorList), file, "UTF-8");
            }
        }
        //通知StreamResourceManager
        StreamResourceLifeCycleRequestEvent resourceLifeCycleRequestEvent = new StreamResourceLifeCycleRequestEvent();

        // TODO 上下文只能从Actor内部获取 具体创建逻辑需在Actor内部进行
        ActorContext actorContext = null;
        ActorRef resourceManagerActorRef = StreamResourceManagerActorProvider.getInstance(actorContext.system()).getStreamResourceManagerActorRef();
        resourceLifeCycleRequestEvent.setOperationType(OperationType.DELETE);
        resourceLifeCycleRequestEvent.setT(streamResourceId);
        resourceManagerActorRef.tell(resourceLifeCycleRequestEvent, ActorRef.noSender());
        return ResponseHelper.successful();
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateStreamResource(@RequestBody StreamResourceDescriptor streamResourceDescriptor) {
        //todo 这里先从json里面查找更新
        File file = FileUtil.file("C:/stream-test/stream-resource.json");
        boolean exist = file.exists();
        if (exist) {
            List<StreamResourceDescriptor> streamResourceDescriptorList = null;
            String dataStr = FileUtil.readString(file, "UTF-8");
            if (StringUtils.isNotBlank(dataStr)) {
                streamResourceDescriptorList = JSONUtil.toList(dataStr, StreamResourceDescriptor.class);
                StreamResourceDescriptor existStreamResourceDescriptor = streamResourceDescriptorList.stream().filter(e -> streamResourceDescriptor.getStreamResourceDescriptorId() == e.getStreamResourceDescriptorId()).findFirst().get();
                BeanUtil.copyProperties(streamResourceDescriptor, existStreamResourceDescriptor);
                FileUtil.writeString(JSONUtil.toJsonStr(streamResourceDescriptorList), file, "UTF-8");
            }
        }
        //通知StreamResourceManager
        return ResponseHelper.successful();
    }
}
