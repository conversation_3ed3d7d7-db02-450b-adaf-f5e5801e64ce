package com.siteweb.stream.service.controller;


import com.siteweb.stream.core.manager.StreamGraphInstanceManager;
import com.siteweb.stream.core.runtime.StreamGraphInstance;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * <AUTHOR> (2025-02-24)
 **/
@RestController
@RequestMapping("/graph-tracer-streams")
public class TracerStreamController {

    @GetMapping(value = "/{graphId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<SseEmitter> getAllInfo(@PathVariable("graphId") Long graphId) throws IOException {
        // TODO 这块未实现
        StreamGraphInstance instance = StreamGraphInstanceManager.getInstance().getInstance(graphId);
        return new ResponseEntity<SseEmitter>(instance.getGraphTracer().connect(), HttpStatus.OK);
    }


}
