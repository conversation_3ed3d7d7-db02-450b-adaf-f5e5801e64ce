package com.siteweb.tcs.backend;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.stream.service.service.StreamLibraryService;
import com.siteweb.tcs.backend.plugin.ThingConnectPluginManager;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.domain.HubDomainHolder;
import com.siteweb.tcs.hub.domain.process.lifecycle.GatewayPipelineProxy;
import com.siteweb.tcs.hub.service.ITcsGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.util.Arrays;

/**
 * 服务启动器，由Backend 同一调度
 *
 * <AUTHOR> (2024-10-28)
 **/
@Slf4j
@Component
public class ServiceRunner implements CommandLineRunner {
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ServiceRunner.class);
    
    @Autowired
    private ThingConnectPluginManager thingConnectPluginManager;

    @Autowired
    private HubDomainHolder hubDomainHolder;

    @Autowired
    private StreamLibraryService streamLibraryService;

    @Autowired
    private ApplicationContext context;

    @Autowired
    private ITcsGatewayService gatewayService;

    /**
     * 此方法在所有容器初始化完成且所有依赖注入完成之后调用
     *
     * @param args incoming main method arguments
     * @throws Exception 由Spring Boot 捕获
     */
    @Override
    public void run(String... args) throws Exception {

        //测试


        // 启动Hub服务
//        hubDomainHolder.start();
        // 加载Stream modules
        streamLibraryService.loadModules();
        // 启动插件
        thingConnectPluginManager.start();
//        printControllers();
    }


    private void printControllers() {
        log.info("===== 扫描到的 Controller 列表 =====");

        String[] allBeanNames = context.getBeanDefinitionNames();
        Arrays.stream(allBeanNames)
                .map(name -> context.getBean(name).getClass())
                .filter(clazz -> clazz.isAnnotationPresent(Controller.class)
                        || clazz.isAnnotationPresent(RestController.class))
                .distinct()
                .forEach(this::printControllerMapping);
    }


    private void printControllerMapping(Class<?> clazz) {
        // 类级别路径
        RequestMapping classMapping = AnnotatedElementUtils.findMergedAnnotation(clazz, RequestMapping.class);
        String[] basePaths = classMapping != null ? classMapping.value() : new String[]{""};

        for (Method method : clazz.getDeclaredMethods()) {
            // 获取方法级别路径
            RequestMapping methodMapping = AnnotatedElementUtils.findMergedAnnotation(method, RequestMapping.class);
            GetMapping get = AnnotatedElementUtils.findMergedAnnotation(method, GetMapping.class);
            PostMapping post = AnnotatedElementUtils.findMergedAnnotation(method, PostMapping.class);
            PutMapping put = AnnotatedElementUtils.findMergedAnnotation(method, PutMapping.class);
            DeleteMapping delete = AnnotatedElementUtils.findMergedAnnotation(method, DeleteMapping.class);

            String httpMethod = "ANY";
            String[] paths = null;

            if (methodMapping != null) {
                paths = methodMapping.value();
                httpMethod = Arrays.toString(methodMapping.method());
            } else if (get != null) {
                paths = get.value();
                httpMethod = "GET";
            } else if (post != null) {
                paths = post.value();
                httpMethod = "POST";
            } else if (put != null) {
                paths = put.value();
                httpMethod = "PUT";
            } else if (delete != null) {
                paths = delete.value();
                httpMethod = "DELETE";
            }

            if (paths != null) {
                for (String base : basePaths) {
                    for (String path : paths) {
                        log.info(String.format("%-8s %-40s %-25s %s",
                                httpMethod,
                                base + path,
                                clazz.getSimpleName(),
                                method.getName()));

                    }
                }
            }
        }
    }

}
