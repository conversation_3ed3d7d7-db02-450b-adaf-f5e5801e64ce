/*
 * Copyright 2014-2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.siteweb.tcs.backend.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.convert.DurationUnit;
import org.springframework.http.CacheControl;
import org.springframework.lang.Nullable;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Data
@ConfigurationProperties("spring.web")
public class ServerProperties {
    private List<String> languages = List.of("en-US", "zh-CN");

    /**
     * Wether the thymeleaf templates should be cached.
     */
    private boolean cacheTemplates = false;

    /**
     * Cache-Http-Header settings.
     */
    private Cache cache = new Cache();


    @Data
    public static class Cache {

        /**
         * include "max-age" directive in Cache-Control http header.
         */
        @Nullable
        @DurationUnit(ChronoUnit.SECONDS)
        private Duration maxAge = Duration.ofSeconds(3600);

        /**
         * include "no-cache" directive in Cache-Control http header.
         */
        private Boolean noCache = true;

        /**
         * include "no-store" directive in Cache-Control http header.
         */
        private Boolean noStore = true;

        public CacheControl toCacheControl() {
            if (Boolean.TRUE.equals(this.noStore)) {
                return CacheControl.noStore();
            }
            if (Boolean.TRUE.equals(this.noCache)) {
                return CacheControl.noCache();
            }
            if (this.maxAge != null) {
                return CacheControl.maxAge(this.maxAge.getSeconds(), TimeUnit.SECONDS);
            }
            return CacheControl.empty();
        }

    }

}
