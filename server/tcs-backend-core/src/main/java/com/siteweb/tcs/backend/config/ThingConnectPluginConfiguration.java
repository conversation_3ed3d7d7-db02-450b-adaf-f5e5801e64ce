package com.siteweb.tcs.backend.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@Data
@ConfigurationProperties(prefix = "spring.plugins")
public class ThingConnectPluginConfiguration {
    private boolean enabled ;
    private String runtimeMode;
    private String path ;
    private String backupPath ;
    private List<String> devPath ;
}