package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.backend.service.ClusterManagementService;
import com.siteweb.tcs.backend.dto.ClusterInfoDto;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 集群管理 REST API 控制器
 *
 * 提供四层结构的集群管理接口：
 * 服务器列表 → 分片类型(shardingnames) → 分片(shardings) → 实体(entities)
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/cluster")
public class ClusterManagementController {

    @Autowired
    private ClusterManagementService clusterManagementService;

    /**
     * 获取集群状态概览
     */
    @GetMapping(value = "/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getClusterStatus() {
        try {
            ClusterInfoDto.ClusterStatus status = clusterManagementService.getClusterStatus();
            return ResponseHelper.successful(status);
        } catch (Exception e) {
            log.error("获取集群状态失败", e);
            return ResponseHelper.failed("获取集群状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取完整的集群四层树状结构 ⭐ 核心接口
     *
     * 四层结构：
     * 1. 服务器列表 (ClusterMember)
     * 2. 分片类型 (ShardTypeInfo) - shardingnames
     * 3. 分片 (ShardInfo) - shardings
     * 4. 实体 (EntityInfo) - entities
     */
    @GetMapping(value = "/tree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getClusterTree() {
        try {
            List<ClusterInfoDto.ClusterMember> members = clusterManagementService.getClusterMembers();
            return ResponseHelper.successful(members);
        } catch (Exception e) {
            log.error("获取集群树状结构失败", e);
            return ResponseHelper.failed("获取集群树状结构失败: " + e.getMessage());
        }
    }

    /**
     * 获取集群成员列表（简化版，不包含分片详情）
     */
    @GetMapping(value = "/members", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getClusterMembersSimple() {
        try {
            List<ClusterInfoDto.ClusterMember> members = clusterManagementService.getClusterMembers();
            // 移除分片详情，只保留基本信息
            members.forEach(member -> member.setShardTypes(null));
            return ResponseHelper.successful(members);
        } catch (Exception e) {
            log.error("获取集群成员列表失败", e);
            return ResponseHelper.failed("获取集群成员列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定成员的分片类型信息 (第二层：shardingnames)
     */
    @GetMapping(value = "/members/{address}/shard-types", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMemberShardTypes(@PathVariable String address) {
        try {
            List<ClusterInfoDto.ShardTypeInfo> shardTypes = clusterManagementService.getMemberShardTypes(address);
            return ResponseHelper.successful(shardTypes);
        } catch (Exception e) {
            log.error("获取成员分片类型信息失败: {}", address, e);
            return ResponseHelper.failed("获取成员分片类型信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有分片类型列表 (shardingnames)
     */
    @GetMapping(value = "/shard-types", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getShardTypes() {
        try {
            List<String> shardTypes = clusterManagementService.getRegisteredShardTypes();
            return ResponseHelper.successful(shardTypes);
        } catch (Exception e) {
            log.error("获取分片类型失败", e);
            return ResponseHelper.failed("获取分片类型失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定分片类型的详细信息 (第三层：shardings)
     * 包含该类型下所有分片和实体的完整信息
     */
    @GetMapping(value = "/shard-types/{typeName}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getShardTypeDetails(@PathVariable String typeName) {
        try {
            // 获取当前节点的指定分片类型信息
            String selfAddress = clusterManagementService.getClusterStatus().getSelfAddress();
            List<ClusterInfoDto.ShardTypeInfo> shardTypes = clusterManagementService.getMemberShardTypes(selfAddress);

            ClusterInfoDto.ShardTypeInfo targetType = shardTypes.stream()
                .filter(st -> st.getTypeName().equals(typeName))
                .findFirst()
                .orElse(null);

            return ResponseHelper.successful(targetType);
        } catch (Exception e) {
            log.error("获取分片类型详细信息失败: {}", typeName, e);
            return ResponseHelper.failed("获取分片类型详细信息失败: " + e.getMessage());
        }
    }


    /**
     * 健康检查接口
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> healthCheck() {
        try {
            ClusterInfoDto.ClusterStatus status = clusterManagementService.getClusterStatus();
            boolean healthy = status.getUpMembers() > 0 && status.getUnreachableMembers() == 0;

            Map<String, Object> healthInfo = Map.of(
                "status", healthy ? "healthy" : "unhealthy",
                "upMembers", status.getUpMembers(),
                "totalMembers", status.getTotalMembers(),
                "unreachableMembers", status.getUnreachableMembers(),
                "leader", status.getLeader() != null ? status.getLeader() : "unknown"
            );
            return ResponseHelper.successful(healthInfo);
        } catch (Exception e) {
            log.error("集群健康检查失败", e);
            return ResponseHelper.failed("集群健康检查失败: " + e.getMessage());
        }
    }
    /**
     * 使服务器 address leave
     */
    @PostMapping(value = "/members/leave", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> leaveMember(@RequestBody String address) {
       clusterManagementService.leaveMember(address);
       return ResponseHelper.successful();
    }
}
