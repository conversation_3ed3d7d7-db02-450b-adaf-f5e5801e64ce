package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.backend.plugin.PluginInfo;
import com.siteweb.tcs.backend.plugin.ThingConnectPluginManager;
import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.dal.dto.RolePermissionMapDTO;
import com.siteweb.tcs.hub.service.PermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.pf4j.PluginState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/permission")
@Api(value = "PermissionController", tags = {"权限操作接口"})
public class PermissionController {

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private ThingConnectPluginManager pluginManager;

    @ApiOperation(value = "获取菜单树(无权限)")
    @GetMapping(value = "/menutree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMenuTree() {
        return ResponseHelper.successful(permissionService.findMenuTree());
    }

    @ApiOperation(value = "根据角色Id获取菜单项")
    @GetMapping(value = "/permmenutreebyroleid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMenuPermissionTreeByRoleId(@RequestParam("roleId") Integer roleId) {
        return ResponseHelper.successful(permissionService.findMenuPermissionTreeById(roleId));
    }

    @ApiOperation(value = "角色授权菜单")
    @PostMapping(value = "/rolemenumap", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createMenuRoleMap(@RequestBody RolePermissionMapDTO rolePermissionMapDTO) {
        int res = permissionService.createMenuPermissionRoleMap(rolePermissionMapDTO, 1);
        if (res > 0)
            return ResponseHelper.successful(res);
        else if (res == -1) {
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, "Require Para Is Null "); // CREATE_OBJECT_ERROR
        } else
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID,"Create MenuRoleMap Error");
    }

    @ApiOperation(value = "角色授权区域")
    @PostMapping(value = "/roleregionmap", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createRegionRoleMap(@RequestBody RolePermissionMapDTO rolePermissionMapDTO) {
        int res = permissionService.createMenuPermissionRoleMap(rolePermissionMapDTO, 2);
        if (res > 0)
            return ResponseHelper.successful(res);
        else if (res == -1) {
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, "Require Para Is Null ");
        } else
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, "Create MenuRoleMap Error");
    }

    @ApiOperation(value = "根据角色Id获取区域项")
    @GetMapping(value = "/permregiontreebyroleid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRegionPermissionTreeByRoleId(@RequestParam("roleId") Integer roleId) {
        return ResponseHelper.successful(permissionService.findRegionPermissionTreeById(roleId));
    }

    @ApiOperation(value = "根据UserId获取菜单树及Path")
    @GetMapping(value = "/menutreebyuserid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMenuTreeByUserId(@RequestParam("userId") Integer userId) {
        List<String> startPluginName = pluginManager.getAllInfo().stream()
                .filter(pluginInfo -> PluginState.STARTED.equals(pluginInfo.getState()))
                .map(PluginInfo::getPluginId)
                .toList();
        return ResponseHelper.successful(permissionService.getMenuTreeByUserId(userId, startPluginName));
    }

    @ApiOperation(value = "根据UserId获取区域树")
    @GetMapping(value = "/regiontreebyuserid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRegionTreeByUserId(@RequestParam(name = "pluginId", required = false) String pluginId) {
        if (pluginId == null || pluginId.isEmpty()) {
            return ResponseHelper.successful(permissionService.getRegionTreeByUserId());
        }
        return ResponseHelper.successful(permissionService.findItemTreeByUserId(pluginId));
    }
}
