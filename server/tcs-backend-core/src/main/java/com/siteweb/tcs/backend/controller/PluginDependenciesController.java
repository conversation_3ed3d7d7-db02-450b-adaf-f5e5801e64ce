package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.backend.entity.TcsPluginDependencies;
import com.siteweb.tcs.backend.service.PluginDependenciesService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> (2024-10-25)
 **/

@RestController
@RequestMapping("/plugin-dependencies")
public class PluginDependenciesController {

    @Autowired
    private PluginDependenciesService pluginDependenciesService;

    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> find(@RequestParam("applicationName") String applicationName, @RequestParam("pluginId") String pluginId) {
        return ResponseHelper.successful(pluginDependenciesService.find(applicationName, pluginId));
    }

    @PostMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> create(@RequestBody TcsPluginDependencies dependencies) {
        if (pluginDependenciesService.create(dependencies) > 0) {
            return ResponseHelper.successful(dependencies);
        } else {
            return ResponseHelper.failed("err");
        }
    }

    @PutMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> put(@RequestBody TcsPluginDependencies dependencies) {
        if (pluginDependenciesService.update(dependencies) > 0) {
            return ResponseHelper.successful(dependencies);
        } else {
            return ResponseHelper.failed("err");
        }
    }

    @DeleteMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delete(@RequestParam("applicationName") String applicationName, @RequestParam("pluginId") String pluginId) {
        if (pluginDependenciesService.deleteById(applicationName,pluginId) > 0) {
            return ResponseHelper.successful();
        } else {
            return ResponseHelper.failed("err");
        }
    }
}
