package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.dal.entity.Region;
import com.siteweb.tcs.hub.service.RegionItemService;
import com.siteweb.tcs.hub.service.RegionService;
import com.siteweb.tcs.hub.service.RegionTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/region")
public class RegionController {

    @Autowired
    private RegionService regionService;

    @Autowired
    private RegionItemService regionItemService;

    @Autowired
    private RegionTreeService regionTreeService;



    /**
     * 查询指定区域
     *
     * @param regionId 采集结构ID
     * <AUTHOR> (2024/6/4)
     */
    @GetMapping(value = "/{regionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> find(@PathVariable("regionId") Integer regionId) {
        return ResponseHelper.successful(regionService.findByRegionId(regionId));
    }

    /**
     * 查询区域列表
     *
     * <AUTHOR> (2024/6/4)
     */
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> list() {
        return ResponseHelper.successful(regionService.findAll());
    }

    /**
     * 查询区域树
     * api/thing/region/tree 仅获取 区域树
     * api/thing/region/tree?pluginId=* 获取区域树并获取树下面挂的所有插件Item
     * api/thing/region/tree?pluginId=south-ctcc-plugin  获取区域树并获取指定插件的Item
     *
     * <AUTHOR> (2024/6/4)
     */
    @GetMapping(value = "/tree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTree(@RequestParam(name = "pluginId", required = false) String pluginId) {
        if (pluginId == null || pluginId.isEmpty()) {
            return ResponseHelper.successful(regionTreeService.findTree());
        }
        return ResponseHelper.successful(regionTreeService.findItemTree(pluginId));
    }


    /**
     * 创建区域
     *
     * @param region 采集结构对象
     * <AUTHOR> (2024/6/4)
     */
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> create(@RequestBody Region region) {
        if (regionService.create(region)) {
            return ResponseHelper.successful(region);
        } else {
            return ResponseHelper.failed("");
        }
    }


    /**
     * 更新区域
     *
     * @param region 采集结构对象
     * <AUTHOR> (2024/6/4)
     */
    @PutMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> put(@RequestBody Region region) {
        if (regionService.update(region)) {
            return ResponseHelper.successful(region);
        } else {
            return ResponseHelper.failed("");
        }
    }


    /**
     * 删除区域
     *
     * @param regionId 采集结构ID
     * <AUTHOR> (2024/6/4)
     */
    @DeleteMapping(value = "/delete/{regionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delete(@PathVariable("regionId") Integer regionId) {
        if (regionService.deleteBy(regionId)) {
            regionItemService.deleteRegionItems(regionId);
            return ResponseHelper.successful();
        } else {
            return ResponseHelper.failed("");
        }
    }

    /**
     * 从siteweb6同步
     */

    @PostMapping(value = "/syncByS6", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> sync() {
        try {
            List<List<Region>> lists = regionService.syncByS6();
            return ResponseHelper.successful(lists);
        } catch (Exception e) {
            return ResponseHelper.failed(e.getMessage());
        }
    }




}
