package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/trace-graph")
public class TraceGraphController {
//
//    //获取图列表，参数插件id和名称
//    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getGraphList(@RequestParam(required = false, defaultValue = "") String pluginId,
//                                                       @RequestParam(required = false, defaultValue = "") String name) {
//        return ResponseHelper.successful(TraceGraphManager.getGraphList(pluginId, name));
//    }
//
//    //根据id获取图json
//    @GetMapping(value = "/json", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getGraphString(@RequestParam String id
//    ) {
//        return ResponseHelper.successful(TraceGraphManager.getGraphString(id));
//    }


}
