package com.siteweb.tcs.backend.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 集群信息数据传输对象
 *
 * <AUTHOR> Assistant
 */
public class ClusterInfoDto {

    /**
     * 集群状态信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ClusterStatus {
        private String clusterName;
        private String selfAddress;
        private String leader;
        private int totalMembers;
        private int upMembers;
        private int joiningMembers;
        private int leavingMembers;
        private int unreachableMembers;
        private boolean isUp;
        private LocalDateTime timestamp;
        private Set<String> roles;
    }

    /**
     * 集群成员信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ClusterMember {
        private String address;
        private String status;
        private Set<String> roles;
        private boolean isLeader;
        private boolean isSelf;
        private String hostname;
        private int port;
        private List<ShardTypeInfo> shardTypes; // 改为分片类型列表
    }

    /**
     * 分片类型信息 - 新增的层级
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShardTypeInfo {
        private String typeName;
        private int totalShards;
        private int totalEntities;
        private List<ShardInfo> shards; // 该类型下的所有分片
    }

    /**
     * 分片信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShardInfo {
        private String typeName;
        private String shardId;
        private int entityCount;
        private String actorPath;
        private List<EntityInfo> entities;
    }

    /**
     * 实体信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EntityInfo {
        private String entityId;
        private String shardId;
        private String actorPath;
    }

}
