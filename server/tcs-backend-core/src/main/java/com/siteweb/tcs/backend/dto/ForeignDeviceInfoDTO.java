package com.siteweb.tcs.backend.dto;

import com.siteweb.tcs.hub.dal.entity.ForeignAlarm;
import com.siteweb.tcs.hub.dal.entity.ForeignControl;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.dal.entity.ForeignSignal;
import com.siteweb.tcs.hub.domain.letter.EquipmentSignalHisData;
import com.siteweb.tcs.hub.domain.letter.RealSignal;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ForeignDeviceInfoDTO {

    public ForeignDeviceInfoDTO(ForeignDevice foreignDevice) {
        this.foreignGatewayID = foreignDevice.getForeignGatewayID();
        this.foreignDeviceID = foreignDevice.getForeignDeviceID();
        this.monitorUnitId = foreignDevice.getMonitorUnitId();
        this.equipmentId = foreignDevice.getEquipmentId();
        this.equipmentTemplateId = foreignDevice.getEquipmentTemplateId();
        this.stationId = foreignDevice.getStationId();
        this.foreignSignalList = foreignDevice.getForeignSignalList();
        this.foreignAlarmList = foreignDevice.getForeignAlarmList();
        this.foreignControlList = foreignDevice.getForeignControlList();
    }

    private String foreignGatewayID;

    private String foreignDeviceID;

    private int monitorUnitId;

    private int equipmentId;

    private Integer equipmentTemplateId;

    private Integer stationId;

    private List<ForeignSignal> foreignSignalList;

    private List<RealSignal> realSignalList;

    private List<ForeignAlarm> foreignAlarmList;

    private List<ForeignControl> foreignControlList;

    private List<EquipmentSignalHisData> foreignHisDataList;

    private Integer state;
}
