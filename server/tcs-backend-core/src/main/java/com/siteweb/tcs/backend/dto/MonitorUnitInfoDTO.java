package com.siteweb.tcs.backend.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class MonitorUnitInfoDTO {
    private String foreignGatewayID;
    private int monitorUnitID;
    private String pluginId;
    private LocalDateTime timeStamp;
    private int connectState;
    private String adpaterPath;
    private String stateStorePath;
    private String spoutPath;
    private List<ForeignDeviceInfoDTO> foreignDeviceList;
}
