package com.siteweb.tcs.backend.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-06-18)
 **/
@Data
@TableName(value = "tcs_plugins")
@AllArgsConstructor
@NoArgsConstructor
public class TcsPlugin {
    //插件唯一ID，从插件文件读取
    @TableId(value = "pluginId", type = IdType.INPUT)
    private String pluginId;
    // 插件名称，只读
    @TableField("pluginName")
    private String pluginName;
    // 当前插件版本(只读)
    @TableField("version")
    private String version;
    // 插件技术支持
    @TableField("provider")
    private String provider;
    // 插件说明
    @TableField("description")
    private String description;
    // 插件类名
    @TableField("className")
    private String className;
    // 插件编译时间
    @TableField("buildTime")
    private String buildTime;
    // 插件文件名
    @TableField("fileName")
    private String fileName;
    // 是否启用
    @TableField("enabled")
    private boolean enabled;

    // 上传时间
    @TableField("uploadJARDate")
    private LocalDateTime uploadJARDate;
    // 更新jar时间
    @TableField("updateJARDate")
    private LocalDateTime updateJARDate;

    // 启用/禁用 时间
    @TableField("changeDate")
    private LocalDateTime changeDate;
    // 操作时间  手动启动/停止
    @TableField("operateDate")
    private LocalDateTime operateDate;

    @TableField("applicationName")
    private String  applicationName;

}
