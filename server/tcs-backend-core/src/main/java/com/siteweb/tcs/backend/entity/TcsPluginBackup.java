package com.siteweb.tcs.backend.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@TableName(value = "tcs_plugins_backup")
@AllArgsConstructor
@NoArgsConstructor
public class TcsPluginBackup {
    //ID
    @TableId(value = "backupId", type = IdType.AUTO)
    private Long backupId;
    //插件ID
    @TableField("pluginId")
    private String pluginId;
    //名称
    @TableField("backupName")
    private String backupName;
    //版本
    @TableField("version")
    private String version;
    //日期
    @TableField("backupDate")
    private LocalDateTime backupDate;
    //大小
    @TableField("backupSize")
    private String backupSize;
    //文件名称
    @TableField("fileName")
    private String fileName;
}
