package com.siteweb.tcs.backend.mapping;


import com.siteweb.tcs.backend.config.WebXController;
import com.siteweb.tcs.backend.utils.PathUtils;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.pattern.PathPattern;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * <AUTHOR> (2024-05-18)
 **/
public class ServletControllerHandlerMapping extends RequestMappingHandlerMapping {

    private final String adminContextPath;

    public ServletControllerHandlerMapping(String adminContextPath) {
        this.adminContextPath = adminContextPath;
    }

    @Override
    protected boolean isHandler(Class<?> beanType) {
        return AnnotatedElementUtils.hasAnnotation(beanType, WebXController.class);
    }

    @Override
    protected void registerHandlerMethod(Object handler, Method method, RequestMappingInfo mapping) {
        super.registerHandlerMethod(handler, method, withPrefix(mapping));
    }

    private RequestMappingInfo withPrefix(RequestMappingInfo mapping) {
        if (!StringUtils.hasText(this.adminContextPath)) {
            return mapping;
        }

        RequestMappingInfo.Builder mutate = mapping.mutate();

        return mutate.paths(withNewPatterns(mapping.getPathPatternsCondition().getPatterns())).build();
    }

    private String[] withNewPatterns(Set<PathPattern> patterns) {
        return patterns.stream()
                .map((pattern) -> PathUtils.normalizePath(this.adminContextPath + pattern.getPatternString()))
                .toArray(String[]::new);
    }

}
