package com.siteweb.tcs.backend.plugin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.tcs.backend.entity.TcsPlugin;
import com.siteweb.tcs.common.runtime.ThingConnectPluginWrapper;
import lombok.Data;
import org.pf4j.PluginState;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-06-07)
 **/
@Data
public class PluginInfo {
    private String pluginId;
    private String pluginName;
    private String version;
    private String provider;
    private String description;
    private String className;
    private String buildTime;
    private String fileName;
    private String location;
    private boolean loaded;
    private PluginState state;
    private boolean enabled;
    private String applicationName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bootTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime downTime;





    public static PluginInfo fromEntity(TcsPlugin wrapper) {
        PluginInfo info = new PluginInfo();
        info.setPluginId(wrapper.getPluginId());
        info.setPluginName(wrapper.getPluginName());
        info.setVersion(wrapper.getVersion());
        info.setProvider(wrapper.getProvider());
        info.setDescription(wrapper.getDescription());
        info.setClassName(wrapper.getClassName());
        info.setBuildTime(wrapper.getBuildTime());
        info.setFileName(wrapper.getFileName());
        info.setEnabled(wrapper.isEnabled());
        info.setUploadDate(wrapper.getUploadJARDate());
        info.setUpdateDate(wrapper.getUpdateJARDate());
        info.setApplicationName(wrapper.getApplicationName());
        return info;
    }


    public PluginInfo copyFrom(ThingConnectPluginWrapper wrapper) {
//        var descriptor =  (ThingConnectPluginDescriptor)wrapper.getDescriptor();
//        this.setPluginName(descriptor.getPluginName());
        this.setLocation(wrapper.getPluginPath().getParent().toString());
        this.setBootTime(wrapper.getBootTime());
        this.setDownTime(wrapper.getDownTime());
        this.setState(wrapper.getPluginState());
        return this;
    }

//
//
//    public static PluginInfo fromWrapper(ThingConnectPluginWrapper wrapper) {
//        PluginInfo info = new PluginInfo();
//        ThingConnectPluginDescriptor descriptor = (ThingConnectPluginDescriptor)wrapper.getDescriptor();
//        info.setPluginId(descriptor.getPluginId());
//        info.setPluginName(descriptor.getPluginName());
//        info.setVersion(descriptor.getVersion());
//        info.setProvider(descriptor.getProvider());
//        info.setDescription(descriptor.getPluginDescription());
//        info.setClassName(descriptor.getPluginClass());
//        info.setBuildTime(descriptor.getBuildTime());
//        info.setLocation("./plugins");
//        info.setFileName("xxx.jar");
//        info.setEnabled(!wrapper.getPluginState().equals(PluginState.DISABLED));
//        info.setUploadDate(LocalDateTime.now());
//        info.setBootTime(wrapper.getBootTime());
//        info.setDownTime(wrapper.getDownTime());
//        info.setState(wrapper.getPluginState());
//        return info;
//    }


}
