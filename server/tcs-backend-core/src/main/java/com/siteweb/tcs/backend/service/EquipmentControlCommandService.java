package com.siteweb.tcs.backend.service;

import com.siteweb.tcs.hub.domain.letter.LiveControlCommand;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.siteweb.tcs.hub.domain.process.LocalEquipmentControlCommandCache.queryAllLiveControlCommand;

@Component
public class EquipmentControlCommandService {
    public List<LiveControlCommand> getAllControlCommands()
    {
        return queryAllLiveControlCommand();
    }
}
