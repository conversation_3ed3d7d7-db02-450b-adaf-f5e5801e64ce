package com.siteweb.tcs.backend.service;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.domain.HubConnectorDataHolder;
import com.siteweb.tcs.hub.domain.letter.GetEquipmentStateAction;
import com.siteweb.tcs.hub.domain.letter.LiveEquipmentState;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.siteweb.tcs.hub.domain.process.LocalEquipmentStateStore.queryAllEquipmentState;
import static com.siteweb.tcs.hub.domain.process.LocalEquipmentStateStore.queryEquipmentState;

@Component
public class EquipmentStateService {

    @Autowired
    private HubConnectorDataHolder hubConnectorDataHolder;

    public List<LiveEquipmentState> getAllEquipmentState()
    {
        return queryAllEquipmentState();
    }

    public List<LiveEquipmentState> getEquipmentState(List<Integer> equipmentIds){
        GetEquipmentStateAction getEquipmentStateAction = new GetEquipmentStateAction();
        getEquipmentStateAction.setEquipmentIds(equipmentIds);
        getEquipmentStateAction.setGetAllData(false);
        return queryEquipmentState(getEquipmentStateAction);
    }

    public void control(Integer monitorUnitId, LifeCycleEventType eventType, List<Integer> equipmentIds){
        if(ObjectUtil.isEmpty(monitorUnitId) || ObjectUtil.isEmpty(equipmentIds))return;
//        HubLifeCycleEvent lifeCycleEvent = new HubLifeCycleEvent();
//        lifeCycleEvent.setEventType(eventType);
//        lifeCycleEvent.setThingType(ThingType.DEVICE);
//        lifeCycleEvent.setMonitorUnitId(monitorUnitId);
//        lifeCycleEvent.setEquipmentIdList(equipmentIds);
//        hubConnectorDataHolder.getGatewayLifeCycleManager().tell(lifeCycleEvent, ActorRef.noSender());
    }
}

