package com.siteweb.tcs.backend.service;

import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.common.o11y.message.ProbeActionRequest;
import com.siteweb.tcs.common.o11y.message.ProbeActionResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorSelection;
import org.apache.pekko.util.Timeout;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 探针服务
 * 负责与Actor通信，获取和设置探针信息
 */
@Slf4j
@Service
public class ProbeService {

    //private final ActorSystem actorSystem;
    private final Timeout timeout = Timeout.create(Duration.ofSeconds(5));

    public ProbeService() {
        //this.actorSystem = PekkoRegistry.getActorSystem();
    }

    /**
     * 根据路径查找Actor
     */
    private ActorSelection findActor(String actorPath) {
        //return actorSystem.actorSelection(actorPath);
        return null;
    }

    /**
     * 发送请求并等待响应
     */
    private ProbeActionResponse sendRequest(ActorSelection actorSelection, ProbeActionRequest request) {
//        try {
//            scala.concurrent.Future<Object> scalaFuture = Patterns.ask(actorSelection, request, timeout);
//            CompletionStage<Object> future = FutureConverters.toJava(scalaFuture);
//            CompletableFuture<Object> completableFuture = future.toCompletableFuture();
//            Object result = completableFuture.get(timeout.duration().toMillis(), TimeUnit.MILLISECONDS);
//
//            if (result instanceof ProbeActionResponse) {
//                return (ProbeActionResponse) result;
//            } else {
//                log.error("收到未知响应类型: {}", result.getClass().getName());
//                return ProbeActionResponse.failure("收到未知响应类型");
//            }
//        } catch (Exception e) {
//            log.error("发送请求失败: {}", e.getMessage());
//            return ProbeActionResponse.failure("发送请求失败: " + e.getMessage());
//        }
        return null;
    }

    /**
     * 获取根Actor探针列表
     * obsolete 遗弃
     */
    public List<ActorProbe> getRootActorProbes() {
        // 实现根据系统获取根Actor的逻辑
        // 这里需要根据实际情况实现
        // TODO: probe默认是hub的诊断工具，如果要获取全部的id，就需要有超越集群的数据集被维护
        // 这违反了集群的管理理念，增加了复杂度，可以通过业务的gatewayID查找probe，所以，此api废弃
        return new ArrayList<>();
    }

    /**
     * 根据路径获取子Actor探针列表
     */
    public List<ActorProbe> getChildActorProbes(String parentPath) {
        // 实现根据父路径获取子Actor的逻辑
        // 这里需要根据实际情况实现
        // todo： 这里也是根据规则生成，不需要去后端访问具体的actor（跨进程，也慢）
        // 同上，这个内容实际是在这里拼字符串
        return new ArrayList<>();
    }

    /**
     * 根据路径获取Actor探针
     * obsolete 遗弃
     */
    public ActorProbe getProbe(String actorPath) {
        // 实现根据路径获取Actor探针的逻辑
        // 这里需要根据实际情况实现
        // todo：未来probe只能通过message访问，所以此方法废弃
        return null;
    }

    /**
     * 根据路径模糊搜索Actor探针
     * obsolete 遗弃
     */
    public List<ActorProbe> getProbesByActorPath(String actorPath) {
        // 实现根据路径模糊搜索的逻辑
        // 这里需要根据实际情况实现
        // todo： 这里也是根据规则生成，不需要去后端访问具体的actor
        return new ArrayList<>();
    }

    /**
     * 根据路径和过滤条件获取Actor探针列表
     */
    public List<ActorProbe> getProbeList(String actorPath, List<String> filter) {
        // 实现根据路径和过滤条件获取Actor探针列表的逻辑
        // 这里需要根据实际情况实现
        // todo： 这里也是根据规则生成，不需要去后端访问具体的actor
        return new ArrayList<>();
    }

    /**
     * 启用/禁用Actor日志
     */
    public boolean enableActorLogByActorPath(String actorPath, boolean enable) {
        ActorSelection actorSelection = findActor(actorPath);
        ProbeActionRequest request = ProbeActionRequest.builder()
                .actionType(ProbeActionRequest.ActionType.ENABLE_ACTOR_LOG)
                .paramValue(String.valueOf(enable))
                .build();

        ProbeActionResponse response = sendRequest(actorSelection, request);
        return response.isSuccess();
    }

    /**
     * 启用/禁用旁路
     */
    public boolean enableBypassByActorPath(String actorPath, boolean enable) {
        ActorSelection actorSelection = findActor(actorPath);
        ProbeActionRequest request = ProbeActionRequest.builder()
                .actionType(ProbeActionRequest.ActionType.ENABLE_BYPASS)
                .paramValue(String.valueOf(enable))
                .build();

        ProbeActionResponse response = sendRequest(actorSelection, request);
        return response.isSuccess();
    }

    /**
     * 启用/禁用调试日志
     */
    public boolean enableDebugLogByActorPath(String actorPath, boolean enable) {
        ActorSelection actorSelection = findActor(actorPath);
        ProbeActionRequest request = ProbeActionRequest.builder()
                .actionType(ProbeActionRequest.ActionType.ENABLE_DEBUG_LOG)
                .paramValue(String.valueOf(enable))
                .build();

        ProbeActionResponse response = sendRequest(actorSelection, request);
        return response.isSuccess();
    }

    /**
     * 获取窗口日志队列内容
     */
    public String getWindowLogsQueueStringContent(String actorPath, String queueName) {
        ActorSelection actorSelection = findActor(actorPath);
        ProbeActionRequest request = ProbeActionRequest.builder()
                .actionType(ProbeActionRequest.ActionType.GET_LOGS)
                .paramName(queueName)
                .build();

        ProbeActionResponse response = sendRequest(actorSelection, request);
        if (response.isSuccess() && response.getData() != null) {
            return response.getData().toString();
        }
        return "";
    }

    /**
     * 设置Actor参数
     */
    public boolean setActorParameters(String actorPath, String parameterName, String value) {
        ActorSelection actorSelection = findActor(actorPath);
        ProbeActionRequest request = ProbeActionRequest.builder()
                .actionType(ProbeActionRequest.ActionType.SET_PARAMETER)
                .paramName(parameterName)
                .paramValue(value)
                .build();

        ProbeActionResponse response = sendRequest(actorSelection, request);
        return response.isSuccess();
    }

    /**
     * 批量启用/禁用Actor日志
     */
    public void batchEnableActorLog(String actorPath, boolean enable) {
        // 实现批量启用/禁用Actor日志的逻辑
        // 这里需要根据实际情况实现
    }

    /**
     * 批量启用/禁用旁路
     */
    public void batchEnableBypass(String actorPath, boolean enable) {
        // 实现批量启用/禁用旁路的逻辑
        // 这里需要根据实际情况实现
    }

    /**
     * 批量启用/禁用调试日志
     */
    public void batchEnableDebugLog(String actorPath, boolean enable) {
        // 实现批量启用/禁用调试日志的逻辑
        // 这里需要根据实际情况实现
    }
}