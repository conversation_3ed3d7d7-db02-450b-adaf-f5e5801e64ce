package com.siteweb.tcs.backend.utils;

import com.siteweb.tcs.backend.plugin.ThingConnectManifestPluginDescriptorFinder;
import com.siteweb.tcs.common.runtime.ThingConnectPluginDescriptor;
import lombok.Data;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> (2024-06-21)
 **/
public class ZipUtils {


    @Data
    public static class JARPlugin {
        private ZipEntry entry;
        private ThingConnectPluginDescriptor descriptor;

        private JARPlugin(ZipEntry entry, ThingConnectPluginDescriptor descriptor) {
            this.entry = entry;
            this.descriptor = descriptor;
        }

    }

    public static JARPlugin findJARPlugin(ZipFile zipFile) throws IOException {
        var jars = ZipUtils.find(zipFile, ".jar");
        for (ZipEntry entry : jars) {
            var jar = zipFile.getInputStream(entry);
            Path jarPath = null;
            try {
                jarPath = ZipUtils.unZipTempFile(jar);
                ThingConnectManifestPluginDescriptorFinder finder = new ThingConnectManifestPluginDescriptorFinder();
                ThingConnectPluginDescriptor descriptor = (ThingConnectPluginDescriptor) finder.find(jarPath);
                if (descriptor.getPluginId() != null) return new JARPlugin(entry, descriptor);
            } finally {
                if (jarPath != null) {
                    if (Files.exists(jarPath)) {
                        jarPath.toFile().delete();
                    }
                }
            }
        }
        return null;
    }


    public static List<? extends ZipEntry> find(ZipFile zipFile, String fileEndsWith) {
        List<ZipEntry> result = new ArrayList<>();
        Enumeration<? extends ZipEntry> entries = zipFile.entries();
        while (entries.hasMoreElements()) {
            ZipEntry entry = entries.nextElement();
            if (entry.getName().endsWith(fileEndsWith)) {
                result.add(entry);
            }
        }
        return result;
    }


    public static void unZip(ZipFile zipFile, Path destDirPath) throws IOException {
        Enumeration<? extends ZipEntry> entries = zipFile.entries();
        while (entries.hasMoreElements()) {
            ZipEntry entry = entries.nextElement();
            Path entryDestination = destDirPath.resolve(entry.getName());
            if (entry.isDirectory()) {
                Files.createDirectories(entryDestination);
            } else {
                Files.createDirectories(entryDestination.getParent());
                try (InputStream in = zipFile.getInputStream(entry);
                     FileOutputStream out = new FileOutputStream(entryDestination.toFile())) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = in.read(buffer)) > 0) {
                        out.write(buffer, 0, len);
                    }
                }
            }
        }
    }


    public static Path unZipTempFile(InputStream jarInputStream) throws IOException {
        // 创建一个临时文件来存储输入流的内容
        File tempFile = File.createTempFile("zip", ".tmp");
        tempFile.deleteOnExit();
        try (FileOutputStream out = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = jarInputStream.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }
        return tempFile.toPath();
    }

    /**
     * 压缩指定的文件和目录到一个ZIP文件
     * @param sourcePaths 要压缩的文件和目录的路径列表
     * @param zipFilePath 目标ZIP文件的路径
     * @throws IOException 如果压缩过程中发生IO错误
     */
    public static void zipFiles(List<Path> sourcePaths, Path zipFilePath) throws IOException {
        Files.createDirectories(zipFilePath.getParent());

        try (FileOutputStream fos = new FileOutputStream(zipFilePath.toFile());
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            for (Path sourcePath : sourcePaths) {
                if (Files.exists(sourcePath)) {
                    if (Files.isDirectory(sourcePath)) {
                        zipDirectory(sourcePath, sourcePath.getFileName().toString(), zos);
                    } else {
                        zipFile(sourcePath, sourcePath.getFileName().toString(), zos);
                    }
                } else {
                    System.out.println("警告：文件或目录不存在，已跳过: " + sourcePath);
                }
            }
        }
    }

    private static void zipDirectory(Path sourceDir, String parentDirectoryName, ZipOutputStream zos) throws IOException {
        // 首先添加目录本身
        String dirZipEntryName = parentDirectoryName + "/";
        zos.putNextEntry(new ZipEntry(dirZipEntryName));
        zos.closeEntry();

        Files.walkFileTree(sourceDir, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                String zipEntryName = parentDirectoryName + "/" + sourceDir.relativize(dir).toString() + "/";
                zos.putNextEntry(new ZipEntry(zipEntryName));
                zos.closeEntry();
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                String zipEntryName = parentDirectoryName + "/" + sourceDir.relativize(file).toString();
                zipFile(file, zipEntryName, zos);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    private static void zipFile(Path filePath, String zipEntryName, ZipOutputStream zos) throws IOException {
        zos.putNextEntry(new ZipEntry(zipEntryName));
        if (Files.exists(filePath) && !Files.isDirectory(filePath)) {
            Files.copy(filePath, zos);
        }
        zos.closeEntry();
    }


}
