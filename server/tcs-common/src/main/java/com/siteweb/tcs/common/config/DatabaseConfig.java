package com.siteweb.tcs.common.config;

import com.siteweb.tcs.common.db.DatabaseDialectAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 数据库配置类
 * 用于根据不同的数据库环境加载相应的配置
 */
@Configuration
public class DatabaseConfig {

    @Value("${spring.profiles.active:mysql}")
    private String activeProfile;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private DatabaseDialectAdapter dialectAdapter;

    /**
     * 创建JdbcTemplate Bean
     *
     * @return JdbcTemplate实例
     */
    @Bean
    @Primary
    public JdbcTemplate jdbcTemplate() {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        
        // 根据不同数据库类型设置特定配置
        DatabaseDialectAdapter.DatabaseType dbType = dialectAdapter.getCurrentDatabaseType();
        switch (dbType) {
            case POSTGRESQL:
            case OPENGAUSS:
                // PostgreSQL和openGauss特定配置
                jdbcTemplate.setFetchSize(1000); // 设置较大的fetch size以提高性能
                break;
            case SQLITE:
                // SQLite特定配置
                jdbcTemplate.setFetchSize(100); // SQLite使用较小的fetch size
                break;
            case H2:
                // H2特定配置
                break;
            case MYSQL:
            default:
                // MySQL特定配置
                jdbcTemplate.setFetchSize(Integer.MIN_VALUE); // MySQL的流式查询设置
                break;
        }
        
        return jdbcTemplate;
    }

    /**
     * 获取当前激活的数据库配置文件名
     *
     * @return 配置文件名
     */
    public String getActiveDatabaseProfile() {
        return activeProfile;
    }

    /**
     * 判断当前是否使用PostgreSQL数据库
     *
     * @return 是否使用PostgreSQL
     */
    public boolean isPostgreSQL() {
        return "postgresql".equalsIgnoreCase(activeProfile);
    }

    /**
     * 判断当前是否使用openGauss数据库
     *
     * @return 是否使用openGauss
     */
    public boolean isOpenGauss() {
        return "opengauss".equalsIgnoreCase(activeProfile);
    }

    /**
     * 判断当前是否使用H2数据库
     *
     * @return 是否使用H2
     */
    public boolean isH2() {
        return "h2".equalsIgnoreCase(activeProfile);
    }

    /**
     * 判断当前是否使用SQLite数据库
     *
     * @return 是否使用SQLite
     */
    public boolean isSQLite() {
        return "sqlite".equalsIgnoreCase(activeProfile);
    }

    /**
     * 判断当前是否使用MySQL数据库
     *
     * @return 是否使用MySQL
     */
    public boolean isMySQL() {
        return "mysql".equalsIgnoreCase(activeProfile) || 
               (!isPostgreSQL() && !isOpenGauss() && !isH2() && !isSQLite()); // 默认为MySQL
    }
    @Bean
    @Primary
    public PlatformTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource);
    }
}