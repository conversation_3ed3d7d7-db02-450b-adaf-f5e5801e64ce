package com.siteweb.tcs.common.config;

import com.siteweb.tcs.common.runtime.PluginI18nMessageSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;


@Configuration
public class MessageSourceConfig {


    @Value("${spring.web.locale:zh_CN}")
    public String locale;


    @Bean
    public MessageSource messageSource() throws IOException {
        return new PluginI18nMessageSource(this.getClass().getClassLoader());
    }
}