package com.siteweb.tcs.common.config;

import com.siteweb.tcs.common.db.DatabaseDialectAdapter;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * 配置文件处理类
 * 用于确保通过命令行参数设置的配置文件能够被正确识别
 */
@Configuration
public class ProfileConfig {

    @Autowired
    private Environment environment;
    
    @Autowired
    private DatabaseDialectAdapter dialectAdapter;
    
    @Value("${spring.profiles.active:mysql}")
    private String activeProfile;
    
    /**
     * 初始化方法，在Bean创建后执行
     * 确保命令行参数中的配置文件设置能够被正确识别
     */
    @PostConstruct
    public void init() {
        // 获取当前激活的配置文件
        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length > 0) {
            // 如果有通过命令行参数设置的配置文件，则使用第一个
            dialectAdapter.setActiveProfile(activeProfiles[0]);
        } else {
            // 否则使用默认配置
            dialectAdapter.setActiveProfile(activeProfile);
        }
    }
}