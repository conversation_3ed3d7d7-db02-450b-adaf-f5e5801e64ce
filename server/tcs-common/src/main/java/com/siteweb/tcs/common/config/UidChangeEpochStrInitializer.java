package com.siteweb.tcs.common.config;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: UidChangeEpochStrInitializer
 * @descriptions: Uid起始时间配置修改，保证每次重启获取到的初始时间是当前时间
 * 这样可以保证只要重启一次，id生成可以再撑34年，搭配workId每次重启都会修改保证理论可以永久生成
 * @author: xsx
 * @date: 12/9/2024 2:42 PM
 **/
public class UidChangeEpochStrInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        ConfigurableEnvironment environment = applicationContext.getEnvironment();
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 格式化日期
        String formattedDate = now.format(formatter);

        // 修改配置文件内容
        Map<String, Object> customProperties = new HashMap<>();
        customProperties.put("uid.epochStr", formattedDate);

        // 将自定义属性添加到环境变量
        environment.getPropertySources().addFirst(new MapPropertySource("customProperties", customProperties));
    }
}
