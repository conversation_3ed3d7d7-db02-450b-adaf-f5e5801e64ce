package com.siteweb.tcs.common.db;

import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用SQL迁移实现类
 * 结合Flyway的迁移管理功能和自定义SQL转换能力
 */
public abstract class CommonSqlMigration extends BaseJavaMigration {

    private static final Logger logger = LoggerFactory.getLogger(CommonSqlMigration.class);

    @Autowired
    private DatabaseDialectAdapter dialectAdapter;

    /**
     * 执行迁移
     *
     * @param context Flyway迁移上下文
     * @throws Exception 迁移过程中的异常
     */
    @Override
    public void migrate(Context context) throws Exception {
        Connection connection = context.getConnection();
        // 使用Spring的JdbcTemplate简化数据库操作
        JdbcTemplate jdbcTemplate = new JdbcTemplate(new SingleConnectionDataSource(connection, true));

        try {
            // 获取需要执行的SQL语句列表
            List<String> sqlStatements = getSqlStatements();

            // 转换SQL语句
            List<String> convertedSqlStatements = convertSqlStatements(sqlStatements);

            // 执行转换后的SQL语句
            for (String sql : convertedSqlStatements) {
                logger.debug("执行SQL: {}\n", sql);
                jdbcTemplate.execute(sql);
            }

            logger.info("迁移脚本 {} 执行成功", getDescription());
        } catch (Exception e) {
            logger.error("迁移脚本 {} 执行失败: {}", getDescription(), e.getMessage());
            throw e;
        }
    }

    /**
     * 获取需要执行的SQL语句列表
     * 子类需要实现此方法提供具体的SQL语句
     *
     * @return SQL语句列表
     */
    protected abstract List<String> getSqlStatements();

    /**
     * 转换SQL语句
     * 使用DatabaseDialectAdapter进行SQL方言转换
     *
     * @param originalSqlStatements 原始SQL语句列表
     * @return 转换后的SQL语句列表
     */
    protected List<String> convertSqlStatements(List<String> originalSqlStatements) {
        if (dialectAdapter == null) {
            logger.warn("DatabaseDialectAdapter未注入，SQL语句将不会进行方言转换");
            return originalSqlStatements;
        }

        // 分析SQL语句判断源数据库类型
        String detectedSourceType = detectSourceDatabaseType(originalSqlStatements);
        dialectAdapter.setSourceType(detectedSourceType);
        logger.debug("检测到源数据库类型: {}", detectedSourceType);

        List<String> convertedStatements = new ArrayList<>();
        for (String sql : originalSqlStatements) {
            try {
                // 检查源数据库和目标数据库是否相同类型
                if (dialectAdapter.isSameSourceAndTargetType()) {
                    convertedStatements.add(sql);
                    logger.debug("源数据库和目标数据库类型相同，跳过转换: {}", sql);
                } else {
                    String convertedSql = dialectAdapter.convert(sql);
                    convertedStatements.add(convertedSql);
                    logger.debug("SQL转换成功: {} -> {}", sql, convertedSql);
                }
            } catch (UnsupportedOperationException e) {
                logger.error("SQL转换不支持: {}, 错误: {}", sql, e.getMessage());
                // 转换不支持时使用原始SQL
                convertedStatements.add(sql);
            } catch (Exception e) {
                logger.error("SQL转换失败: {}, 错误: {}", sql, e.getMessage());
                // 转换失败时使用原始SQL
                convertedStatements.add(sql);
            }
        }
        return convertedStatements;
    }

    /**
     * 通过分析SQL语句特征检测源数据库类型
     * 
     * @param sqlStatements SQL语句列表
     * @return 检测到的数据库类型（mysql, postgresql, opengauss, h2, sqlite等）
     */
    private String detectSourceDatabaseType(List<String> sqlStatements) {
        // 默认为MySQL
        if (sqlStatements == null || sqlStatements.isEmpty()) {
            return "mysql";
        }

        // 遍历所有SQL语句，查找特征
        for (String sql : sqlStatements) {
            sql = sql.toLowerCase();

            // PostgreSQL特征
            if (sql.contains("::timestamp") || 
                sql.contains("serial primary key") || 
                sql.contains("now()")) {
                return "postgresql";
            }

            // OpenGauss特征
            if (sql.contains("nvarchar2") || 
                sql.contains("number(") || 
                sql.contains("sysdate")) {
                return "opengauss";
            }

            // H2特征
            if (sql.contains("auto_increment(1)") || 
                sql.contains("if exists") || 
                sql.contains("current_timestamp()")) {
                return "h2";
            }

            // SQLite特征
            if (sql.contains("autoincrement") || 
                sql.contains("datetime('now')") || 
                sql.contains("integer primary key")) {
                return "sqlite";
            }
        }

        // 如果没有检测到特定特征，默认为MySQL
        return "mysql";
    }

    /**
     * 获取迁移描述
     * 子类可以重写此方法提供更详细的描述信息
     *
     * @return 迁移描述
     */
    public String getDescription() {
        return this.getClass().getSimpleName();
    }
}