package com.siteweb.tcs.common.db;

import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationInfo;
import org.flywaydb.core.api.MigrationInfoService;
import org.flywaydb.core.api.resource.LoadableResource;
import org.flywaydb.core.internal.resource.StringResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据库迁移服务
 * 提供基于Flyway的数据库迁移功能，包括手动执行脚本、获取迁移历史等
 */
@Service
public class DatabaseMigrationService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseMigrationService.class);

    @Autowired
    private Flyway flyway;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ResourceLoader resourceLoader;

    @Value("${flyway.table:tcs_flyway_schema_history}")
    private String historyTable;

    /**
     * 手动执行指定脚本
     *
     * @param scriptPath 脚本路径
     * @param description 描述
     * @return 执行结果
     */
    public boolean executeScript(String scriptPath, String description) {
        try {
            Resource resource = resourceLoader.getResource(scriptPath);
            if (!resource.exists()) {
                logger.error("脚本不存在: {}", scriptPath);
                return false;
            }

            String content = readResourceContent(resource);
            String filename = resource.getFilename() != null ? resource.getFilename() : "manual_script";

            // 创建一个临时的Flyway实例来执行单个脚本
            Flyway tempFlyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/manual") // 临时位置，不会影响正常迁移
                    .baselineOnMigrate(true)
                    .validateOnMigrate(false) // 手动脚本不需要验证
                    .outOfOrder(true) // 允许乱序执行
                    .table(historyTable)
                    .load();

            // 使用Flyway API执行脚本
            LoadableResource sqlResource = new StringResource(content);

            tempFlyway.migrate();
            logger.info("手动脚本执行成功: {}", scriptPath);
            return true;
        } catch (Exception e) {
            logger.error("手动执行脚本失败: {}", scriptPath, e);
            return false;
        }
    }

    /**
     * 获取所有迁移历史
     *
     * @return 迁移历史列表
     */
    public List<Map<String, Object>> getMigrationHistory() {
        try {
            MigrationInfoService migrationInfoService = flyway.info();
            MigrationInfo[] migrations = migrationInfoService.all();

            List<Map<String, Object>> result = new ArrayList<>();
            for (MigrationInfo migration : migrations) {
                Map<String, Object> info = new HashMap<>();
                info.put("script", migration.getScript());
                info.put("version", migration.getVersion() != null ? migration.getVersion().toString() : "baseline");
                info.put("description", migration.getDescription());
                info.put("type", migration.getType().name());
                info.put("state", migration.getState().name());
                info.put("installedOn", migration.getInstalledOn());
                info.put("executionTime", migration.getExecutionTime());
                result.add(info);
            }

            return result;
        } catch (Exception e) {
            logger.error("获取迁移历史失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查数据库是否需要迁移
     *
     * @return 是否需要迁移
     */
    public boolean needMigration() {
        try {
            MigrationInfoService migrationInfoService = flyway.info();
            MigrationInfo[] pendingMigrations = migrationInfoService.pending();
            return pendingMigrations.length > 0;
        } catch (Exception e) {
            logger.error("检查迁移状态失败", e);
            return false;
        }
    }

    /**
     * 获取当前数据库版本
     *
     * @return 当前版本信息
     */
    public Map<String, Object> getCurrentVersion() {
        try {
            MigrationInfoService migrationInfoService = flyway.info();
            MigrationInfo current = migrationInfoService.current();

            if (current == null) {
                return Map.of("version", "未初始化", "description", "数据库未初始化");
            }

            return Map.of(
                    "version", current.getVersion() != null ? current.getVersion().toString() : "baseline",
                    "description", current.getDescription(),
                    "installedOn", current.getInstalledOn(),
                    "state", current.getState().name()
            );
        } catch (Exception e) {
            logger.error("获取当前版本失败", e);
            return Map.of("version", "未知", "description", "获取版本信息失败");
        }
    }

    /**
     * 执行基线迁移
     * 用于已有数据库的初始化
     *
     * @return 是否成功
     */
    public boolean baseline() {
        try {
            flyway.baseline();
            logger.info("基线迁移成功");
            return true;
        } catch (Exception e) {
            logger.error("基线迁移失败", e);
            return false;
        }
    }

    /**
     * 修复失败的迁移
     *
     * @return 是否成功
     */
    public boolean repair() {
        try {
            flyway.repair();
            logger.info("修复迁移成功");
            return true;
        } catch (Exception e) {
            logger.error("修复迁移失败", e);
            return false;
        }
    }

    /**
     * 上传并执行增量SQL脚本
     *
     * @param scriptContent SQL脚本内容
     * @param version 版本号
     * @param description 描述信息
     * @return 执行结果
     */
    public boolean uploadAndExecuteIncrementalScript(String scriptContent, String version, String description) {
        try {
            logger.info("开始执行增量SQL脚本，版本: {}, 描述: {}", version, description);

            // 创建临时的Flyway实例来执行增量脚本
            Flyway tempFlyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/incremental") // 增量脚本的临时位置
                    .baselineOnMigrate(true)
                    .validateOnMigrate(true)
                    .outOfOrder(true) // 允许乱序执行
                    .table(historyTable)
                    .load();

            // 生成唯一的脚本文件名
            String filename = String.format("V%s__%s.sql", version.replace(".", "_"), description.replace(" ", "_"));

            // 使用Flyway API执行脚本
            org.flywaydb.core.internal.resource.StringResource sqlResource = new org.flywaydb.core.internal.resource.StringResource(scriptContent);

            tempFlyway.migrate();

            // 记录执行历史
            Map<String, Object> currentVersion = getCurrentVersion();
            logger.info("增量SQL脚本执行成功，当前版本: {}", currentVersion.get("version"));

            return true;
        } catch (Exception e) {
            logger.error("增量SQL脚本执行失败", e);
            return false;
        }
    }

    /**
     * 读取资源内容
     */
    private String readResourceContent(Resource resource) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()))) {
            return reader.lines().collect(Collectors.joining("\n"));
        }
    }
}