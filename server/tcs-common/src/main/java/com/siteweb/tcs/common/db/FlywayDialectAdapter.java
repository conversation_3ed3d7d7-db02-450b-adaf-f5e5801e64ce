package com.siteweb.tcs.common.db;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Flyway数据库方言适配器
 * 用于支持不同数据库类型的Flyway配置
 */
@Component
public class FlywayDialectAdapter {

    @Value("${spring.profiles.active:mysql}")
    private String activeProfile;

    /**
     * 获取当前数据库类型
     * 
     * @return 数据库类型
     */
    public String getDatabaseType() {
        return activeProfile.toLowerCase();
    }

    /**
     * 获取Flyway迁移脚本位置
     * 
     * @return 迁移脚本位置数组
     */
    public String[] getMigrationLocations() {
        // 基础路径
        String basePath = "classpath:sql";
        
        // 特定数据库类型的路径
        String dbSpecificPath = String.format("classpath:sql/%s", getDatabaseType());
        
        return new String[] { basePath, dbSpecificPath };
    }

    /**
     * 获取Flyway迁移脚本前缀
     * 
     * @return 迁移脚本前缀
     */
    public String getMigrationPrefix() {
        return "V";
    }

    /**
     * 获取Flyway迁移脚本分隔符
     * 
     * @return 迁移脚本分隔符
     */
    public String getMigrationSeparator() {
        return "__";
    }

    /**
     * 获取Flyway迁移脚本后缀
     * 
     * @return 迁移脚本后缀数组
     */
    public String[] getMigrationSuffixes() {
        return new String[] { ".sql" };
    }

    /**
     * 获取Flyway迁移表名
     * 
     * @return 迁移表名
     */
    public String getSchemaHistoryTable() {
        return "tcs_flyway_schema_history";
    }
}