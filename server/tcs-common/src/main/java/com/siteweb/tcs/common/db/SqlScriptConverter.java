package com.siteweb.tcs.common.db;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL脚本转换工具
 * 用于将MySQL特有的SQL语法转换为PostgreSQL和openGauss兼容的语法
 */
@Component
public class SqlScriptConverter {

    @Autowired
    private DatabaseDialectAdapter dialectAdapter;

    /**
     * 转换建表语句
     *
     * @param sqlScript MySQL格式的建表语句
     * @return 转换后的SQL语句
     */
    public String convertCreateTableStatement(String sqlScript) {
        DatabaseDialectAdapter.DatabaseType dbType = dialectAdapter.getCurrentDatabaseType();
        
        // 如果是MySQL，不需要转换
        if (dbType == DatabaseDialectAdapter.DatabaseType.MYSQL) {
            return sqlScript;
        }
        
        // 替换自增列语法
        String result = replaceAutoIncrement(sqlScript, dbType);
        
        // 移除ENGINE, CHARSET等MySQL特有的表选项
        result = removeTableOptions(result, dbType);
        
        // 替换数据类型
        result = replaceDataTypes(result, dbType);
        
        return result;
    }
    
    /**
     * 替换自增列语法
     */
    private String replaceAutoIncrement(String sql, DatabaseDialectAdapter.DatabaseType dbType) {
        if (dbType == DatabaseDialectAdapter.DatabaseType.POSTGRESQL || 
            dbType == DatabaseDialectAdapter.DatabaseType.OPENGAUSS) {
            // 将 AUTO_INCREMENT 替换为 SERIAL 类型
            Pattern pattern = Pattern.compile("(\\w+)\\s+(?:int|bigint).*?\\s+AUTO_INCREMENT", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(sql);
            
            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                String columnName = matcher.group(1);
                matcher.appendReplacement(sb, columnName + " SERIAL");
            }
            matcher.appendTail(sb);
            return sb.toString();
        }
        return sql;
    }
    
    /**
     * 移除MySQL特有的表选项
     */
    private String removeTableOptions(String sql, DatabaseDialectAdapter.DatabaseType dbType) {
        if (dbType == DatabaseDialectAdapter.DatabaseType.POSTGRESQL || 
            dbType == DatabaseDialectAdapter.DatabaseType.OPENGAUSS || 
            dbType == DatabaseDialectAdapter.DatabaseType.H2 ||
            dbType == DatabaseDialectAdapter.DatabaseType.SQLITE) {
            // 移除 ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 等表选项
            return sql.replaceAll("(?i)\\s+ENGINE\\s*=\\s*\\w+", "")
                     .replaceAll("(?i)\\s+DEFAULT\\s+CHARSET\\s*=\\s*\\w+", "")
                     .replaceAll("(?i)\\s+COLLATE\\s*=\\s*\\w+", "");
        }
        return sql;
    }
    
    /**
     * 替换数据类型
     */
    private String replaceDataTypes(String sql, DatabaseDialectAdapter.DatabaseType dbType) {
        if (dbType == DatabaseDialectAdapter.DatabaseType.POSTGRESQL || 
            dbType == DatabaseDialectAdapter.DatabaseType.OPENGAUSS ||
            dbType == DatabaseDialectAdapter.DatabaseType.SQLITE) {
            // 替换 DATETIME 为 TIMESTAMP
            sql = sql.replaceAll("(?i)DATETIME", "TIMESTAMP");
            
            // 替换 TINYINT(1) 为 BOOLEAN
            sql = sql.replaceAll("(?i)TINYINT\\s*\\(\\s*1\\s*\\)", "BOOLEAN");
            
            // 替换 LONGTEXT 为 TEXT
            sql = sql.replaceAll("(?i)LONGTEXT", "TEXT");
        }
        return sql;
    }
    
    /**
     * 转换插入语句
     *
     * @param sqlScript MySQL格式的插入语句
     * @return 转换后的SQL语句
     */
    public String convertInsertStatement(String sqlScript) {
        DatabaseDialectAdapter.DatabaseType dbType = dialectAdapter.getCurrentDatabaseType();
        
        // 如果是MySQL，不需要转换
        if (dbType == DatabaseDialectAdapter.DatabaseType.MYSQL) {
            return sqlScript;
        }
        
        // PostgreSQL、openGauss、H2和SQLite中，插入语句中的布尔值需要转换
        if (dbType == DatabaseDialectAdapter.DatabaseType.POSTGRESQL || 
            dbType == DatabaseDialectAdapter.DatabaseType.OPENGAUSS ||
            dbType == DatabaseDialectAdapter.DatabaseType.H2 ||
            dbType == DatabaseDialectAdapter.DatabaseType.SQLITE) {
            // 将 '0', '1' 转换为 'false', 'true'
            String result = sqlScript;
            result = result.replaceAll("'0'(?=[\\s]*,|[\\s]*\\))", "'false'");
            result = result.replaceAll("'1'(?=[\\s]*,|[\\s]*\\))", "'true'");
            return result;
        }
        
        return sqlScript;
    }
    
    /**
     * 转换完整的SQL脚本
     *
     * @param sqlScript 完整的SQL脚本
     * @return 转换后的SQL脚本
     */
    public String convertScript(String sqlScript) {
        // 分割SQL语句
        String[] statements = sqlScript.split(";");
        StringBuilder result = new StringBuilder();
        
        for (String statement : statements) {
            if (statement.trim().isEmpty()) {
                continue;
            }
            
            // 判断语句类型并进行相应转换
            if (statement.trim().toUpperCase().startsWith("CREATE TABLE")) {
                result.append(convertCreateTableStatement(statement));
            } else if (statement.trim().toUpperCase().startsWith("INSERT")) {
                result.append(convertInsertStatement(statement));
            } else if (statement.trim().toUpperCase().startsWith("CREATE PROCEDURE") || 
                      statement.trim().toUpperCase().startsWith("CREATE FUNCTION")) {
                // 处理存储过程和函数
                result.append(convertStoredProcedureStatement(statement));
            } else {
                // 其他类型的语句暂不处理
                result.append(statement);
            }
            
            result.append(";");
        }
        
        return result.toString();
    }
    
    /**
     * 转换存储过程语句
     *
     * @param sqlScript MySQL格式的存储过程语句
     * @return 转换后的SQL语句
     */
    public String convertStoredProcedureStatement(String sqlScript) {
        DatabaseDialectAdapter.DatabaseType dbType = dialectAdapter.getCurrentDatabaseType();
        
        // 如果是MySQL，不需要转换
        if (dbType == DatabaseDialectAdapter.DatabaseType.MYSQL) {
            return sqlScript;
        }
        
        // 检查当前数据库是否支持存储过程
        if (!dialectAdapter.supportsStoredProcedures()) {
            // 如果不支持存储过程，添加注释说明
            return "-- 当前数据库不支持存储过程\n-- 以下是原始存储过程脚本：\n" + sqlScript;
        }
        
        // 使用数据库方言进行存储过程转换
        return dialectAdapter.convertStoredProcedure(sqlScript);
    }
}