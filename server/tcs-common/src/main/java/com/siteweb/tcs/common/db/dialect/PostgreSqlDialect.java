package com.siteweb.tcs.common.db.dialect;

import org.springframework.stereotype.Component;

/**
 * PostgreSQL数据库方言实现
 */
@Component
public class PostgreSqlDialect implements DatabaseDialectInterface {

    @Override
    public String getPaginationSql(String sql, int offset, int limit) {
        return sql + " LIMIT " + limit + " OFFSET " + offset;
    }

    @Override
    public String getAutoIncrementSql(String columnName) {
        return columnName + " SERIAL";
    }

    @Override
    public String getCurrentTimestampFunction() {
        return "CURRENT_TIMESTAMP";
    }

    @Override
    public String getConcatFunction(String... strings) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < strings.length; i++) {
            sb.append(strings[i]);
            if (i < strings.length - 1) {
                sb.append(" || ");
            }
        }
        return sb.toString();
    }

    @Override
    public String getLimitSql(int limit) {
        return " LIMIT " + limit;
    }

    @Override
    public String getTableOptions() {
        return ""; // PostgreSQL不需要指定引擎和字符集
    }

    @Override
    public String convertStoredProcedure(String procedureScript) {
        // 将MySQL的存储过程语法转换为PostgreSQL语法
        String result = procedureScript;
        
        // 替换DELIMITER语句
        result = result.replaceAll("(?i)DELIMITER\\s+\\$\\$", "");
        result = result.replaceAll("(?i)DELIMITER\\s+;", "");
        
        // 替换BEGIN...END块
        result = result.replaceAll("(?i)BEGIN", "BEGIN");
        result = result.replaceAll("(?i)END\\s*\\$\\$", "END;");
        
        // 替换声明语法
        result = result.replaceAll("(?i)DECLARE\\s+CONTINUE\\s+HANDLER\\s+FOR\\s+SQLEXCEPTION\\s+.*?;", "");
        
        // 替换IF语句
        result = result.replaceAll("(?i)IF\\s+(.+?)\\s+THEN", "IF $1 THEN");
        result = result.replaceAll("(?i)END\\s+IF", "END IF;");
        
        // 替换变量声明和赋值
        result = result.replaceAll("(?i)DECLARE\\s+(\\w+)\\s+(\\w+)", "$1 $2");
        result = result.replaceAll("(?i)SET\\s+(\\w+)\\s*=\\s*(.+?);", "$1 := $2;");
        
        return result;
    }

    @Override
    public boolean supportsStoredProcedures() {
        return true;
    }
}