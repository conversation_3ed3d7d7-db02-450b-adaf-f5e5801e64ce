package com.siteweb.tcs.common.db.dialect;

import org.springframework.stereotype.Component;

/**
 * SQLite数据库方言实现
 */
@Component
public class SqliteDialect implements DatabaseDialectInterface {

    @Override
    public String getPaginationSql(String sql, int offset, int limit) {
        return sql + " LIMIT " + limit + " OFFSET " + offset;
    }

    @Override
    public String getAutoIncrementSql(String columnName) {
        return columnName + " INTEGER PRIMARY KEY AUTOINCREMENT";
    }

    @Override
    public String getCurrentTimestampFunction() {
        return "datetime('now')";
    }

    @Override
    public String getConcatFunction(String... strings) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < strings.length; i++) {
            sb.append(strings[i]);
            if (i < strings.length - 1) {
                sb.append(" || ");
            }
        }
        return sb.toString();
    }

    @Override
    public String getLimitSql(int limit) {
        return " LIMIT " + limit;
    }

    @Override
    public String getTableOptions() {
        return ""; // SQLite不需要指定引擎和字符集
    }

    @Override
    public String convertStoredProcedure(String procedureScript) {
        // SQLite不支持存储过程，返回警告信息
        return "-- SQLite不支持存储过程\n-- 以下是原始存储过程脚本：\n" + procedureScript;
    }

    @Override
    public boolean supportsStoredProcedures() {
        return false; // SQLite不支持存储过程
    }
}