package com.siteweb.tcs.common.exception.code;

/**
 * Standard business error codes for the system.
 * These are common business error codes that can be used across the system.
 */
public enum StandardBusinessErrorCode implements BusinessErrorCode {
    // General business errors
    BUSINESS_ERROR("BUSINESS_ERROR", "业务处理失败"),
    VALIDATION_FAILED("VALIDATION_FAILED", "数据验证失败"),
    OPERATION_FAILED("OPERATION_FAILED", "操作失败"),
    
    // Data related errors
    REQUEST_PARAMETER_INVALID("REQUEST_PARAMETER_INVALID", "请求参数无效"),
    DATA_NOT_FOUND("DATA_NOT_FOUND", "数据不存在"),
    DATA_ALREADY_EXISTS("DATA_ALREADY_EXISTS", "数据已存在"),
    DATA_VALIDATION_FAILED("DATA_VALIDATION_FAILED", "数据验证失败"),
    DATA_INTEGRITY_VIOLATION("DATA_INTEGRITY_VIOLATION", "数据完整性违规"),
    
    // User related errors
    USER_NOT_FOUND("USER_NOT_FOUND", "用户不存在"),
    USER_ALREADY_EXISTS("USER_ALREADY_EXISTS", "用户已存在"),
    USER_UNAUTHORIZED("USER_UNAUTHORIZED", "用户未授权"),
    USER_FORBIDDEN("USER_FORBIDDEN", "用户禁止访问"),
    USER_INACTIVE("USER_INACTIVE", "用户未激活"),
    USER_LOCKED("USER_LOCKED", "用户已锁定"),
    USER_PASSWORD_EXPIRED("USER_PASSWORD_EXPIRED", "用户密码已过期"),
    USER_ACCOUNT_EXPIRED("USER_ACCOUNT_EXPIRED", "用户账号已过期"),
    
    // Authentication related errors
    AUTHENTICATION_FAILED("AUTHENTICATION_FAILED", "认证失败"),
    INVALID_CREDENTIALS("INVALID_CREDENTIALS", "无效的凭证"),
    TOKEN_EXPIRED("TOKEN_EXPIRED", "令牌已过期"),
    TOKEN_INVALID("TOKEN_INVALID", "令牌无效"),




    // Device related errors
    RECORD_CREATE_ERROR("RECORD_CREATE_ERROR", "记录创建失败"),
    RECORD_REPEAT_ERROR("RECORD_REPEAT_ERROR", "记录重复"),
    RECORD_UPDATE_ERROR("RECORD_UPDATE_ERROR", "记录更新失败"),
    RECORD_DELETE_ERROR("RECORD_DELETE_ERROR", "记录删除失败"),


    DEVICE_NOT_FOUND("DEVICE_NOT_FOUND", "设备不存在"),
    DEVICE_ALREADY_EXISTS("DEVICE_ALREADY_EXISTS", "设备已存在"),
    DEVICE_OFFLINE("DEVICE_OFFLINE", "设备离线"),
    DEVICE_BUSY("DEVICE_BUSY", "设备忙"),
    DEVICE_ERROR("DEVICE_ERROR", "设备错误");

    private final String code;
    private final String message;

    StandardBusinessErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
