package com.siteweb.tcs.common.exception.code;

/**
 * Standard plugin error codes for the system.
 * These are common plugin error codes that can be used across the system.
 */
public enum StandardPluginErrorCode implements PluginErrorCode {
    // Plugin lifecycle errors
    PLUGIN_LOAD_ERROR("PLUGIN_LOAD_ERROR", "插件加载错误"),
    PLUGIN_START_ERROR("PLUGIN_START_ERROR", "插件启动错误"),
    PLUGIN_STOP_ERROR("PLUGIN_STOP_ERROR", "插件停止错误"),
    PLUGIN_UNLOAD_ERROR("PLUGIN_UNLOAD_ERROR", "插件卸载错误"),
    
    // Plugin state errors
    PLUGIN_NOT_FOUND("PLUGIN_NOT_FOUND", "插件不存在"),
    PLUGIN_ALREADY_STARTED("PLUGIN_ALREADY_STARTED", "插件已启动"),
    PLUGIN_NOT_STARTED("PLUGIN_NOT_STARTED", "插件未启动"),
    PLUGIN_ALREADY_STOPPED("PLUGIN_ALREADY_STOPPED", "插件已停止"),
    
    // Plugin dependency errors
    PLUGIN_DEPENDENCY_ERROR("PLUGIN_DEPENDENCY_ERROR", "插件依赖错误"),
    PLUGIN_DEPENDENCY_NOT_FOUND("PLUGIN_DEPENDENCY_NOT_FOUND", "插件依赖不存在"),
    PLUGIN_DEPENDENCY_VERSION_MISMATCH("PLUGIN_DEPENDENCY_VERSION_MISMATCH", "插件依赖版本不匹配"),
    
    // Plugin configuration errors
    PLUGIN_CONFIGURATION_ERROR("PLUGIN_CONFIGURATION_ERROR", "插件配置错误"),
    PLUGIN_INVALID_CONFIGURATION("PLUGIN_INVALID_CONFIGURATION", "无效的插件配置"),
    PLUGIN_MISSING_CONFIGURATION("PLUGIN_MISSING_CONFIGURATION", "缺少插件配置"),
    
    // Plugin resource errors
    PLUGIN_RESOURCE_ERROR("PLUGIN_RESOURCE_ERROR", "插件资源错误"),
    PLUGIN_RESOURCE_NOT_FOUND("PLUGIN_RESOURCE_NOT_FOUND", "插件资源不存在"),
    PLUGIN_RESOURCE_ACCESS_DENIED("PLUGIN_RESOURCE_ACCESS_DENIED", "插件资源访问被拒绝"),
    
    // Plugin communication errors
    PLUGIN_COMMUNICATION_ERROR("PLUGIN_COMMUNICATION_ERROR", "插件通信错误"),
    PLUGIN_MESSAGE_ERROR("PLUGIN_MESSAGE_ERROR", "插件消息错误"),
    
    // Plugin actor errors
    PLUGIN_ACTOR_ERROR("PLUGIN_ACTOR_ERROR", "插件Actor错误"),
    PLUGIN_ACTOR_CREATION_ERROR("PLUGIN_ACTOR_CREATION_ERROR", "插件Actor创建错误"),
    PLUGIN_ACTOR_TERMINATION_ERROR("PLUGIN_ACTOR_TERMINATION_ERROR", "插件Actor终止错误"),
    
    // Plugin database errors
    PLUGIN_DATABASE_ERROR("PLUGIN_DATABASE_ERROR", "插件数据库错误"),
    PLUGIN_DATABASE_MIGRATION_ERROR("PLUGIN_DATABASE_MIGRATION_ERROR", "插件数据库迁移错误"),
    
    // Plugin security errors
    PLUGIN_SECURITY_ERROR("PLUGIN_SECURITY_ERROR", "插件安全错误"),
    PLUGIN_UNAUTHORIZED("PLUGIN_UNAUTHORIZED", "插件未授权"),
    PLUGIN_FORBIDDEN("PLUGIN_FORBIDDEN", "插件禁止访问"),
    
    // Plugin business errors
    PLUGIN_BUSINESS_ERROR("PLUGIN_BUSINESS_ERROR", "插件业务错误"),
    PLUGIN_VALIDATION_ERROR("PLUGIN_VALIDATION_ERROR", "插件验证错误"),
    
    // Plugin technical errors
    PLUGIN_TECHNICAL_ERROR("PLUGIN_TECHNICAL_ERROR", "插件技术错误"),
    PLUGIN_SYSTEM_ERROR("PLUGIN_SYSTEM_ERROR", "插件系统错误");

    private final String code;
    private final String message;

    StandardPluginErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
