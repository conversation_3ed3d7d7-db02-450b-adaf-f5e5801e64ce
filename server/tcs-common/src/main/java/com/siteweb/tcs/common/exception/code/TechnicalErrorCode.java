package com.siteweb.tcs.common.exception.code;

import com.siteweb.tcs.common.exception.core.TechnicalException;

/**
 * Interface for technical error codes.
 * All technical error code enums should implement this interface.
 */
public interface TechnicalErrorCode extends ErrorCode {
    /**
     * Get the error category
     * @return Always returns TECHNICAL
     */
    @Override
    default ErrorCategory getCategory() {
        return ErrorCategory.TECHNICAL;
    }

    /**
     * Convert to a TechnicalException
     * @return A new TechnicalException with this error code
     */
    default TechnicalException toException() {
        return new TechnicalException(this);
    }

    /**
     * Convert to a TechnicalException with a component
     * @param component The technical component that generated the exception
     * @return A new TechnicalException with this error code and component
     */
    default TechnicalException toException(String component) {
        return new TechnicalException(this, component);
    }

    /**
     * Convert to a TechnicalException with details
     * @param details Additional error details
     * @return A new TechnicalException with this error code and details
     */
    default TechnicalException toException(Object details) {
        return new TechnicalException(this, details);
    }

    /**
     * Convert to a TechnicalException with a cause
     * @param cause The cause of the exception
     * @return A new TechnicalException with this error code and cause
     */
    default TechnicalException toException(Throwable cause) {
        return new TechnicalException(this, cause);
    }

    /**
     * Convert to a TechnicalException with details and a cause
     * @param details Additional error details
     * @param cause The cause of the exception
     * @return A new TechnicalException with this error code, details, and cause
     */
    default TechnicalException toException(Object details, Throwable cause) {
        return new TechnicalException(this, details, cause);
    }

    /**
     * Convert to a TechnicalException with details, cause, and component
     * @param details Additional error details
     * @param cause The cause of the exception
     * @param component The technical component that generated the exception
     * @return A new TechnicalException with this error code, details, cause, and component
     */
    default TechnicalException toException(Object details, Throwable cause, String component) {
        return new TechnicalException(this, details, cause, component);
    }
}
