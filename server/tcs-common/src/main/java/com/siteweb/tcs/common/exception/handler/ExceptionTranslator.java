package com.siteweb.tcs.common.exception.handler;

import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.common.exception.code.StandardTechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.BusinessException;
import com.siteweb.tcs.common.exception.core.TCSException;
import com.siteweb.tcs.common.exception.core.TechnicalException;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.sql.SQLException;

/**
 * Exception translator for converting low-level exceptions to application exceptions.
 * This translator converts exceptions from various layers to appropriate application exceptions.
 */
public class ExceptionTranslator {

    /**
     * Translate a Throwable to an appropriate application exception
     * @param throwable The Throwable to translate
     * @return An appropriate application exception
     */
    public static TCSException translate(Throwable throwable) {
        if (throwable instanceof TCSException) {
            return (TCSException) throwable;
        }
        
        // Database exceptions
        if (throwable instanceof DataIntegrityViolationException) {
            return new BusinessException(StandardBusinessErrorCode.DATA_INTEGRITY_VIOLATION, throwable);
        } else if (throwable instanceof EmptyResultDataAccessException) {
            return new BusinessException(StandardBusinessErrorCode.DATA_NOT_FOUND, throwable);
        } else if (throwable instanceof BadSqlGrammarException) {
            return new TechnicalException(StandardTechnicalErrorCode.DATABASE_QUERY_ERROR, throwable);
        } else if (throwable instanceof DataAccessException) {
            return new TechnicalException(StandardTechnicalErrorCode.DATABASE_ERROR, throwable);
        } else if (throwable instanceof SQLException) {
            return new TechnicalException(StandardTechnicalErrorCode.DATABASE_ERROR, throwable);
        }
        
        // Network exceptions
        if (throwable instanceof ConnectException) {
            return new TechnicalException(StandardTechnicalErrorCode.CONNECTION_REFUSED, throwable);
        } else if (throwable instanceof SocketTimeoutException) {
            return new TechnicalException(StandardTechnicalErrorCode.CONNECTION_TIMEOUT, throwable);
        } else if (throwable instanceof IOException) {
            return new TechnicalException(StandardTechnicalErrorCode.NETWORK_ERROR, throwable);
        }
        
        // Default
        return new TechnicalException(StandardTechnicalErrorCode.SYSTEM_ERROR, throwable);
    }

    /**
     * Create a ResponseErrorHandler that translates exceptions
     * @return A ResponseErrorHandler that translates exceptions
     */
    public static ResponseErrorHandler createResponseErrorHandler() {
        return new ResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                return response.getStatusCode().isError();
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                throw new TechnicalException(
                    StandardTechnicalErrorCode.API_ERROR.getCode(),
                    "API error: " + response.getStatusCode() + " " + response.getStatusText(),
                    response.getStatusCode().value()
                );
            }
        };
    }
}
