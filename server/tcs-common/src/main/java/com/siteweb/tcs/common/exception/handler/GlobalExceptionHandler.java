package com.siteweb.tcs.common.exception.handler;

import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.common.exception.code.StandardTechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.BusinessException;
import com.siteweb.tcs.common.exception.core.PluginException;
import com.siteweb.tcs.common.exception.core.TCSException;
import com.siteweb.tcs.common.exception.core.TechnicalException;
import com.siteweb.tcs.common.exception.plugin.PluginBusinessException;
import com.siteweb.tcs.common.exception.plugin.PluginTechnicalException;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.HashMap;
import java.util.Map;

/**
 * Global exception handler for the application.
 * This handler catches exceptions and converts them to appropriate HTTP responses.
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * Handle TCSException
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(TCSException.class)
    public ResponseEntity<ResponseResult> handleTCSException(TCSException ex) {
        log.error("TCS Exception: {}", ex.getMessage(), ex);
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        result.setData(ex.getDetails());
        
        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Handle BusinessException
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ResponseResult> handleBusinessException(BusinessException ex) {
        log.error("Business Exception: {}", ex.getMessage(), ex);
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        result.setData(ex.getDetails());
        
        return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle TechnicalException
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(TechnicalException.class)
    public ResponseEntity<ResponseResult> handleTechnicalException(TechnicalException ex) {
        log.error("Technical Exception: {}", ex.getMessage(), ex);
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        result.setData(ex.getDetails());
        
        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Handle PluginException
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(PluginException.class)
    public ResponseEntity<ResponseResult> handlePluginException(PluginException ex) {
        log.error("Plugin Exception: {} (Plugin: {})", ex.getMessage(), ex.getPluginId(), ex);
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        
        Map<String, Object> details = new HashMap<>();
        if (ex.getDetails() != null) {
            details.put("details", ex.getDetails());
        }
        details.put("pluginId", ex.getPluginId());
        if (ex.getPluginName() != null) {
            details.put("pluginName", ex.getPluginName());
        }
        
        result.setData(details);
        
        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Handle PluginBusinessException
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(PluginBusinessException.class)
    public ResponseEntity<ResponseResult> handlePluginBusinessException(PluginBusinessException ex) {
        log.error("Plugin Business Exception: {} (Plugin: {})", ex.getMessage(), ex.getPluginId(), ex);
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        
        Map<String, Object> details = new HashMap<>();
        if (ex.getDetails() != null) {
            details.put("details", ex.getDetails());
        }
        details.put("pluginId", ex.getPluginId());
        if (ex.getPluginName() != null) {
            details.put("pluginName", ex.getPluginName());
        }
        
        result.setData(details);
        
        return new ResponseEntity<>(result, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle PluginTechnicalException
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(PluginTechnicalException.class)
    public ResponseEntity<ResponseResult> handlePluginTechnicalException(PluginTechnicalException ex) {
        log.error("Plugin Technical Exception: {} (Plugin: {})", ex.getMessage(), ex.getPluginId(), ex);
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        
        Map<String, Object> details = new HashMap<>();
        if (ex.getDetails() != null) {
            details.put("details", ex.getDetails());
        }
        details.put("pluginId", ex.getPluginId());
        if (ex.getPluginName() != null) {
            details.put("pluginName", ex.getPluginName());
        }
        if (ex.getComponent() != null) {
            details.put("component", ex.getComponent());
        }
        
        result.setData(details);
        
        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Handle validation exceptions
     * @param ex The exception
     * @return ResponseEntity with validation error details
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<ResponseResult> handleValidationExceptions(Exception ex) {
        Map<String, String> errors = new HashMap<>();
        
        if (ex instanceof MethodArgumentNotValidException) {
            ((MethodArgumentNotValidException) ex).getBindingResult().getAllErrors().forEach((error) -> {
                String fieldName = ((FieldError) error).getField();
                String errorMessage = error.getDefaultMessage();
                errors.put(fieldName, errorMessage);
            });
        } else if (ex instanceof BindException) {
            ((BindException) ex).getBindingResult().getAllErrors().forEach((error) -> {
                String fieldName = ((FieldError) error).getField();
                String errorMessage = error.getDefaultMessage();
                errors.put(fieldName, errorMessage);
            });
        }
        
        log.error("Validation Exception: {}", errors);
        
        return ResponseHelper.failed(
            StandardBusinessErrorCode.VALIDATION_FAILED.getCode(),
            StandardBusinessErrorCode.VALIDATION_FAILED.getMessage(),
            errors,
            HttpStatus.BAD_REQUEST
        );
    }

    /**
     * Handle method argument type mismatch exceptions
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ResponseResult> handleMethodArgumentTypeMismatch(MethodArgumentTypeMismatchException ex) {
        String message = String.format("Parameter '%s' should be of type '%s'", 
            ex.getName(), ex.getRequiredType().getSimpleName());
        
        log.error("Method Argument Type Mismatch: {}", message, ex);
        
        return ResponseHelper.failed(
            StandardBusinessErrorCode.VALIDATION_FAILED.getCode(),
            message,
            HttpStatus.BAD_REQUEST
        );
    }

    /**
     * Handle all other exceptions
     * @param ex The exception
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResponseResult> handleAllExceptions(Exception ex) {
        log.error("Unhandled Exception: {}", ex.getMessage(), ex);
        
        return ResponseHelper.failed(
            StandardTechnicalErrorCode.SYSTEM_ERROR.getCode(),
            "An unexpected error occurred: " + ex.getMessage(),
            HttpStatus.INTERNAL_SERVER_ERROR
        );
    }
}
