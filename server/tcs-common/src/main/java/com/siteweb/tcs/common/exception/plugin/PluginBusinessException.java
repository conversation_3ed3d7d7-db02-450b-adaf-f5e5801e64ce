package com.siteweb.tcs.common.exception.plugin;

import com.siteweb.tcs.common.exception.code.BusinessErrorCode;
import com.siteweb.tcs.common.exception.core.BusinessException;
import lombok.Getter;

import java.io.Serial;

/**
 * Business exception specific to plugins.
 * This exception type should be used for business logic errors in plugins.
 */
@Getter
public class PluginBusinessException extends BusinessException {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Plugin ID that generated the exception
     */
    private final String pluginId;

    /**
     * Plugin name that generated the exception
     */
    private final String pluginName;

    public PluginBusinessException(String code, String message, String pluginId) {
        super(code, message);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(String code, String message, String pluginId, String pluginName) {
        super(code, message);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }

    public PluginBusinessException(String code, String message, Object details, String pluginId) {
        super(code, message, details);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(String code, String message, Throwable cause, String pluginId) {
        super(code, message, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(String code, String message, Object details, Throwable cause, String pluginId) {
        super(code, message, details, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(String code, String message, Object details, Throwable cause, String pluginId, String pluginName) {
        super(code, message, details, cause);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }

    public PluginBusinessException(BusinessErrorCode errorCode, String pluginId) {
        super(errorCode);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(BusinessErrorCode errorCode, String pluginId, String pluginName) {
        super(errorCode);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }

    public PluginBusinessException(BusinessErrorCode errorCode, Object details, String pluginId) {
        super(errorCode, details);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(BusinessErrorCode errorCode, Throwable cause, String pluginId) {
        super(errorCode, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(BusinessErrorCode errorCode, Object details, Throwable cause, String pluginId) {
        super(errorCode, details, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginBusinessException(BusinessErrorCode errorCode, String[] params, String pluginId) {
        super(errorCode, params);
        this.pluginId = pluginId;
        this.pluginName = null;
    }
}
