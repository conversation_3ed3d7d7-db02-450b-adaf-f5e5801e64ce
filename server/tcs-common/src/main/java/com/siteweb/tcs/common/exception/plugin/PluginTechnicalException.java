package com.siteweb.tcs.common.exception.plugin;

import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.TechnicalException;
import lombok.Getter;

import java.io.Serial;

/**
 * Technical exception specific to plugins.
 * This exception type should be used for technical/system errors in plugins.
 */
@Getter
public class PluginTechnicalException extends TechnicalException {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Plugin ID that generated the exception
     */
    private final String pluginId;

    /**
     * Plugin name that generated the exception
     */
    private final String pluginName;

    public PluginTechnicalException(String code, String message, String pluginId) {
        super(code, message);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(String code, String message, String component, String pluginId) {
        super(code, message, component);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(String code, String message, Object details, String pluginId) {
        super(code, message, details);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(String code, String message, Throwable cause, String pluginId) {
        super(code, message, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(String code, String message, Object details, Throwable cause, String pluginId) {
        super(code, message, details, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(String code, String message, Object details, Throwable cause, String component, String pluginId) {
        super(code, message, details, cause, component);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(TechnicalErrorCode errorCode, String pluginId) {
        super(errorCode);
        this.pluginId = pluginId;
        this.pluginName = null;
    }


    public PluginTechnicalException(TechnicalErrorCode errorCode, String component, String pluginId) {
        super(errorCode, component);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(TechnicalErrorCode errorCode, Object details, String pluginId) {
        super(errorCode, details);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(TechnicalErrorCode errorCode, Throwable cause, String pluginId) {
        super(errorCode, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause, String pluginId) {
        super(errorCode, details, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginTechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause, String component, String pluginId) {
        super(errorCode, details, cause, component);
        this.pluginId = pluginId;
        this.pluginName = null;
    }
}
