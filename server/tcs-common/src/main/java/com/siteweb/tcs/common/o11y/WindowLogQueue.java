package com.siteweb.tcs.common.o11y;

import lombok.Getter;
import org.springframework.data.util.Pair;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class WindowLogQueue {
    private final int maxSize;
    private final LinkedList<WindowLogItemWrapper> queue;

    public WindowLogQueue() {
        this(100);
    }

    public WindowLogQueue(int maxSize) {
        this.maxSize = maxSize;
        this.queue = new LinkedList<>();
    }

    public Long getSize() {
        synchronized (queue) {
            return (long) queue.size();
        }
    }

    public void clear() {
        synchronized (queue) {
            queue.clear();
        }
    }

    public void enqueue(WindowLogItem windowLogItem) {
        synchronized (queue) {
            if (queue.size() >= maxSize) {
                queue.poll();
            }
            queue.offer(new WindowLogItemWrapper(windowLogItem));
        }
    }

    public String getSnapLog() {
        StringBuilder sb = new StringBuilder();
        synchronized (queue) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (WindowLogItemWrapper wrapper : queue) {
                sb.append(sdf.format(wrapper.getInsertTime())).append("  ")
                        .append(wrapper.getWindowLogItem().getWindowLogString())
                        .append("\n");
            }
        }
        return sb.toString();
    }

    public List<Pair<String, String>> getSnapLogList() {
        List<Pair<String, String>> result = new ArrayList<>();
        synchronized (queue) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (WindowLogItemWrapper wrapper : queue) {
                result.add(Pair.of(sdf.format(wrapper.getInsertTime()), wrapper.getWindowLogItem().getWindowLogString()));
            }
        }
        return result;
    }

    @Getter
    private static class WindowLogItemWrapper {
        private final WindowLogItem windowLogItem;
        private final long insertTime;

        public WindowLogItemWrapper(WindowLogItem windowLogItem) {
            this.windowLogItem = windowLogItem;
            this.insertTime = System.currentTimeMillis();
        }

    }
}
