package com.siteweb.tcs.common.runtime;

import org.apache.pekko.actor.AbstractActor;

/**
 * <AUTHOR> (2024-05-11)
 **/
public abstract class AbstractPluginActor extends AbstractActor {
    protected abstract void onStart();

    protected abstract void onStop();

    protected abstract void onRestart(final Throwable reason);


    @Override
    public void preStart() throws Exception {
        super.preStart();
        onStart();
    }

    @Override
    public void postStop() throws Exception {
        onStop();
        super.postStop();
    }

    public void postRestart(final Throwable reason) throws Exception {
        onRestart(reason);
        super.postRestart(reason);
    }

    public abstract Receive createReceive();
}

