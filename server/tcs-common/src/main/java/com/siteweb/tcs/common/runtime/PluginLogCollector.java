package com.siteweb.tcs.common.runtime;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Objects;

/**
 * <AUTHOR> (2024-06-24)
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PluginLogCollector extends AppenderBase<ILoggingEvent> {
    private final SSeEmitterCollection emitters = new SSeEmitterCollection();
    private final LinkedList<LogInfo> list = new LinkedList<>();
    private int maxSize = 200;
    private String packageName;
    private PatternLayoutEncoder encoder;

    public PluginLogCollector(String packageName) {
        this.packageName = packageName;

    }


    public void setMaxSize(int maxSize) {
        this.maxSize = maxSize;
        while (list.size() > maxSize) {
            list.removeFirst();
        }
    }

    public LogInfo[] getLogs() {
        return list.toArray(new LogInfo[]{});
    }

    public SseEmitter getStream() {
        SseEmitter emitter = new SseEmitter();
        emitter.onCompletion(() -> emitters.remove(emitter));
        emitter.onTimeout(() -> emitters.remove(emitter));
        emitter.onError(e -> emitters.remove(emitter));
        //根据日志级别过滤
        // 获取 Logger 上下文 日志等级
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = loggerContext.getLogger(packageName);
        Level level = logger.getLevel();
        // 根据日志等级过滤
        if (level != null && !Objects.equals(level.levelStr, "ALL")) {
            emitters.connected(emitter, list.stream().filter(e -> e.getLevel().equals(level.levelStr)).toArray());
        } else {
            emitters.connected(emitter, list);
        }
        return emitter;
    }

    @Override
    public void start() {
        if(encoder == null){
            encoder = new PatternLayoutEncoder();
            encoder.setContext(this.getContext());
            encoder.setPattern("%msg%n");
            encoder.start();
        }
        list.clear();
        super.start();
        emitters.clean();
    }

    @Override
    public void stop() {
        super.stop();
        if (encoder != null) {
            encoder.stop();
            encoder = null;
        }
    }


    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!started) return;
        StackTraceElement[] steArray = new Throwable().getStackTrace();
        var hit = Arrays.stream(steArray).anyMatch(e -> e.getClassName().startsWith(packageName));
        if (hit) {
            LogInfo info = new LogInfo();
            info.setTimeStamp(LocalDateTime.ofInstant(Instant.ofEpochMilli(eventObject.getTimeStamp()), ZoneId.systemDefault()));
            info.setMessage(new String(this.encoder.encode(eventObject)));
            info.setLevel(eventObject.getLevel().levelStr);
            info.setLoggerName(eventObject.getLoggerName());
            info.setThread(eventObject.getThreadName());
            list.addLast(info);
            if (list.size() > maxSize) {
                list.removeFirst();
            }
            emitters.append(info);
        }
    }


    @Data
    static
    public class LogInfo {
        public LocalDateTime timeStamp;
        public String level;
        public String loggerName;
        public String message;
        public String thread;
    }

}
