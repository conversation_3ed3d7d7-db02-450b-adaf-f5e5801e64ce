package com.siteweb.tcs.common.runtime;

/**
 * 插件的静态资源解析器
 * 用于加载插件的前端资源文件
 * <AUTHOR> (2024-06-19)
 **/

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.servlet.resource.ResourceResolver;
import org.springframework.web.servlet.resource.ResourceResolverChain;

import java.util.List;

public class PluginResourceResolver implements ResourceResolver {

    private final ResourceLoader resourceLoader;

    public PluginResourceResolver(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    @Override
    public Resource resolveResource(HttpServletRequest request, String requestPath, List<? extends Resource> locations, ResourceResolverChain chain) {
        for (int i = 0; i < locations.size(); i++) {
            Resource local = locations.get(i);
            if (local instanceof ClassPathResource classPath) {
                Resource resource = resourceLoader.getResource("classpath:/" + classPath.getPath() + requestPath);
                if (resource.exists() && resource.isReadable()) {
                    return resource;
                }
            }
        }
        return chain.resolveResource(request, requestPath, locations);
    }

    @Override
    public String resolveUrlPath(String resourcePath, List<? extends Resource> locations, ResourceResolverChain chain) {
        Resource resource = resourceLoader.getResource("classpath:/META-INF/com-siteweb-webroot" + resourcePath);
        if (resource.exists() && resource.isReadable()) {
            return resourcePath;
        }
        return chain.resolveUrlPath(resourcePath, locations);
    }
}
