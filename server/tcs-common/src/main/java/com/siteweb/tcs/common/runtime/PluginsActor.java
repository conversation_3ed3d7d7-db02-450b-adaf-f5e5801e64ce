package com.siteweb.tcs.common.runtime;

import com.siteweb.tcs.common.actions.CreatePluginActorAction;
import com.siteweb.tcs.common.actions.DeletePluginActorAction;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.ActorPathBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.actor.Terminated;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> (2024-05-09)
 **/

@Slf4j
public class PluginsActor extends ProbeActor {

    public static Props props() {
        return Props.create(PluginsActor.class);
    }

    private final Map<String, ActorRef> waitingSenders = new HashMap<>();

    private void handlerStartPluginMessage(CreatePluginActorAction action) {
//        ActorRef actor = getContext().actorOf(Props.create(PluginsActor.class), "");
        // 判断是否已经有节点了
        if (getContext().findChild(action.getPluginId()).isPresent()) return;
        // 创建子节点
        ActorRef actorRef = context().actorOf(action.getProps(), ActorPathBuilder.create().append(action.getPluginId()).build());
        // 监视Terminated
        context().watch(actorRef);
        // 返回
        getSender().tell(actorRef, getSelf());
        log.info("插件“{}” 创建Actor成功.", action.getPluginId());
    }


    private void handlerStopPluginMessage(DeletePluginActorAction action) {
        var actorRef = getContext().findChild(action.getPluginId());
        if (actorRef.isPresent()) {
            var actor = actorRef.get();
            var actorPath = actor.path().toString();
            log.info("插件“{}”的Actor执行停止.", actor.path().name());
            waitingSenders.put(actorPath, getSender());
            context().stop(actor);
        }else{
            getSender().tell(false, getSelf());
        }
    }

    private void handlerChildTerminated(Terminated action) {
        var actorPath = action.getActor().path().toString();
        var sender = waitingSenders.get(actorPath);
        if (sender != null) {
            log.info("插件“{}”的Actor已停止.", action.actor().path().name());
            waitingSenders.remove(actorPath);
            sender.tell(true, getSelf());
        }
    }


    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(CreatePluginActorAction.class, this::handlerStartPluginMessage)
                .match(DeletePluginActorAction.class, this::handlerStopPluginMessage)
                .match(Terminated.class, this::handlerChildTerminated)
                .build()
                .orElse(super.createReceive());
    }

}
