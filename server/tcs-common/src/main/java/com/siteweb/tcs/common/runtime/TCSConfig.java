package com.siteweb.tcs.common.runtime;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.UUID;

public class TCSConfig {
    public static final String TCS_CONFIG_FILE = "tcs-config.yml";
    private static final String TCS_ID_FILE = ".tcsid";
    private String tcsId;
    private static final Path workspaceFolder = Path.of("./config");

    public String getTcsId() {
        if (tcsId == null) {
            tcsId = loadOrGenerateTcsId();
        }

        return tcsId;
    }

    private static String loadOrGenerateTcsId() {
        Path tcsIdPath = workspaceFolder.resolve(TCS_ID_FILE);
        if (Files.exists(tcsIdPath)) {
            try {
                return Files.readString(tcsIdPath, StandardCharsets.UTF_8).trim();
            } catch (IOException e) {
                throw new RuntimeException("Failed to read TCS ID", e);
            }
        }

        String newTcsId = UUID.randomUUID().toString().substring(0, 8);
        try {
            Files.writeString(tcsIdPath, newTcsId, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException("Failed to save TCS ID", e);
        }
        return newTcsId;
    }
}
