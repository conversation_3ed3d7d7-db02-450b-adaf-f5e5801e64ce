package com.siteweb.tcs.common.runtime;

/**
 * <AUTHOR> (2024-06-07)
 **/
public interface ThingConnectPluginEvent {

    /**
     * 插件上下文已启动成功，但还未执行用户启动方法
     * @param plugin
     */
    void pluginStart(ThingConnectPlugin plugin);

    /**
     * 插件启动失败
     * @param plugin
     */
    void pluginStartFail(ThingConnectPlugin plugin);

    /**
     *
     * @param plugin
     */
    void pluginStop(ThingConnectPlugin plugin);

    void pluginStopFail(ThingConnectPlugin plugin);

}
