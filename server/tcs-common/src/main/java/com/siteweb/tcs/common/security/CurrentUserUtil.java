package com.siteweb.tcs.common.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Arrays;
import java.util.List;

/**
 * 当前用户信息获取工具类
 *
 * 提供便捷的方法获取当前登录用户的各种信息，用于记录操作日志、权限检查等场景。
 * 这个类被提取到公共模块中，避免循环依赖问题。
 *
 * 使用反射机制避免直接依赖具体的用户类，提高通用性和兼容性。
 *
 * <AUTHOR> System
 * @since 1.0
 */
@Slf4j
public class CurrentUserUtil {

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID，未登录时返回null
     */
    public static Integer getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    // 通过反射获取用户ID，避免直接依赖TokenUser类
                    return (Integer) invokeMethod(details, "getUserId");
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current user ID", e);
        }
        return null;
    }

    /**
     * 获取当前登录用户名
     *
     * @return 用户名，未登录时返回null
     */
    public static String getCurrentUserName() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    // 首先尝试获取用户对象中的用户名
                    Object user = invokeMethod(details, "getUser");
                    if (user != null) {
                        String userName = (String) invokeMethod(user, "getUserName");
                        if (userName != null) {
                            return userName;
                        }
                    }

                    // 如果没有用户对象，尝试直接从认证主体获取
                    Object principal = authentication.getPrincipal();
                    if (principal instanceof String) {
                        return (String) principal;
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current user name", e);
        }
        return null;
    }

    /**
     * 获取当前登录用户的登录ID
     *
     * @return 登录ID，未登录时返回null
     */
    public static String getCurrentLoginId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    Object user = invokeMethod(details, "getUser");
                    if (user != null) {
                        return (String) invokeMethod(user, "getLoginId");
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current login ID", e);
        }
        return null;
    }

    /**
     * 获取当前用户角色ID字符串
     *
     * @return 角色ID（逗号分隔），未登录时返回null
     */
    public static String getCurrentUserRoles() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    Object user = invokeMethod(details, "getUser");
                    if (user != null) {
                        return (String) invokeMethod(user, "getRoleIds");
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current user roles", e);
        }
        return null;
    }

    /**
     * 获取当前用户角色ID列表
     *
     * @return 角色ID列表，未登录或无角色时返回空列表
     */
    public static List<String> getCurrentUserRoleList() {
        String roles = getCurrentUserRoles();
        if (roles != null && !roles.trim().isEmpty()) {
            return Arrays.asList(roles.split(","));
        }
        return List.of();
    }

    /**
     * 获取当前用户部门ID
     *
     * @return 部门ID，未登录时返回null
     */
    public static Long getCurrentUserDepartmentId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    Object user = invokeMethod(details, "getUser");
                    if (user != null) {
                        return (Long) invokeMethod(user, "getDepartmentId");
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current user department ID", e);
        }
        return null;
    }

    /**
     * 获取当前用户部门名称
     *
     * @return 部门名称，未登录时返回null
     */
    public static String getCurrentUserDepartmentName() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    Object user = invokeMethod(details, "getUser");
                    if (user != null) {
                        return (String) invokeMethod(user, "getDepartmentName");
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current user department name", e);
        }
        return null;
    }

    /**
     * 获取当前用户的别名
     *
     * @return 用户别名，未登录时返回null
     */
    public static String getCurrentUserAlias() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    Object user = invokeMethod(details, "getUser");
                    if (user != null) {
                        return (String) invokeMethod(user, "getAlias");
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current user alias", e);
        }
        return null;
    }

    /**
     * 获取当前用户的登录类型
     *
     * @return 登录类型，未登录时返回null
     */
    public static String getCurrentLoginType() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object details = authentication.getDetails();
                if (details != null) {
                    Object user = invokeMethod(details, "getUser");
                    if (user != null) {
                        return (String) invokeMethod(user, "getLoginType");
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get current login type", e);
        }
        return null;
    }

    /**
     * 检查用户是否已登录
     *
     * @return true表示已登录，false表示未登录
     */
    public static boolean isUserLoggedIn() {
        return getCurrentUserId() != null;
    }

    /**
     * 检查当前是否有已认证的用户
     *
     * @return true如果有已认证的用户，否则false
     */
    public static boolean hasAuthenticatedUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return authentication != null && authentication.isAuthenticated()
                    && !"anonymousUser".equals(authentication.getPrincipal());
        } catch (Exception e) {
            log.debug("Failed to check authentication status", e);
            return false;
        }
    }

    /**
     * 检查当前用户是否有指定角色
     *
     * @param roleId 角色ID（字符串）
     * @return true表示有该角色，false表示没有或未登录
     */
    public static boolean hasRole(String roleId) {
        if (roleId == null || roleId.trim().isEmpty()) {
            return false;
        }
        List<String> userRoles = getCurrentUserRoleList();
        return userRoles.contains(roleId.trim());
    }

    /**
     * 检查当前用户是否有指定角色
     *
     * @param roleId 角色ID（整数）
     * @return true表示有该角色，false表示没有或未登录
     */
    public static boolean hasRole(Integer roleId) {
        if (roleId == null) {
            return false;
        }
        return hasRole(roleId.toString());
    }

    /**
     * 检查当前用户是否有任意一个指定角色
     *
     * @param roleIds 角色ID数组
     * @return true表示有任意一个角色，false表示都没有或未登录
     */
    public static boolean hasAnyRole(String... roleIds) {
        if (roleIds == null || roleIds.length == 0) {
            return false;
        }
        List<String> userRoles = getCurrentUserRoleList();
        for (String roleId : roleIds) {
            if (roleId != null && userRoles.contains(roleId.trim())) {
                return true;
            }
        }
        return false;
    }

    // ==================== 安全方法（未登录时抛出异常） ====================

    /**
     * 必须获取当前用户ID（未登录时抛出异常）
     *
     * @return 用户ID
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static Integer requireCurrentUserId() {
        Integer userId = getCurrentUserId();
        if (userId == null) {
            throw new IllegalStateException("用户未登录，无法获取用户ID");
        }
        return userId;
    }

    /**
     * 必须获取当前用户名（未登录时抛出异常）
     *
     * @return 用户名
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static String requireCurrentUserName() {
        String userName = getCurrentUserName();
        if (userName == null) {
            throw new IllegalStateException("用户未登录，无法获取用户名");
        }
        return userName;
    }

    /**
     * 必须获取当前用户部门ID（未登录时抛出异常）
     *
     * @return 部门ID
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static Long requireCurrentUserDepartmentId() {
        Long departmentId = getCurrentUserDepartmentId();
        if (departmentId == null) {
            throw new IllegalStateException("用户未登录，无法获取部门ID");
        }
        return departmentId;
    }

    /**
     * 要求用户必须有指定角色（否则抛出异常）
     *
     * @param roleId 角色ID
     * @throws IllegalStateException 用户未登录或没有指定角色时抛出
     */
    public static void requireRole(String roleId) {
        if (!hasRole(roleId)) {
            throw new IllegalStateException("用户没有必需的角色权限: " + roleId);
        }
    }

    /**
     * 要求用户必须有指定角色（否则抛出异常）
     *
     * @param roleId 角色ID
     * @throws IllegalStateException 用户未登录或没有指定角色时抛出
     */
    public static void requireRole(Integer roleId) {
        if (!hasRole(roleId)) {
            throw new IllegalStateException("用户没有必需的角色权限: " + roleId);
        }
    }

    /**
     * 要求用户必须已登录（否则抛出异常）
     *
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static void requireUserLoggedIn() {
        if (!isUserLoggedIn()) {
            throw new IllegalStateException("用户未登录");
        }
    }

    /**
     * 通过反射调用方法，避免直接依赖具体的类
     *
     * @param obj 目标对象
     * @param methodName 方法名
     * @return 方法返回值
     */
    private static Object invokeMethod(Object obj, String methodName) {
        try {
            return obj.getClass().getMethod(methodName).invoke(obj);
        } catch (Exception e) {
            log.debug("Failed to invoke method {} on object {}", methodName, obj.getClass().getSimpleName());
            return null;
        }
    }
}
