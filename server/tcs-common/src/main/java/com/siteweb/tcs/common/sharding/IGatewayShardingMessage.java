package com.siteweb.tcs.common.sharding;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import org.apache.pekko.actor.ActorRef;

import java.io.Serializable;

/**
 * Gateway 分片消息接口
 *
 * <AUTHOR> (2025-05-08)
 **/
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
public interface IGatewayShardingMessage extends Serializable {
    /**
     * 获取分片对象ID 如 GatewayId
     */
    String getGatewayId();

    /**
     * 获取发送者Actor（可空）
     * @return
     */
    ActorRef getSender();
}
