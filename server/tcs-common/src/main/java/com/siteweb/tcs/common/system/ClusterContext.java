package com.siteweb.tcs.common.system;

import com.siteweb.tcs.common.sharding.ShardingExtractor;
import com.typesafe.config.Config;
import lombok.Getter;
import org.apache.pekko.actor.*;
import org.apache.pekko.cluster.Cluster;
import org.apache.pekko.cluster.sharding.ClusterSharding;
import org.apache.pekko.cluster.sharding.ClusterShardingSettings;
import org.apache.pekko.cluster.sharding.ShardCoordinator;
import org.apache.pekko.cluster.sharding.internal.LeastShardAllocationStrategy;
import org.apache.pekko.cluster.singleton.ClusterSingletonManager;
import org.apache.pekko.cluster.singleton.ClusterSingletonManagerSettings;
import org.apache.pekko.cluster.singleton.ClusterSingletonProxy;
import org.apache.pekko.cluster.singleton.ClusterSingletonProxySettings;
import org.apache.pekko.http.javadsl.Http;
import org.apache.pekko.stream.Materializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import scala.Function1;
import scala.concurrent.ExecutionContextExecutor;

/**
 * 应用集群统一上下文
 * 每个TCS应用实例都有一个集群上下文
 *
 * <AUTHOR> (2025-04-18)
 **/
@Component
public class ClusterContext {

    /**
     * 获取本机ActorSystem环境
     */
    @Getter
    private static ActorSystem actorSystem;

    @Getter
    private static ExecutionContextExecutor dispatcher;


    @Getter
    public static ApplicationContext context;

    /**
     * 获取集群管理器
     */
    @Getter
    private static Cluster cluster;

    /**
     * 获取集群Sharding管理器
     */
    @Getter
    private static ClusterSharding clusterSharding;

    /**
     * 获取本地 Materializer
     */
    @Getter
    private static Materializer materializer;

    /**
     * 获取本地 HttpSingen
     */
    @Getter
    private static Http http;


    /**
     * 获取AkkaSystem
     *
     * @return ActorSystem
     */
    public static ActorSystem system() {
        return actorSystem;
    }

    /**
     * 获取ActorSystem 异步上下文执行器
     */
    public static ExecutionContextExecutor dispatcher() {
        return dispatcher;
    }


    /***
     * 使用自定义规则创建集群分片 Sharding
     * <AUTHOR> (2025/4/19)
     * @param shardingName 分片名称
     * @param entityProps 分片的Props生成规则
     * @param extractor 分片策略
     */
    public static ActorRef createGatewaySharding(String shardingName, final Function1<String, Props> entityProps, ShardingExtractor extractor) {
        var shardingSettings = ClusterShardingSettings.create(ClusterContext.system())
                .withRememberEntities(true)  // 启用实体持久化，确保故障恢复
                .withRole("compute");  // 指定分片角色，用于负载均衡

        // 分配策略
        Config config = actorSystem.settings().config().getConfig("pekko.cluster.sharding.least-shard-allocation-strategy");
        int rebalanceThreshold = config.hasPath("rebalance-threshold") ? config.getInt("rebalance-threshold") : 10; // 默认值
        int maxSimultaneousRebalance = config.hasPath("max-simultaneous-rebalance") ? config.getInt("max-simultaneous-rebalance") : 3; // 默认值
        ShardCoordinator.ShardAllocationStrategy allocationStrategy = new LeastShardAllocationStrategy(rebalanceThreshold, maxSimultaneousRebalance);

        // 停止消息
        Object stopMessage = PoisonPill.getInstance();
        //
        return ClusterContext.getClusterSharding().internalStart(shardingName,
                entityProps,
                shardingSettings,
                extractor,
                extractor::extractShardId,
                allocationStrategy,
                stopMessage
        );
    }

    /**
     * 获取当前机器的指定名称分片
     *
     * @param typeName 分片名称
     * @return
     */
    public static ActorRef shardRegion(String typeName) {
        return clusterSharding.shardRegion(typeName);
    }

    /***
     * 注册集群上线事件
     * <AUTHOR> (2025/4/19)
     * @param callback
     */
    public void registerOnMemberUp(final Runnable callback) {
        cluster.registerOnMemberUp(callback);
    }

    /**
     * 注册集群离线事件
     *
     * @param callback
     */
    public void registerOnMemberRemoved(final Runnable callback) {
        cluster.registerOnMemberRemoved(callback);
    }


    /**
     * 关闭 Akka System
     */
    public static void shutdown() {

        if (cluster != null) {
            cluster.shutdown();
            cluster = null;
        }

        if (actorSystem != null) {
            actorSystem.terminate();
            actorSystem = null;
        }
    }


    @Autowired
    private void setApplicationContext(ApplicationContext appContext) {
        context = appContext;
    }

    @Autowired
    private void setBeanAkkaSystem(ActorSystem system) {
        actorSystem = system;
        dispatcher = system.getDispatcher();
    }

    @Autowired
    private void setBeanCluster(Cluster _cluster) {
        cluster = _cluster;
    }

    @Autowired
    private void setBeanClusterSharding(ClusterSharding sharding) {
        clusterSharding = sharding;
    }

    @Autowired
    private void setBeanMaterializer(Materializer mater) {
        materializer = mater;
    }

    @Autowired
    private void setBeanMaterializer(Http httpSingle) {
        http = httpSingle;
    }

    /**
     * 创建全局单例Actor
     *
     * @param singletonClazz  单例名称
     */
    public static ActorRef createSingleton(Class<?> singletonClazz) {
        String singletonName = singletonClazz.getSimpleName();
        // 启动全局Singleton管理器
        ActorRef singletonManager = actorSystem.actorOf(
                ClusterSingletonManager.props(
                        Props.create(singletonClazz),
                        PoisonPill.getInstance(),
                        ClusterSingletonManagerSettings.create(actorSystem)
                ),
                singletonName
        );
        // 创建本地Singleton代理
        return actorSystem.actorOf(
                ClusterSingletonProxy.props(
                        "/user/" + singletonName,
                        ClusterSingletonProxySettings.create(actorSystem)
                ),
                singletonName + "Proxy"
        );
    }


    /**
     * 获取一个单例的代理对象ActorRef
     * @param singleton
     * @return
     */
    public static ActorRef getSingleton(Class<?> singleton){
        String singletonName = singleton.getSimpleName();
        return ClusterContext.system().actorSelection("/user/" + singletonName + "Proxy").anchor();
    }





}
