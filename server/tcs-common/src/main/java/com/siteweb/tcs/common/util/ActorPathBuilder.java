package com.siteweb.tcs.common.util;

import org.apache.pekko.actor.Actor;
import org.apache.pekko.actor.ActorPath;
import org.apache.pekko.actor.ActorSelection;

import java.util.ArrayList;
import java.util.List;

/**
 * ActorPathBuilder 是一个用于构建 Pekko actor 路径的工具类。
 * <p>
 * 使用示例：
 * <p>
 * // 1. 不使用根路径
 * ActorPathBuilder builder = ActorPathBuilder.create();
 * String path = builder
 * .append("deviceChangeKeeper")
 * .append("deviceChangeAdapter")
 * .appendIds("stringId", 12345)
 * .build();
 * // 输出: deviceChangeKeeper/deviceChangeAdapter&_&stringId&_&12345
 * <p>
 * // 2. 使用默认根路径
 * builder = ActorPathBuilder.create();
 * path = builder
 * .withRoot()
 * .append("CustomActor")
 * .appendIds("customId", 67890)
 * .build();
 * // 输出: org.apache.pekko://tcs/user/CustomActor&_&customId&_&67890
 * <p>
 * // 3. 使用自定义根路径
 * builder = ActorPathBuilder.create();
 * path = builder
 * .withRoot("org.apache.pekko://mySystem/user")
 * .append("CustomRootActor")
 * .appendIds("customRootId", 22222)
 * .build();
 * // 输出: org.apache.pekko://mySystem/user/CustomRootActor&_&customRootId&_&22222
 * <p>
 * // 4. 构建多段路径
 * builder = ActorPathBuilder.create();
 * path = builder
 * .withRoot()
 * .append("parent")
 * .append("child")
 * .append("grandchild")
 * .build();
 * // 输出: org.apache.pekko://tcs/user/parent/child/grandchild
 * <p>
 * // 5. 使用 buildActorPath 方法
 * builder = ActorPathBuilder.create();
 * ActorPath actorPath = builder
 * .withRoot()
 * .append("someActor")
 * .buildActorPath();
 * // 返回 ActorPath 对象
 * <p>
 * // 6. 使用 appendWildcard 方法
 * builder = ActorPathBuilder.create();
 * path = builder
 * .withRoot()
 * .append("parent")
 * .appendWildcard()
 * .build();
 * // 输出: org.apache.pekko://tcs/user/parent&_&*
 * <p>
 * // 7. 使用 appendRaw 方法
 * builder = ActorPathBuilder.create();
 * path = builder
 * .withRoot()
 * .append("parent")
 * .appendRaw("&_&customRaw")
 * .build();
 * // 输出: org.apache.pekko://tcs/user/parent&_&customRaw
 */
public class ActorPathBuilder {

    // 单机沿用之前的配置
    // 集群的情况下需要指定Actor所在服务器地址 pekko://TcsSystem@127.0.0.1:2551/user/worker1/task1
    // 所以集群情况下的消息发送使用sharding

    private StringBuilder pathBuilder;
    private static final String SYSTEM_NAME = "tcs";
    private static final String HUB_NAME = "hubEntry";
    private static final String DEFAULT_ROOT = "org.apache.pekko://" + SYSTEM_NAME + "/user";
    private static final String ID_SEPARATOR = "&_&";
    private static final String WILDCARD = "*";


    public static class ActorPathBuilder2 {
        private final int ROOT_INDEX = 2;
        private final Actor actor;
        private final List<String> elements = new ArrayList<>();

        public ActorPathBuilder2(Actor env) {
            actor = env;
            env.self().path().getElements().forEach(elements::add);
        }

        public ActorPathBuilder2 fallback(int level) {
            for (var i = 0; i < level && elements.size() > ROOT_INDEX; i++) {
                elements.remove(elements.size() - 1);
            }
            return this;
        }

        public ActorPathBuilder2 parent() {
            if (elements.size() > ROOT_INDEX) {
                elements.remove(elements.size() - 1);
            }
            return this;
        }

        public ActorSelection toSelection() {
            var path = String.join("/", elements);
            return actor.context().actorSelection(path);
        }

        public ActorPathBuilder2 append(String segment) {
            elements.add(segment);
            return this;
        }

    }


    public ActorPathBuilder() {
        this.pathBuilder = new StringBuilder();
    }

    public static ActorPathBuilder2 from(Actor env) {
        return new ActorPathBuilder2(env);
    }

    public static ActorPathBuilder create() {
        return new ActorPathBuilder();
    }

    /**
     * 设置默认的根路径（org.apache.pekko://tcs/user）。
     *
     * @return 当前的 ActorPathBuilder 实例，用于方法链接。
     */
    public ActorPathBuilder withRoot() {
        return withRoot(DEFAULT_ROOT);
    }

    /**
     * 设置默认的根路径（org.apache.pekko://tcs/user/hub）。
     *
     * @return 当前的 ActorPathBuilder 实例，用于方法链接。
     */
    public ActorPathBuilder withHubRoot() {
        return withRoot(DEFAULT_ROOT).append(HUB_NAME);
    }

    /**
     * 设置自定义的根路径。如果提供的根路径为 null 或空字符串，则使用默认根路径。
     *
     * @param root 自定义的根路径
     * @return 当前的 ActorPathBuilder 实例，用于方法链接。
     */
    public ActorPathBuilder withRoot(String root) {
        this.pathBuilder = new StringBuilder(root != null && !root.isEmpty() ? root : DEFAULT_ROOT);
        return this;
    }

    /**
     * 添加一个路径段到当前路径。
     *
     * @param segment 要添加的路径段
     * @return 当前的 ActorPathBuilder 实例，用于方法链接。
     */
    public ActorPathBuilder append(String segment) {
        if (segment == null || segment.length() == 0) {
            return this;
        }
        if (pathBuilder.length() > 0 && !pathBuilder.toString().endsWith("/")) {
            pathBuilder.append("/");
        }
        pathBuilder.append(segment);
        return this;
    }

    /**
     * 添加一个通配符到当前路径。
     *
     * @return 当前的 ActorPathBuilder 实例，用于方法链接。
     */

    public ActorPathBuilder appendWildcard() {
        pathBuilder.append(ID_SEPARATOR).append(WILDCARD);
        return this;
    }

    /**
     * 添加一个原始字符串到当前路径。
     *
     * @param raw 要添加的原始字符串
     * @return 当前的 ActorPathBuilder 实例，用于方法链接。
     */

    public ActorPathBuilder appendRaw(String raw) {
        pathBuilder.append(raw);
        return this;
    }

    /**
     * 添加一个或多个 ID 到当前路径。ID 之间使用 ID_SEPARATOR 分隔。
     *
     * @param ids 要添加的 ID
     * @return 当前的 ActorPathBuilder 实例，用于方法链接。
     */
    public ActorPathBuilder appendIds(Object... ids) {
        for (Object id : ids) {
            pathBuilder.append(ID_SEPARATOR).append(id.toString());
        }
        return this;
    }

    /**
     * 构建并返回最终的路径字符串。
     *
     * @return 构建的路径字符串
     */
    public String build() {
        return pathBuilder.toString();
    }

    /**
     * 构建并返回 ActorPath 对象。
     *
     * @return 构建的 ActorPath 对象
     */
    public ActorPath buildActorPath() {
        return ActorPath.fromString(build());
    }


    /**
     * 获取用于分隔 ID 的分隔符。
     *
     * @return ID 分隔符
     */
    public static String getIdSeparator() {
        return ID_SEPARATOR;
    }

    @Override
    public String toString() {
        return build();
    }
}
