package com.siteweb.tcs.common.util;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.common.exception.code.StandardTechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.TechnicalException;
import org.apache.ibatis.type.JdbcType;

import java.lang.reflect.Field;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;


/**
 * 增强的Jackson类型处理器
 * 基于JacksonTypeHandler，增强了对不同数据库的支持
 * 支持PostgreSQL、OpenGauss、MySQL、H2、达梦等数据库
 *
 * <AUTHOR> Team
 */


public class EnhancedJacksonTypeHandler<T> extends JacksonTypeHandler {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // 构造函数：接收字段类型（由 MyBatis 自动传入）

    public EnhancedJacksonTypeHandler(Class<?> type) {
        super(type);
    }
    public EnhancedJacksonTypeHandler(Class<?> type, Field field) {
       super(type, field);
    }


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) {
        try {
            // 使用JacksonTypeHandler的序列化逻辑，这会使用配置好的ObjectMapper
            String json = super.toJson(parameter);
            
            // 获取数据库信息
            String databaseProductName = ps.getConnection().getMetaData().getDatabaseProductName().toLowerCase();
            String driverName = ps.getConnection().getMetaData().getDriverName().toLowerCase();
            String url = ps.getConnection().getMetaData().getURL().toLowerCase();
            
            // 针对不同数据库的处理策略
            if (isOpenGauss(databaseProductName, driverName, url)) {
                // OpenGauss：使用专用的PGobject处理
                if (!tryOpenGaussPGobject(ps, i, json) &&
                    !tryOpenGaussStringCast(ps, i, json)) {
                    ps.setString(i, json);
                }
            } else if (isPostgreSQL(databaseProductName, driverName, url)) {
                // 标准PostgreSQL：使用PGobject
                if (!tryPostgreSQLPGobject(ps, i, json)) {
                    ps.setString(i, json);
                }
            } else {
                // 其他数据库（MySQL、达梦、H2等）：直接使用字符串
                ps.setString(i, json);
            }
            
        } catch (Exception e) {
            throw new TechnicalException(StandardTechnicalErrorCode.DATABASE_UPDATE_ERROR, e);
        }
    }

    /**
     * OpenGauss专用：尝试使用PGobject设置jsonb类型
     */
    private boolean tryOpenGaussPGobject(PreparedStatement ps, int i, String json) {
        try {
            // opengauss对json和jsonb敏感
            Class<?> pgObjectClass = Class.forName("org.opengauss.util.PGobject");
            Object pgObject = pgObjectClass.getDeclaredConstructor().newInstance();
            pgObjectClass.getMethod("setType", String.class).invoke(pgObject, "json");
            pgObjectClass.getMethod("setValue", String.class).invoke(pgObject, json);
            ps.setObject(i, pgObject);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * OpenGauss专用：尝试使用字符串并指定JSONB类型
     */
    private boolean tryOpenGaussStringCast(PreparedStatement ps, int i, String json) {
        try {
            ps.setObject(i, json, java.sql.Types.OTHER);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * PostgreSQL专用：尝试使用PGobject设置jsonb类型
     */
    private boolean tryPostgreSQLPGobject(PreparedStatement ps, int i, String json) {
        try {
            Class<?> pgObjectClass = Class.forName("org.postgresql.util.PGobject");
            Object pgObject = pgObjectClass.getDeclaredConstructor().newInstance();
            pgObjectClass.getMethod("setType", String.class).invoke(pgObject, "jsonb");
            pgObjectClass.getMethod("setValue", String.class).invoke(pgObject, json);
            ps.setObject(i, pgObject);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检测是否为OpenGauss数据库
     */
    private boolean isOpenGauss(String databaseProductName, String driverName, String url) {
        return url.contains("opengauss") ||
               url.contains("gaussdb") ||
               driverName.contains("opengauss") ||
               driverName.contains("gaussdb") ||
               databaseProductName.contains("opengauss") ||
               databaseProductName.contains("gaussdb");
    }

    /**
     * 检测是否为标准PostgreSQL数据库
     */
    private boolean isPostgreSQL(String databaseProductName, String driverName, String url) {
        return (databaseProductName.contains("postgresql") ||
                driverName.contains("postgresql") ||
                url.contains("postgresql")) &&
               !isOpenGauss(databaseProductName, driverName, url);
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            String json = rs.getString(columnName);
            return parseJsonToObject(json);
        } catch (Exception e) {
            throw new TechnicalException(StandardTechnicalErrorCode.DATABASE_QUERY_ERROR,
                "Failed to deserialize JSON from column: " + columnName, e);
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            String json = rs.getString(columnIndex);
            return parseJsonToObject(json);
        } catch (Exception e) {
            throw new TechnicalException(StandardTechnicalErrorCode.DATABASE_QUERY_ERROR,
                "Failed to deserialize JSON from column index: " + columnIndex, e);
        }
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            String json = cs.getString(columnIndex);
            return parseJsonToObject(json);
        } catch (Exception e) {
            throw new TechnicalException(StandardTechnicalErrorCode.DATABASE_QUERY_ERROR,
                "Failed to deserialize JSON from callable statement column index: " + columnIndex, e);
        }
    }

    /**
     * 解析JSON字符串为对象
     */
    @SuppressWarnings("unchecked")
    private T parseJsonToObject(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }

        try {
            // 清理JSON字符串，处理可能的双重转义
            json = cleanJsonString(json);

            // 使用父类的反序列化逻辑，这会使用配置好的ObjectMapper和类型信息
            return (T) super.parse(json);
        } catch (Exception e) {
            throw new TechnicalException(StandardTechnicalErrorCode.DATABASE_QUERY_ERROR,
                "Failed to parse JSON string to object: " + json + ", error: " + e.getMessage(), e);
        }
    }

    /**
     * 清理JSON字符串，处理双重转义等问题
     */
    private String cleanJsonString(String json) {
        if (json == null) {
            return null;
        }

        // 去除首尾空白
        json = json.trim();

        // 处理双重转义的情况：如果JSON字符串被双重引号包围，需要去除外层引号并处理转义
        if (json.startsWith("\"") && json.endsWith("\"") && json.length() > 1) {
            // 去除外层引号
            json = json.substring(1, json.length() - 1);
            // 处理转义字符
            json = json.replace("\\\"", "\"")
                      .replace("\\\\", "\\")
                      .replace("\\n", "\n")
                      .replace("\\r", "\r")
                      .replace("\\t", "\t");
        }

        return json;
    }


}
