package com.siteweb.tcs.common.util;

import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.MDC;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 增强的日志工具类，提供更完整的日志记录功能
 * 包括：
 * 1. 国际化支持
 * 2. MDC上下文管理
 * 3. 方法执行时间监控
 * 4. 结构化日志记录
 * 5. 异常详细信息记录
 */
public class EnhancedLogUtils {
    private static final String TRACE_ID = "traceId";
    private static final String USER_ID = "userId";
    private static final String REQUEST_ID = "requestId";
    private static final String MODULE = "module";
    
    @Setter
    private static MessageSource messageSource;
    private static final Map<String, Instant> methodStartTimes = new ConcurrentHashMap<>();

    /**
     * 获取国际化消息
     */
    public static String getMessage(String code, Object... args) {
        if (messageSource == null) {
            return code;
        }
        return messageSource.getMessage(code, args, LocaleContextHolder.getLocale());
    }

    /**
     * 开始一个新的日志追踪上下文
     */
    public static void startContext(String userId, String module) {
        MDC.put(TRACE_ID, generateTraceId());
        MDC.put(REQUEST_ID, generateRequestId());
        MDC.put(USER_ID, userId);
        MDC.put(MODULE, module);
    }

    /**
     * 清理日志追踪上下文
     */
    public static void clearContext() {
        MDC.clear();
    }

    /**
     * 记录方法进入，并开始计时
     */
    public static void logMethodEntry(Logger logger, String methodName, Object... args) {
        String traceKey = generateTraceKey(methodName);
        methodStartTimes.put(traceKey, Instant.now());
        
        if (logger.isDebugEnabled()) {
            logger.debug(getMessage("log.method.entry"), 
                    methodName, 
                    args.length > 0 ? args : "无参数");
        }
    }

    /**
     * 记录方法退出，并计算执行时间
     */
    public static void logMethodExit(Logger logger, String methodName, Object result) {
        String traceKey = generateTraceKey(methodName);
        Instant startTime = methodStartTimes.remove(traceKey);
        
        if (startTime != null && logger.isDebugEnabled()) {
            Duration duration = Duration.between(startTime, Instant.now());
            logger.debug(getMessage("log.method.exit"), 
                    methodName,
                    result != null ? result : "void",
                    duration.toMillis());
        }
    }

    /**
     * 记录业务操作
     */
    public static void logBusinessOperation(Logger logger, String operation, String result, Object... context) {
        logger.info(getMessage("log.business.operation"),
                operation,
                result,
                context);
    }

    /**
     * 记录系统错误，包含详细的异常信息
     */
    public static void logSystemError(Logger logger, String message, Throwable e, Object... context) {
        StringBuilder errorDetails = new StringBuilder()
                .append("\nError Message: ").append(e.getMessage())
                .append("\nError Type: ").append(e.getClass().getName())
                .append("\nStack Trace: ");
                
        for (StackTraceElement element : e.getStackTrace()) {
            errorDetails.append("\n\tat ").append(element.toString());
        }

        logger.error(getMessage("log.system.error"),
                message,
                errorDetails.toString(),
                context,
                e);
    }

    /**
     * 记录业务警告
     */
    public static void logBusinessWarning(Logger logger, String message, Object... context) {
        logger.warn(getMessage("log.business.warning"),
                message,
                context);
    }

    /**
     * 记录性能指标
     */
    public static void logPerformanceMetric(Logger logger, String operation, long duration, Object... context) {
        logger.info(getMessage("log.performance.metric"),
                operation,
                duration,
                context);
    }

    private static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private static String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    private static String generateTraceKey(String methodName) {
        return Thread.currentThread().getId() + "_" + methodName;
    }
}