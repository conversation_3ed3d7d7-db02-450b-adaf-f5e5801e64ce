package com.siteweb.tcs.common.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.common.exception.code.StandardTechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.TechnicalException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * 通用JSON类型处理器
 * 支持多数据库的Map<String, Object>与JSON字段的相互转换
 * 支持PostgreSQL、OpenGauss、MySQL、H2等数据库
 *
 * <AUTHOR> Team
 */
@MappedTypes(Map.class)
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.OTHER})
public class JsonMapTypeHandler extends BaseTypeHandler<Map<String, Object>> {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, Object> parameter, JdbcType jdbcType) {
        try {
            String json = OBJECT_MAPPER.writeValueAsString(parameter);
            
            // 获取数据库信息
            String databaseProductName = ps.getConnection().getMetaData().getDatabaseProductName().toLowerCase();
            String driverName = ps.getConnection().getMetaData().getDriverName().toLowerCase();
            String url = ps.getConnection().getMetaData().getURL().toLowerCase();
            
            // 针对不同数据库的处理策略
            if (isOpenGauss(databaseProductName, driverName, url)) {
                // OpenGauss：使用专用的PGobject处理
                if (!tryOpenGaussPGobject(ps, i, json) &&
                    !tryOpenGaussStringCast(ps, i, json)) {
                    ps.setString(i, json);
                }
            } else if (isPostgreSQL(databaseProductName, driverName, url)) {
                // 标准PostgreSQL：使用PGobject
                if (!tryPostgreSQLPGobject(ps, i, json)) {
                    ps.setString(i, json);
                }
            } else {
                // 其他数据库：直接使用字符串
                ps.setString(i, json);
            }
            
        } catch (Exception e) {
            throw new TechnicalException(StandardTechnicalErrorCode.DATABASE_UPDATE_ERROR, e);
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJsonToMap(json);
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJsonToMap(json);
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJsonToMap(json);
    }

    /**
     * OpenGauss专用：尝试使用PGobject设置jsonb类型
     */
    private boolean tryOpenGaussPGobject(PreparedStatement ps, int i, String json) {
        try {
            Class<?> pgObjectClass = Class.forName("org.opengauss.util.PGobject");
            Object pgObject = pgObjectClass.getDeclaredConstructor().newInstance();
            pgObjectClass.getMethod("setType", String.class).invoke(pgObject, "jsonb");
            pgObjectClass.getMethod("setValue", String.class).invoke(pgObject, json);
            ps.setObject(i, pgObject);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * OpenGauss专用：尝试使用字符串并指定JSONB类型
     */
    private boolean tryOpenGaussStringCast(PreparedStatement ps, int i, String json) {
        try {
            ps.setObject(i, json, java.sql.Types.OTHER);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * PostgreSQL专用：尝试使用PGobject设置jsonb类型
     */
    private boolean tryPostgreSQLPGobject(PreparedStatement ps, int i, String json) {
        try {
            Class<?> pgObjectClass = Class.forName("org.postgresql.util.PGobject");
            Object pgObject = pgObjectClass.getDeclaredConstructor().newInstance();
            pgObjectClass.getMethod("setType", String.class).invoke(pgObject, "jsonb");
            pgObjectClass.getMethod("setValue", String.class).invoke(pgObject, json);
            ps.setObject(i, pgObject);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检测是否为OpenGauss数据库
     */
    private boolean isOpenGauss(String databaseProductName, String driverName, String url) {
        return url.contains("opengauss") ||
               url.contains("gaussdb") ||
               driverName.contains("opengauss") ||
               driverName.contains("gaussdb") ||
               databaseProductName.contains("opengauss") ||
               databaseProductName.contains("gaussdb");
    }

    /**
     * 检测是否为标准PostgreSQL数据库
     */
    private boolean isPostgreSQL(String databaseProductName, String driverName, String url) {
        return (databaseProductName.contains("postgresql") ||
                driverName.contains("postgresql") ||
                url.contains("postgresql")) &&
               !isOpenGauss(databaseProductName, driverName, url);
    }

    /**
     * 解析JSON字符串为Map对象
     */
    private Map<String, Object> parseJsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 清理JSON字符串
            json = cleanJsonString(json);
            return OBJECT_MAPPER.readValue(json, new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new TechnicalException(StandardTechnicalErrorCode.DATABASE_QUERY_ERROR, "Failed to parse JSON string to Map: " + json + ", error: " + e.getMessage(), e);
        }
    }

    /**
     * 清理JSON字符串，处理各种转义情况
     */
    private String cleanJsonString(String json) {
        if (json == null) {
            return null;
        }
        
        // 去除首尾空白字符
        json = json.trim();
        
        // 处理多层转义的情况
        while (json.startsWith("\"") && json.endsWith("\"")) {
            json = json.substring(1, json.length() - 1);
            // 反转义
            json = json.replace("\\\"", "\"")
                      .replace("\\\\", "\\")
                      .replace("\\r\\n", "")
                      .replace("\\n", "")
                      .replace("\\r", "")
                      .replace("\\t", "");
        }
        
        return json;
    }
}
