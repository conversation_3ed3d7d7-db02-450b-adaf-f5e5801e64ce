package com.siteweb.tcs.common.util;

import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
public class LocaleMessageSourceUtil {

    @Resource
    private MessageSource messageSource;

    @Autowired
    Environment environment;

    public String getMessage(String code) {
        return getMessage(code, null);
    }

    /**
     * @param code ：对应messages配置的key.
     * @param args : 数组参数.
     * @return
     */
    public String getMessage(String code, Object[] args) {
        return getMessage(code, args, "");
    }

    /**
     * @param code           ：对应messages配置的key.
     * @param args           : 数组参数.
     * @param defaultMessage : 没有设置key的时候的默认值.
     * @return
     */
    public String getMessage(String code, Object[] args, String defaultMessage) {
        //默认Locale为zh_CN
        Locale locale = Locale.SIMPLIFIED_CHINESE;
        String localeProperty = environment.getProperty("spring.web.locale");
        if (localeProperty != null) {
            String[] splitArray = localeProperty.split("_");
            if (splitArray != null && splitArray.length == 2) {
                locale = new Locale(splitArray[0], splitArray[1]);
            }
        }
        return messageSource.getMessage(code, args, defaultMessage, locale);
    }

    public String getMessageByHeader(String code, String languageHeader) {
        String[] split = languageHeader.split("-");
        Locale locale = new Locale(split[0], split[1]);
        return messageSource.getMessage(code, null, locale);
    }
}
