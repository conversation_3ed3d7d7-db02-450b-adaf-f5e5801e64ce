package com.siteweb.tcs.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Random;
import java.util.regex.Pattern;


public class NumberUtil {

    private static Random random = new Random();

    private NumberUtil() {
        //
    }

    public static boolean isDouble(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
        return pattern.matcher(str).matches();
    }

    // 数字转字符串
    public static String formatNumeric(double numeric, String pattern) {
        if (numeric == -0) {
            numeric = 0;
        }
        DecimalFormat decFormat = new DecimalFormat(pattern);
        return decFormat.format(numeric);
    }

    public static String formatNumeric(BigDecimal numeric, String pattern) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return formatNumeric(numeric.doubleValue(), pattern);
    }

    // 数字转逗号分隔字符串
    public static String formatNumeric(double numeric) {
        return formatNumeric(numeric, "#,##0.00");
    }

    public static String formatNumeric(BigDecimal numeric) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return formatNumeric(numeric.doubleValue());
    }

    // 数字转逗号分隔字符串,附加小数位数(保留8位小数，那么dec参数为6，即，最少要有2位小数)
    public static String formatNumeric(double numeric, int dec) {
        StringBuilder p = new StringBuilder("");
        for (int i = 0; i < dec; i++) {
            p.append("#");
        }
        return formatNumeric(numeric, "#,##0.00" + p);
    }

    public static String formatNumeric(BigDecimal numeric, int dec) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return formatNumeric(numeric.doubleValue(), dec);
    }

    // 数字转逗号分隔字符串；如果数字为零，则返回空
    public static String formatNumericEx(double numeric) {
        String result;
        if (numeric != 0) {
            result = formatNumeric(numeric);
        } else {
            result = "0.00";
        }
        return result;
    }

    public static String formatNumericEx(BigDecimal numeric) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return formatNumericEx(numeric.doubleValue());
    }

    // 将大数字格式化为字符串，避免以科学计数法显示
    public static String formatDouble(double numeric) {
        return formatNumeric(numeric, "#0.00");
    }

    public static String formatDouble(BigDecimal numeric) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return formatDouble(numeric.doubleValue());
    }

    // 将大数字格式化为字符串，避免以科学计数法显示5位
    public static String formatDoubles(double numeric) {
        return formatNumeric(numeric, "#0.00000");
    }

    // 将大数字格式化为字符串，避免以科学计数法显示4位
    public static String formatDoubles(BigDecimal numeric, int dec) {
        StringBuilder p = new StringBuilder("");
        if (dec < 2) {
            dec = 2;
        }
        for (int i = 0; i < dec; i++) {
            p.append("0");
        }

        return formatNumeric(numeric, "#0." + p);
    }

    public static String formatDoubles(BigDecimal numeric) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return formatNumeric(numeric.doubleValue(), "#0.00000");
    }

    public static String formatDoubleWithSix(double numeric) {
        return formatNumeric(numeric, "#0.000000");
    }

    public static String formatDoubleWithSix(BigDecimal numeric) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return formatNumeric(numeric.doubleValue(), "#0.000000");
    }

    // 将形如234，567.00的逗号分隔字符串格式化为Double
    public static double formatNumeric(String str) {
        try {
            return (new DecimalFormat("#,##0.00######")).parse(str)
                    .doubleValue();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 数据非空转换为零
     */
    public static BigDecimal formatNumericNull(BigDecimal numeric) {
        if (numeric == null) {
            numeric = BigDecimal.ZERO;
        }
        return numeric;
    }

    /**
     * 浮点数保留精度
     *
     * @param value    原数值
     * @param accuracy 精度
     *                 默认保留一位精度
     * @return
     */
    public static Double doubleAccuracy(Double value, Integer accuracy) {
        StringBuilder pattern = new StringBuilder("#.0");
        for (int i = 1; i < accuracy; i++) {
            pattern.append("0");
        }
        DecimalFormat df = new DecimalFormat(pattern.toString());
        String result = df.format(value);
        if (result != null && result.length() > 1 && result.substring(0, 1).equals(".")) {
            result = "0" + result;
        }
        return Double.parseDouble(result);
    }

    public static Double toDouble(String str) {
        try {
            return StringUtils.isBlank(str) ? null : Double.valueOf(str);
        } catch (Exception e) {
            return null;
        }
    }
    public static Integer toInteger(String str) {
        try {
            return StringUtils.isBlank(str) ? null : Integer.valueOf(str);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取Double数组的累加值
     *
     * @param dataList
     * @return
     */
    public static Double getSumData(List<Double> dataList) {
        Double sum = 0d;
        for (int i = 0; i < dataList.size(); i++) {
            sum += dataList.get(i);
        }
        return doubleAccuracy(sum, 2);
    }

    /**
     * 获取Double数组中的最小值
     *
     * @param dataList
     * @return
     */
    public static Double getMinData(List<Double> dataList) {
        Double min = dataList.get(0);
        for (int i = 1; i < dataList.size(); i++) {
            if (min > dataList.get(i)) {
                min = dataList.get(i);
            }
        }
        return doubleAccuracy(min, 2);
    }

    /**
     * 获取Double数组中的最大值
     *
     * @param dataList
     * @return
     */
    public static Double getMaxData(List<Double> dataList) {
        Double max = dataList.get(0);
        for (int i = 1; i < dataList.size(); i++) {
            if (max < dataList.get(i)) {
                max = dataList.get(i);
            }
        }
        return doubleAccuracy(max, 2);
    }


    /**
     * 判断字符串内容是否为正整数
     *
     * @param value
     * @return
     */
    public static Boolean checkIsPositiveIntegers(String value) {
        Boolean result = false;
        if (value != null && !value.equals("")) {
            Integer intValue = -1;
            try {
                intValue = Integer.parseInt(value);
            } catch (Exception e) {
                intValue = -1;
            }
            if (intValue > 0) {
                result = true;
            }
        }
        return result;
    }

    //如果是数字，创建new BigDecimal()时肯定不会报错，那就可以直接返回true
    public static boolean isNumeric(String str) {
        try {
            String bigStr = new BigDecimal(str).toString();
            if (bigStr != null) {
                return true;
            }
        } catch (Exception e) {
            return false;//异常 说明包含非数字。
        }
        return true;
    }

    /**
     * 纯数字4位随机数
     *
     * @return
     */
    public static String getRandomCode(int length) {
        String numberArray = "1234567890";
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(numberArray.length());
            char c = numberArray.charAt(index);
            result.append(c);
        }
        return result.toString();

    }


    /**
     * 获取BigDecimal数组中的最大值
     *
     * @param dataList
     * @return
     */
    public static BigDecimal getBigDecimalMaxData(List<BigDecimal> dataList, Integer accuracy) {
        BigDecimal max = dataList.get(0);
        for (int i = 1; i < dataList.size(); i++) {
            if (max.compareTo(dataList.get(i)) < 0) {
                max = dataList.get(i);
            }
        }
        return max.setScale(accuracy, RoundingMode.UP);
    }

    /**
     * 获取BigDecimal数组中的最小值
     *
     * @param dataList
     * @return
     */
    public static BigDecimal getBigDecimalMinData(List<BigDecimal> dataList, Integer accuracy) {
        BigDecimal min = dataList.get(0);
        for (int i = 1; i < dataList.size(); i++) {
            if (min.compareTo(dataList.get(i)) > 0) {
                min = dataList.get(i);
            }
        }
        return min.setScale(accuracy, RoundingMode.UP);
    }

    /**
     * 获取BigDecimal数组的累加值
     *
     * @param dataList
     * @return
     */
    public static BigDecimal getBigDecimalSumData(List<BigDecimal> dataList, Integer accuracy) {
        BigDecimal sum = new BigDecimal(0);
        for (int i = 0; i < dataList.size(); i++) {
            sum = sum.add(dataList.get(i));
        }
        return sum.setScale(accuracy, RoundingMode.UP);
    }
}
