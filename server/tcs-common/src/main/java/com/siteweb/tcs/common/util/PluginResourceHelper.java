package com.siteweb.tcs.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 插件资源加载工具类
 *
 * 提供统一的插件资源加载方法，解决插件环境中 classpath* 资源加载问题
 *
 * <AUTHOR> for Plugin Resource Loading
 */
@Slf4j
@Component
public class PluginResourceHelper {

    @Value("${spring.plugins.runtime-mode:production}")
    private String runtimeMode;

    @Value("${tcs.rootPath:./}")
    private String rootPath;

    /**
     * 获取插件 MyBatis Mapper 资源
     *
     * @param pluginId 插件ID
     * @param mapperPath Mapper文件路径模式，如 "mapper/south-cmcc-plugin/*.xml"
     * @return Mapper资源数组
     * @throws IOException 资源加载异常
     */
    public Resource[] getPluginMapperResources(String pluginId, String mapperPath) throws IOException {
        boolean isDevelopment = "development".equals(runtimeMode);
        Resource[] mapperResources;

        // 获取当前线程的类加载器（插件的类加载器）
        ClassLoader pluginClassLoader = Thread.currentThread().getContextClassLoader();
        log.info("获取插件 {} 的类加载器: {}", pluginId, pluginClassLoader != null ? pluginClassLoader.getClass().getSimpleName() : "默认");
        if (isDevelopment) {
            // 开发模式：使用classpath路径，使用插件的类加载器
            String mapperPattern = "classpath*:" + mapperPath;
            PathMatchingResourcePatternResolver resolver = pluginClassLoader != null
                ? new PathMatchingResourcePatternResolver(pluginClassLoader)
                : new PathMatchingResourcePatternResolver();
            mapperResources = resolver.getResources(mapperPattern);
            log.info("插件 {} [开发模式] Mapper路径: {}, 使用类加载器: {}, 找到 {} 个Mapper文件",
                    pluginId, mapperPattern,
                    pluginClassLoader != null ? pluginClassLoader.getClass().getSimpleName() : "默认",
                    mapperResources.length);
        } else {
            // 生产模式：使用文件系统路径
            String pluginBasePath = System.getProperty("plugin.cmcc.path", rootPath+"plugins");
            String mapperPattern = "file:" + pluginBasePath + "/" + pluginId + "/" + mapperPath;
            PathMatchingResourcePatternResolver resolver = pluginClassLoader != null
                ? new PathMatchingResourcePatternResolver(pluginClassLoader)
                : new PathMatchingResourcePatternResolver();
            mapperResources = resolver.getResources(mapperPattern);
            log.info("插件 {} [生产模式] Mapper路径: {}, 使用类加载器: {}, 找到 {} 个Mapper文件",
                    pluginId, mapperPattern,
                    pluginClassLoader != null ? pluginClassLoader.getClass().getSimpleName() : "默认",
                    mapperResources.length);
        }

        return mapperResources;
    }

    /**
     * 获取插件 Flyway 迁移路径
     *
     * @param pluginId 插件ID
     * @param migrationPath 迁移文件路径，如 "db/south-cmcc-plugin/migration/h2"
     * @return Flyway 迁移路径
     */
    public String getPluginFlywayLocation(String pluginId, String migrationPath) {
        boolean isDevelopment = "development".equals(runtimeMode);
        String flywayLocation;

        if (isDevelopment) {
            // 开发模式：使用classpath路径
            flywayLocation = "classpath:" + migrationPath;
            log.info("插件 {} [开发模式] Flyway迁移路径: {}", pluginId, flywayLocation);
        } else {
            // 生产模式：使用文件系统路径
            String pluginBasePath = System.getProperty("plugin.cmcc.path", rootPath+"plugins");
            String fullMigrationPath = pluginBasePath + "/" + pluginId + "/" + migrationPath;
            flywayLocation = "filesystem:" + fullMigrationPath;
            log.info("插件 {} [生产模式] Flyway迁移路径: {}", pluginId, flywayLocation);
        }

        return flywayLocation;
    }

    /**
     * 获取插件 MyBatis Mapper 资源 (简化版本)
     *
     * @param pluginPath 插件路径，如 "south-cmcc-plugin"、"tcs-south-omc"
     * @return Mapper资源数组
     * @throws IOException 资源加载异常
     */
    public Resource[] getMapperResourcesByPluginPath(String pluginPath) throws IOException {
        String mapperPath = "mapper/" + pluginPath + "/*.xml";
        return getPluginMapperResources(pluginPath, mapperPath);
    }

    /**
     * 获取插件 MyBatis Mapper 资源 (使用指定类加载器)
     *
     * @param pluginPath 插件路径，如 "south-cmcc-plugin"、"tcs-south-omc"
     * @param classLoader 指定的类加载器
     * @return Mapper资源数组
     * @throws IOException 资源加载异常
     */
    public Resource[] getMapperResourcesByPluginPath(String pluginPath, ClassLoader classLoader) throws IOException {
        boolean isDevelopment = "development".equals(runtimeMode);
        Resource[] mapperResources;
        String mapperPath = "mapper/" + pluginPath + "/*.xml";

        if (isDevelopment) {
            // 开发模式：使用classpath路径，使用指定的类加载器
            String mapperPattern = "classpath*:" + mapperPath;
            PathMatchingResourcePatternResolver resolver = classLoader != null
                ? new PathMatchingResourcePatternResolver(classLoader)
                : new PathMatchingResourcePatternResolver();
            mapperResources = resolver.getResources(mapperPattern);
            log.info("插件 {} [开发模式] Mapper路径: {}, 使用指定类加载器: {}, 找到 {} 个Mapper文件",
                    pluginPath, mapperPattern,
                    classLoader != null ? classLoader.getClass().getSimpleName() : "默认",
                    mapperResources.length);
        } else {
            // 生产模式：使用文件系统路径
            String pluginBasePath = System.getProperty("plugin.cmcc.path", rootPath+"plugins");
            String mapperPattern = "file:" + pluginBasePath + "/" + pluginPath + "/" + mapperPath;
            PathMatchingResourcePatternResolver resolver = classLoader != null
                ? new PathMatchingResourcePatternResolver(classLoader)
                : new PathMatchingResourcePatternResolver();
            mapperResources = resolver.getResources(mapperPattern);
            log.info("插件 {} [生产模式] Mapper路径: {}, 使用指定类加载器: {}, 找到 {} 个Mapper文件",
                    pluginPath, mapperPattern,
                    classLoader != null ? classLoader.getClass().getSimpleName() : "默认",
                    mapperResources.length);
        }

        return mapperResources;
    }

    /**
     * 获取插件 Flyway 迁移路径 (简化版本)
     *
     * @param pluginPath 插件路径，如 "south-cmcc-plugin"、"south-omc-siteweb"
     * @param databaseType 数据库类型，如 "h2", "mysql", "postgresql"
     * @return Flyway 迁移路径
     */
    public String getFlywayLocationByPluginPath(String pluginPath, String databaseType) {
        String migrationPath = "db/" + pluginPath + "/migration/" + databaseType.toLowerCase();
        return getPluginFlywayLocation(pluginPath, migrationPath);
    }
}
