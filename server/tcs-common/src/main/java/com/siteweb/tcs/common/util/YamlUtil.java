package com.siteweb.tcs.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;

/**
 * YAML工具类
 * 支持使用中间件文件服务或本地文件系统
 * <AUTHOR> (2024-06-20)
 **/
@Slf4j
@Component
public class YamlUtil {

    private static FileUtil fileUtil;

    /**
     * 设置FileUtil实例（用于依赖注入）
     */
    @Autowired(required = false)
    public void setFileUtil(FileUtil fileUtil) {
        YamlUtil.fileUtil = fileUtil;
    }


    public static void appendToApplicationContext(AnnotationConfigApplicationContext applicationContext, String configName, Path configFile) throws IOException {
        Properties properties = YamlUtil.loadProperties(configFile);
        // 移除非法的配置
        removeIllegalProperties(properties);

        PropertiesPropertySource propertySource = new PropertiesPropertySource(configName, properties);
        applicationContext
                .getEnvironment()
                .getPropertySources()
                .addFirst(propertySource);
    }

    private static void removeIllegalProperties(Properties properties) {
        Iterator<Map.Entry<Object, Object>> iterator = properties.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Object, Object> entry = iterator.next();
            String key = (String) entry.getKey();
            if (key.startsWith("spring") || key.startsWith("server")) {
                iterator.remove();
            }
        }
    }



    /**
     * 加载Properties，优先使用FileUtil，降级到本地文件系统
     */
    public static Properties loadProperties(Path path) throws IOException {
        Properties properties = new Properties();
        Yaml yaml = new Yaml();

        // 尝试使用FileUtil
        if (fileUtil != null) {
            try {
                String fileName = path.getFileName().toString();
                String parentPath = path.getParent() != null ? path.getParent().toString() : "";

                byte[] content = fileUtil.readFile(parentPath, fileName);
                if (content != null) {
                    try (InputStream inputStream = new ByteArrayInputStream(content)) {
                        Map<String, Object> yamlMap = yaml.load(inputStream);
                        if (yamlMap != null) {
                            flattenMap("", yamlMap, properties);
                        }
                    }
                    return properties;
                } else {
                    log.debug("FileUtil未找到文件，降级到本地文件系统: {}", path);
                }
            } catch (Exception e) {
                log.warn("FileUtil读取文件失败，降级到本地文件系统: {}", path, e);
            }
        }

        // 降级到本地文件系统
        return fallbackLoadProperties(path, yaml, properties);
    }

    /**
     * 降级实现：使用本地文件系统加载Properties
     */
    private static Properties fallbackLoadProperties(Path path, Yaml yaml, Properties properties) throws IOException {
        // Ensure the file exists and is readable
        if (Files.exists(path) && Files.isReadable(path)) {
            // Load YAML file into Map
            try (var inputStream = Files.newInputStream(path)) {
                Map<String, Object> yamlMap = yaml.load(inputStream);
                if (yamlMap != null){
                    // Convert Map to Properties
                    flattenMap("", yamlMap, properties);
                }
            }
        } else {
            throw new IOException("Cannot read file: " + path);
        }
        return properties;
    }


    private static void flattenMap(String prefix, Map<String, Object> map, Properties properties) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = prefix + entry.getKey();
            Object value = entry.getValue();
            if (value instanceof Map) {
                flattenMap(key + ".", (Map<String, Object>) value, properties);
            } else {
                properties.put(key, value.toString());
            }
        }
    }


}
