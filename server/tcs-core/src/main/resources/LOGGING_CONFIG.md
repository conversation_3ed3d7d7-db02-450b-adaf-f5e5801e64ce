# 日志配置说明

## 概述

本文档说明了 TCS 系统的日志配置，包括如何控制不同组件的日志级别，以及如何减少数据库相关日志的输出。

## 日志级别说明

### 日志级别从低到高
- `TRACE` - 最详细的日志信息
- `DEBUG` - 调试信息
- `INFO` - 一般信息
- `WARN` - 警告信息
- `ERROR` - 错误信息

### 当前配置的日志级别

#### 生产环境 (默认)
- **根日志级别**: `WARN`
- **业务日志**: `INFO`
- **数据库日志**: `ERROR` 或 `WARN`
- **框架日志**: `WARN`
- **分布式发布/订阅**: `INFO`

#### 开发环境
- **根日志级别**: `INFO`
- **业务日志**: `INFO`
- **数据库日志**: `WARN` 或 `INFO`
- **框架日志**: `INFO`
- **分布式发布/订阅**: `INFO`

## 配置文件说明

### 1. logback-spring.xml
主要的日志配置文件，控制日志的输出格式、文件滚动策略等。

### 2. application.yml
全局日志配置，设置不同包的日志级别。

### 3. application-{profile}.yml
特定环境的日志配置，如 `application-dev.yml`、`application-mysql.yml` 等。

## 如何减少数据库日志

### 1. 关闭 MyBatis-Plus SQL 日志
```yaml
mybatis-plus:
  configuration:
    # 关闭SQL日志输出
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
```

### 2. 调整数据库相关包的日志级别
```yaml
logging:
  level:
    org.hibernate: ERROR
    org.mybatis: ERROR
    com.baomidou: ERROR
    com.zaxxer.hikari: ERROR
    org.springframework.jdbc: WARN
    org.springframework.transaction: WARN
```

### 3. 保留重要的业务日志
```yaml
logging:
  level:
    com.siteweb.tcs: INFO
    com.siteweb.tcs.hub: INFO
    com.siteweb.tcs.north: INFO
    com.siteweb.tcs.south: INFO
```

## 分布式发布/订阅日志

为了调试分布式发布/订阅功能，以下包的日志级别设置为 `INFO`：

```yaml
logging:
  level:
    # 分布式发布/订阅相关日志
    com.siteweb.tcs.hub.domain.v2.process.lifecycle: INFO
    com.siteweb.tcs.north.s6.connector.process: INFO
    org.apache.pekko.cluster.pubsub: INFO
```

## 切换环境

### 开发环境
```bash
# 使用开发环境配置
java -jar tcs.jar --spring.profiles.active=dev
```

### 生产环境
```bash
# 使用生产环境配置（默认）
java -jar tcs.jar
# 或者明确指定
java -jar tcs.jar --spring.profiles.active=mysql
```

## 日志文件位置

- **控制台**: 标准输出
- **文件**: `./logs/siteweb-tcs/` 目录下
- **文件滚动**: 按日期和大小滚动，最大 10MB，保留 30 天

## 常见问题

### 1. 日志太多，控制台被冲掉
- 检查根日志级别是否设置过高
- 检查特定包的日志级别设置
- 使用 `WARN` 或 `ERROR` 级别减少日志输出

### 2. 看不到重要的业务日志
- 确保业务相关包的日志级别设置为 `INFO` 或更低
- 检查是否有其他配置覆盖了日志级别

### 3. 数据库日志仍然很多
- 检查 MyBatis-Plus 的 `log-impl` 配置
- 检查数据库相关包的日志级别设置
- 检查连接池的日志级别设置

## 自定义日志配置

如果需要为特定包设置自定义的日志级别，可以在 `application.yml` 中添加：

```yaml
logging:
  level:
    com.your.package: DEBUG
    org.your.framework: WARN
```

## 注意事项

1. **重启应用**: 修改日志配置后需要重启应用才能生效
2. **性能影响**: 日志级别越低，性能影响越大
3. **磁盘空间**: 注意日志文件的滚动策略，避免占用过多磁盘空间
4. **生产环境**: 生产环境建议使用 `WARN` 或 `ERROR` 级别
