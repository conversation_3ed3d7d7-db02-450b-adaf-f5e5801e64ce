# 开发环境配置
spring:
  profiles:
    active: dev

# 开发环境日志配置
logging:
  level:
    # 根日志级别 - 开发环境可以稍微详细一些
    root: INFO
    # 业务相关日志级别
    com.siteweb.tcs: INFO
    # 分布式发布/订阅相关日志 - 开发环境需要详细日志
    com.siteweb.tcs.hub.domain.v2.process.lifecycle: INFO
    com.siteweb.tcs.north.s6.connector.process: INFO
    # 数据库相关日志级别 - 开发环境可以显示一些SQL
    org.springframework.jdbc: INFO
    org.springframework.transaction: INFO
    org.hibernate: WARN
    org.mybatis: WARN
    com.baomidou: WARN
    # 框架日志级别
    org.springframework.web: INFO
    org.springframework.boot: INFO
    org.springframework.security: WARN
    org.springframework.cache: WARN
    org.springframework.data: WARN
    # 连接池日志级别
    com.zaxxer.hikari: WARN
    # 数据库驱动日志级别
    com.mysql: WARN
    org.postgresql: WARN
    # Pekko 相关日志级别 - 开发环境需要详细日志
    org.apache.pekko.cluster.pubsub: INFO
    org.apache.pekko.cluster: INFO
    org.apache.pekko.remote: INFO
  pattern:
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%15.15t] %-40.40logger{39} : %m%n"

# 开发环境数据库配置
mybatis-plus:
  configuration:
    # 开发环境可以显示SQL日志，但不要显示参数
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
