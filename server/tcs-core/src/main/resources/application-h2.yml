spring:
  application:
    name: tcs
  datasource:
    url: jdbc:h2:mem:tcs_hub;DB_CLOSE_DELAY=-1;MODE=MySQL
    username: sa
    password:
    driver-class-name: org.h2.Driver
    hikari:
      pool-name: Retail_HikariCP
      minimum-idle: 5
      idle-timeout: 180000
      maximum-pool-size: 200
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  # Redis disabled
  data:
   redis:
     database: 0
     host: *************
     port: 6379
     password: siteweb1!
     jedis:
       pool:
         max-active: 200
         max-wait: -1
         max-idle: 10
         min-idle: 0
       timeout: 1000
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    deserialization:
      fail-on-unknown-properties: false
  main:
    banner-mode: console
    pathmatch:
      matching-strategy: ant_path_matcher
    date-format: yyyy-MM-dd HH:mm:ss
    servlet:
      load-on-startup: 1
      path: /api/thing
    throw-exception-if-no-handler-found: true
  web:
    locale: zh_CN
    locale-resolver: fixed
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  messages:
    basename: i18n/messages
    cache-duration: -1s
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        trace: false
        web-allow-others: true

# H2特有配置
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: update
    show-sql: true

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*Mapper.xml, classpath:mapper/h2/*.xml

# Logging configuration
logging:
  level:
    root: info
    org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: DEBUG

  pattern:
    file: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
  config: classpath:logback-spring.xml