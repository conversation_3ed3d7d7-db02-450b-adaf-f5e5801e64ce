spring:
  application:
    name: tcs
  datasource:
    url: *************************************************************************************************************************************************************************************************************************************************************************************************
    username: postgres
    password: Vertiv@086
    driver-class-name: org.opengauss.Driver
    hikari:
      pool-name: Retail_HikariCP
      minimum-idle: 5
      idle-timeout: 180000
      maximum-pool-size: 200
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  data:
    redis:
      database: 0
      host: localhost
      port: 6379
      password: siteweb1!
      jedis:
        pool:
          max-active: 200
          max-wait: -1
          max-idle: 10
          min-idle: 0
        timeout: 1000
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    deserialization:
      fail-on-unknown-properties: false
  main:
    banner-mode: console
    pathmatch:
      matching-strategy: ant_path_matcher
    date-format: yyyy-MM-dd HH:mm:ss
    servlet:
      load-on-startup: 1
      path: /api/thing
    throw-exception-if-no-handler-found: true
  web:
    locale: zh_CN
    locale-resolver: fixed
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  messages:
    basename: i18n/messages
    cache-duration: -1s

# openGauss特有配置
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          lob:
            non_contextual_creation: true

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    database-id: postgres
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.siteweb.tcs.entity