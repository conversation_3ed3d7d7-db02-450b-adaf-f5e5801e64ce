spring:
  application:
    name: tcs
  datasource:
    url: ********************************************************************
    username: postgres
    password: Vertiv@086
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: Retail_HikariCP
      minimum-idle: 5
      idle-timeout: 180000
      maximum-pool-size: 200
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  data:
    redis:
      database: 0
      host: localhost
      port: 6379
      password: siteweb1!
      jedis:
        pool:
          max-active: 200
          max-wait: -1
          max-idle: 10
          min-idle: 0
        timeout: 1000
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    deserialization:
      fail-on-unknown-properties: false
  main:
    banner-mode: console
    pathmatch:
      matching-strategy: ant_path_matcher
    date-format: yyyy-MM-dd HH:mm:ss
    servlet:
      load-on-startup: 1
      path: /api/thing
    throw-exception-if-no-handler-found: true
  web:
    locale: zh_CN
    locale-resolver: fixed
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  messages:
    basename: i18n/messages
    cache-duration: -1s

# PostgreSQL特有配置
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          lob:
            non_contextual_creation: true

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 关闭SQL日志输出，减少控制台日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    map-underscore-to-camel-case: true
    database-id: postgres
  mapper-locations: classpath*:mapper/*Mapper.xml, classpath:mapper/postgres/*.xml
  global-config:
    banner: false
    db-config:
      naming-strategy: com.siteweb.tcs.common.config.CustomPhysicalNamingStrategy

# Logging configuration
logging:
  level:
    root: info
    org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: DEBUG

  pattern:
    file: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
  config: classpath:logback-spring.xml