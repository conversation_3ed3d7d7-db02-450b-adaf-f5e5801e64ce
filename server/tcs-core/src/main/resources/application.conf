# pekko {
# 
# extensions = [org.apache.pekko.persistence.Persistence]
# 
# persistence {
# 
# journal {
# plugin = "org.apache.pekko.persistence.journal.leveldb"
# auto-start-journals = ["org.apache.pekko.persistence.journal.leveldb"]
# }
# 
# //snapshot-store {
# //plugin = "org.apache.pekko.persistence.snapshot-store.local"
# //auto-start-snapshot-stores = ["org.apache.pekko.persistence.snapshot-store.local"]
# //}
# 
# }
# 
# }

pekko {
  # 使用集群作为 Actor 提供者
  loglevel = debug
  actor.system-name = "TcsSystem"
  loggers = ["org.apache.pekko.event.slf4j.Slf4jLogger"]
  
  # 启用分布式发布/订阅扩展
  extensions = ["org.apache.pekko.cluster.pubsub.DistributedPubSub"]
  
  actor {
    provider = cluster
    allow-java-serialization = on
    # Add serialization bindings for better compatibility
    serializers {
      jackson-json = "org.apache.pekko.serialization.jackson.JacksonJsonSerializer"
      # jackson-cbor = "org.apache.pekko.serialization.jackson.JacksonCborSerializer"
      # proto = "org.apache.pekko.remote.serialization.ProtobufSerializer"
    }
    serialization-bindings {
      "java.io.Serializable" = java
      # Custom message classes - use Jackson JSON for better compatibility
      "com.siteweb.tcs.common.ISerializableMessage" = jackson-json
      "com.siteweb.tcs.common.sharding.IGatewayShardingMessage" = jackson-json
    }
  }
  
  # HttpClient
  http {
    client {
      parsing {
        max-content-length = 10m
      }
      log-unencrypted-network-bytes = 1024
    }
    server {
      remote-address-header = on
      parsing {
        max-content-length = 10m
      }
    }
  }
  
  
  # Configure serializers
  serializers {
    jackson-json = "org.apache.pekko.serialization.jackson.JacksonJsonSerializer"
    jackson-cbor = "org.apache.pekko.serialization.jackson.JacksonCborSerializer"
    proto = "org.apache.pekko.remote.serialization.ProtobufSerializer"
  }
  
  # Configure serialization bindings for all message types
  serialization-bindings {
    # Default fallback for Serializable objects
    "java.io.Serializable" = java
    
    # Custom message classes - use Jackson JSON for better compatibility
    "com.siteweb.stream.common.messages.StreamMessage" = jackson-json
    "com.siteweb.tcs.common.system.IShardingMessage" = jackson-json
    # Add other common message types
    "java.util.ArrayList" = java
    "java.util.HashMap" = java
    "java.lang.String" = java
    "java.lang.Long" = java
    "java.lang.Integer" = java
    "java.time.LocalDateTime" = jackson-json
  }
  
  # 配置远程通信
  remote {
    artery {
      canonical {
        hostname = 127.0.0.1
        port = 2551
      }
      transport = tcp
      
      # Add connection timeout settings
      advanced {
        connection-timeout = 15s
        handshake-timeout = 15s
      }
    }
    
    log-remote-lifecycle-events = on
  }
  
  # 配置集群
  cluster {
    # Fix: Use only the primary seed node first
    # The secondary node (this one) should join the primary seed node
    seed-nodes = [
      "pekko://TcsSystem@127.0.0.1:2551"
    ]
    
    # IMPORTANT: Add cluster roles - this fixes the sharding coordinator issue
    roles = ["compute", "backend"]
    
    # Important: Add initialization settings
    seed-node-timeout = 30s
    shutdown-after-unsuccessful-join-seed-nodes = 60s
    
    # Allow this node to join itself if it's the first node
    allow-weakly-up-members = off
    
    # Add retry settings for seed node joining
    retry-unsuccessful-join-after = 10s
    
    # 调试配置
    debug {
      verbose-heartbeat = on
      verbose-membership = on
    }
    
    # 配置脑裂解决策略
    downing-provider-class = "org.apache.pekko.cluster.sbr.SplitBrainResolverProvider"
    
    # Add Split Brain Resolver configuration
    split-brain-resolver {
      active-strategy = keep-majority
      stable-after = 20s
      
      keep-majority {
        role = ""
      }
    }
    
    # 配置分片
    sharding {
      # 使用分布式数据存储分片状态 - disable LMDB for Java 11+ compatibility
      state-store-mode = ddata
      
      # Disable durable storage to avoid LMDB issues
      distributed-data {
        durable {
          keys = [] # Empty list disables LMDB usage
        }
      }
      
      # 记忆分片分配状态
      remember-entities = on
      
      # 使用一致性哈希分片策略
      
      # Add sharding coordinator settings
      coordinator-failure-backoff = 5s
      retry-interval = 2s
      buffer-size = 100000
      
      # Ensure proper coordination timing
      waiting-for-state-timeout = 5s
      
      # 分片分配策略
      least-shard-allocation-strategy {
        # 节点间分片数量差异超过此阈值时触发重平衡
        rebalance-threshold = 10
        
        # 每次最多迁移的分片数量
        max-simultaneous-rebalance = 3
      }
    }
  }
}
