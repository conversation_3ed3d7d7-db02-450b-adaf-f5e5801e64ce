spring:
  profiles:
    active: h2
  servlet:
    multipart:
#      单次请求文件大小设置
      max-file-size: 100MB
      max-request-size: 120MB
  flyway:
    enabled: true
    baseline-on-migrate: true
    out-of-order: true
    locations: classpath:sql
    table: tcs_flyway_schema_history
---
spring:
  mvc:
    servlet:
      load-on-startup: 1



---
spring:
  plugins:
    #启用开关
    enabled: false
    #运行模式：
    runtime-mode: @spring.plugins.runtime-mode@
    #扩展插件目录 【默认项目./plugins】
    path: plugins
    #备份插件目录 【默认项目./backup】
    backup-path: backup
    #调试插件路径，当runtime-mode 为 development时，直接使用idea调试
    dev-path:
#      - tcs-south-crcc
#      - tcs-south-cucc
      - tcs-south-omc-siteweb
      - tcs-north-s6
      - tcs-south-cmcc
#      - tcs-south-cucc
#      - tcs-south-cmcc
#      - tcs-south-seed
      # 暂时禁用所有插件
#       - tcs-south-ctcc
      # - tcs-north-test
      # - tcs-south-simulator
      # - tcs-south-modbus
      # - tcs-south-cmcc
---
# SPA 客户端配置
spring:
  boot:
    admin:
      client:
        enabled: false
        url: http://localhost:9800
  #        username: admin
  #        password: admin
        instance:
          service-url: http://localhost:9700
          service-host-type: IP
          metadata:
            tags:
              environment: test
              service-test-1: A large content
              service-test-2: A large content
              service-test-3: A large content
            service-test-4: A large content
            service-test-5: A large content
          prefer-ip: true
# SPA的EndPoints默认配置
management:
  health:
    redis:
      enabled: false
  endpoints:
    enabled-by-default: true
    web:
      base-path: /api/thing/manage
      exposure:
        include: '*'
  endpoint:
    refresh:
      enabled: true
    restart:
      enabled: true
    shutdown:
      enabled: true
    env:
      post:
        enabled: true
    health:
      show-details: ALWAYS
    logfile:
      enabled: true
      external-file: ./logs/siteweb-tcs.log
  trace:
    http:
      enabled: true

info:
  appName: boot-admin
  appVersion: 1.0.0

# Siteweb 配置服务客户端连接的服务器 - 暂时禁用
# http://*************:8011
siteweb:
 config:
   client:
     baseUrl: http://*************:8011
     idPrefix: thing-client

# ---
# spring:
#   main:
#     allow-bean-definition-overriding: true

webAuth:
  jwt:
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC/DZ8QrtQaoyFwO/fBno3LguBv2CWsUqJlCjh0opI3jrDnI8bTIRGotKLrknT0GSEXw5CtakEAa1C8tudwJ5nL09UAvQXRAgH23d/P6HSQVjDwh/2VIf9j9hh6meJGH/7xFr4HRMyB/cVHgqwvmiCNZIbvCWbyijcTXr0VPkPzL4sWF2pgdvJZzmgKkAz0xcOHqUfqp5YsyEIRovX8xmp2qcBzvpxHZYkaYujv/cO7FNc9bOA6dhh5ayZu3MeAU313g72nqt/nA4yTWq27d0zkKfdy17ZhKA+vYvLJKlrv+8tO5UDpIpB0vQhKd7+S2w9r7tHVGfezUWCZgKxZ/LL3AgMBAAECggEBALK1FRLP1crMyJxpG4javJueYj18G1EjQo/sjX5cCxU4vbSXPIWEqzX5MWPU7NzfHJtT7OKpPwAbYbwEAlxgTnXgQZ+dL/GfRSMbyxx4vX+9f62eJs72rCNesOsNQiCCEUCGG15FNl5pd706N8GXE9fuLmEtlEROkNHnjkpuobS4JWhGGJci4mNQqw4ZgCOuUr5YZ4QA/Mb6BBAu6bcvu6es23oGB84ZcxpMjh69PSwfiYFFsppuiJflzjQjohtyv99MqstDRvrzPi+i/T3qZ2W9FRUuP1pf/dMaamg7PoTAoOgJjKbSo7aEgLZIo/SF7TKnjtB6SmBnlWxi7HLyKyECgYEA+PP0CbhnkcVolsCQtLwa4lL/3mOurjjtgCWYXBkYjbZ9vJgGWw2oJ+HUBNhpEa7ELrzjUvXumgqCnicDeUC/9QJDCf029ldGje9GkrHsOSbU6IHzzdGbmYHwgfnjB6vFwZx5VjvT2t3VBMYeet5HqrL33KD+WxRYziel6URSbKkCgYEAxHYYcw0ORtq57g8N7SakScNE2oVf+/1H3WqSUJDg+T38WEkxU9M6sqnJ9q2v9QSR2QynxS0SZKxvwJc4LzUbiHMP7v20xhBrA5+a8kzjPqKlM50p0C/Nkt7pSwFiX+LwFQlMUV9tWdY/Z6fBhKks9uxpEPuCbCjDlCg0T0iNRp8CgYEA7WinlwV2LztUrD7jQJgKAz8npsrk8Fx1kTlI/LsqASrA6bMIjJiPfckMSbqfKC/EAtY66wiBDAFt4qhN1bn71QjdKY+CdJVyQTSn1ok6Pp5bd4dGG0cC3fdehnTpHo2evy4bQDM5q4TU+gJ9WqrTKWQWnx4gsnbK4X5J6BQxjlECgYBxtZi5Hplg0UBEVVpOJMt6FhdIE2JWy2ZI9WHyV6ifGg1wXAy848lZl4RZznXFbvurkPOZ4FiBBH06D0xppmdlNpPGU/nJmb8Wvc5E59OvcRwFH7YP1Vs64uJMk2SI8yTaSCNwBbeZA7R3HlWXnwNzd6noNmpqh72LhymfqfJ7KQKBgEjDxUq7q3KU0KeYiitT3WGOHQ5kGmfEwNFjuxMqJohyyJ4Z3ajHbIbrm2TZ5Wq3Ims+XcQFiOhZ8UIIUiOldjgFPlL5sBWLyh2Q0MO8lvBH+AJVAsirmNSTHzCIcvGDLEHnppSnmBOtC89h6n8Hdcz2Ybd1kVuYzrVXBnodakL0
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2fEK7UGqMhcDv3wZ6Ny4Lgb9glrFKiZQo4dKKSN46w5yPG0yERqLSi65J09BkhF8OQrWpBAGtQvLbncCeZy9PVAL0F0QIB9t3fz+h0kFYw8If9lSH/Y/YYepniRh/+8Ra+B0TMgf3FR4KsL5ogjWSG7wlm8oo3E169FT5D8y+LFhdqYHbyWc5oCpAM9MXDh6lH6qeWLMhCEaL1/MZqdqnAc76cR2WJGmLo7/3DuxTXPWzgOnYYeWsmbtzHgFN9d4O9p6rf5wOMk1qtu3dM5Cn3cte2YSgPr2LyySpa7/vLTuVA6SKQdL0ISne/ktsPa+7R1Rn3s1FgmYCsWfyy9wIDAQAB
  token:
    expireMinute: 30
  # 开发模式配置 - 用于跳过Token校验，方便调试
  development:
    skipTokenValidation: true  # 设置为 true 时跳过 Token 校验

# 中间件配置
middleware:
  encryption:
    enabled: true
    secret-key: ${MIDDLEWARE_ENCRYPTION_KEY:default-dev-key-not-for-production}

  # 资源配置
  resource:
    # 资源健康检查配置
    health-check:
      enabled: true
      initial-delay: 60000  # 启动后延迟60秒进行首次健康检查
      fixed-delay: 300000   # 每5分钟进行一次健康检查

  # 服务配置
  service:
    # 服务健康检查配置
    health-check:
      enabled: true
      initial-delay: 90000  # 启动后延迟90秒进行首次健康检查
      fixed-delay: 300000   # 每5分钟进行一次健康检查
#debug: true
server:
  port: 8550
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html
    min-response-size: 102400
  replica-count: 1 # 1-单机 2-双机 3-

# Pekko cluster configuration
pekko:
  cluster:
    is-seed-node: true
    hostname: ${PEKKO_HOSTNAME:*************}
    port: ${PEKKO_PORT:2551}
    seed-nodes: ${PEKKO_SEED_NODES:pekko://TcsSystem@*************:2551}

# 雪花算法生成配置
uid:
  timeBits: 30             # 时间位, 默认:30，大概可以使用34年
  workerBits: 16           # 机器位, 默认:16 可以重启65535次
  seqBits: 17               # 序列号, 默认:17 一秒生成131072个id
  epochStr: "2019-02-20"   # 初始时间, 默认:"2019-02-20" 占位，程序重启后会替换为重启时的年月日
  enableBackward: true    # 是否容忍时钟回拨, 默认:true
  maxBackwardSeconds: 1    # 时钟回拨最长容忍时间（秒）, 默认:1
  CachedUidGenerator:     # CachedUidGenerator相关参数
    boostPower: 3          # RingBuffer size扩容参数, 可提高UID生成的吞吐量, 默认:3
    paddingFactor: 50      # 指定何时向RingBuffer中填充UID, 取值为百分比(0, 100), 默认为50

tcs:
  filesystem:
    type: local
    rootPath: "./"
#tcs:
#  filesystem:
#    type: minio
#    enableAuth: true
#    username: "admin"
#    password: "admin123"
#    url: "http://10.163.96.113:9001?bucketName=test-bucket"

#tcs:
#  filesystem:
#    type: zookeeper
#    rootPath: "/filesystem"
#    enableAuth: false  # 启用ACL时需要
#    url: "192.168.56.104:2181"
#tcs:
#  filesystem:
#    type: consul
#    rootPath: "filesystem"
#    enableAuth: false
#    url: "http://10.169.42.153:8500"

# 日志配置
logging:
  level:
    # 根日志级别
    root: WARN
    # 业务相关日志级别
    com.siteweb.tcs: INFO
    # 分布式发布/订阅相关日志
    com.siteweb.tcs.hub.domain.v2.process.lifecycle: INFO
    com.siteweb.tcs.north.s6.connector.process: INFO
    # 数据库相关日志级别
    org.springframework.jdbc: WARN
    org.springframework.transaction: WARN
    org.hibernate: ERROR
    org.mybatis: ERROR
    com.baomidou: ERROR
    # 框架日志级别
    org.springframework.web: WARN
    org.springframework.boot: WARN
    org.springframework.security: WARN
    org.springframework.cache: WARN
    org.springframework.data: WARN
    # 连接池日志级别
    com.zaxxer.hikari: ERROR
    # 数据库驱动日志级别
    com.mysql: ERROR
    org.postgresql: ERROR
    # Pekko 相关日志级别
    org.apache.pekko.cluster.pubsub: INFO
    org.apache.pekko.cluster: WARN
    org.apache.pekko.remote: WARN
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%15.15t] %-40.40logger{39} : %m%n"
