CREATE TABLE `tbl_devicesubtypecmcc` (
  `<PERSON><PERSON><PERSON>ypeID` INT NOT NULL,
  `<PERSON><PERSON>SubTypeID` INT NOT NULL,
  `DeviceSubTypeName` VARCHAR(128) DEFAULT NULL,
  `Description` TEXT,
  PRIMARY KEY (`DeviceSubTypeID`,`DeviceTypeID`)
);

CREATE TABLE `tbl_devicetypecmcc` (
  `DeviceTypeID` INT NOT NULL,
  `DeviceTypeName` VARCHAR(128) DEFAULT NULL,
  `Description` TEXT,
  PRIMARY KEY (`DeviceTypeID`)
);

CREATE TABLE `tbl_equipmentcmcc` (
  `StationId` INT NOT NULL,
  `MonitorUnitId` INT NOT NULL,
  `EquipmentId` INT NOT NULL,
  `DeviceID` VARCHAR(255) DEFAULT NULL,
  `DeviceName` VARCHAR(255) DEFAULT NULL,
  `FSUID` VARCHAR(255) DEFAULT '',
  `SiteID` VARCHAR(255) DEFAULT '',
  `SiteName` VARCHAR(255) DEFAULT NULL,
  `RoomID` VARCHAR(255) DEFAULT NULL,
  `RoomName` VARCHAR(255) DEFAULT NULL,
  `DeviceType` INT DEFAULT NULL,
  `DeviceSubType` INT DEFAULT NULL,
  `Model` VARCHAR(255) DEFAULT NULL,
  `Brand` VARCHAR(255) DEFAULT NULL,
  `RatedCapacity` DOUBLE DEFAULT NULL,
  `Version` VARCHAR(20) DEFAULT NULL,
  `BeginRunTime` TIMESTAMP DEFAULT NULL,
  `DevDescribe` VARCHAR(255) DEFAULT NULL,
  `ExtendField1` VARCHAR(255) DEFAULT NULL,
  `ExtendField2` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);
CREATE INDEX `TBL_EquipmentCMCC_IDX1` ON `tbl_equipmentcmcc` (`StationId`,`EquipmentId`);
CREATE INDEX `TBL_EquipmentCMCC_IDX2` ON `tbl_equipmentcmcc` (`FSUID`,`DeviceID`);

CREATE TABLE `tbl_equipmentcucc` (
  `StationId` INT NOT NULL,
  `MonitorUnitId` INT NOT NULL,
  `EquipmentId` INT NOT NULL,
  `DeviceID` VARCHAR(255) DEFAULT NULL,
  `DeviceName` VARCHAR(255) DEFAULT NULL,
  `DeviceRID` VARCHAR(255) DEFAULT NULL,
  `SUID` VARCHAR(255) DEFAULT NULL,
  `DeviceVender` VARCHAR(255) DEFAULT NULL,
  `DeviceType` VARCHAR(255) DEFAULT NULL,
  `MFD` VARCHAR(255) DEFAULT NULL,
  `ControllerType` VARCHAR(255) DEFAULT NULL,
  `SoftwareVersion` VARCHAR(255) DEFAULT NULL,
  `BatchNo` VARCHAR(255) DEFAULT NULL,
  `Password` VARCHAR(512) DEFAULT NULL,
  `ExtendField1` VARCHAR(255) DEFAULT NULL,
  `ExtendField2` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);
CREATE INDEX `TBL_EquipmentCUCC_IDX1` ON `tbl_equipmentcucc` (`StationId`,`EquipmentId`);
CREATE INDEX `IDX_EquipmentCUCC_SU_DeviceId` ON `tbl_equipmentcucc` (`SUID`,`DeviceID`);

CREATE TABLE `tbl_roomcmcc` (
  `StationId` INT NOT NULL,
  `HouseId` INT NOT NULL,
  `RoomID` VARCHAR(125) DEFAULT NULL,
  `RoomName` VARCHAR(255) DEFAULT NULL,
  `SiteID` VARCHAR(255) DEFAULT NULL,
  `Description` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`HouseId`,`StationId`)
);
CREATE INDEX `TBL_RoomCMCC_IDX1` ON `tbl_roomcmcc` (`StationId`,`HouseId`);

CREATE TABLE `tbl_stationcmcc` (
  `StationId` INT NOT NULL,
  `SiteID` VARCHAR(255) DEFAULT NULL,
  `SiteName` VARCHAR(255) DEFAULT NULL,
  `Description` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`StationId`)
);
CREATE INDEX `TBL_StationCMCC_IDX1` ON `tbl_stationcmcc` (`StationId`);

CREATE TABLE `tsl_monitorunitcmcc` (
  `StationId` INT NOT NULL,
  `MonitorUnitId` INT NOT NULL,
  `FSUID` VARCHAR(255) DEFAULT '',
  `FSUName` VARCHAR(255) DEFAULT NULL,
  `SiteID` VARCHAR(255) DEFAULT '',
  `SiteName` VARCHAR(255) DEFAULT NULL,
  `RoomID` VARCHAR(255) DEFAULT NULL,
  `RoomName` VARCHAR(255) DEFAULT NULL,
  `UserName` VARCHAR(40) DEFAULT NULL,
  `PassWord` VARCHAR(40) DEFAULT NULL,
  `FSUIP` VARCHAR(255) DEFAULT NULL,
  `FSUMAC` VARCHAR(20) DEFAULT NULL,
  `FSUVER` VARCHAR(20) DEFAULT NULL,
  `Result` INT DEFAULT NULL,
  `FailureCause` VARCHAR(255) DEFAULT NULL,
  `CPUUsage` DOUBLE DEFAULT NULL,
  `MEMUsage` DOUBLE DEFAULT NULL,
  `HardDiskUsage` DOUBLE DEFAULT NULL,
  `GetFSUInfoResult` INT DEFAULT NULL,
  `GetFSUFaliureCause` VARCHAR(255) DEFAULT NULL,
  `GetFSUTime` TIMESTAMP DEFAULT NULL,
  `FTPUserName` VARCHAR(40) DEFAULT NULL,
  `FTPPassWord` VARCHAR(40) DEFAULT NULL,
  `ExtendField1` VARCHAR(255) DEFAULT NULL,
  `ExtendField2` VARCHAR(255) DEFAULT NULL,
  `GetConfigFlag` INT DEFAULT 0,
  PRIMARY KEY (`MonitorUnitId`,`StationId`)
);
CREATE INDEX `TSL_MonitorUnitCMCC_IDX1` ON `tsl_monitorunitcmcc` (`StationId`,`MonitorUnitId`);
CREATE INDEX `IDX_MonitorUnitCMCC_1` ON `tsl_monitorunitcmcc` (`FSUID`);

CREATE TABLE `tsl_monitorunitcucc` (
  `StationId` INT NOT NULL,
  `MonitorUnitId` INT NOT NULL,
  `SUID` VARCHAR(255) DEFAULT NULL,
  `SUName` VARCHAR(255) DEFAULT NULL,
  `SURID` VARCHAR(255) DEFAULT NULL,
  `UserName` VARCHAR(40) DEFAULT NULL,
  `PassWord` VARCHAR(40) DEFAULT NULL,
  `SUIP` VARCHAR(255) DEFAULT NULL,
  `SUVER` VARCHAR(20) DEFAULT NULL,
  `SUPort` VARCHAR(20) DEFAULT NULL,
  `SUVendor` VARCHAR(20) DEFAULT NULL,
  `SUModel` VARCHAR(20) DEFAULT NULL,
  `SUHardVER` VARCHAR(20) DEFAULT NULL,
  `Longitude` DOUBLE DEFAULT NULL,
  `Latitude` DOUBLE DEFAULT NULL,
  `Result` INT DEFAULT NULL,
  `FailureCause` VARCHAR(255) DEFAULT NULL,
  `CPUUsage` DOUBLE DEFAULT NULL,
  `MEMUsage` DOUBLE DEFAULT NULL,
  `GetSUInfoResult` INT DEFAULT NULL,
  `GetSUTime` TIMESTAMP DEFAULT NULL,
  `FTPUserName` VARCHAR(40) DEFAULT NULL,
  `FTPPassWord` VARCHAR(40) DEFAULT NULL,
  `ConfigState` INT DEFAULT NULL,
  `RegisterTime` TIMESTAMP DEFAULT NULL,
  `SUConfigTime` VARCHAR(255) DEFAULT NULL,
  `CenterConfigTime` VARCHAR(255) DEFAULT NULL,
  `Devices` TEXT,
  `ExtendField1` VARCHAR(255) DEFAULT NULL,
  `ExtendField2` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`MonitorUnitId`,`StationId`)
);
CREATE INDEX `TSL_MonitorUnitCUCC_IDX1` ON `tsl_monitorunitcucc` (`StationId`,`MonitorUnitId`);