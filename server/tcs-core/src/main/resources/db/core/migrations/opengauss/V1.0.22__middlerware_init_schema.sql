-- =====================================================
-- Table Definition
-- =====================================================

-- Create resource type table
CREATE TABLE IF NOT EXISTS mw_resource_type (
                                                id VARCHAR(100) PRIMARY KEY, -- Unique resource type identifier (e.g., "MYSQL", "KAFKA")
    name VARCHAR(200) NOT NULL, -- Resource type name (e.g., "MySQL", "Kafka")
    category VARCHAR(100) NOT NULL, -- Resource category (e.g., RELATIONAL_DB, MESSAGE_QUEUE)
    description TEXT, -- Resource type description
    default_config JSON NOT NULL, -- Configuration template (JSON), defines the structure of parameters required for creating this type of resource
    ui_component VARCHAR(100) NOT NULL, -- Frontend component for resource configuration
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Creation time
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Update time
    CONSTRAINT mw_resource_type_update_time CHECK (update_time >= create_time) -- Ensure update_time is never earlier than create_time
    );

-- Comment on columns
COMMENT ON COLUMN mw_resource_type.id IS '资源类型唯一标识 (如 "MYSQL", "KAFKA")';
COMMENT ON COLUMN mw_resource_type.name IS '资源类型名称 (如 "MySQL", "Kafka")';
COMMENT ON COLUMN mw_resource_type.category IS '资源类别（如 RELATIONAL_DB, MESSAGE_QUEUE）';
COMMENT ON COLUMN mw_resource_type.description IS '资源类型描述';
COMMENT ON COLUMN mw_resource_type.default_config IS '配置模板（JSON），定义了创建该类型资源配置所需的参数结构';
COMMENT ON COLUMN mw_resource_type.ui_component IS '前端页面资源配置组件';
COMMENT ON COLUMN mw_resource_type.create_time IS '创建时间';
COMMENT ON COLUMN mw_resource_type.update_time IS '更新时间';

-- Create resource configuration table
CREATE TABLE IF NOT EXISTS mw_resource_configuration (
                                                         id VARCHAR(100) PRIMARY KEY, -- Unique resource configuration identifier
    resource_id VARCHAR(100) NOT NULL, -- Associated resource type ID
    name VARCHAR(200) NOT NULL, -- Resource configuration name (e.g., "Production Master", "Test Redis")
    description TEXT, -- Resource configuration description
    config JSON NOT NULL, -- Resource configuration details (JSON), stores connection parameters, account passwords, etc.
    status VARCHAR(40) NOT NULL DEFAULT 'DISABLED', -- Resource configuration management status (ENABLED/DISABLED/PENDING)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Creation time
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Update time
    created_by VARCHAR(100), -- Creator
    updated_by VARCHAR(100), -- Updater
    CONSTRAINT fk_resource_id FOREIGN KEY (resource_id) REFERENCES mw_resource_type(id) -- Foreign key constraint to mw_resource_type
    );

-- Comment on columns
COMMENT ON COLUMN mw_resource_configuration.id IS '资源配置唯一标识 (UUID 或其他全局唯一 ID)';
COMMENT ON COLUMN mw_resource_configuration.resource_id IS '关联的资源类型ID';
COMMENT ON COLUMN mw_resource_configuration.name IS '资源配置名称（如"生产主库"，"测试Redis"）';
COMMENT ON COLUMN mw_resource_configuration.description IS '资源配置描述';
COMMENT ON COLUMN mw_resource_configuration.config IS '资源配置详情（JSON），存储了具体的连接参数、账号密码等';
COMMENT ON COLUMN mw_resource_configuration.status IS '资源配置的管理状态（ENABLED/DISABLED/PENDING）';
COMMENT ON COLUMN mw_resource_configuration.create_time IS '创建时间';
COMMENT ON COLUMN mw_resource_configuration.update_time IS '更新时间';
COMMENT ON COLUMN mw_resource_configuration.created_by IS '创建者';
COMMENT ON COLUMN mw_resource_configuration.updated_by IS '更新者';

-- Create service type table
CREATE TABLE IF NOT EXISTS mw_service_type (
                                               id VARCHAR(100) PRIMARY KEY, -- Unique service type identifier (e.g., "DATABASE_SERVICE", "CACHE_SERVICE")
    name VARCHAR(200) NOT NULL, -- Service type name (e.g., "Database Service", "Cache Service")
    description TEXT, -- Service type description
    default_config JSON, -- Configuration template (JSON), defines service-level configuration parameters
    ui_component VARCHAR(100) NOT NULL, -- Frontend component for service configuration
    supported_resource_category VARCHAR(100) NOT NULL, -- Supported resource categories (e.g., RELATIONAL_DB, KEY_VALUE)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Creation time
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- Update time
    );

-- Comment on columns
COMMENT ON COLUMN mw_service_type.id IS '服务类型唯一标识 (如 "DATABASE_SERVICE", "CACHE_SERVICE")';
COMMENT ON COLUMN mw_service_type.name IS '服务类型名称 (如 "Database Service", "Cache Service")';
COMMENT ON COLUMN mw_service_type.description IS '服务类型描述';
COMMENT ON COLUMN mw_service_type.default_config IS '配置模板（JSON），定义服务层面的配置参数';
COMMENT ON COLUMN mw_service_type.ui_component IS '前端页面资源配置组件';
COMMENT ON COLUMN mw_service_type.supported_resource_category IS '支持的资源类别（如 RELATIONAL_DB, KEY_VALUE）';
COMMENT ON COLUMN mw_service_type.create_time IS '创建时间';
COMMENT ON COLUMN mw_service_type.update_time IS '更新时间';

-- Create service configuration table
CREATE TABLE IF NOT EXISTS mw_service_configuration (
                                                        id VARCHAR(100) PRIMARY KEY, -- Unique service configuration identifier
    service_id VARCHAR(100) NOT NULL, -- Associated service type ID
    name VARCHAR(200) NOT NULL, -- Service configuration name (e.g., "Primary DB Service", "User Cache Service")
    description TEXT, -- Service configuration description
    config JSON, -- Service configuration details (JSON), stores instance-specific parameters
    resource_configuration_id VARCHAR(100), -- Associated resource configuration ID
    status VARCHAR(40) NOT NULL DEFAULT 'DISABLED', -- Service configuration management status (ENABLED/DISABLED/PENDING)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Creation time
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Update time
    created_by VARCHAR(100), -- Creator
    updated_by VARCHAR(100), -- Updater
    CONSTRAINT fk_service_id FOREIGN KEY (service_id) REFERENCES mw_service_type(id), -- Foreign key constraint to mw_service_type
    CONSTRAINT fk_resource_configuration_id FOREIGN KEY (resource_configuration_id) REFERENCES mw_resource_configuration(id) -- Foreign key constraint to mw_resource_configuration
    );

-- Comment on columns
COMMENT ON COLUMN mw_service_configuration.id IS '服务配置唯一标识';
COMMENT ON COLUMN mw_service_configuration.service_id IS '关联的服务类型ID';
COMMENT ON COLUMN mw_service_configuration.name IS '服务配置名称（如"主库服务"，"用户缓存服务"）';
COMMENT ON COLUMN mw_service_configuration.description IS '服务配置描述';
COMMENT ON COLUMN mw_service_configuration.config IS '服务配置详情（JSON），存储服务实例特有的参数';
COMMENT ON COLUMN mw_service_configuration.resource_configuration_id IS '关联的资源配置ID';
COMMENT ON COLUMN mw_service_configuration.status IS '服务配置的管理状态（ENABLED/DISABLED/PENDING）';
COMMENT ON COLUMN mw_service_configuration.create_time IS '创建时间';
COMMENT ON COLUMN mw_service_configuration.update_time IS '更新时间';
COMMENT ON COLUMN mw_service_configuration.created_by IS '创建者';
COMMENT ON COLUMN mw_service_configuration.updated_by IS '更新者';

-- =====================================================
-- Index Creation
-- =====================================================

-- Create indexes for resource configuration table
CREATE INDEX IF NOT EXISTS idx_resource_configuration_resource_id ON mw_resource_configuration(resource_id);

-- Create indexes for service configuration table
CREATE INDEX IF NOT EXISTS idx_service_configuration_service_id ON mw_service_configuration(service_id);
CREATE INDEX IF NOT EXISTS idx_service_configuration_resource_configuration_id ON mw_service_configuration(resource_configuration_id);

