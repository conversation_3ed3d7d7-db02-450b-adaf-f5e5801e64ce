-- =====================================================
-- 基础数据初始化 - 资源类型（简化版）
-- =====================================================

-- 1. MySQL资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'MYSQL',
    'MySQL',
    'RELATIONAL_DB',
    'MySQL关系型数据库',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "tcs_middleware",
        "username": "root",
        "password": "ENC(password)",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'mysql-config'
);

-- 2. Redis资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'REDIS',
    'Redis',
    'KEY_VALUE_STORE',
    'Redis键值存储',
    '{
        "host": "localhost",
        "port": 6379,
        "password": "ENC(password)",
        "database": 0,
        "connectionTimeout": 2000,
        "maxTotal": 8,
        "maxIdle": 8,
        "minIdle": 0
    }',
    'redis-config'
);

-- 3. H2资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'H2',
    'H2 Database',
    'RELATIONAL_DB',
    'H2内存/文件数据库',
    '{
        "dbName": "testdb",
        "mode": "MEMORY",
        "filePath": "./h2db/testdb",
        "username": "sa",
        "password": "ENC(password)",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'h2-config'
);

-- 4. PostgreSQL资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'POSTGRESQL',
    'PostgreSQL',
    'RELATIONAL_DB',
    'PostgreSQL关系型数据库',
    '{
        "host": "localhost",
        "port": 5432,
        "database": "tcs_middleware",
        "username": "postgres",
        "password": "ENC(password)",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "maxLifetime": 1800000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware",
        "autoCommit": true,
        "transactionIsolation": "TRANSACTION_READ_COMMITTED"
    }',
    'postgresql-config'
);



-- 5. Kafka资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'KAFKA',
    'Kafka',
    'MESSAGE_QUEUE',
    'Kafka消息队列',
    '{
        "bootstrapServers": "localhost:9092",
        "clientId": "tcs-middleware",
        "keySerializer": "org.apache.kafka.common.serialization.StringSerializer",
        "valueSerializer": "org.apache.kafka.common.serialization.StringSerializer",
        "acks": "all",
        "retries": 3,
        "batchSize": 16384,
        "lingerMs": 1,
        "bufferMemory": 33554432
    }',
    'kafka-config'
);

-- 6. MQTT资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'MQTT',
    'MQTT(Mosquito)',
    'MESSAGE_QUEUE',
    'MQTT消息队列',
    '{
        "serverUri": "tcp://localhost:1883",
        "clientId": "tcs-middleware",
        "username": "",
        "password": "ENC(password)",
        "cleanSession": true,
        "connectionTimeout": 30,
        "keepAliveInterval": 60,
        "automaticReconnect": true
    }',
    'mos-mqtt-config'
);

-- 7. HTTP服务器资源类型
-- 更新现有的HTTP_SERVER为Akka HTTP
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'AKKA_HTTP_SERVER',
    'Akka HTTP服务器',
    'WEB_SERVER',
    '基于Akka HTTP的高性能HTTP服务器资源',
    '{
        "host": "0.0.0.0",
        "port": 8080,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'akka-http-server-config'
);

-- 添加Netty HTTP服务器类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'NETTY_HTTP_SERVER',
    'Netty HTTP服务器',
    'WEB_SERVER',
    '基于Netty的高性能HTTP服务器资源',
    '{
        "host": "0.0.0.0",
        "port": 8080,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'netty-http-server-config'
);


-- =====================================================
-- 基础数据初始化 - 服务类型
-- =====================================================

-- 1. 数据库服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'DATABASE',
    '数据库服务',
    '提供数据库访问服务',
    '{}',
    'database-service-config',
    'RELATIONAL_DB'
);

-- 2. 键值存储服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'KEY_VALUE_STORE',
    '键值存储服务',
    '提供键值存储服务',
    '{
        "batchThreshold": 100,
        "timeThresholdMs": 3000
    }',
    'key-value-store-service-config',
    'KEY_VALUE_STORE'
);

-- 3. 消息队列服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'MESSAGE_QUEUE',
    '消息队列服务',
    '提供消息队列服务',
    '{
        "defaultTopic": "default-topic",
        "producerConfig": {
            "acks": "all",
            "retries": 3,
            "batchSize": 16384
        },
        "consumerConfig": {
            "autoCommit": true,
            "autoCommitInterval": 5000,
            "maxPollRecords": 500
        }
    }',
    'message-queue-service-config',
    'MESSAGE_QUEUE'
);

-- 4. Siteweb持久化服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'SITEWEB_PERSISTENT',
    'Siteweb持久化服务',
    '用于封装tcs-siteweb模块中的各种service，提供统一的访问接口，支持关系型数据库',
    '{}',
    'siteweb-persistent-service-config',
    'RELATIONAL_DB'
);

INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
           'HTTP_SERVER',
           'HTTP服务器服务',
           '基于HTTP服务器资源的Web服务，支持路由管理、统计功能等',
           '{
               "maxParsingFailureRecords": 10000,
               "enableParsingFailureRecording": true,
               "parsingFailureRetentionHours": 24
           }',
           'http-server-service-config',
        'WEB_SERVER'
       );

INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
           'FILESYSTEM',
           '文件系统服务',
           '提供统一的分布式文件管理接口，支持多种存储后端',
           '{
               "threadPoolSize": 10,
               "asyncMode": true
           }',
           'filesystem-service-config',
           'FILE_STORAGE'
       );
-- =====================================================
-- 测试数据初始化 - 资源配置
-- =====================================================
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-h2-config-001',
    'H2',
    '测试H2资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "testdb",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cucc-h2-config-primary',
    'H2',
    'cucc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_cucc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cmcc-h2-config-primary',
    'H2',
    'cmcc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_cmcc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'omc-h2-config-primary',
    'H2',
    'omc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_omc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

-- 1. MySQL测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mysql-config-001',
    'MYSQL',
    '测试MySQL资源',
    '用于测试的MySQL资源配置',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "tcs_middleware",
        "username": "root",
        "password": "root",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mysql-siteweb-001',
    'MYSQL',
    '测试MySQL资源',
    '用于测试的MySQL资源配置',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "siteweb_ali",
        "username": "root",
        "password": "root",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'ENABLED',
    'system'
);

-- 2. PostgreSQL测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-postgresql-config-001',
    'POSTGRESQL',
    '测试PostgreSQL资源',
    '用于测试的PostgreSQL资源配置',
    '{
        "host": "*************",
        "port": 5432,
        "database": "postgres",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test"
    }',
    'ENABLED',
    'system'
);

-- 3. Redis测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-redis-config-001',
    'REDIS',
    '测试Redis资源',
    '用于测试的Redis资源配置',
    '{
        "host": "**************",
        "port": 6379,
        "password": "siteweb1!",
        "database": 0,
        "connectionTimeout": 2000,
        "maxTotal": 8,
        "maxIdle": 8,
        "minIdle": 0
    }',
    'ENABLED',
    'system'
);

-- 4. MQTT测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mqtt-config-001',
    'MQTT',
    '测试MQTT资源',
    '用于测试的MQTT资源配置',
    '{
        "serverUri": "tcp://*************:1883",
        "clientId": "tcs-middleware-test",
        "username": "",
        "password": "",
        "cleanSession": true,
        "connectionTimeout": 30,
        "keepAliveInterval": 60,
        "automaticReconnect": true
    }',
    'ENABLED',
    'system'
);

-- 5. HTTP服务器测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-http-server-config-001',
    'AKKA_HTTP_SERVER',
    '测试HTTP服务器',
    '用于测试的HTTP服务器资源配置',
    '{
        "host": "localhost",
        "port": 8088,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'ENABLED',
    'system'
);

-- =====================================================
-- 测试数据初始化 - 服务配置
-- =====================================================

-- 1. 数据库服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-db-service-001',
    'DATABASE',
    '测试数据库服务',
    '用于测试的数据库服务配置',
    '{}',
    'test-postgresql-config-001',
    'ENABLED',
    'system'
);

-- 2. 键值存储服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-kvs-service-001',
    'KEY_VALUE_STORE',
    '测试键值存储服务',
    '用于测试的键值存储服务配置，包含批处理优化',
    '{
        "defaultTTL": 3600,
        "keyPrefix": "test:",
        "serializationFormat": "JSON",
        "batchThreshold": 50,
        "timeThresholdMs": 2000,
        "resourceId": "test-redis-config-001"
    }',
    'test-redis-config-001',
    'ENABLED',
    'system'
);

-- 3. 消息队列服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-mq-service-001',
    'MESSAGE_QUEUE',
    '测试消息队列服务',
    '用于测试的消息队列服务配置',
    '{
        "defaultTopic": "test-topic",
        "producerConfig": {
            "acks": "all",
            "retries": 3,
            "batchSize": 16384
        },
        "consumerConfig": {
            "autoCommit": true,
            "autoCommitInterval": 5000,
            "maxPollRecords": 500
        }
    }',
    'test-mqtt-config-001',
    'ENABLED',
    'system'
);
-- =====================================================
-- H2文件类型存储配置初始化
-- =====================================================

-- H2文件类型存储配置 - 基础版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-config-001',
    'H2',
    'H2文件数据库资源',
    '用于持久化存储的H2文件数据库配置',
    '{
        "dbName": "tcs_file_db",
        "mode": "FILE",
        "filePath": "./data/h2db/tcs_file_db",
        "username": "sa",
        "password": "",
        "compatibilityMode": "MYSQL",
        "autoServerMode": false,
        "autoReconnect": true,
        "maxPoolSize": 15,
        "minIdle": 2,
        "idleTimeout": 30000,
        "connectionTimeout": 30000,
        "maxLifetime": 1800000,
        "autoCommit": true
    }',
    'ENABLED',
    'system'
);

-- H2文件类型存储配置 - 生产环境版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-prod-config-001',
    'H2',
    '生产H2文件数据库',
    '生产环境使用的H2文件数据库配置，具有更大的连接池和更长的超时时间',
    '{
        "dbName": "tcs_prod_db",
        "mode": "FILE",
        "filePath": "./data/h2db/production/tcs_prod_db",
        "username": "sa",
        "password": "ENC(prod_password)",
        "compatibilityMode": "MYSQL",
        "autoServerMode": true,
        "autoReconnect": true,
        "maxPoolSize": 30,
        "minIdle": 5,
        "idleTimeout": 60000,
        "connectionTimeout": 30000,
        "maxLifetime": 3600000,
        "autoCommit": true
    }',
    'ENABLED',
    'system'
);

-- H2文件类型存储配置 - 开发环境版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-dev-config-001',
    'H2',
    '开发H2文件数据库',
    '开发环境使用的H2文件数据库配置，启用H2控制台访问',
    '{
        "dbName": "tcs_dev_db",
        "mode": "FILE",
        "filePath": "./data/h2db/development/tcs_dev_db",
        "username": "sa",
        "password": "",
        "compatibilityMode": "MYSQL",
        "autoServerMode": true,
        "autoReconnect": true,
        "maxPoolSize": 10,
        "minIdle": 1,
        "idleTimeout": 30000,
        "connectionTimeout": 30000,
        "maxLifetime": 1800000,
        "autoCommit": true
    }',
    'ENABLED',
    'system'
);

-- H2文件类型存储配置 - 备份数据库版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-backup-config-001',
    'H2',
    '备份H2文件数据库',
    '用于数据备份的H2文件数据库配置',
    '{
        "dbName": "tcs_backup_db",
        "mode": "FILE",
        "filePath": "./data/h2db/backup/tcs_backup_db",
        "username": "sa",
        "password": "ENC(backup_password)",
        "compatibilityMode": "MYSQL",
        "autoServerMode": false,
        "autoReconnect": true,
        "maxPoolSize": 5,
        "minIdle": 1,
        "idleTimeout": 30000,
        "connectionTimeout": 30000,
        "maxLifetime": 1800000,
        "autoCommit": true
    }',
    'DISABLED',
    'system'
);

-- =====================================================
-- H2文件配置初始化完成
-- =====================================================

-- 4. Siteweb持久化服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-siteweb-persistent-service-001',
    'SITEWEB_PERSISTENT',
    '测试Siteweb持久化服务',
    '用于测试的Siteweb持久化服务配置，封装tcs-siteweb模块的服务，使用H2数据库',
    '{}',
    'test-mysql-siteweb-001',
    'ENABLED',
    'system'
);

-- =====================================================
-- omc 模块配置
-- =====================================================
--INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
--VALUES (
--    'omc-postgres-config-primary',
--    'POSTGRESQL',
--    'omc PostgreSQL资源',
--    'omc PostgreSQL资源配置',
--    '{
--        "host": "*************",
--        "port": 5555,
--        "database": "omc_siteweb",
--        "username": "postgres",
--        "password": "123456",
--        "schema": "public",
--        "minPoolSize": 5,
--        "maxPoolSize": 20,
--        "connectionTimeout": 30000,
--        "idleTimeout": 600000,
--        "sslEnabled": false,
--        "sslMode": "prefer",
--        "applicationName": "TCS-Middleware-Test"
--    }',
--    'ENABLED',
--    'system'
--);
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'omc-postgres-config-primary',
    'POSTGRESQL',
    'omc PostgreSQL资源',
    'omc PostgreSQL资源配置',
    '{
        "host": "************",
        "port": 5432,
        "database": "siteweb_omc",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test"
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cmcc-postgres-config-primary',
    'POSTGRESQL',
    'omc PostgreSQL资源',
    'omc PostgreSQL资源配置',
    '{
        "host": "************",
        "port": 5432,
        "database": "tcs_cmcc",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test"
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'omc-siteweb-persistent-service',
    'SITEWEB_PERSISTENT',
    'omc Siteweb持久化服务',
    'omc Siteweb持久化服务配置，封装tcs-siteweb模块的服务，使用PostgreSQL数据库',
    '{}',
    'omc-postgres-config-primary',
    'ENABLED',
    'system'
);

INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'omc-siteweb-persistent-service-h2',
    'SITEWEB_PERSISTENT',
    'omc Siteweb持久化服务',
    'omc Siteweb持久化服务配置，封装tcs-siteweb模块的服务，使用PostgreSQL数据库',
    '{}',
    'omc-h2-config-primary',
    'ENABLED',
    'system'
);


-- =====================================================
-- OpenGauss资源类型和测试数据初始化
-- =====================================================

-- 1. OpenGauss resource type
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES
    ('OPENGAUSS', 'OpenGauss', 'RELATIONAL_DB', 'OpenGauss关系型数据库',
     '{
         "host": "localhost",
         "port": 5432,
         "database": "tcs_middleware",
         "username": "gaussdb",
         "password": "ENC(password)",
         "schema": "public",
         "minPoolSize": 5,
         "maxPoolSize": 20,
         "connectionTimeout": 30000,
         "idleTimeout": 600000,
         "maxLifetime": 1800000,
         "sslEnabled": false,
         "sslMode": "prefer",
         "applicationName": "TCS-Middleware",
         "autoCommit": true,
         "transactionIsolation": "TRANSACTION_READ_COMMITTED",
         "compatibilityMode": "PG",
         "characterEncoding": "UTF-8"
     }', 'opengauss-config');

-- 2. Dameng resource type
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES
    ('DAMENG', 'Dameng', 'RELATIONAL_DB', 'Dameng关系型数据库',
     '{
         "host": "localhost",
         "port": 5236,
         "username": "SYSDBA",
         "password": "ENC(password)",
         "schema": "SYSDBA",
         "minPoolSize": 5,
         "maxPoolSize": 20,
         "connectionTimeout": 30000,
         "idleTimeout": 600000,
         "maxLifetime": 1800000,
         "leakDetectionThreshold": 60000,
         "sslEnabled": false,
         "applicationName": "TCS-Middleware",
         "autoCommit": true,
         "transactionIsolation": "TRANSACTION_READ_COMMITTED",
         "validationQuery": "SELECT 1 FROM DUAL",
         "validationTimeout": 5,
         "testOnBorrow": true,
         "testOnReturn": false,
         "testWhileIdle": true,
         "timeBetweenEvictionRunsMillis": 60000,
         "poolName": "DamengPool",
         "jmxEnabled": false,
         "characterEncoding": "UTF-8",
         "batchEnabled": true,
         "batchSize": 1000,
         "prepStmtCacheEnabled": true,
         "prepStmtCacheSize": 250,
         "prepStmtCacheSqlLimit": 2048
     }', 'dameng-config');

-- 2. OpenGauss测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-opengauss-config-001',
    'OPENGAUSS',
    '测试OpenGauss资源',
    '用于测试的OpenGauss资源配置',
    '{
        "host": "*************",
        "port": 5432,
        "database": "test_cmcc",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test",
        "compatibilityMode": "PG",
        "characterEncoding": "UTF-8"
    }',
    'ENABLED',
    'system'
);

-- =====================================================
-- 文件系统服务初始化 - 资源类型
-- =====================================================

-- 1. 本地文件系统资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
           'LOCAL_FILESYSTEM',
           '本地文件系统',
           'FILE_STORAGE',
           '本地文件系统存储资源',
           '{
               "rootDirectory": "/data/files",
               "createDirectories": true
           }',
           'local-filesystem-config'
       );

-- 2. Minio对象存储资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
           'MINIO_FILESYSTEM',
           'Minio对象存储',
           'FILE_STORAGE',
           'Minio分布式对象存储资源',
           '{
               "endpoint": "http://localhost:9000",
               "accessKey": "minioadmin",
               "secretKey": "ENC(minioadmin)",
               "bucketName": "default",
               "region": "us-east-1",
               "useHttps": false,
               "connectTimeout": 30,
               "readTimeout": 30,
               "writeTimeout": 30
           }',
           'minio-filesystem-config'
       );

-- 3. Zookeeper分布式存储资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
           'ZOOKEEPER_FILESYSTEM',
           'Zookeeper分布式存储',
           'FILE_STORAGE',
           'Zookeeper分布式协调存储资源',
           '{
               "connectString": "localhost:2181",
               "sessionTimeoutMs": 60000,
               "connectionTimeoutMs": 15000,
               "baseSleepTimeMs": 1000,
               "maxRetries": 3,
               "rootPath": "/filesystem",
               "namespace": "",
               "enableAcl": false,
               "aclUsername": "",
               "aclPassword": "ENC(password)",
               "maxDataSize": 1048576
           }',
           'zookeeper-filesystem-config'
       );

-- 4. Consul服务发现与配置存储资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
           'CONSUL_FILESYSTEM',
           'Consul配置存储',
           'FILE_STORAGE',
           'Consul服务发现与配置存储资源',
           '{
               "host": "localhost",
               "port": 8500,
               "scheme": "http",
               "token": "ENC(token)",
               "datacenter": "dc1",
               "rootPath": "filesystem",
               "connectTimeout": 30000,
               "readTimeout": 30000,
               "writeTimeout": 30000,
               "maxDataSize": 524288
           }',
           'consul-filesystem-config'
       );



-- 文件系统服务类型



-- 1. 本地文件系统测试配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'test-local-filesystem-config-001',
           'LOCAL_FILESYSTEM',
           '测试本地文件系统',
           '用于测试的本地文件系统资源配置',
           '{
               "rootDirectory": "./data/files/test",
               "createDirectories": true
           }',
           'ENABLED',
           'system'
       );

-- 2. Minio对象存储测试配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'test-minio-filesystem-config-001',
           'MINIO_FILESYSTEM',
           '测试Minio对象存储',
           '用于测试的Minio对象存储资源配置',
           '{
               "endpoint": "http://*************:9001",
               "accessKey": "admin",
               "secretKey": "admin123",
               "bucketName": "test-bucket",
               "region": "us-east-1",
               "useHttps": false,
               "connectTimeout": 30,
               "readTimeout": 30,
               "writeTimeout": 30
           }',
           'ENABLED',
           'system'
       );

-- 3. Zookeeper分布式存储测试配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'test-zookeeper-filesystem-config-001',
           'ZOOKEEPER_FILESYSTEM',
           '测试Zookeeper分布式存储',
           '用于测试的Zookeeper分布式存储资源配置',
           '{
               "connectString": "**************:2181",
               "sessionTimeoutMs": 60000,
               "connectionTimeoutMs": 60000,
               "baseSleepTimeMs": 1000,
               "maxRetries": 3,
               "rootPath": "/tmp/zookeeper",
               "namespace": "test",
               "enableAcl": false,
               "maxDataSize": 1048576
           }',
           'ENABLED',
           'system'
       );

-- 4. Consul配置存储测试配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'test-consul-filesystem-config-001',
           'CONSUL_FILESYSTEM',
           '测试Consul配置存储',
           '用于测试的Consul配置存储资源配置',
           '{
               "host": "*************",
               "port": 8500,
               "scheme": "http",
               "datacenter": "dc1",
               "rootPath": "filesystem/test",
               "connectTimeout": 30000,
               "readTimeout": 30000,
               "writeTimeout": 30000,
               "maxDataSize": 524288,
               "enableSsl": false
           }',
           'ENABLED',
           'system'
       );

-- =====================================================
-- 文件系统服务初始化 - 服务配置
-- =====================================================

-- 1. 本地文件系统服务配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'test-local-filesystem-service-001',
           'FILESYSTEM',
           '测试本地文件系统服务',
           '基于本地文件系统的文件管理服务',
           '{
               "threadPoolSize": 5,
               "asyncMode": false
           }',
           'test-local-filesystem-config-001',
           'ENABLED',
           'system'
       );

-- 2. Minio文件系统服务配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'test-minio-filesystem-service-001',
           'FILESYSTEM',
           '测试Minio文件系统服务',
           '基于Minio对象存储的分布式文件管理服务',
           '{
               "threadPoolSize": 10,
               "asyncMode": true
           }',
           'test-minio-filesystem-config-001',
           'ENABLED',
           'system'
       );

-- 3. Zookeeper文件系统服务配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'test-zookeeper-filesystem-service-001',
           'FILESYSTEM',
           '测试Zookeeper文件系统服务',
           '基于Zookeeper的分布式文件管理服务',
           '{
               "threadPoolSize": 5,
               "asyncMode": true
           }',
           'test-zookeeper-filesystem-config-001',
           'ENABLED',
           'system'
       );

-- 4. Consul文件系统服务配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'test-consul-filesystem-service-001',
           'FILESYSTEM',
           '测试Consul文件系统服务',
           '基于Consul的分布式文件管理服务',
           '{
               "threadPoolSize": 5,
               "asyncMode": true
           }',
           'test-consul-filesystem-config-001',
           'ENABLED',
           'system'
       );




INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'cmcc-akka-http-server-config',
           'AKKA_HTTP_SERVER',
           'CMCC Akka HTTP服务器',
           'CMCC模块使用的Akka HTTP服务器配置',
           '{
               "host": "0.0.0.0",
               "port": 8080,
               "idleTimeout": 60,
               "backlog": 100
           }',
           'ENABLED',
           'system'
       );

-- 添加Netty HTTP服务器配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
           'cmcc-netty-http-server-config',
           'NETTY_HTTP_SERVER',
           'CMCC Netty HTTP服务器',
           'CMCC模块使用的Netty HTTP服务器配置',
           '{
               "host": "0.0.0.0",
               "port": 8080,
               "idleTimeout": 60,
               "backlog": 100
           }',
           'ENABLED',
           'system'
       );
-- 添加HTTP服务器服务配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'cmcc-akka-http-server-service',
           'HTTP_SERVER',
           'CMCC HTTP服务器服务',
           'CMCC模块使用的HTTP服务器服务',
            '{
               "maxRequestFailureRecords": 8000,
               "enableRequestFailureRecording": true,
               "requestFailureRetentionHours": 48
           }',
           'cmcc-akka-http-server-config',
           'ENABLED',
           'system'
       );

-- 添加Netty HTTP服务器服务配置示例
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
           'cmcc-netty-http-server-service',
           'HTTP_SERVER',
           'CMCC Netty HTTP服务器服务',
           'CMCC模块使用的Netty HTTP服务器服务',
            '{
               "maxRequestFailureRecords": 8000,
               "enableRequestFailureRecording": true,
               "requestFailureRetentionHours": 48
           }',
           'cmcc-netty-http-server-config',
           'ENABLED',
           'system'
       );

