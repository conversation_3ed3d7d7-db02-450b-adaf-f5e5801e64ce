-- Flyway migration script for Department tables (PostgreSQL compatible)

-- Table: tcs_department
-- Comment: 部门表
CREATE TABLE tcs_department (
  id BIGSERIAL PRIMARY KEY, -- 部门ID
  name VARCHAR(256) NOT NULL, -- 部门名称
  code VARCHAR(128) NOT NULL, -- 部门编码，唯一标识
  parent_id BIGINT DEFAULT NULL, -- 父部门ID，顶级部门为NULL
  leader VARCHAR(128) DEFAULT NULL, -- 负责人
  phone VARCHAR(64) DEFAULT NULL, -- 联系电话
  email VARCHAR(256) DEFAULT NULL, -- 邮箱
  status INTEGER DEFAULT 1, -- 启用状态：1-启用，0-停用
  sort INTEGER DEFAULT 0, -- 排序字段，同级部门按此字段排序
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted INTEGER DEFAULT 0, -- 逻辑删除：0-未删除，1-已删除
  remark VARCHAR(1024) DEFAULT NULL -- 备注
);

-- Add comment to the table
COMMENT ON TABLE tcs_department IS '部门表';
COMMENT ON COLUMN tcs_department.id IS '部门ID';
COMMENT ON COLUMN tcs_department.name IS '部门名称';
COMMENT ON COLUMN tcs_department.code IS '部门编码，唯一标识';
COMMENT ON COLUMN tcs_department.parent_id IS '父部门ID，顶级部门为NULL';
COMMENT ON COLUMN tcs_department.leader IS '负责人';
COMMENT ON COLUMN tcs_department.phone IS '联系电话';
COMMENT ON COLUMN tcs_department.email IS '邮箱';
COMMENT ON COLUMN tcs_department.status IS '启用状态：1-启用，0-停用';
COMMENT ON COLUMN tcs_department.sort IS '排序字段，同级部门按此字段排序';
COMMENT ON COLUMN tcs_department.create_time IS '创建时间';
COMMENT ON COLUMN tcs_department.update_time IS '更新时间';
COMMENT ON COLUMN tcs_department.deleted IS '逻辑删除：0-未删除，1-已删除';
COMMENT ON COLUMN tcs_department.remark IS '备注';

-- Create indexes for better performance
CREATE INDEX idx_department_parent_id ON tcs_department (parent_id);
CREATE INDEX idx_department_code ON tcs_department (code);
CREATE INDEX idx_department_status ON tcs_department (status);
CREATE INDEX idx_department_deleted ON tcs_department (deleted);
CREATE INDEX idx_department_sort ON tcs_department (sort);

-- Add unique constraint for department code (excluding deleted records)
ALTER TABLE tcs_department ADD CONSTRAINT uk_department_code_deleted UNIQUE (code, deleted);