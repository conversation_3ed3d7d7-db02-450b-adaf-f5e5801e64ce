-- 基础设备类型表
CREATE TABLE tbl_equipmentbasetype (
                                       BaseEquipmentId SERIAL PRIMARY KEY,
                                       BaseEquipmentName VARCHAR(256) DEFAULT NULL,
                                       EquipmentTypeId INT NOT NULL,
                                       EquipmentSubTypeId INT NOT NULL,
                                       Description TEXT,
                                       ExtField VARCHAR DEFAULT NULL,
                                       CONSTRAINT tbl_equipmentbasetype_BaseEquipmentId UNIQUE (BaseEquipmentId)
);

-- 基本类别字典表
CREATE TABLE tbl_baseclassdic (
                                  BaseClassId SERIAL PRIMARY KEY,
                                  BaseClassName VARCHAR(510) NOT NULL,
                                  BaseClassIcon VARCHAR(510) DEFAULT NULL
);

-- 基本命令代码表
CREATE TABLE tbl_basecommandcode (
                                     CodeId SERIAL PRIMARY KEY,
                                     Command VARCHAR(510) NOT NULL,
                                     Description VARCHAR(510) DEFAULT NULL
);

-- 基础设备类别映射表
CREATE TABLE tbl_baseequipmentcategorymap (
                                              BaseEquipmentID INT NOT NULL,
                                              EquipmentCategory INT NOT NULL,
                                              PRIMARY KEY (BaseEquipmentID, EquipmentCategory)
);

-- 基础信号事件代码表
CREATE TABLE tbl_basesignaleventcode (
                                         CodeId SERIAL PRIMARY KEY,
                                         Category VARCHAR(510) NOT NULL,
                                         Signal VARCHAR(510) DEFAULT NULL,
                                         EVENT VARCHAR(510) DEFAULT NULL,
                                         Description VARCHAR(510) DEFAULT NULL
);

-- 基础单位字典表
CREATE TABLE tbl_baseunitdic (
                                 BaseUnitID SERIAL PRIMARY KEY,
                                 BaseUnitName VARCHAR(510) NOT NULL,
                                 BaseUnitSymbol VARCHAR(510) NOT NULL,
                                 BaseUnitDescription VARCHAR(510) DEFAULT NULL
);

-- 信号基础字典表
CREATE TABLE tbl_signalbasedic (
                                   BaseTypeId NUMERIC(12,0) NOT NULL PRIMARY KEY,
                                   BaseTypeName VARCHAR(256) NOT NULL,
                                   BaseEquipmentId INT NOT NULL,
                                   EnglishName TEXT,
                                   BaseLogicCategoryId INT DEFAULT NULL,
                                   StoreInterval INT DEFAULT NULL,
                                   AbsValueThreshold DOUBLE PRECISION DEFAULT NULL,
                                   PercentThreshold DOUBLE PRECISION DEFAULT NULL,
                                   StoreInterval2 INT DEFAULT NULL,
                                   AbsValueThreshold2 DOUBLE PRECISION DEFAULT NULL,
                                   PercentThreshold2 DOUBLE PRECISION DEFAULT NULL,
                                   ExtendField1 TEXT,
                                   ExtendField2 TEXT,
                                   ExtendField3 TEXT,
                                   UnitId INT DEFAULT NULL,
                                   BaseStatusId INT DEFAULT NULL,
                                   BaseHysteresis DOUBLE PRECISION DEFAULT NULL,
                                   BaseFreqPeriod INT DEFAULT NULL,
                                   BaseFreqCount INT DEFAULT NULL,
                                   BaseShowPrecision VARCHAR(60) DEFAULT NULL,
                                   BaseStatPeriod INT DEFAULT NULL,
                                   CGElement VARCHAR(256) DEFAULT NULL,
                                   Description TEXT,
                                   BaseNameExt VARCHAR(256) DEFAULT NULL,
                                   IsSystem BOOLEAN NOT NULL DEFAULT TRUE,
                                   CONSTRAINT tbl_signalbasedic_BaseTypeId UNIQUE (BaseTypeId)
);

-- 信号基础映射表
CREATE TABLE tbl_signalbasemap (
                                   StandardDicId INT NOT NULL,
                                   StandardType INT NOT NULL,
                                   StationBaseType INT NOT NULL,
                                   BaseTypeId NUMERIC(12,0) NOT NULL,
                                   BaseCondId INT DEFAULT NULL,
                                   PRIMARY KEY (BaseTypeId, StandardDicId, StandardType, StationBaseType)
);

-- 信号基础确认表
CREATE TABLE tbl_signalbaseconfirm (
                                       EquipmentTemplateId INT NOT NULL,
                                       SignalId INT NOT NULL,
                                       StateValue INT DEFAULT NULL,
                                       SubState VARCHAR(32) DEFAULT NULL
);

CREATE INDEX idxsignalbaseconfirmID ON tbl_signalbaseconfirm (EquipmentTemplateId, SignalId);

-- 状态基础字典表
CREATE TABLE tbl_statusbasedic (
                                   BaseStatusId INT NOT NULL,
                                   BaseStatusName VARCHAR(256) NOT NULL,
                                   BaseCondId INT NOT NULL,
                                   Operator VARCHAR(60) NOT NULL,
                                   "Value" INT DEFAULT NULL,  -- 使用双引号
                                   Meaning VARCHAR(256) DEFAULT NULL,
                                   Description TEXT,
                                   PRIMARY KEY (BaseCondId, BaseStatusId)
);

-- 命令基础字典表
CREATE TABLE tbl_commandbasedic (
                                    BaseTypeId NUMERIC(12,0) NOT NULL PRIMARY KEY,
                                    BaseTypeName VARCHAR(256) NOT NULL,
                                    BaseEquipmentId INT NOT NULL,
                                    EnglishName TEXT,
                                    BaseLogicCategoryId INT DEFAULT NULL,
                                    CommandType INT NOT NULL,
                                    BaseStatusId INT DEFAULT NULL,
                                    ExtendField1 TEXT,
                                    ExtendField2 TEXT,
                                    ExtendField3 TEXT,
                                    Description TEXT,
                                    BaseNameExt VARCHAR(256) DEFAULT NULL,
                                    IsSystem BOOLEAN NOT NULL DEFAULT TRUE,
                                    CONSTRAINT tbl_commandbasedic_BaseTypeId UNIQUE (BaseTypeId)
);

-- 命令基础映射表
CREATE TABLE tbl_commandbasemap (
                                    StandardDicId INT NOT NULL,
                                    StandardType INT NOT NULL,
                                    StationBaseType INT NOT NULL,
                                    BaseTypeId NUMERIC(12,0) NOT NULL,
                                    BaseCondId INT DEFAULT NULL,
                                    PRIMARY KEY (BaseTypeId, StandardDicId, StandardType, StationBaseType)
);

-- 控制基础确认表
CREATE TABLE tbl_controlbaseconfirm (
                                        EquipmentTemplateId INT NOT NULL,
                                        ControlId INT NOT NULL,
                                        ParameterValue INT DEFAULT NULL,
                                        SubState VARCHAR(32) DEFAULT NULL
);

-- 事件基础字典表
CREATE TABLE tbl_eventbasedic (
                                  BaseTypeId NUMERIC(12,0) NOT NULL PRIMARY KEY,
                                  BaseTypeName VARCHAR(256) NOT NULL,
                                  BaseEquipmentId INT NOT NULL,
                                  EnglishName TEXT,
                                  EventSeverityId INT NOT NULL,
                                  ComparedValue DOUBLE PRECISION DEFAULT NULL,
                                  BaseLogicCategoryId INT DEFAULT NULL,
                                  StartDelay INT DEFAULT NULL,
                                  EndDelay INT DEFAULT NULL,
                                  ExtendField1 TEXT,
                                  ExtendField2 TEXT,
                                  ExtendField3 TEXT,
                                  ExtendField4 TEXT,
                                  ExtendField5 TEXT,
                                  Description TEXT,
                                  BaseNameExt VARCHAR(256) DEFAULT NULL,
                                  IsSystem BOOLEAN NOT NULL DEFAULT TRUE,
                                  CONSTRAINT tbl_eventbasedic_BaseTypeId UNIQUE (BaseTypeId)
);

-- 事件基础确认表
CREATE TABLE tbl_eventbaseconfirm (
                                      EquipmentTemplateId INT NOT NULL,
                                      EventId INT NOT NULL,
                                      EventConditionId INT NOT NULL,
                                      SubState VARCHAR(32) DEFAULT NULL,
                                      PRIMARY KEY (EquipmentTemplateId, EventConditionId, EventId)
);

-- 事件基础映射表
CREATE TABLE tbl_eventbasemap (
                                  StandardDicId INT NOT NULL,
                                  StandardType INT NOT NULL,
                                  StationBaseType INT NOT NULL,
                                  BaseTypeId NUMERIC(12,0) NOT NULL,
                                  PRIMARY KEY (BaseTypeId, StandardDicId, StandardType, StationBaseType)
);
