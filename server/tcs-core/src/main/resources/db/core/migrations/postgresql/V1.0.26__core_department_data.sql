-- Flyway migration script for Department initial data (PostgreSQL compatible)

-- Insert initial department data
INSERT INTO tcs_department (id, name, code, parent_id, leader, phone, email, status, sort, remark) VALUES
-- 顶级部门
(1, '技术部', 'TECH', NULL, '张三', '13800138001', '<EMAIL>', 1, 1, '负责技术研发工作'),
(2, '产品部', 'PROD', NULL, '钱七', '13800138005', '<EMAIL>', 1, 2, '负责产品设计和规划'),
(3, '人事部', 'HR', NULL, '周九', '13800138008', '<EMAIL>', 1, 3, '负责人力资源管理'),
(4, '财务部', 'FIN', NULL, '吴十', '13800138009', '<EMAIL>', 1, 4, '负责财务管理');

-- 技术部子部门
INSERT INTO tcs_department (id, name, code, parent_id, leader, phone, email, status, sort, remark) VALUES
(11, '前端开发组', 'TECH_FE', 1, '李四', '13800138002', '<EMAIL>', 1, 1, '负责前端界面开发'),
(12, '后端开发组', 'TECH_BE', 1, '王五', '13800138003', '<EMAIL>', 1, 2, '负责后端服务开发'),
(13, '测试组', 'TECH_QA', 1, '赵六', '13800138004', '<EMAIL>', 1, 3, '负责软件测试');

-- 前端开发组子部门
INSERT INTO tcs_department (id, name, code, parent_id, leader, phone, email, status, sort, remark) VALUES
(111, 'Vue开发小组', 'TECH_FE_VUE', 11, '小明', '13800138011', '<EMAIL>', 1, 1, '专注Vue.js技术栈'),
(112, 'React开发小组', 'TECH_FE_REACT', 11, '小红', '13800138012', '<EMAIL>', 1, 2, '专注React技术栈');

-- 后端开发组子部门
INSERT INTO tcs_department (id, name, code, parent_id, leader, phone, email, status, sort, remark) VALUES
(121, 'Java开发小组', 'TECH_BE_JAVA', 12, '小刚', '13800138013', '<EMAIL>', 1, 1, '专注Java后端开发'),
(122, 'Python开发小组', 'TECH_BE_PYTHON', 12, '小丽', '13800138014', '<EMAIL>', 1, 2, '专注Python后端开发');

-- 产品部子部门
INSERT INTO tcs_department (id, name, code, parent_id, leader, phone, email, status, sort, remark) VALUES
(21, '产品设计组', 'PROD_DESIGN', 2, '孙八', '13800138006', '<EMAIL>', 1, 1, '负责产品原型设计'),
(22, '用户体验组', 'PROD_UX', 2, '小李', '13800138007', '<EMAIL>', 1, 2, '负责用户体验优化');

-- 人事部子部门
INSERT INTO tcs_department (id, name, code, parent_id, leader, phone, email, status, sort, remark) VALUES
(31, '招聘组', 'HR_RECRUIT', 3, '小张', '13800138015', '<EMAIL>', 1, 1, '负责人才招聘'),
(32, '培训组', 'HR_TRAIN', 3, '小王', '13800138016', '<EMAIL>', 1, 2, '负责员工培训');

-- Reset sequence to prevent conflicts
SELECT setval('tcs_department_id_seq', 100, false);