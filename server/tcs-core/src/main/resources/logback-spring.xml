<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--引入默认的一些设置-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <contextName>logback</contextName>
    <property name="log.path" value="logs" />
    <property name="log.name" value="siteweb-tcs" />




    <!--web信息-->
    <logger name="org.springframework.web" level="error"/>
    <logger name="org.springframework.boot" level="error"/>
    <logger name="com.siteweb.tcs" level="error"/>
    <logger name="org.apache" level="error"/>
    <logger name="org.hibernate" level="error"/>
    <logger name="org.mybatis" level="error"/>
    <logger name="org.apache.ibatis" level="error"/>
    <logger name="com.baomidou" level="error"/>

    <!--写入日志到控制台的appender,用默认的,但是要去掉charset,否则windows下tomcat下乱码-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <property name="LOG_PATH" value="./logs/siteweb-tcs"/>
    <!--写入日志到文件的appender-->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${log.name}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_PATH}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- each file should be at most 10MB, keep 30 days worth of history, but at most 2GB -->
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level [%15thread] %40logger : %msg%n</pattern>
        </encoder>
    </appender>




    <!--异步到文件-->
    <appender name="asyncFileAppender" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>500</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!--异步到Loki-->
    <appender name="asyncLOKIAppender" class="com.github.loki4j.logback.Loki4jAppender">
        <labels>
            app = TCS
            host = ${HOSTNAME}
        </labels>
        <structuredMetadata>
            level=%level
            thread=%thread
            logger=%logger
            *=%%mdc
            *=%%kvp
        </structuredMetadata>
        <message>
            <pattern>%date %-5level [%thread] %40logger : %msg%n</pattern>
        </message>
        <http>
            <url>http://**********:3100/loki/api/v1/push</url>
            <useProtobufApi>true</useProtobufApi>
            <sender class="com.github.loki4j.logback.ApacheHttpSender" />
            <requestTimeoutMs>10000</requestTimeoutMs>
        </http>
        <batch>
            <maxItems>100</maxItems>
            <timeoutMs>10000</timeoutMs>
        </batch>
        <verbose>true</verbose>
    </appender>




    <!--生产环境:打印控制台和输出到文件-->
    <springProfile name="mysql">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="asyncFileAppender"/>
<!--            <appender-ref ref="asyncLOKIAppender"/>-->
        </root>
    </springProfile>
    <springProfile name="postgresql">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="asyncFileAppender"/>
<!--            <appender-ref ref="asyncLOKIAppender"/>-->
        </root>
    </springProfile>
    <springProfile name="dameng">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="asyncFileAppender"/>
<!--            <appender-ref ref="asyncLOKIAppender"/>-->
        </root>
    </springProfile>
    <springProfile name="opengauss">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="asyncFileAppender"/>
<!--            <appender-ref ref="asyncLOKIAppender"/>-->
        </root>
    </springProfile>
    <springProfile name="h2">
        <root level="error">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="asyncFileAppender"/>
<!--            <appender-ref ref="asyncLOKIAppender"/>-->
        </root>
    </springProfile>

</configuration>
