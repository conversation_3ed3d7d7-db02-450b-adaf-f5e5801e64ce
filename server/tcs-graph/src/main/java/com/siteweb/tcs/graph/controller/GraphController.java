package com.siteweb.tcs.graph.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;

import com.siteweb.tcs.graph.service.GraphQueryService;
import com.siteweb.tcs.graph.service.GraphSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for graph operations
 */
@RestController
@RequestMapping("/graph")
@Slf4j
public class GraphController {
    
    @Autowired
    private GraphSyncService graphSyncService;
    
    @Autowired
    private GraphQueryService graphQueryService;
    
    @Value("${tcs.graph.query.max-traversal-depth:5}")
    private int maxTraversalDepth;
    
    /**
     * Synchronize all data from relational database to Neo4j
     * 
     * @return synchronization results
     */
    @PostMapping(value = "/sync", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> syncAll() {
        log.info("Received request to synchronize all data to Neo4j");
        
        try {
            Map<String, Object> result = graphSyncService.syncAll();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Error synchronizing data to Neo4j", e);
            return ResponseHelper.failed("Error synchronizing data: " + e.getMessage());
        }
    }
    
    /**
     * Check consistency between relational database and Neo4j
     * 
     * @return consistency check results
     */
    @GetMapping(value = "/consistency", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> checkConsistency() {
        log.info("Received request to check consistency between relational database and Neo4j");
        
        try {
            Map<String, Object> result = graphSyncService.checkConsistency();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Error checking consistency", e);
            return ResponseHelper.failed("Error checking consistency: " + e.getMessage());
        }
    }
    
    /**
     * Get graph statistics
     * 
     * @return graph statistics
     */
    @GetMapping(value = "/statistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStatistics() {
        log.info("Received request to get graph statistics");
        
        try {
            Map<String, Object> statistics = graphQueryService.getGraphStatistics();
            return ResponseHelper.successful(statistics);
        } catch (Exception e) {
            log.error("Error getting graph statistics", e);
            return ResponseHelper.failed("Error getting graph statistics: " + e.getMessage());
        }
    }
    
    /**
     * Get the region hierarchy
     * 
     * @return the region hierarchy
     */
    @GetMapping(value = "/regions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRegionHierarchy() {
        log.info("Received request to get region hierarchy");
        
        try {
            List<RegionNode> regions = graphQueryService.getRegionHierarchy();
            return ResponseHelper.successful(regions);
        } catch (Exception e) {
            log.error("Error getting region hierarchy", e);
            return ResponseHelper.failed("Error getting region hierarchy: " + e.getMessage());
        }
    }
    
    /**
     * Get a region with its descendants
     * 
     * @param regionId the ID of the region
     * @param depth the maximum depth to traverse (optional, default is 1)
     * @return the region with its descendants
     */
    @GetMapping(value = "/regions/{regionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRegionWithDescendants(
            @PathVariable Long regionId,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("Received request to get region with ID {} and its descendants to depth {}", regionId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            Optional<RegionNode> region = graphQueryService.getRegionWithDescendants(regionId, actualDepth);
            
            if (region.isPresent()) {
                return ResponseHelper.successful(region.get());
            } else {
                return ResponseHelper.failed("Region not found with ID: " + regionId);
            }
        } catch (Exception e) {
            log.error("Error getting region with descendants", e);
            return ResponseHelper.failed("Error getting region with descendants: " + e.getMessage());
        }
    }
    
    /**
     * Get all gateways in a region
     * 
     * @param regionId the ID of the region
     * @param depth the maximum depth to traverse (optional, default is 1)
     * @return list of gateways in the region
     */
    @GetMapping(value = "/regions/{regionId}/gateways", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGatewaysInRegion(
            @PathVariable Long regionId,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("Received request to get gateways in region with ID {} to depth {}", regionId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            List<GatewayNode> gateways = graphQueryService.getGatewaysInRegion(regionId, actualDepth);
            return ResponseHelper.successful(gateways);
        } catch (Exception e) {
            log.error("Error getting gateways in region", e);
            return ResponseHelper.failed("Error getting gateways in region: " + e.getMessage());
        }
    }
    
    /**
     * Get all devices in a region
     * 
     * @param regionId the ID of the region
     * @param depth the maximum depth to traverse (optional, default is 1)
     * @return list of devices in the region
     */
    @GetMapping(value = "/regions/{regionId}/devices", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDevicesInRegion(
            @PathVariable Long regionId,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("Received request to get devices in region with ID {} to depth {}", regionId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            List<DeviceNode> devices = graphQueryService.getDevicesInRegion(regionId, actualDepth);
            return ResponseHelper.successful(devices);
        } catch (Exception e) {
            log.error("Error getting devices in region", e);
            return ResponseHelper.failed("Error getting devices in region: " + e.getMessage());
        }
    }
    
    /**
     * Get devices by type in a region
     * 
     * @param regionId the ID of the region
     * @param deviceType the device type
     * @param depth the maximum depth to traverse (optional, default is 1)
     * @return list of devices of the given type in the region
     */
    @GetMapping(value = "/regions/{regionId}/devices/type/{deviceType}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDevicesByTypeInRegion(
            @PathVariable Long regionId,
            @PathVariable String deviceType,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("Received request to get devices of type {} in region with ID {} to depth {}", deviceType, regionId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            List<DeviceNode> devices = graphQueryService.getDevicesByTypeInRegion(regionId, deviceType, actualDepth);
            return ResponseHelper.successful(devices);
        } catch (Exception e) {
            log.error("Error getting devices by type in region", e);
            return ResponseHelper.failed("Error getting devices by type in region: " + e.getMessage());
        }
    }
    
    /**
     * Get devices by manufacturer in a region
     * 
     * @param regionId the ID of the region
     * @param manufacturer the manufacturer name
     * @param depth the maximum depth to traverse (optional, default is 1)
     * @return list of devices from the given manufacturer in the region
     */
    @GetMapping(value = "/regions/{regionId}/devices/manufacturer/{manufacturer}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDevicesByManufacturerInRegion(
            @PathVariable Long regionId,
            @PathVariable String manufacturer,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("Received request to get devices from manufacturer {} in region with ID {} to depth {}", manufacturer, regionId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            List<DeviceNode> devices = graphQueryService.getDevicesByManufacturerInRegion(regionId, manufacturer, actualDepth);
            return ResponseHelper.successful(devices);
        } catch (Exception e) {
            log.error("Error getting devices by manufacturer in region", e);
            return ResponseHelper.failed("Error getting devices by manufacturer in region: " + e.getMessage());
        }
    }
    
    /**
     * Get a gateway with its devices
     * 
     * @param gatewayId the ID of the gateway
     * @return the gateway with its devices
     */
    @GetMapping(value = "/gateways/{gatewayId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGatewayWithDevices(@PathVariable String gatewayId) {
        log.info("Received request to get gateway with ID {} and its devices", gatewayId);
        
        try {
            Optional<GatewayNode> gateway = graphQueryService.getGatewayWithDevices(gatewayId);
            
            if (gateway.isPresent()) {
                return ResponseHelper.successful(gateway.get());
            } else {
                return ResponseHelper.failed("Gateway not found with ID: " + gatewayId);
            }
        } catch (Exception e) {
            log.error("Error getting gateway with devices", e);
            return ResponseHelper.failed("Error getting gateway with devices: " + e.getMessage());
        }
    }
    
    /**
     * Find the path between two entities
     * 
     * @param sourceId the ID of the source entity
     * @param targetId the ID of the target entity
     * @return the path between the entities
     */
    @GetMapping(value = "/path", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findPath(
            @RequestParam String sourceId,
            @RequestParam String targetId) {
        log.info("Received request to find path between source {} and target {}", sourceId, targetId);
        
        try {
            List<Map<String, Object>> path = graphQueryService.findPath(sourceId, targetId);
            
            if (!path.isEmpty()) {
                return ResponseHelper.successful(path);
            } else {
                return ResponseHelper.failed("No path found between source and target");
            }
        } catch (Exception e) {
            log.error("Error finding path", e);
            return ResponseHelper.failed("Error finding path: " + e.getMessage());
        }
    }
    
    /**
     * Synchronize a specific region and its descendants
     * 
     * @param regionId the ID of the region to synchronize
     * @return the synchronized region node
     */
    @PostMapping(value = "/sync/regions/{regionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> syncRegion(@PathVariable Long regionId) {
        log.info("Received request to synchronize region with ID: {}", regionId);
        
        try {
            RegionNode region = graphSyncService.syncRegion(regionId);
            
            if (region != null) {
                return ResponseHelper.successful(region);
            } else {
                return ResponseHelper.failed("Failed to synchronize region with ID: " + regionId);
            }
        } catch (Exception e) {
            log.error("Error synchronizing region", e);
            return ResponseHelper.failed("Error synchronizing region: " + e.getMessage());
        }
    }
    
    /**
     * Synchronize a specific gateway and its devices
     * 
     * @param gatewayId the ID of the gateway to synchronize
     * @return the synchronized gateway node
     */
    @PostMapping(value = "/sync/gateways/{gatewayId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> syncGateway(@PathVariable String gatewayId) {
        log.info("Received request to synchronize gateway with ID: {}", gatewayId);
        
        try {
            GatewayNode gateway = graphSyncService.syncGateway(gatewayId);
            
            if (gateway != null) {
                return ResponseHelper.successful(gateway);
            } else {
                return ResponseHelper.failed("Failed to synchronize gateway with ID: " + gatewayId);
            }
        } catch (Exception e) {
            log.error("Error synchronizing gateway", e);
            return ResponseHelper.failed("Error synchronizing gateway: " + e.getMessage());
        }
    }
    
    /**
     * Synchronize a specific device
     * 
     * @param deviceId the ID of the device to synchronize
     * @return the synchronized device node
     */
    @PostMapping(value = "/sync/devices/{deviceId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> syncDevice(@PathVariable String deviceId) {
        log.info("Received request to synchronize device with ID: {}", deviceId);
        
        try {
            DeviceNode device = graphSyncService.syncDevice(deviceId);
            
            if (device != null) {
                return ResponseHelper.successful(device);
            } else {
                return ResponseHelper.failed("Failed to synchronize device with ID: " + deviceId);
            }
        } catch (Exception e) {
            log.error("Error synchronizing device", e);
            return ResponseHelper.failed("Error synchronizing device: " + e.getMessage());
        }
    }
}
