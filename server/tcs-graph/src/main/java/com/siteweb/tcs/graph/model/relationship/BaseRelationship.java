package com.siteweb.tcs.graph.model.relationship;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Property;

import java.time.LocalDateTime;

/**
 * Base class for all Neo4j relationship entities
 */
@Data
public abstract class BaseRelationship {
    
    @Id
    @GeneratedValue
    private Long id;
    
    @Property("created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Property("updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
}
