package com.siteweb.tcs.graph.model.relationship;

import com.siteweb.tcs.graph.model.node.DeviceNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.Property;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

/**
 * Has Device relationship entity for Neo4j
 * Used for gateway-to-device relationships
 */
@RelationshipProperties
@Data
@EqualsAndHashCode(callSuper = true)
public class HasDeviceRelationship extends BaseRelationship {
    
    @TargetNode
    private DeviceNode device;
    
    @Property("connection_type")
    private String connectionType;
}
