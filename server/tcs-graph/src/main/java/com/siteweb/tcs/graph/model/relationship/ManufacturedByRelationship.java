package com.siteweb.tcs.graph.model.relationship;

import com.siteweb.tcs.graph.model.node.ManufacturerNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.Property;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

/**
 * Manufactured By relationship entity for Neo4j
 * Used for device/gateway to manufacturer relationships
 */
@RelationshipProperties
@Data
@EqualsAndHashCode(callSuper = true)
public class ManufacturedByRelationship extends BaseRelationship {
    
    @TargetNode
    private ManufacturerNode manufacturer;
    
    @Property("model")
    private String model;
    
    @Property("production_date")
    private String productionDate;
}
