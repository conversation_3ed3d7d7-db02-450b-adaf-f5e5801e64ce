package com.siteweb.tcs.graph.repository;

import com.siteweb.tcs.graph.model.node.DeviceNode;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for DeviceNode entities
 */
@Repository
public interface DeviceNodeRepository extends Neo4jRepository<DeviceNode, Long> {
    
    /**
     * Find a device by its source ID
     * 
     * @param sourceId the source ID from the relational database
     * @return the device node if found
     */
    Optional<DeviceNode> findBySourceId(String sourceId);
    
    /**
     * Find devices by foreign device ID
     * 
     * @param foreignDeviceId the foreign device ID
     * @return list of devices with the given foreign device ID
     */
    List<DeviceNode> findByForeignDeviceId(String foreignDeviceId);
    
    /**
     * Find devices by equipment ID
     * 
     * @param equipmentId the equipment ID
     * @return list of devices with the given equipment ID
     */
    List<DeviceNode> findByEquipmentId(Integer equipmentId);
    
    /**
     * Find devices by device type
     * 
     * @param deviceType the device type
     * @return list of devices of the given type
     */
    List<DeviceNode> findByDeviceType(String deviceType);
    
    /**
     * Find devices by manufacturer
     * 
     * @param manufacturer the manufacturer name
     * @return list of devices from the given manufacturer
     */
    List<DeviceNode> findByManufacturer(String manufacturer);
    
    /**
     * Find devices by model
     * 
     * @param model the model name
     * @return list of devices of the given model
     */
    List<DeviceNode> findByModel(String model);
    
    /**
     * Find all devices in a region (directly or indirectly)
     * 
     * @param regionSourceId the source ID of the region
     * @param depth the maximum depth to traverse
     * @return list of devices in the region
     */
    @Query("MATCH (r:Region {source_id: $regionSourceId})-[:CONTAINS*1..$depth]->(g:Gateway)-[:HAS_DEVICE]->(d:Device) " +
           "RETURN d")
    List<DeviceNode> findDevicesInRegion(@Param("regionSourceId") String regionSourceId, @Param("depth") int depth);
    
    /**
     * Find all devices of a specific type in a region
     * 
     * @param regionSourceId the source ID of the region
     * @param deviceType the device type
     * @param depth the maximum depth to traverse
     * @return list of devices of the given type in the region
     */
    @Query("MATCH (r:Region {source_id: $regionSourceId})-[:CONTAINS*1..$depth]->(g:Gateway)-[:HAS_DEVICE]->(d:Device) " +
           "WHERE d.device_type = $deviceType " +
           "RETURN d")
    List<DeviceNode> findDevicesInRegionByType(
            @Param("regionSourceId") String regionSourceId,
            @Param("deviceType") String deviceType,
            @Param("depth") int depth);
}
