package com.siteweb.tcs.graph.repository;

import com.siteweb.tcs.graph.model.node.ManufacturerNode;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for ManufacturerNode entities
 */
@Repository
public interface ManufacturerNodeRepository extends Neo4jRepository<ManufacturerNode, Long> {
    
    /**
     * Find a manufacturer by its name
     * 
     * @param name the manufacturer name
     * @return the manufacturer node if found
     */
    Optional<ManufacturerNode> findByName(String name);
    
    /**
     * Find a manufacturer by its source ID
     * 
     * @param sourceId the source ID from the relational database
     * @return the manufacturer node if found
     */
    Optional<ManufacturerNode> findBySourceId(String sourceId);
    
    /**
     * Find all manufacturers with their devices and gateways
     * 
     * @return list of manufacturers with relationships
     */
    @Query("MATCH (m:Manufacturer) " +
           "OPTIONAL MATCH (d:Device)-[:MANUFACTURED_BY]->(m) " +
           "OPTIONAL MATCH (g:Gateway)-[:MANUFACTURED_BY]->(m) " +
           "RETURN m, collect(d) as devices, collect(g) as gateways")
    List<ManufacturerNode> findAllWithRelationships();
    
    /**
     * Find manufacturers with devices of a specific type
     * 
     * @param deviceType the device type
     * @return list of manufacturers that produce the given device type
     */
    @Query("MATCH (d:Device)-[:MANUFACTURED_BY]->(m:Manufacturer) " +
           "WHERE d.device_type = $deviceType " +
           "RETURN DISTINCT m")
    List<ManufacturerNode> findManufacturersWithDeviceType(@Param("deviceType") String deviceType);
}
