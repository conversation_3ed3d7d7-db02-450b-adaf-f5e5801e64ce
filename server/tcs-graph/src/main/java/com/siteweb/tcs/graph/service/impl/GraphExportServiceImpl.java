package com.siteweb.tcs.graph.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.graph.model.node.RegionNode;
import com.siteweb.tcs.graph.repository.DeviceNodeRepository;
import com.siteweb.tcs.graph.repository.GatewayNodeRepository;
import com.siteweb.tcs.graph.repository.RegionNodeRepository;
import com.siteweb.tcs.graph.service.GraphExportService;
import com.siteweb.tcs.graph.service.GraphQueryService;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.Driver;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 图数据导出服务实现类
 */
@Service
@Slf4j
public class GraphExportServiceImpl implements GraphExportService {
    
    @Autowired
    private Driver neo4jDriver;
    
    @Autowired
    private RegionNodeRepository regionNodeRepository;
    
    @Autowired
    private GatewayNodeRepository gatewayNodeRepository;
    
    @Autowired
    private DeviceNodeRepository deviceNodeRepository;
    
    @Autowired
    private GraphQueryService graphQueryService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Value("${tcs.graph.query.max-traversal-depth:5}")
    private int maxTraversalDepth;
    
    @Override
    public String exportGraphToJson() {
        log.info("导出整个图数据为JSON格式");
        
        try {
            Map<String, Object> graphData = new HashMap<>();
            
            // 获取所有节点
            List<Map<String, Object>> nodes = new ArrayList<>();
            try (Session session = neo4jDriver.session()) {
                Result result = session.run("MATCH (n) RETURN n, labels(n) as labels");
                result.list().forEach(record -> {
                    Map<String, Object> node = new HashMap<>(record.get("n").asMap());
                    node.put("id", record.get("n").asNode().id());
                    node.put("labels", record.get("labels").asList(value -> value.asString()));
                    nodes.add(node);
                });
            }
            
            // 获取所有关系
            List<Map<String, Object>> relationships = new ArrayList<>();
            try (Session session = neo4jDriver.session()) {
                Result result = session.run("MATCH ()-[r]->() RETURN r, type(r) as type, id(startNode(r)) as source, id(endNode(r)) as target");
                result.list().forEach(record -> {
                    Map<String, Object> relationship = new HashMap<>(record.get("r").asMap());
                    relationship.put("id", record.get("r").asRelationship().id());
                    relationship.put("type", record.get("type").asString());
                    relationship.put("source", record.get("source").asLong());
                    relationship.put("target", record.get("target").asLong());
                    relationships.add(relationship);
                });
            }
            
            graphData.put("nodes", nodes);
            graphData.put("relationships", relationships);
            
            return objectMapper.writeValueAsString(graphData);
        } catch (Exception e) {
            log.error("导出图数据为JSON格式时出错", e);
            return "{ \"error\": \"" + e.getMessage() + "\" }";
        }
    }
    
    @Override
    public String exportRegionToJson(Long regionId, int depth) {
        log.info("导出区域 {} 的图数据为JSON格式，深度为 {}", regionId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            Optional<RegionNode> regionOpt = regionNodeRepository.findById(regionId);
            
            if (regionOpt.isEmpty()) {
                return "{ \"error\": \"区域不存在\" }";
            }
            
            RegionNode region = regionOpt.get();
            Optional<RegionNode> regionWithDescendants = regionNodeRepository.findRegionWithDescendants(
                    region.getSourceId(), actualDepth);
            
            if (regionWithDescendants.isEmpty()) {
                return "{ \"error\": \"无法获取区域及其后代\" }";
            }
            
            return objectMapper.writeValueAsString(regionWithDescendants.get());
        } catch (Exception e) {
            log.error("导出区域图数据为JSON格式时出错", e);
            return "{ \"error\": \"" + e.getMessage() + "\" }";
        }
    }
    
    @Override
    public String exportGatewayToJson(String gatewayId) {
        log.info("导出网关 {} 的图数据为JSON格式", gatewayId);
        
        try {
            Optional<Map<String, Object>> gatewayData = Optional.empty();
            
            try (Session session = neo4jDriver.session()) {
                String query = "MATCH (g:Gateway {source_id: $gatewayId}) " +
                        "OPTIONAL MATCH (g)-[r:HAS_DEVICE]->(d:Device) " +
                        "RETURN g, collect({relationship: r, device: d}) as devices";
                
                Result result = session.run(query, Map.of("gatewayId", gatewayId));
                
                if (result.hasNext()) {
                    Record record = result.next();
                    Map<String, Object> gateway = new HashMap<>(record.get("g").asMap());
                    gateway.put("id", record.get("g").asNode().id());
                    
                    List<Map<String, Object>> devices = new ArrayList<>();
                    record.get("devices").asList(value -> {
                        if (value.get("device").isNull()) {
                            return null;
                        }
                        
                        Map<String, Object> device = new HashMap<>(value.get("device").asMap());
                        device.put("id", value.get("device").asNode().id());
                        
                        Map<String, Object> relationship = new HashMap<>();
                        if (!value.get("relationship").isNull()) {
                            relationship = new HashMap<>(value.get("relationship").asMap());
                            relationship.put("id", value.get("relationship").asRelationship().id());
                            relationship.put("type", value.get("relationship").asRelationship().type());
                        }
                        
                        return Map.of("device", device, "relationship", relationship);
                    }).forEach(item -> {
                        if (item != null) {
                            devices.add(item);
                        }
                    });
                    
                    gateway.put("devices", devices);
                    gatewayData = Optional.of(gateway);
                }
            }
            
            if (gatewayData.isEmpty()) {
                return "{ \"error\": \"网关不存在\" }";
            }
            
            return objectMapper.writeValueAsString(gatewayData.get());
        } catch (Exception e) {
            log.error("导出网关图数据为JSON格式时出错", e);
            return "{ \"error\": \"" + e.getMessage() + "\" }";
        }
    }
    
    @Override
    public String exportStatisticsToJson() {
        log.info("导出图统计信息为JSON格式");
        
        try {
            Map<String, Object> statistics = graphQueryService.getGraphStatistics();
            return objectMapper.writeValueAsString(statistics);
        } catch (Exception e) {
            log.error("导出图统计信息为JSON格式时出错", e);
            return "{ \"error\": \"" + e.getMessage() + "\" }";
        }
    }
    
    @Override
    public String exportGraphToGraphML() {
        log.info("导出图数据为GraphML格式");
        
        try {
            StringBuilder graphML = new StringBuilder();
            graphML.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
            graphML.append("<graphml xmlns=\"http://graphml.graphdrawing.org/xmlns\"\n");
            graphML.append("         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n");
            graphML.append("         xsi:schemaLocation=\"http://graphml.graphdrawing.org/xmlns\n");
            graphML.append("         http://graphml.graphdrawing.org/xmlns/1.0/graphml.xsd\">\n");
            
            // 添加属性定义
            graphML.append("  <key id=\"name\" for=\"node\" attr.name=\"name\" attr.type=\"string\"/>\n");
            graphML.append("  <key id=\"source_id\" for=\"node\" attr.name=\"source_id\" attr.type=\"string\"/>\n");
            graphML.append("  <key id=\"label\" for=\"node\" attr.name=\"label\" attr.type=\"string\"/>\n");
            graphML.append("  <key id=\"type\" for=\"edge\" attr.name=\"type\" attr.type=\"string\"/>\n");
            
            graphML.append("  <graph id=\"G\" edgedefault=\"directed\">\n");
            
            // 添加节点
            try (Session session = neo4jDriver.session()) {
                Result result = session.run("MATCH (n) RETURN id(n) as id, n, labels(n) as labels");
                result.list().forEach(record -> {
                    long id = record.get("id").asLong();
                    Map<String, Object> properties = record.get("n").asMap();
                    List<String> labels = record.get("labels").asList(value -> value.asString());
                    
                    graphML.append("    <node id=\"n").append(id).append("\">\n");
                    graphML.append("      <data key=\"label\">").append(String.join(":", labels)).append("</data>\n");
                    
                    if (properties.containsKey("name")) {
                        graphML.append("      <data key=\"name\">").append(properties.get("name")).append("</data>\n");
                    }
                    
                    if (properties.containsKey("source_id")) {
                        graphML.append("      <data key=\"source_id\">").append(properties.get("source_id")).append("</data>\n");
                    }
                    
                    graphML.append("    </node>\n");
                });
            }
            
            // 添加关系
            try (Session session = neo4jDriver.session()) {
                Result result = session.run("MATCH ()-[r]->() RETURN id(r) as id, type(r) as type, id(startNode(r)) as source, id(endNode(r)) as target");
                result.list().forEach(record -> {
                    long id = record.get("id").asLong();
                    String type = record.get("type").asString();
                    long source = record.get("source").asLong();
                    long target = record.get("target").asLong();
                    
                    graphML.append("    <edge id=\"e").append(id).append("\" source=\"n").append(source).append("\" target=\"n").append(target).append("\">\n");
                    graphML.append("      <data key=\"type\">").append(type).append("</data>\n");
                    graphML.append("    </edge>\n");
                });
            }
            
            graphML.append("  </graph>\n");
            graphML.append("</graphml>");
            
            return graphML.toString();
        } catch (Exception e) {
            log.error("导出图数据为GraphML格式时出错", e);
            return "<!-- Error: " + e.getMessage() + " -->";
        }
    }
    
    @Override
    public Map<String, String> exportGraphToCSV() {
        log.info("导出图数据为CSV格式");
        
        Map<String, String> csvFiles = new HashMap<>();
        
        try {
            // 导出节点CSV
            StringBuilder nodesCSV = new StringBuilder();
            nodesCSV.append("id,labels,source_id,name,created_at,updated_at\n");
            
            try (Session session = neo4jDriver.session()) {
                Result result = session.run("MATCH (n) RETURN id(n) as id, labels(n) as labels, n.source_id as source_id, n.name as name, n.created_at as created_at, n.updated_at as updated_at");
                result.list().forEach(record -> {
                    nodesCSV.append(record.get("id").asLong()).append(",");
                    nodesCSV.append("\"").append(String.join(":", record.get("labels").asList(value -> value.asString()))).append("\",");
                    nodesCSV.append("\"").append(record.get("source_id").asString()).append("\",");
                    nodesCSV.append("\"").append(record.get("name").asString()).append("\",");
                    nodesCSV.append("\"").append(record.get("created_at").asString()).append("\",");
                    nodesCSV.append("\"").append(record.get("updated_at").asString()).append("\"\n");
                });
            }
            
            csvFiles.put("nodes.csv", nodesCSV.toString());
            
            // 导出关系CSV
            StringBuilder relationshipsCSV = new StringBuilder();
            relationshipsCSV.append("id,type,source_id,target_id,created_at,updated_at\n");
            
            try (Session session = neo4jDriver.session()) {
                Result result = session.run("MATCH ()-[r]->() RETURN id(r) as id, type(r) as type, id(startNode(r)) as source_id, id(endNode(r)) as target_id, r.created_at as created_at, r.updated_at as updated_at");
                result.list().forEach(record -> {
                    relationshipsCSV.append(record.get("id").asLong()).append(",");
                    relationshipsCSV.append("\"").append(record.get("type").asString()).append("\",");
                    relationshipsCSV.append(record.get("source_id").asLong()).append(",");
                    relationshipsCSV.append(record.get("target_id").asLong()).append(",");
                    
                    if (record.get("created_at").isNull()) {
                        relationshipsCSV.append("\"\",");
                    } else {
                        relationshipsCSV.append("\"").append(record.get("created_at").asString()).append("\",");
                    }
                    
                    if (record.get("updated_at").isNull()) {
                        relationshipsCSV.append("\"\"\n");
                    } else {
                        relationshipsCSV.append("\"").append(record.get("updated_at").asString()).append("\"\n");
                    }
                });
            }
            
            csvFiles.put("relationships.csv", relationshipsCSV.toString());
            
            return csvFiles;
        } catch (Exception e) {
            log.error("导出图数据为CSV格式时出错", e);
            csvFiles.put("error.txt", "Error: " + e.getMessage());
            return csvFiles;
        }
    }
}
