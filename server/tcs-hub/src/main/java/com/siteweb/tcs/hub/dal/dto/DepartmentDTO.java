package com.siteweb.tcs.hub.dal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 部门数据传输对象
 */
@Data
@NoArgsConstructor
public class DepartmentDTO {

    /**
     * 部门ID
     */
    private Integer id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门编码
     */
    private String code;

    /**
     * 父部门ID
     */
    private Integer parentId;

    /**
     * 父部门名称
     */
    private String parentName;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 启用状态：1-启用，0-停用
     */
    private Integer status;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子部门列表（用于树形结构）
     */
    private List<DepartmentDTO> children;

    /**
     * 是否有子部门
     */
    private Boolean hasChildren;
}