package com.siteweb.tcs.hub.dal.dto;

import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.HandleDeviceLifeCycleRequest;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class EquipmentDTO {
    private Integer stationId;
    private Integer equipmentId;
    private String equipmentName;
    private String equipmentNo;
    private String equipmentModule;
    private String equipmentStyle;
    private Integer assetState;
    private Double price;
    private Double usedLimit;
    private LocalDateTime usedDate;
    private LocalDateTime buyDate;
    private String vendor;
    private String unit;
    private Integer equipmentCategory;
    private Integer equipmentType;
    private Integer equipmentClass;
    private Integer equipmentState;
    private String eventExpression;
    private Double startDelay;
    private Double endDelay;
    private String property;
    private String description;
    private Integer equipmentTemplateId;
    private Integer houseId;
    private Integer monitorUnitId;
    private Integer workStationId;
    private Integer samplerUnitId;
    private Integer displayIndex;
    private Integer connectState;
    private LocalDateTime updateTime;
    private String parentEquipmentId;
    private String ratedCapacity;
    private String installedModule;
    private String projectName;
    private String contractNo;
    private LocalDateTime installTime;
    private String equipmentSn;
    private String so;
    private Integer resourceStructureId;
    private List<SignalDTO> signalDTOList;
    private List<AlarmDTO> alarmDTOList;
    private List<ControlDTO> controlDTOList;

    private LifeCycleEventType eventType;

    public ForeignDevice toForeignDevice(String foreignGatewayID, String foreignDeviceID, String pluginId) {
        ForeignDevice foreignDevice = new ForeignDevice();
        foreignDevice.setForeignDeviceID(foreignDeviceID)
                .setForeignGatewayID(foreignGatewayID)
                .setMonitorUnitId(monitorUnitId)
                .setEquipmentId(equipmentId)
                .setEquipmentTemplateId(equipmentTemplateId)
                .setPluginId(pluginId);
        return foreignDevice;
    }
}

