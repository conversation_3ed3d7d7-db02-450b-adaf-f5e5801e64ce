package com.siteweb.tcs.hub.dal.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import lombok.Data;

import java.util.List;

/**
 * 网关配置变更DTO
 */
@Data
public class GatewayConfigChangeDto {

    /**
     * 网关ID
     */
    private Long id;

    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 南向网关ID
     */
    private String southGatewayId;

    /**
     * 南向网关名称
     */
    private String southGatewayName;

    /**
     * 南向地址
     */
    private String southAddress;

    /**
     * 南向元数据
     */
    private JsonNode metadata;

    /**
     * 逻辑删除标志
     */
    private Boolean deleted;

    /**
     * 生命周期事件类型
     */
    private LifeCycleEventEnum lifeCycleEvent;

    /**
     * 设备配置变更列表（可选）
     */
    private List<DeviceConfigChangeDto> devices;

    public CreateIdMapDto getCreateIdMapDto(){
        CreateIdMapDto createIdMapDto = new CreateIdMapDto();
        if(lifeCycleEvent == LifeCycleEventEnum.CREATE) {
            createIdMapDto.setGatewayId(id);
            createIdMapDto.setSouthGatewayId(southGatewayId);
        }
        if(CollectionUtil.isNotEmpty(devices)){
            devices.forEach(e -> e.getCreateIdMapDto(createIdMapDto));
        }
        return createIdMapDto;
    }
} 