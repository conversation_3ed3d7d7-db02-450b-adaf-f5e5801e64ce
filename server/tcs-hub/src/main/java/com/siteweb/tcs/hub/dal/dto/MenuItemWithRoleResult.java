package com.siteweb.tcs.hub.dal.dto;

import lombok.Data;

/**
 * 菜单项及角色查询结果DTO
 * 用于处理菜单与角色关联查询的结果
 * 
 * <AUTHOR> Code
 * @since 1.0.0
 */
@Data
public class MenuItemWithRoleResult {
    
    /**
     * 菜单项ID
     */
    private Integer menuItemId;
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 菜单名称
     */
    private String menuItemName;
    
    /**
     * 路由名称（用于路由跳转）
     */
    private String name;
    
    /**
     * 父菜单ID
     */
    private Integer parentMenuItemId;
    
    /**
     * 路由路径
     */
    private String path;
    
    /**
     * 图标
     */
    private String icon;
    
    /**
     * 组件路径
     */
    private String component;
    
    /**
     * 是否在导航菜单中显示
     */
    private Boolean showLink;
    
    /**
     * 是否显示父级菜单
     */
    private Boolean showParent;
    
    /**
     * 激活路径
     */
    private String activePath;
    
    /**
     * 重定向路径
     */
    private String redirect;
    
    /**
     * 排序权重
     */
    private Integer rank;
    
    /**
     * 权限标识
     */
    private String auths;
    
    /**
     * 角色代码
     */
    private String roleCode;
    
    /**
     * 角色名称
     */
    private String roleName;
}