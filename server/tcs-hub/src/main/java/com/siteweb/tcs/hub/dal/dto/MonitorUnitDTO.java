package com.siteweb.tcs.hub.dal.dto;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 监控单元数据传输类
 */
@Data
public class MonitorUnitDTO implements Serializable {
    private Integer monitorUnitId;
    private String monitorUnitName;
    private Integer monitorUnitCategory;
    private String monitorUnitCode;
    private Integer workStationId;
    private Integer stationId;
    private String ipAddress;
    private Integer runMode;
    private String configFileCode;
    private LocalDateTime configUpdateTime;
    private String sampleConfigCode;
    private String softwareVersion;
    private String description;
    private LocalDateTime startTime;
    private LocalDateTime heartbeatTime;
    private Integer connectState;
    private LocalDateTime updateTime;
    private Boolean isSync;
    private LocalDateTime syncTime;
    private Boolean isConfigOK;
    private String configFileCode_Old;
    private String sampleConfigCode_Old;
    private Integer appConfigId;
    private Boolean canDistribute;
    private Boolean enable;


    /**
     * RDS服务器
     */
    private String rdsServer;

    /**
     * 数据服务器
     */
    private String dataServer;

    private LocalDateTime installTime;
    private Boolean fsu;
}
