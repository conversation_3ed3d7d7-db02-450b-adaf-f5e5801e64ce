package com.siteweb.tcs.hub.dal.dto;

import lombok.Data;

/**
 * 角色查询DTO
 * 用于支持前端角色列表的搜索和分页功能
 */
@Data
public class RoleQueryDTO {
    
    /**
     * 角色名称（模糊查询）
     */
    private String roleName;
    
    /**
     * 角色标识代码（模糊查询）
     */
    private String roleCode;
    
    /**
     * 启用状态：1=启用，0=停用，null=全部
     */
    private Integer status;
    
    /**
     * 当前页码，从1开始
     */
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 20;
    
    /**
     * 排序字段
     */
    private String sortField = "sort";
    
    /**
     * 排序方向：asc=升序，desc=降序
     */
    private String sortOrder = "asc";
}