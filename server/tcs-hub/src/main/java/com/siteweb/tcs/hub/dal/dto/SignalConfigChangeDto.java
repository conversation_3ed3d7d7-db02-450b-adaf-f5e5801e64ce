package com.siteweb.tcs.hub.dal.dto;

import cn.hutool.core.lang.Pair;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.JsonObject;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import lombok.Data;

import java.util.Map;

/**
 * 信号配置变更DTO
 */
@Data
public class SignalConfigChangeDto {

    /**
     * 信号ID
     */
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 南向信号ID
     */
    private String southSignalId;

    /**
     * 南向信号名称
     */
    private String southSignalName;

    /**
     * 南向信号含义
     */
    private Map<Integer,String> southSignalMeanings;

    /**
     * 南向信号单位
     */
    private String southSignalUnit;

    /**
     * 信号类型
     */
    private Integer signalType;

    /**
     * 南向元数据
     */
    private JsonNode metadata;

    /**
     * 逻辑删除标志
     */
    private Boolean deleted;

    /**
     * 生命周期事件类型
     */
    private LifeCycleEventEnum lifeCycleEvent;

    public void getCreateIdMapDto(String southDeviceId,CreateIdMapDto createIdMapDto) {
        if(lifeCycleEvent == LifeCycleEventEnum.CREATE) createIdMapDto.getSouthSignalIdHubIdMap().put(Pair.of(southDeviceId,southSignalId),id);
    }


}