package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("tcs_account_role_map")
public class AccountRoleMap {

    @TableId(value = "userId", type = IdType.INPUT)
    private Integer userId;

    private Integer roleId;
}
