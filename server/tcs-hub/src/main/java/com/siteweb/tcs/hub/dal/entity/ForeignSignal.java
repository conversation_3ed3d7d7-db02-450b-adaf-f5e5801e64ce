package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@TableName(value = "tcs_foreign_signal")
public class ForeignSignal  implements Serializable {
    @TableField(exist = false)
    private String foreignGatewayID;
    @TableField(exist = false)
    private String foreignDeviceID;
    @TableField(value = "ForeignSignalId")
    private String foreignSignalID;
    @TableField(exist = false)
    private int monitorUnitId;
    @TableField(value = "EquipmentId")
    private int equipmentId;
    @TableField(value = "SignalId")
    private int signalId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ForeignSignal that = (ForeignSignal) o;
        return equipmentId == that.equipmentId && Objects.equals(foreignSignalID, that.foreignSignalID);
    }

    @Override
    public int hashCode() {
        return Objects.hash(foreignSignalID, equipmentId);
    }
}
