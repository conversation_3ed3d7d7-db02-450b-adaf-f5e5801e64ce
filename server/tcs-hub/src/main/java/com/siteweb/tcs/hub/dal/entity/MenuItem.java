package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@TableName("tcs_menu_item")
public class MenuItem {
    @TableId(value = "menuItemId", type = IdType.INPUT)
    private Integer menuItemId;
    /**
     * 插件ID
     */
    private String pluginId;
    /**
     * 菜单名称
     */
    private String menuItemName;
    /**
     * 路由名称（用于路由跳转）
     */

    private String name;
    /**
     * 父节点ID
     */
    private Integer parentMenuItemId;
    /**
     * 路由
     */
    private String path;
    /**
     * 图标
     */
    private String icon;
    /**
     * 组件路径（字符串形式）
     */
    private String component;
    /**
     * 是否在导航菜单中显示
     */
    private Boolean showLink;
    /**
     * 是否显示父级菜单
     */
    private Boolean showParent;
    /**
     * 激活路径（用于详情页等）
     */
    private String activePath;
    /**
     * 重定向路径
     */
    private String redirect;
    /**
     * 排序权重
     */
    private Integer rank;
    /**
     * 权限标识（JSON数组格式）
     */
    private String auths;
    /**
     * 可访问的角色列表（仅用于前端展示，不存储到数据库）
     * 该字段通过关联查询 tcs_role_permission_map 表动态填充
     */
    @TableField(exist = false)
    private List<String> roles;

    @TableField(exist = false)
    private List<MenuItem> children;
    public List<MenuItem> getChildren() {
        if(children == null){
            children = new ArrayList<>();
        }
        return children;
    }
}
