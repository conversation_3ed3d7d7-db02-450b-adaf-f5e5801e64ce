package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tcs_permission")
public class Permission {
    @TableId(value = "permissionId", type = IdType.AUTO)
    private Integer permissionId;
    /**
     * 插件ID
     */
    private String pluginId;
    /**
     * 权限名称
     */
    private String permissionName;
    /**
     * 权限类型
     */
    private Integer permissionType;
    /**
     * 描述
     */
    private String description;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}
