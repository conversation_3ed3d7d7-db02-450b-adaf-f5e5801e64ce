package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName("tcs_role")
public class Role {

    @TableId(value = "RoleId", type = IdType.AUTO)
    private Integer roleId;

    @TableField("RoleName")
    private String roleName;

    @TableField("Description")
    private String description;

    @TableField("role_code")
    private String roleCode;

    @TableField("status")
    private Integer status = 1; // 启用状态：1=启用，0=停用

    @TableField("sort_order")
    private Integer sort = 0; // 排序值，数值越小越靠前

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    //@TableField(exist = false)
    //List<RolePermissionMap> rolePermissionMapList;
}
