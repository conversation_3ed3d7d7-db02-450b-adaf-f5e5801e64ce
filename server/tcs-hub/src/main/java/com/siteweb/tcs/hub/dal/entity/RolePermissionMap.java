package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.common.enums.PermissionType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 角色权限映射实体
 * 用于存储角色与各种类型权限的映射关系
 * 
 * <AUTHOR> Code
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tcs_role_permission_map")
public class RolePermissionMap {
    
    /**
     * 插件ID
     */
    @TableField("PluginId")
    private String pluginId;
    
    /**
     * 角色ID
     */
    @TableField("RoleId")
    private Integer roleId;
    
    /**
     * 权限ID
     * - 当权限类型为MENU时，此字段存储menuItemId
     * - 当权限类型为REGION时，此字段存储regionId
     * - 当权限类型为其他类型时，存储对应的权限资源ID
     */
    @TableField("PermissionId")
    private Integer permissionId;
    
    /**
     * 权限类型
     * 使用PermissionType枚举，支持菜单权限、区域权限、按钮权限等
     */
    @TableField("permissionType")
    private PermissionType permissionType;

    /**
     * 构造器 - 创建菜单权限映射
     * @param pluginId 插件ID
     * @param roleId 角色ID
     * @param menuItemId 菜单项ID
     * @return 菜单权限映射对象
     */
    public static RolePermissionMap createMenuPermission(String pluginId, Integer roleId, Integer menuItemId) {
        return new RolePermissionMap(pluginId, roleId, menuItemId, PermissionType.MENU);
    }

    /**
     * 构造器 - 创建区域权限映射
     * @param pluginId 插件ID
     * @param roleId 角色ID
     * @param regionId 区域ID
     * @return 区域权限映射对象
     */
    public static RolePermissionMap createRegionPermission(String pluginId, Integer roleId, Integer regionId) {
        return new RolePermissionMap(pluginId, roleId, regionId, PermissionType.REGION);
    }

    /**
     * 检查是否为菜单权限
     * @return 是否为菜单权限
     */
    public boolean isMenuPermission() {
        return permissionType != null && permissionType.isMenu();
    }

    /**
     * 检查是否为区域权限
     * @return 是否为区域权限
     */
    public boolean isRegionPermission() {
        return permissionType != null && permissionType.isRegion();
    }
}
