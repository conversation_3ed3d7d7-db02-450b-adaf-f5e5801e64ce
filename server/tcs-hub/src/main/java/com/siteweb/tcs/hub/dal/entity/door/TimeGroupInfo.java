package com.siteweb.tcs.hub.dal.entity.door;

import lombok.Data;

import java.util.List;

/**
 * @ClassName: TimeGroupInfo
 * @descriptions: 时间组实体
 * @author: xsx
 * @date: 2024/9/19 16:55
 **/
@Data
public class TimeGroupInfo {
    /**
     * 时间组id
     */
    private Integer timeGroupId;
    /**
     * 时间组名称
     */
    private String timeGroupName;
    /**
     * 时间组类型
     */
    private Integer timeGroupType;
    /**
     * 时间组种类
     */
    private Integer timeGroupCategory;
    /**
     * 时间段列表
     */
    private List<TimeGroupSpanInfo> timeGroupSpanInfoList;
}
