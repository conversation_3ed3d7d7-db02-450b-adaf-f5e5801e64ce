package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.entity.AuthCode;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AuthCodeMapper extends BaseMapper<AuthCode> {
    
    /**
     * 获取用户的权限代码列表
     * @param userId 用户ID
     * @return 权限代码列表
     */
    @Select("""
        SELECT ac.auth_code FROM tcs_auth_code ac
        JOIN tcs_role_permission_map rpm ON ac.auth_id = rpm.PermissionId
        JOIN tcs_account_role_map arm ON rpm.RoleId = arm.RoleId
        WHERE arm.UserId = #{userId} AND rpm.permissionType = 3
        """)
    List<String> getUserAuthCodes(Integer userId);
    
    /**
     * 获取菜单的所有权限代码
     * @param menuItemId 菜单ID
     * @return 权限代码列表
     */
    @Select("SELECT * FROM tcs_auth_code WHERE menu_item_id = #{menuItemId}")
    List<AuthCode> getAuthCodesByMenuId(Long menuItemId);
    
    /**
     * 获取角色的权限代码
     * @param roleId 角色ID
     * @return 权限代码列表
     */
    @Select("""
        SELECT ac.* FROM tcs_auth_code ac
        JOIN tcs_role_permission_map rpm ON ac.auth_id = rpm.PermissionId
        WHERE rpm.RoleId = #{roleId} AND rpm.permissionType = 3
        """)
    List<AuthCode> getRoleAuthCodes(Integer roleId);
    
    /**
     * 通过角色ID列表获取权限代码
     * @param roleIds 角色ID列表
     * @return 权限代码列表
     */
    @Select("""
        <script>
        SELECT DISTINCT ac.auth_code FROM tcs_auth_code ac
        JOIN tcs_role_permission_map rpm ON ac.auth_id = rpm.PermissionId
        WHERE rpm.permissionType = 3 AND rpm.RoleId IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        </script>
        """)
    List<String> getUserAuthCodesByRoles(@Param("roleIds") List<Integer> roleIds);
    
    /**
     * 获取所有权限代码
     * @return 权限代码列表
     */
    @Select("SELECT * FROM tcs_auth_code ORDER BY menu_item_id, auth_id")
    List<AuthCode> getAllAuthCodes();
    
    /**
     * 根据权限ID列表批量查询插件ID
     * @param authIds 权限ID列表
     * @return 权限ID和插件ID的映射列表
     */
    @Select("""
        <script>
        SELECT auth_id, plugin_id FROM tcs_auth_code 
        WHERE auth_id IN 
        <foreach collection="authIds" item="authId" open="(" separator="," close=")">
            #{authId}
        </foreach>
        </script>
        """)
    @Results({
        @Result(property = "authId", column = "auth_id"),
        @Result(property = "pluginId", column = "plugin_id")
    })
    List<AuthPluginMapping> getPluginIdsByAuthIds(@Param("authIds") List<Long> authIds);
    
    /**
     * 权限ID与插件ID映射结果类
     */
    class AuthPluginMapping {
        private Long authId;
        private String pluginId;
        
        public Long getAuthId() { return authId; }
        public void setAuthId(Long authId) { this.authId = authId; }
        
        public String getPluginId() { return pluginId; }
        public void setPluginId(String pluginId) { this.pluginId = pluginId; }
    }
}