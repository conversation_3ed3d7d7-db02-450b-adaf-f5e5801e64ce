package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.entity.TcsAlarm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 告警表 Mapper 接口
 */
@Mapper
@Repository
public interface TcsAlarmMapper extends BaseMapper<TcsAlarm> {

    /**
     * 根据设备ID查询告警列表
     * @param deviceId 设备ID
     * @return 告警列表
     */
    List<TcsAlarm> selectByDeviceId(@Param("deviceId") Long deviceId);

    /**
     * 根据南向告警ID查询告警
     * @param southAlarmId 南向告警ID
     * @return 告警信息
     */
    TcsAlarm selectBySouthAlarmId(@Param("deviceId") Long deviceId,@Param("southAlarmId") String southAlarmId);

    /**
     * 根据关联信号ID查询告警列表
     * @param relatedSignalId 关联信号ID
     * @return 告警列表
     */
    List<TcsAlarm> selectByRelatedSignalId(@Param("relatedSignalId") Long relatedSignalId);
} 