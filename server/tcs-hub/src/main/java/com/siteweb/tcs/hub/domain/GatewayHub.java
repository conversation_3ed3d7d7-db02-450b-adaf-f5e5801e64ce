package com.siteweb.tcs.hub.domain;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.siteweb.tcs.common.ISerializableMessage;
import com.siteweb.tcs.common.sharding.IGatewayShardingMessage;
import com.siteweb.tcs.common.system.ClusterContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorPath;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.pattern.Patterns;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletionStage;

/**
 * <AUTHOR> (2025-07-11)
 **/
@Slf4j
public class GatewayHub extends AbstractActor {

    private final Map<String, GatewayCached> shardRegionMap = new HashMap<>();

    @Data
    public static class GatewayRegister implements ISerializableMessage {
        private String shardingName;
        private String pluginId;
        private String gatewayId;
        private ActorRef actor;
    }

    @Data
    private static class GatewayCached implements ISerializableMessage{
        private String shardingName;
        private String pluginId;
        private String gatewayId;
        private ActorRef actorRef;
        private ActorPath path;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class GetGateway implements ISerializableMessage{
        private String pluginId;
        private String gatewayId;
        private ActorRef replyTo;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ForwardMessage implements ISerializableMessage{

        /**
         * 目标插件ID
         */
        private String pluginId;

        /**
         * 目标的GatewayId
         */
        private String gatewayId;

        /**
         * 分片消息
         */
        @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
        private Object message;
    }


    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(GatewayRegister.class, this::registerGateway)
                .match(ForwardMessage.class, this::forward)
                .match(GetGateway.class, this::getGatewayHandle)
                .build();
    }

    private void getGatewayHandle(GetGateway get) {
        var uniqueName = get.getPluginId() + ":" + get.getGatewayId();
        var cached = shardRegionMap.get(uniqueName);
        if (cached != null) {
            get.getReplyTo().tell(cached.getActorRef(), self());
        } else {
            get.getReplyTo().tell(null, self());
        }
    }

    private void registerGateway(GatewayRegister register) {
        var uniqueName = register.pluginId + ":" + register.gatewayId;
        var cached = shardRegionMap.computeIfAbsent(uniqueName, (k) -> new GatewayCached());
        cached.setGatewayId(register.getGatewayId());
        cached.setPluginId(register.getPluginId());
        cached.setShardingName(register.getShardingName());
        cached.setActorRef(register.getActor());
        cached.setPath(register.getActor().path());
    }

    private void forward(ForwardMessage message) {
        var uniqueName = message.pluginId + ":" + message.gatewayId;
        var cached = shardRegionMap.get(uniqueName);
        if (cached == null) {
            log.error("Gateway Instance not found Plugin: {}, Gateway: {}", message.pluginId, message.gatewayId);
            return;
        }
//        var actor = ClusterContext.getClusterSharding().shardRegion(cached.shardingName);
        cached.getActorRef().tell(message.message, ActorRef.noSender());
    }

    private String getShardRegion(String pluginId, String gatewayId) {
        var uniqueName = pluginId + ":" + gatewayId;
        var cached = shardRegionMap.get(uniqueName);
        if (cached != null) return cached.shardingName;
        return null;
    }


    /**
     * 异步获取Gateway地址
     *
     * @param pluginId  插件ID
     * @param gatewayId Gateway ID
     * @return
     */

    public static CompletionStage<Object> getGatewayAsync(String pluginId, String gatewayId) {

        return getGatewayAsync(pluginId, gatewayId, Duration.ofSeconds(1));
    }

    public static CompletionStage<Object> getGatewayAsync(String pluginId, String gatewayId, Duration timeout) {
        var proxy = ClusterContext.getSingleton(GatewayHub.class);
        return Patterns.askWithReplyTo(
                proxy,
                (ActorRef replyTo) -> new GetGateway(pluginId, gatewayId, replyTo),
                timeout
        );
    }


}
