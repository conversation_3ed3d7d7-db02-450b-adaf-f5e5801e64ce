package com.siteweb.tcs.hub.domain.letter;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class EquipmentChange extends BaseEquipmentDTO {

    private Integer equipmentState;
    private LocalDateTime timeStamp;
    //是否发生变化
    private boolean isChange;

    private static final String redisKeyTemplate = "EqtConState:%s";
    private static final String redisValueTemplate = "\"%s~%s\"";


    public String getRedisKey(){
        return String.format(redisKeyTemplate,equipmentId);
    }

    public String getRedisValue(){
        return String.format(redisValueTemplate,equipmentId,equipmentState);
    }

    public static String getRedisKey(Integer equipmentId){
        return String.format(redisKeyTemplate,equipmentId);
    }

    public static String getRedisValue(Integer equipmentId,Integer equipmentState){
        return String.format(redisValueTemplate,equipmentId,equipmentState);
    }

}
