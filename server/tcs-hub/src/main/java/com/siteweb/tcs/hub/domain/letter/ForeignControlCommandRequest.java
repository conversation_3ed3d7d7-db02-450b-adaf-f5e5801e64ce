package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.ControlTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ForeignControlCommandRequest {
    private String foreignGatewayId;
    private String foreignDeviceId;
    private String foreignControlId;
    private ControlTypeEnum controlType;
    private Integer controlCategory;
    private DynamicValue parameter;
    private LocalDateTime startTime;
    private String sequenceNo;
}
