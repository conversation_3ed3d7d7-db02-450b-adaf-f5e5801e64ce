package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class ForeignDeviceChange {
    private String foreignGatewayId;
    private String foreignDeviceId;
    private String foreignDeviceName;
    private EnumDeviceConnectState connectState;
    private LocalDateTime timeStamp;
}
