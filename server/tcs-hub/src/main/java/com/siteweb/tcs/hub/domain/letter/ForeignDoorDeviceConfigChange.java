package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.siteweb.entity.Door;
import lombok.Data;

/**
 * @ClassName: ForeignDoorDeviceConfigChange
 * @descriptions: 外部有门禁设备配置变化实体类
 * @author: xsx
 * @date: 2024/9/21 15:31
 **/
@Data
public class ForeignDoorDeviceConfigChange extends ForeignDeviceConfigChange{
    public Integer doorCategoryId = 21;
    /**
     * 不传默认是1门
     */
    private Integer doorNo = 1;

    public Door toTblDoor(Integer stationId, Integer samplerUnitId, Integer equipmentId){
        Door tblDoor = new Door();
        tblDoor.setDoorName(getDeviceName());
        tblDoor.setStationId(stationId);
        tblDoor.setCategory(1);
        tblDoor.setEquipmentId(equipmentId);
        tblDoor.setSamplerUnitId(samplerUnitId);
        tblDoor.setDoorControlId(doorCategoryId);
        tblDoor.setDoorNo(doorNo);
        return tblDoor;
    }
}
