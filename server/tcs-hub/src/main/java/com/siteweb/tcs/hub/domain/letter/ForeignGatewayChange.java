package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.EnumGatewayConnectState;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
public class ForeignGatewayChange {
    private String ID;
    private String PluginID;
    private String PluginName;
    private String name;
    private String IP;
    private String port;
    private EnumGatewayConnectState connectState;
    private String firmWareVersion;
    private String softWareVersion;
    private String vendor;
    private String modal;
    private LocalDateTime timeStamp;

    public LocalDateTime getTimestamp() {
        return timeStamp;
    }
}
