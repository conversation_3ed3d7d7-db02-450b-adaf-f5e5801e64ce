package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.siteweb.dto.CreateMonitorUnitDTO;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class ForeignGatewayConfigChange implements Serializable {
    private String foreignGatewayID;
    private String pluginID;
    private String gatewayName;
    private String ip;
    private String port;
    private String firmWareVersion;
    private String softWareVersion;
    private String vendor;
    private String modal;
    //区域id
    private Integer regionId;

    private LifeCycleEventType eventType;

    private List<ForeignDeviceConfigChange> foreignDeviceConfigChangeList;

    //使用ForeignGateway增加构造函数


    public CreateMonitorUnitDTO toCreateMonitorUnitDTO(){
        CreateMonitorUnitDTO createMonitorUnitDTO = new CreateMonitorUnitDTO();
        createMonitorUnitDTO.setIpAddress(this.ip);
        createMonitorUnitDTO.setMonitorUnitName(this.gatewayName);
        createMonitorUnitDTO.setMonitorUnitCategory(10);
        return createMonitorUnitDTO;
    }

    public MonitorUnitDTO toMonitorUnitDTO(Integer monitorUnitId){
        MonitorUnitDTO monitorUnitDTO = new MonitorUnitDTO();
        monitorUnitDTO.setMonitorUnitId(monitorUnitId);
        monitorUnitDTO.setIpAddress(ip);
        monitorUnitDTO.setMonitorUnitName(gatewayName);
        monitorUnitDTO.setSoftwareVersion(softWareVersion);
        monitorUnitDTO.setMonitorUnitCategory(10);
        return monitorUnitDTO;
    }

}