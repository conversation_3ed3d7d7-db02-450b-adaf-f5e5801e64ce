package com.siteweb.tcs.hub.domain.letter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.domain.letter.enums.DataTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.SignalCategoryEnum;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import com.siteweb.tcs.siteweb.entity.door.DoorTemplateSignal;
import lombok.Data;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ForeignSignalConfigChange implements Serializable {
    private String foreignSignalId;
    private String signalName;
    /**
     * 数据类型，只有float以及string
     * 必须填
     * 其中电信B接口
     * Long、Short、Float -> FLOAT
     * Char -> STRING
     */
    private DataTypeEnum dataTypeEnum;
    /**
     * 信号种类
     * 对应电信B接口
     * 开关信号对应DI 模拟量对应AI
     * DI -> SWITCH_SIGNAL
     * AI -> ANALOG_SIGNAL
     * 必须填
     */
    private SignalCategoryEnum signalCategoryEnum;

    private LifeCycleEventType eventType;

    private String unit;

    private List<ForeignSignalMeaning> foreignSignalMeaningList;

    /**
     * 是否可见
     */
    private Boolean visible = true;

    /**
     * 描述
     */
    private String description;

    /**
     * 基类id
     */
    private Long baseTypeId;



    public SignalConfigItem toSignalConfigItem(Integer equipmentTemplateId) {
        return toSignalConfigItem(equipmentTemplateId,null);
    }

    public SignalConfigItem toSignalConfigItem(Integer equipmentTemplateId, DoorTemplateSignal doorTemplateSignal) {
        SignalConfigItem signalConfigItem = new SignalConfigItem();
        signalConfigItem.setSignalName(signalName);
        signalConfigItem.setEquipmentTemplateId(equipmentTemplateId);
        signalConfigItem.setDataType(dataTypeEnum.getValue());
        signalConfigItem.setSignalCategory(signalCategoryEnum.getValue());
        // 通道号，主要是site unit中概念，这里默认传-6
        signalConfigItem.setChannelNo(-6);
        signalConfigItem.setEnable(true);
        signalConfigItem.setVisible(visible);
        signalConfigItem.setDisplayIndex(1);
        signalConfigItem.setUnit(unit);
        signalConfigItem.setDescription(description);
        signalConfigItem.setBaseTypeId(baseTypeId);
        /**
         * 都是采集信号
         */
        signalConfigItem.setSignalType(1);
        switch (signalCategoryEnum){
            case SWITCH_SIGNAL:
                // 开关量，对应通道类型为数字量
                signalConfigItem.setChannelType(2);
                //信号含义
                if(CollectionUtil.isNotEmpty(foreignSignalMeaningList)){
                    List<SignalMeanings> tblSignalMeaningList = new ArrayList<>();
                    foreignSignalMeaningList.forEach(e->tblSignalMeaningList.add(e.toTblSignalMeanings(equipmentTemplateId)));
                    signalConfigItem.setSignalMeaningsList(tblSignalMeaningList);
                }
                break;
            case ANALOG_SIGNAL:
                // 模拟量，对应通道类型为模拟量
                signalConfigItem.setChannelType(1);
                break;
        }
        if(ObjectUtil.isNotEmpty(doorTemplateSignal)){
            signalConfigItem.setBaseTypeId(doorTemplateSignal.getBaseTypeId().longValue());
            signalConfigItem.setChannelNo(doorTemplateSignal.getChannelNo());
            signalConfigItem.setSignalCategory(doorTemplateSignal.getSignalCategory());
            signalConfigItem.setSignalType(doorTemplateSignal.getSignalType());
            signalConfigItem.setChannelType(doorTemplateSignal.getChannelType());
        }
        return signalConfigItem;
    }

    @Data
    public class ForeignSignalMeaning{
        private Integer stateValue;
        private String meanings;

        public SignalMeanings toTblSignalMeanings(Integer equipmentTemplateId){
            SignalMeanings tblSignalMeaning = new SignalMeanings();
            tblSignalMeaning.setMeanings(meanings);
            tblSignalMeaning.setStateValue(stateValue);
            tblSignalMeaning.setEquipmentTemplateId(equipmentTemplateId);
            return tblSignalMeaning;
        }
    }
}
