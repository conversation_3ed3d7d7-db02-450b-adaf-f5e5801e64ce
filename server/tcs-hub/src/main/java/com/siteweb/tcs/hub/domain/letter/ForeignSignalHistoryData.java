package com.siteweb.tcs.hub.domain.letter;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ForeignSignalHistoryData {
    private String foreignDeviceId;
    private String foreignSignalId;   // 信号的唯一标识符
    private Double maxVal;            // 最大值
    private LocalDateTime maxValTime; // 最大值的时间
    private Double minVal;            // 最小值
    private LocalDateTime minValTime; // 最小值的时间
    private LocalDateTime periodStart;// 数据统计开始时间
    private LocalDateTime periodEnd;  // 数据统计结束时间

    @Override
    public String toString() {
        return "ForeignSignalHistoryData{" +
                "foreignSignalId=" + foreignSignalId +
                ", maxVal=" + maxVal +
                ", maxValTime=" + maxValTime +
                ", minVal=" + minVal +
                ", minValTime=" + minValTime +
                ", periodStart=" + periodStart +
                ", periodEnd=" + periodEnd +
                '}';
    }
}
