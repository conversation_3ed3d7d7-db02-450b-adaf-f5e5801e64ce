package com.siteweb.tcs.hub.domain.letter;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LiveControlCommand {
    private Integer monitorUnitId;
    private Integer stationId;
    private Integer equipmentId;
    private Integer controlId; //控制ID
    private String sequenceNo;
    private DynamicValue parameter;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer resultCode; //-1 未知，0 失败 1成功 2 低端超时 3 服务器超时
    private String resultDesc; //-1 未知，0 失败 1成功 2 低端超时 3 服务器超时



    public LiveControlCommand(ControlCommandRequest controlCommandRequest) {
        this.monitorUnitId = controlCommandRequest.getMonitorUnitId();
        this.equipmentId = controlCommandRequest.getEquipmentId();
        this.controlId = controlCommandRequest.getControlId();
        this.sequenceNo = controlCommandRequest.getSequenceNo();
        this.parameter = controlCommandRequest.getParameter();
        this.startTime = controlCommandRequest.getStartTime();
        this.stationId = controlCommandRequest.getStationId();
        this.endTime = null;
        this.resultCode = -1;
    }

    public void update(ControlCommandResponse controlCommandResponse) {
        this.endTime = controlCommandResponse.getEndTime();
        this.resultCode = controlCommandResponse.getResultCode();
        this.resultDesc = controlCommandResponse.getResultDesc();
    }
}
