package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.PersistOperationType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class PersistOperationResponse<T> implements Serializable {
    private final long deliveryId;
    private boolean success;
    private T result;
    private PersistOperationType persistOperationType;

    public PersistOperationResponse(long deliveryId) {
        this.deliveryId = deliveryId;
    }
}
