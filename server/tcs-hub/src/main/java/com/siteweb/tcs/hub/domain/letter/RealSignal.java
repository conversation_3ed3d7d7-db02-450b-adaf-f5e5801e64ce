package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.common.o11y.WindowLogItem;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
public class RealSignal implements WindowLogItem {
    private Integer equipmentId;
    private Integer signalId;
    private String value;
    private Integer alarmState;
    private boolean valid;
    private String meanings;
    private LocalDateTime timestamp;
    private final static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public String getWindowLogString() {
        return this.toJson();
    }

    String toJson() {
        return String.format("{\"equipmentId\":%d,\"signalId\":%d,\"value\":\"%s\",\"alarmState\":%d,\"valid\":%s,\"meanings\":\"%s\",\"timestamp\":\"%s\"}",
                equipmentId, signalId, value, alarmState, valid ? "true" : "false", meanings, timestamp.toString());
    }

    public String getRedisKey() {
        return "RealTimeSignal:" + equipmentId + "." + signalId;
    }

    public String getRedisValue() {
        //equipmentId + "~" + signalId + "~" + currentValue + "~" + sampleTime + "~" + eventLevel;
        return String.format("\"%s~%s~%s~%s~%d\"", equipmentId,signalId,value,timestamp.format(dateTimeFormatter),alarmState);
    }

    public void update(RealSignal realSignal) {
        this.value = realSignal.value;
        this.alarmState = realSignal.alarmState;
        this.valid = realSignal.valid;
        this.timestamp = realSignal.timestamp;
        this.meanings = realSignal.meanings;
    }
}
