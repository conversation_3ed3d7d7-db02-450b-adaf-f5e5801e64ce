package com.siteweb.tcs.hub.domain.letter;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName: SwapCardEvent
 * @descriptions: 刷卡事件
 * @author: xsx
 * @date: 2024/9/25 13:25
 **/
@Data
public class SwapCardEvent implements LoggingEvent{
    // 卡号
    private String cardCode;
    // 刷卡事件唯一编号
    private String sequenceId;
    // 门号
    private Integer doorNo;
    //刷卡标志 请参考枚举CardSwapStatusEnum
    private Integer swapFlag;
    //进出门标志 请参考枚举DoorInAndOutEnum
    private Integer enter;
    //刷卡时间
    private LocalDateTime sampleTime;
}
