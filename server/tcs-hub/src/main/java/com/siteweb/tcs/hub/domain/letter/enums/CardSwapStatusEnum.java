package com.siteweb.tcs.hub.domain.letter.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * @ClassName: CardSwapStatusEnum
 * @descriptions: 刷卡标识枚举
 * @author: xsx
 * @date: 2024/9/21 14:54
 **/
public enum CardSwapStatusEnum {
    INVALID_PASSWORD(1,"密码无效"),
    FAILED_TO_SWIPE_CARD(2,"刷卡失败"),
    SWIPE_CARD_OR_CARD_PASSWORD_TO_OPEN_THE_DOOR(3,"刷卡/卡+密码开门"),
    PUSH_BUTTON_TO_OPEN_THE_DOOR(4,"按钮开门"),
    PASSWORD_TO_OPEN_THE_DOOR(5,"密码开门"),
    REMOTE_DOOR_OPENING(6,"远程开门"),
    ROBOT_TO_OPEN_THE_DOOR(7,"机器人开门"),
    ROBOT_TO_CLOSE_THE_DOOR(8,"机器人关门"),
    FACE_RECOGNITION_TO_OPEN_THE_DOOR(9,"人脸识别开门"),
    PUSH_BUTTON_TO_EXIT(10,"按钮出门"),
    LEGAL_CARD(11,"合法卡"),
    EXPIRED_CARD(12,"过期卡"),
    ILLEGAL_TIME_ZONE_CARD(13,"非法时区卡"),
    WRONG_PASSWORD_CARD(14,"密码错误卡"),
    ILLEGAL_DOOR_ZONE_CARD(15,"非法门区卡"),
    ILLEGAL_CARD(16,"非法卡"),
    USER_NUMBER_PLUS_PASSWORD_TO_OPEN_THE_DOOR(17,"用户号加密码开门"),
    SWIPE_CARD_TO_ENTER_THE_DOOR_SUCCESSFULLY(18,"刷卡进门成功"),
    SWIPE_CARD_TO_EXIT_SUCCESSFULLY(19,"刷卡出门成功"),
    REMOTE_CONTROL_TO_OPEN_THE_DOOR(20,"遥控开门"),
    REMOTE_CONTROL_TO_CLOSE_THE_DOOR(21,"遥控关门"),
    ENTRY_DENIED(22,"进门被拒"),
    EXIT_DENIED(23,"出门被拒"),
    UNKNOWN_CARD(24,"未知卡");

    private final int value;

    CardSwapStatusEnum(int _value, String _desc) {
        this.value = _value;
    }

    //    @JsonValue
    public int getValue() {
        return this.value;
    }

    @JsonCreator
    public static CardSwapStatusEnum fromInt(int i) {
        for (CardSwapStatusEnum status : CardSwapStatusEnum.values()) {
            if (status.value == i) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }
}
