package com.siteweb.tcs.hub.domain.letter.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 控制种类
 */
@Getter
@AllArgsConstructor
public enum ControlCategoryEnum {
    UNKNOWN(0,"未设定"),
    GENERAL_CONTROL(1,"普通控制"),
    SWITCH_DOOR_ACCESS(2,"开关门控制"),
    UP(3,"图像上移"),
    DOWN(4,"图像下移"),
    LEFT(5,"图像左移"),
    RIGHT(6,"图像右移"),
    FORWARD(7,"图像前移"),
    BACKWARD(8,"图像后移"),
    BRIGHTNESS(9,"图像亮度控制"),
    CONTRAST(10,"图像对比度控制"),
    FPS(11,"图像帧率控制"),
    ADD_DOOR_ACCESS_CARD(12,"增加门禁卡"),
    DELETE_DOOR_ACCESS_CARD(13,"删除门禁卡"),
    MODIFY_DOOR_ACCESS_CARD_SETTING(14,"修改门禁卡设置"),
    SET_WEEK_INGRESS_TIME_PERIOD(15,"设置星期准进时间段"),
    MODIFY_CONTROL_PRIVILEGE_PASSWORD(16,"修改验证控制密码"),
    DELETE_ALL_DOOR_ACCESS_CARD(17,"删除所有门禁卡"),
    DEPLOY_INFRARED(18,"布防红外"),
    DISARM_INFRARED(19,"撤防红外"),
    DOOR_OPEN_TIMEOUT(20,"开门超时时间"),
    SWIPE_CARD_ENTRY_PASSWORD_WORK_MODE(21,"刷卡进门密码工作方式"),
    SET_TIME(22,"设置时间"),
    ILLEGAL_DOOR_OPEN_EVENT_END_CONFIRM_COMMAND(23,"非法开门告警结束确定命令"),
    DISCHARGE_TEST(24,"放电测试"),
    STOP_DISCHARGE_TEST(25,"停止放电测试"),
    EVENT_RECORD_CONTROL(26,"告警录像控制命令"),
    SET_DISCHARGE_TERMINATE_VOLTAGE(27,"放电终止电压设置"),
    SET_DISCHARGE_TERMINATE_PERIOD(28,"放电终止时长设置"),
    ADD_MULTIPLE_CARDS(29,"添加一人多卡"),
    DELETE_MULTIPLE_CARDS(30,"删除用户名下一人多卡"),
    ADD_OR_UPDATE_FINGER_PRINT(31,"添加或修改指纹"),
    DELETE_FINGER_PRINT(32,"删除指纹"),
    ADD_OR_UPDATE_FACE(33,"添加或修改人脸"),
    DELETE_FACE(34,"删除人脸"),
    ADD_OR_UPDATE_USER(35,"添加或修改用户"),
    DELETE_USER(36,"删除用户"),
    SAMPLE_FACE(37,"采集人脸信息"),
    SAMPLE_FINGER_PRINT(38,"采集指纹信息"),
    DOOR_VERIFY_MODE(39,"设置读卡器默认验证方式（门开方式）"),
    SET_FINGER(40,"读头设置指纹"),
    SET_FACE(41,"读头删除指纹"),
    SET_DOOR_NORMALLY_OPEN(42,"设置门常开"),
    SET_DOOR_NORMALLY_CLOSE(43,"设置门常闭"),
    REMOTE_CLOSE(44,"远程关门"),
    EFFECTIVE_WAY_OF_FIRE_ALARM_SIGNAL(45,"火警信号有效方式"),
    NUMBER_OF_CARD_BLOCK_ERRORS(46,"卡封锁错误次数"),
    CARD_LOCK_TIME(47,"卡封锁时间"),
    ILLEGAL_CARD_SWIPE_INTERVAL(48,"非法卡刷卡间隔"),
    DOOR_OPEN_HOLDING_TIME(49,"门开保持时间"),
    DOOR_OPEN_MODE(50,"门开方式（纽贝尔）");


    ControlCategoryEnum(int _value, String _desc) {
        this.value = _value;
    }

    private final int value;

//    @JsonValue
    public int getValue() {
        return this.value;
    }

    @JsonCreator
    public static ControlCategoryEnum fromInt(int i) {
        for (ControlCategoryEnum status : ControlCategoryEnum.values()) {
            if (status.value == i) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }


}
