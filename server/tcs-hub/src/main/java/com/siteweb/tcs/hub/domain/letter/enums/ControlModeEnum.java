package com.siteweb.tcs.hub.domain.letter.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR> (2024-08-10)
 **/
public enum ControlModeEnum {


    Manual(1, "手动执行"),
    Timed(2, "定时执行"),
    Alarm(3, "告警联动"),
    Grouped(4, "群控");

    ControlModeEnum(int _value, String _desc) {
        this.value = _value;
    }

    private final int value;

    @JsonValue
    public int getValue() {
        return this.value;
    }

    @JsonCreator
    public static ControlModeEnum fromInt(int i) {
        for (ControlModeEnum status : ControlModeEnum.values()) {
            if (status.value == i) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }


}
