package com.siteweb.tcs.hub.domain.letter.enums;

import lombok.Getter;

@Getter
public enum EnumGatewayConnectState {
    OFFLINE(0, "离线"),
    ONLINE(1, "在线");

    private final int code;
    private final String description;

    EnumGatewayConnectState(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 用于根据code查找对应的枚举值
    public static EnumGatewayConnectState fromCode(int code) {
        for (EnumGatewayConnectState state : values()) {
            if (state.getCode() == code) {
                return state;
            }
        }
        throw new IllegalArgumentException("Invalid code for GatewayConnectState: " + code);
    }
}