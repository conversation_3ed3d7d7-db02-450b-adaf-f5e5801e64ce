package com.siteweb.tcs.hub.domain.letter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 2024-7-18 xsx
 * 信号状态枚举
 * 目前只有正常状态一个
 * 因为这不是Hub业务，是S6业务，S6中是可以自定义告警等级的，所以hub不做转换，只列举一个正常状态
 * 其他情况下事件告警等级透传，南向需要根据S6业务去转换，从1开始取
 * 1-1级告警
 * 2-2级告警
 * 3-3级告警
 * 4-4级告警
 * 。。。
 */
@Getter
@AllArgsConstructor
public enum EventLevelEnum {
    LEVEL_4(0,"四级告警"),
    LEVEL_3(1,"三级告警"),
    LEVEL_2(2,"二级告警"),
    LEVEL_1(3,"一级告警");

    private final int value;
    private final String describe;
}
