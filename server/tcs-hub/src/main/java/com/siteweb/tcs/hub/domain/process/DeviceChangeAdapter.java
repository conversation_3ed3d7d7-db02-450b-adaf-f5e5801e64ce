package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentChange;
import com.siteweb.tcs.hub.domain.letter.ForeignDeviceChange;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.StoreDeadLetter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

@Slf4j
public class DeviceChangeAdapter extends ProbeActor {

    private ForeignDevice foreignDevice;
    private final ActorRef stateStore;

    public DeviceChangeAdapter(ForeignDevice foreignDevice, ActorRef stateStore) {
        this.foreignDevice = foreignDevice;
        this.stateStore = stateStore;
        this.getProbe().addRateCalculator("deviceStateRateIn", 60);
        getContext().watchWith(stateStore,new StoreDeadLetter());
    }

    public static Props props(ForeignDevice foreignDevice, ActorRef stateStore) {
        return Props.create(DeviceChangeAdapter.class, foreignDevice, stateStore);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(ForeignDeviceChange.class, this::onForeignDeviceChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.foreignDevice = (ForeignDevice) needUpdateAction.getConfig();
        }
    }

    private void onForeignDeviceChange(ForeignDeviceChange foreignDeviceChange) {
        EquipmentChange equipmentChange = new EquipmentChange();
        equipmentChange.setMonitorUnitId(foreignDevice.getMonitorUnitId());
        equipmentChange.setEquipmentId(foreignDevice.getEquipmentId());
        equipmentChange.setEquipmentState(foreignDeviceChange.getConnectState().getCode());
        equipmentChange.setTimeStamp(foreignDeviceChange.getTimeStamp());
        stateStore.tell(equipmentChange, getSelf());
        this.getProbe().incrementRateSource("deviceStateRateIn");
        this.getProbe().info(" onForeignDeviceChange: foreignDeviceId=" + foreignDeviceChange.getForeignDeviceId()+ "; deviceId=" + foreignDevice.getEquipmentId() + ", muId=" + foreignDevice.getMonitorUnitId());
    }


}

