package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.ControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.ForeignDeviceControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.HashMap;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class DeviceControlCommandResponseAdapter extends ProbeActor {


    private final ActorRef stateStore;
    private ForeignDevice foreignDevice;
    private final HashMap<String,Integer> controlMap = new HashMap<>();

    public DeviceControlCommandResponseAdapter(ForeignDevice foreignDevice, ActorRef stateStore) {
        this.stateStore = stateStore;
        this.foreignDevice = foreignDevice;

        this.foreignDevice.getForeignControlList().forEach(control -> {
            controlMap.put(control.getForeignControlId(), control.getControlId());
        });
        getProbe().addRateCalculator("controlCommandResponseRateIn", 60);
    }

    // Props 工厂方法
    public static Props props(ForeignDevice foreignDevice, ActorRef deviceSignalStateStore) {
        return Props.create(DeviceControlCommandResponseAdapter.class, foreignDevice, deviceSignalStateStore);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(ForeignDeviceControlCommandResponse.class, this::onForeignDeviceControlCommandResponse)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.foreignDevice =(ForeignDevice) needUpdateAction.getConfig();
            this.controlMap.clear();
            this.foreignDevice.getForeignControlList().forEach(control -> {
                this.controlMap.put(control.getForeignControlId(), control.getControlId());
            });
        }
    }

    private void onForeignDeviceControlCommandResponse(ForeignDeviceControlCommandResponse deviceResponse) {
        if (deviceResponse.getForeignGatewayId() == null || deviceResponse.getForeignDeviceId() == null) return;
        if (!deviceResponse.getForeignGatewayId().equals(this.foreignDevice.getForeignGatewayID())) return;
        if (!deviceResponse.getForeignDeviceId().equals(this.foreignDevice.getForeignDeviceID())) return;
        if (deviceResponse.getCommandResponseList() == null || deviceResponse.getCommandResponseList().isEmpty()) return;

        log.trace("控制返回至 ResponseAdapter {} {}", deviceResponse.getForeignGatewayId(), deviceResponse.getForeignDeviceId() );
        EquipmentControlCommandResponse response = new EquipmentControlCommandResponse(
                this.foreignDevice.getMonitorUnitId(),
                this.foreignDevice.getEquipmentId(),
                deviceResponse.getCommandResponseList().stream()
                        .map(resp -> {
                            if (controlMap.containsKey(resp.getForeignControlId())) {
                                ControlCommandResponse cmdResponse = new ControlCommandResponse();
                                cmdResponse.setStationId( null);
                                cmdResponse.setStartTime(null);
                                cmdResponse.setSequenceNo(resp.getSequenceNo());
                                cmdResponse.setEndTime(resp.getEndTime());
                                cmdResponse.setResultCode(resp.getResultCode());
                                cmdResponse.setResultDesc(resp.getResultDesc());
                                cmdResponse.setControlId(controlMap.get(resp.getForeignControlId()));
                                cmdResponse.setMonitorUnitId(this.foreignDevice.getMonitorUnitId());
                                cmdResponse.setEquipmentId(this.foreignDevice.getEquipmentId());
                                log.trace("返回记录 {} {}", this.foreignDevice.getEquipmentId(), controlMap.get(resp.getForeignControlId()));
                                return cmdResponse;
                            }
                            return null;
                        }
        ).filter(Objects::nonNull).collect(Collectors.toList()));
        stateStore.tell(response, getSelf());
        getProbe().updateRateSource("controlCommandResponseRateIn",deviceResponse.getCommandResponseList().size());
    }

}

