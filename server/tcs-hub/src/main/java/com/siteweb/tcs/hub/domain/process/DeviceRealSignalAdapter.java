package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentRealSignal;
import com.siteweb.tcs.hub.domain.letter.ForeignDeviceSignalChange;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.RealSignal;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

@Slf4j
public class DeviceRealSignalAdapter extends ProbeActor {

    private final ActorRef deviceSignalStateStore;
    private ForeignDevice foreignDevice;
    private final HashMap<String,Integer> signalMap = new HashMap<>();

    private final String REAL_DATA = "real_data";

    public DeviceRealSignalAdapter(ForeignDevice foreignDevice, ActorRef deviceSignalStateStore) {
        this.deviceSignalStateStore = deviceSignalStateStore;
        this.foreignDevice = foreignDevice;
        this.foreignDevice.getForeignSignalList().forEach(signal -> {
            signalMap.put(signal.getForeignSignalID(),signal.getSignalId());
        });
        this.getProbe().addRateCalculator("realSignalRateIn", 60);
        this.getProbe().addWindowLog(REAL_DATA);
    }

    public static Props props(ForeignDevice foreignDevice, ActorRef deviceSignalStateStore) {
        return Props.create(DeviceRealSignalAdapter.class, foreignDevice, deviceSignalStateStore);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(ForeignDeviceSignalChange.class, this::handleDeviceSignalChanges)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.foreignDevice = (ForeignDevice) needUpdateAction.getConfig();
            signalMap.clear();
            this.foreignDevice.getForeignSignalList().forEach(signal -> {
                signalMap.put(signal.getForeignSignalID(),signal.getSignalId());
            });
        }
    }


    private void handleDeviceSignalChanges(ForeignDeviceSignalChange foreignDeviceSignalChange) {
        String log = foreignDeviceSignalChange.getSignalChanges().stream().map(signalChange -> signalChange.getForeignSignalId() + ":" + signalChange.getValue()).reduce((a, b) -> a + "," + b).orElse("");

        getProbe().enqueueWindowLogItem(REAL_DATA, new ActorLogItem(ActorLogLevel.INFO,
                "[RealData] receive signal change 设备ID"+foreignDeviceSignalChange.getForeignDeviceId()+" 信号变化:"+log));
        EquipmentRealSignal equipmentRealSignal = convertToEquipmentRealSignal(foreignDeviceSignalChange);
        deviceSignalStateStore.tell(equipmentRealSignal, getSelf());
        this.getProbe().updateRateSource("realSignalRateIn",equipmentRealSignal.getRealSignalList().size());
    }

    private EquipmentRealSignal convertToEquipmentRealSignal(ForeignDeviceSignalChange foreignDeviceSignalChange) {
        //如果ID正确则进行匹配
        if (!Objects.equals(this.foreignDevice.getForeignDeviceID(), foreignDeviceSignalChange.getForeignDeviceId())) {
            return null;
        }
        EquipmentRealSignal equipmentRealSignal = new EquipmentRealSignal();
        equipmentRealSignal.setEquipmentId(this.foreignDevice.getEquipmentId());
        equipmentRealSignal.setRealSignalList(new ArrayList<>());

        foreignDeviceSignalChange.getSignalChanges().forEach(change -> {
            if (signalMap.containsKey(change.getForeignSignalId())) {
                //如果存在映射则转换
                RealSignal realSignal = new RealSignal();
                realSignal.setEquipmentId(this.foreignDevice.getEquipmentId());
                realSignal.setSignalId(signalMap.get(change.getForeignSignalId()));
                realSignal.setValue(change.getValue());
                realSignal.setMeanings(change.getMeanings());
                realSignal.setValid(change.isValid());
                realSignal.setAlarmState(change.getAlarmState());
                realSignal.setTimestamp(change.getTimestamp());
                equipmentRealSignal.getRealSignalList().add(realSignal);
            }
        });
        return equipmentRealSignal;
    }


}

