package com.siteweb.tcs.hub.domain.process;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.MetricInstrument;
import com.siteweb.tcs.common.o11y.WindowLogQueue;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentAlarmChange;
import com.siteweb.tcs.hub.domain.letter.LiveAlarm;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
public class EquipmentAlarmActiveState {
    private final HashMap<Pair<Integer, Integer>, SequenceBar> alarmMatrix = new HashMap<>();

    @Setter
    private MetricInstrument metricInstrument;


    public EquipmentAlarmChange normalize(EquipmentAlarmChange deviceAlarmChange) {
        var alarmKey = Pair.of(deviceAlarmChange.getEventId(), deviceAlarmChange.getEventConditionId());
        if (alarmMatrix.containsKey(alarmKey)) {
            var sequenceBar = alarmMatrix.get(alarmKey);
            return sequenceBar.checkChange(deviceAlarmChange);
        } else {
            // 2024/7/10 xsx 需要考虑重启之后送了结束的上来
            var sequenceBar = new SequenceBar(metricInstrument.getWindowLogQueue("MISFIRE_ALARM"));
            alarmMatrix.put(alarmKey, sequenceBar);
            return  sequenceBar.checkChange(deviceAlarmChange);
        }
    }

    public List<LiveAlarm> getAllLiveAlarms() {
        //遍历alarmMatrix要每个SequenceBar返回activeAlarm如存在则加入列表，统一返回
        List<LiveAlarm> liveAlarms = new ArrayList<>();
        for (Pair<Integer, Integer> alarmKey : alarmMatrix.keySet()) {
            SequenceBar sequenceBar = alarmMatrix.get(alarmKey);
            LiveAlarm activeAlarm = sequenceBar.getLiveAlarm();
            if (activeAlarm != null) {
                liveAlarms.add(activeAlarm);
            }
        }
        return liveAlarms;
    }

    public void refresh(ForeignDevice config) {
        //TODO: 当配置发生变更, 内存的老告警怎么办?
        // 1 全量结束, 确保概念一致,但已结束告警无法再上来,低端未关闭,导致告警丢失.
        // 2 逐条对比, 新增和去除简单,去除同时结束告警,但更新需要对比具体condition
        // 3 如果有标志condition dirty,我们就可以强制结束, 要么进行condition的表达式对比.(需加载DB配置)
        // 4 如果修改设备配置,强制设备重启,失去了update的轻量化更新的本意.
        // 5 建议:如果更新配置, 低端负责重新生成告警配置,此处不处理.此处仅对ID对应告警存在和删除维护.
    }

    @Getter
    @Setter
    private static class SequenceBar {
        private WindowLogQueue errorQueue;

        private LocalDateTime normalStartTime;
        private LocalDateTime normalEndTime;
        private EquipmentAlarmChange alarmChange;
        private LocalDateTime defaultTime = LocalDateTime.of(1970, 1, 1, 0, 0);

        public SequenceBar(WindowLogQueue queue) {
            this.errorQueue = queue;
        }

        public EquipmentAlarmChange checkChange(EquipmentAlarmChange equipmentAlarmChange) {

            if (equipmentAlarmChange == null) return null;

            //如果deviceAlarmChange的开始时间和结束时间同时是null，错误数据，放入错误队列
            if (equipmentAlarmChange.getStartTime() == null && equipmentAlarmChange.getEndTime() == null) {
                log.error("[Check] eId={}, eventId={}, conditionId={}, 怎么可能都为空嘛", equipmentAlarmChange.getEquipmentId(), equipmentAlarmChange.getEventId(), equipmentAlarmChange.getEventConditionId());
                errorQueue.enqueue(equipmentAlarmChange);
                return null;
            }

            //如果deviceAlarmChange的开始时间不为null，却为java时间戳起始时间，1970-01-01 00:00:00，则认为时错误数据，放入错误队列
            assert equipmentAlarmChange.getStartTime() != null;
            if (equipmentAlarmChange.getStartTime().equals(defaultTime)) {
                log.error("[Check] eId={}, eventId={}, conditionId={}, 开始1970，skip", equipmentAlarmChange.getEquipmentId(), equipmentAlarmChange.getEventId(), equipmentAlarmChange.getEventConditionId());
                errorQueue.enqueue(equipmentAlarmChange);
                return null;
            }

            //如果deviceAlarmChange的结束时间不为null，却为java时间戳起始时间，1970-01-01 00:00:00，则认为时错误数据，放入错误队列
            if (ObjectUtil.isNotEmpty(equipmentAlarmChange.getEndTime()) && equipmentAlarmChange.getEndTime().equals(defaultTime)) {
                log.error("[Check] eId={}, eventId={}, conditionId={}, 结束1970，skip", equipmentAlarmChange.getEquipmentId(), equipmentAlarmChange.getEventId(), equipmentAlarmChange.getEventConditionId());
                errorQueue.enqueue(equipmentAlarmChange);
                return null;
            }

//           if (this.alarmChange == null) {
            this.alarmChange = equipmentAlarmChange;
//           }

            return sequenceCheck(alarmChange);
        }

        private EquipmentAlarmChange sequenceCheck(EquipmentAlarmChange alarmChange) {

            // 当前无告警正在发生时（normalStartTime 和 normalEndTime 都为 null）
            if (normalStartTime == null && normalEndTime == null) {
                // 如果 alarmChange 的开始时间不为 null 且正常结束时间为空，说明告警开始
                if (alarmChange.getStartTime() != null && alarmChange.getEndTime() == null) {
                    normalStartTime = alarmChange.getStartTime();
                    return alarmChange;
                } else if (alarmChange.getStartTime() != null && alarmChange.getEndTime() != null){
                    log.error("[Check] eId={}, eventId={}, conditionId={}, 重启收到结束告警，直接返回", alarmChange.getEquipmentId(), alarmChange.getEventId(), alarmChange.getEventConditionId());
                    return alarmChange;
                }else {
                    // 如果 alarmChange 的开始时间不为 null 且结束时间不为 null，说明告警结束,
                    // 既然结束, 内存状态是没开始，这样的数据认为是废数据直接丢弃
                    log.error("[Check] eId={}, eventId={}, conditionId={}, 正常开始结束时间均为空，并且不是开始也不是重启结束，进入错误队列", alarmChange.getEquipmentId(), alarmChange.getEventId(), alarmChange.getEventConditionId());
                    errorQueue.enqueue(alarmChange);
                    return null;
                }
            }
            // 当告警正在进行中（normalStartTime 不为 null，normalEndTime 为 null）
            else if (normalStartTime != null && normalEndTime == null) {
                if (alarmChange.getStartTime() != null && alarmChange.getEndTime() == null) {
                    // 如果 alarmChange 的开始时间不为 null 且结束时间为 null，说明告警仍在继续，本数据还是为告警开始
                    // 这就是重复数据,丢弃不需要返回
                    errorQueue.enqueue(alarmChange);
                    log.error("[Check] eId={}, eventId={}, conditionId={}, 正常开始时间不为空，正常结束时间为空，但是当前变化时间仍为空，证明重复送，进入错误队列，跳过", alarmChange.getEquipmentId(), alarmChange.getEventId(), alarmChange.getEventConditionId());
                    return null;
                }
                // 如果 alarmChange 的开始时间不为 null 且结束时间不为 null，说明告警结束
                if (alarmChange.getStartTime() != null && alarmChange.getEndTime() != null) {
                    // 如果 alarmChange 的结束时间晚于 normalStartTime，更新 normalEndTime 为 alarmChange 的时间并返回 alarmChange
                    if (alarmChange.getEndTime().isAfter(normalStartTime)) {
                        normalEndTime = alarmChange.getEndTime();
                        if (normalStartTime != null && normalEndTime != null) {

                            // 如果告警已经结束（normalStartTime 和 normalEndTime 都不为 null）
                            normalStartTime = null;
                            normalEndTime = null;

                            //恢复标志时间为空，代表该告警当前无活动事件。
                        }else if (normalStartTime == null && normalEndTime != null){
                            normalEndTime = null;
                        }

                        return alarmChange;
                    } else {
                        log.error("[Check] eId={}, eventId={}, conditionId={}, 时间乱序，进入错误队列", alarmChange.getEquipmentId(), alarmChange.getEventId(), alarmChange.getEventConditionId());
                        errorQueue.enqueue(alarmChange);
                        // 如果 alarmChange 的结束时间早于或等于 normalStartTime，说明是同ID旧告警事件的结束，丢弃
                        return null;
                    }
                }
            }
            log.error("[Check] eId={}, eventId={}, conditionId={}, 不知道为啥，失败", alarmChange.getEquipmentId(), alarmChange.getEventId(), alarmChange.getEventConditionId());
            // 如果没有匹配的情况，说明数据不正确，放入错误队列
            errorQueue.enqueue(alarmChange);
            return null;
        }

        public LiveAlarm getLiveAlarm() {
            if (normalStartTime != null && normalEndTime != null) return null;
            if (normalStartTime == null && normalEndTime == null) return null;

            if (normalStartTime != null) {
                LiveAlarm liveAlarm = new LiveAlarm();
                liveAlarm.setEquipmentId(alarmChange.getEquipmentId());
                liveAlarm.setEventId(alarmChange.getEventId());
                liveAlarm.setEventConditionId(alarmChange.getEventConditionId());
                liveAlarm.setAlarmLevel(alarmChange.getAlarmLevel());
                liveAlarm.setMeanings(alarmChange.getMeanings());
                liveAlarm.setMonitorUnitId(alarmChange.getMonitorUnitId());
                liveAlarm.setTriggerValue(alarmChange.getTriggerValue());
                liveAlarm.setStartTime(normalStartTime);

                return liveAlarm;
            }

            return null;
        }
    }
}
