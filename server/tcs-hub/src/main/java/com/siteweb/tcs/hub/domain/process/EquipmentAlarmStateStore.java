package com.siteweb.tcs.hub.domain.process;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentAlarmChange;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class EquipmentAlarmStateStore extends ProbeActor {

    private static final String MISFIRE_ALARM = "MISFIRE_ALARM";
    private final EquipmentAlarmActiveState state = new EquipmentAlarmActiveState();
    private final ActorRef spout;

    public EquipmentAlarmStateStore(ActorRef spout) {
        this.state.setMetricInstrument(this.getProbe().getMetricInstrument());
        this.getProbe().addWindowLog(MISFIRE_ALARM);
        this.spout = spout;
    }

    public static Props props(ActorRef spout) {
        return Props.create(EquipmentAlarmStateStore.class,  () -> new EquipmentAlarmStateStore(spout));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(List.class, this::saveState)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.state.refresh((ForeignDevice) needUpdateAction.getConfig());
        }
    }

    private void saveState(List<EquipmentAlarmChange> deviceAlarms) {
        if(getProbe().isEnableActorLog()) {
            this.getProbe().info("received EquipmentAlarmChange list size:" + deviceAlarms.size());
        }
        List<EquipmentAlarmChange> notifyList = new ArrayList<>();
        for (EquipmentAlarmChange deviceAlarmChange : deviceAlarms) {
            EquipmentAlarmChange confirmChange = this.state.normalize(deviceAlarmChange);
            if (confirmChange == null) {
                if(getProbe().isEnableActorLog()) {
                    this.getProbe().enqueueWindowLogItem(MISFIRE_ALARM, deviceAlarmChange);
                }
                log.warn("[ALARM - {}] Get EquipmentAlarmChange is null, uniqueId: {}, serNo: {}.", getSelf().path().name()
                        , deviceAlarmChange.getUniqueId(), deviceAlarmChange.getSerialNo());
                break;
            }

            notifyList.add(confirmChange);
        }

        //tell spout to send change to watchers
        if(CollectionUtil.isNotEmpty(notifyList)){
            spout.tell(notifyList, self());
        } else {
            log.info("[ALARM - {}] receive empty list, not exec spout.tell().", getSelf().path().name());
        }
    }
}

