package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.HandlerRegisterAction;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.List;

import static com.siteweb.tcs.hub.domain.process.LocalEquipmentControlCommandCache.saveEquipmentControlCommandResponse;

public class EquipmentControlCommandResponseSpout extends ProbeActor {

    private final List<ActorRef> routees = new ArrayList<ActorRef>();
    private final ActorRef gatewayPipelinePublisher;

    EquipmentControlCommandResponseSpout(ActorRef gatewayPipelinePublisher) {
        this.gatewayPipelinePublisher = gatewayPipelinePublisher;
        getProbe().addRateCalculator("controlCommandResponseRateOut", 60);
    }

    public static Props props(ActorRef gatewayPipelinePublisher) {
        return Props.create(EquipmentControlCommandResponseSpout.class, gatewayPipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(HandlerRegisterAction.class, this::registerActor)
                .match(EquipmentControlCommandResponse.class, this::onEquipmentControlCommandResponse)
                .build()
                .orElse(super.createReceive());
    }

    private void onEquipmentControlCommandResponse(EquipmentControlCommandResponse response) {
        saveEquipmentControlCommandResponse(response);
        gatewayPipelinePublisher.tell(response, getSelf());
        routees.forEach(r -> r.forward(response, getContext()));
        getProbe().updateRateSource("controlCommandResponseRateOut", response.getCommandResponseList().size());
    }

    private void registerActor(HandlerRegisterAction msg) {
        routees.add(msg.getActorRef());
    }
}

