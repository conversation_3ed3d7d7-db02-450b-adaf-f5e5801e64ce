package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.StoreDeadLetter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

@Slf4j
public class EquipmentControlCommandStateStore extends ProbeActor {


    private ForeignDevice foreignDevice;
    private final ActorRef requestAdapter;
    private final ActorRef responseSpout;
    private final EquipmentControlCommandActiveState state;

    public EquipmentControlCommandStateStore(ActorRef requestAdapter, ActorRef responseSpout, ForeignDevice foreignDevice) {
        this.requestAdapter = requestAdapter;
        this.responseSpout = responseSpout;
        this.foreignDevice = foreignDevice;
        state = new EquipmentControlCommandActiveState();
        getContext().watchWith(requestAdapter,new StoreDeadLetter());
    }

    public static Props props(ActorRef requestAdapter, ActorRef requestSpout, ForeignDevice foreignDevice) {
        return Props.create(EquipmentControlCommandStateStore.class, () -> new EquipmentControlCommandStateStore(requestAdapter, requestSpout, foreignDevice));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentControlCommandRequest.class, this::onEquipmentControlCommandRequest)
                .match(EquipmentControlCommandResponse.class, this::onEquipmentControlCommandResponse)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.foreignDevice =(ForeignDevice) needUpdateAction.getConfig();
            this.state.refresh(this.foreignDevice);
        }
    }


    private void onEquipmentControlCommandResponse(EquipmentControlCommandResponse response) {
        state.saveResponse(response);
        this.responseSpout.tell(response, getSelf());
    }

    private void onEquipmentControlCommandRequest(EquipmentControlCommandRequest commandRequest) {

        log.trace("收到控制命令：{}" ,commandRequest.getEquipmentId());
        //将请求存储到内部以设备和ControlId为key的缓存中，然后发送给equipmentControlCommandRequestAdapter
        // 由这个actor转为ForeignDeviceControlCommandRequest 发给spout南向插件
        state.saveRequest(commandRequest);
        log.trace("发送至 RequestAdapter：{}" ,commandRequest.getEquipmentId());
        requestAdapter.tell(commandRequest, getSelf());
    }

}

