package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.EquipmentHisBatCurveData;
import com.siteweb.tcs.hub.domain.letter.EquipmentHisData;
import com.siteweb.tcs.hub.domain.letter.EquipmentHistoryData;
import com.siteweb.tcs.hub.domain.letter.HandlerRegisterAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.List;

import static com.siteweb.tcs.hub.domain.process.LocalEquipmentHistoryDataCache.saveEquipmentHistoryData;

/**
 * <AUTHOR>
 */
@Slf4j
public class EquipmentHistoryDataSpout extends ProbeActor {

    private final List<ActorRef> routers = new ArrayList<>();
    private final ActorRef gatewayPipelinePublisher;

    private static final String HISTORY_DATA = "history_data";

    public static Props props() {
        return Props.create(EquipmentHistoryDataSpout.class);
    }

    public EquipmentHistoryDataSpout(ActorRef gatewayPipelinePublisher) {
        this.gatewayPipelinePublisher = gatewayPipelinePublisher;
        getProbe().addCounter("EquipmentHistoryDataSpoutCounter");
        getProbe().addRateCalculator("historyDataRateOut", 60);
        getProbe().addWindowLog(HISTORY_DATA);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(HandlerRegisterAction.class, this::registerActor)
                .match(EquipmentHisData.class, this::handleEquipmentHistoryData)//todo: why two type of his data?
                .match(EquipmentHisBatCurveData.class, this::handleEquipmentHisBatCurveData)
                .match(EquipmentHistoryData.class, this::handleEquipmentStatsHistoryData)
                .build()
                .orElse(super.createReceive());
    }

    private void handleEquipmentHistoryData(EquipmentHisData message) {
        // Log and save historical data to local cache
        getProbe().enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO,
                "[HistoryData] Saving historical data to cache: " + message.toString()));
        saveEquipmentHistoryData(message.getSignalLists());
        gatewayPipelinePublisher.tell(message, getSelf());

        // Update probe counters
        int dataSize = message.getSignalLists().size();
        getProbe().incrementCounterAmount("EquipmentHistoryDataSpoutCounter", dataSize);

        // Notify all registered routees (subscribers)
        routers.forEach(r -> {
            r.tell(message, getSelf());
            getProbe().enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO,
                    "[HistoryData] Notifying subscriber " + r.path().name() + " with historical data."));
        });

        // Update rate calculator
        getProbe().updateRateSource("historyDataRateOut", dataSize);
    }

    private void handleEquipmentHisBatCurveData(EquipmentHisBatCurveData message) {
        getProbe().enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO,
                "[HistoryData] Saving historical data to cache: " + message.toString()));
        routers.forEach(r -> {
            r.tell(message, getSelf());
            getProbe().enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO,
                    "[HistoryData] Notifying subscriber " + r.path().name() + " with historical data."));
        });

    }

    private void handleEquipmentStatsHistoryData(EquipmentHistoryData message) {
        getProbe().enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO,
                "[HistoryData] Saving historical data to cache: " + message.toString()));
        routers.forEach(r -> {
            r.tell(message, getSelf());
            getProbe().enqueueWindowLogItem(HISTORY_DATA, new ActorLogItem(ActorLogLevel.INFO,
                    "[HistoryData] Notifying subscriber " + r.path().name() + " with historical data."));
        });
    }

    private void registerActor(HandlerRegisterAction msg) {
        getProbe().info("[HistoryData] Registering actor for historical data: " + msg.getActorRef().path().name());
        routers.add(msg.getActorRef());
    }

}

