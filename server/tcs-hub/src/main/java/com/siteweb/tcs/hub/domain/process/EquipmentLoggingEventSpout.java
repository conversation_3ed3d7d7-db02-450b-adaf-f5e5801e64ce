package com.siteweb.tcs.hub.domain.process;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.EquipmentLoggingEvent;
import com.siteweb.tcs.hub.domain.letter.HandlerRegisterAction;
import com.siteweb.tcs.hub.domain.letter.enums.LoggingEventEnum;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: EquipmentLoggingEventSpout
 * @descriptions: 日志类型事件输出类
 * @author: xsx
 * @date: 2024/9/25 10:57
 **/
public class EquipmentLoggingEventSpout extends ProbeActor {

    private final Map<LoggingEventEnum, List<ActorRef>> loggingEventEnumActorListMap = new HashMap<>();

    private final String LOGGING_EVENT = "logging_event";
    private final ActorRef gatewayPipelinePublisher;

    private EquipmentLoggingEventSpout(ActorRef gatewayPipelinePublisher){
        this.gatewayPipelinePublisher = gatewayPipelinePublisher;
        getProbe().addCounter("EquipmentLoggingEventSpoutCounter");
        getProbe().addRateCalculator("loggingEventRateOut", 60);
        getProbe().addWindowLog(LOGGING_EVENT);
    }

    public static Props props() {
        return Props.create(EquipmentLoggingEventSpout.class);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(HandlerRegisterAction.class,this::registerActor)
                .match(EquipmentLoggingEvent.class,this::onEquipmentLoggingEvent)
                .build()
                .orElse(super.createReceive());
    }

    private void registerActor(HandlerRegisterAction msg) {
        if(!(msg.getT() instanceof LoggingEventEnum)){
            getProbe().error("[LoggingEvent] registerActor注册日志事件通道异常 " + msg.getActorRef().path().name()+"，请求参数是"+msg);
            return;
        }
        getProbe().info("[LoggingEvent] registerActor注册日志事件通道 " + msg.getActorRef().path().name()+"，类型是"+msg.getT());
        LoggingEventEnum loggingEventEnum = (LoggingEventEnum) msg.getT();
        if(loggingEventEnumActorListMap.containsKey(loggingEventEnum)){
            loggingEventEnumActorListMap.get(loggingEventEnum).add(msg.getActorRef());
        }else {
            List<ActorRef> actorRefList = new ArrayList<>();
            actorRefList.add(msg.getActorRef());
            loggingEventEnumActorListMap.put(loggingEventEnum,actorRefList);
        }
    }

    private void onEquipmentLoggingEvent(EquipmentLoggingEvent equipmentLoggingEvent) {
        if(ObjectUtil.isEmpty(equipmentLoggingEvent)) return;
        gatewayPipelinePublisher.tell(equipmentLoggingEvent,getSelf());
        if(loggingEventEnumActorListMap.containsKey(equipmentLoggingEvent.getLoggingEventEnum())){
            loggingEventEnumActorListMap.get(equipmentLoggingEvent.getLoggingEventEnum())
                    .stream().forEach(e -> e.tell(equipmentLoggingEvent,getSelf()));
        }
    }
}

