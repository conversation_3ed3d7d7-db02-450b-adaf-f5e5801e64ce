package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.ForeignGatewayChange;
import com.siteweb.tcs.hub.domain.letter.MonitorUnitChange;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.enums.EnumGatewayConnectState;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.time.Duration;
import java.time.LocalDateTime;

public class GatewayChangeAdapter extends ProbeActor {

    private LocalDateTime lastTimestamp;
    private EnumGatewayConnectState lastConnectState;
    private ForeignGateway gatewayMap;
    private final ActorRef monitorUnitStateStore;

    public GatewayChangeAdapter(ForeignGateway gateway,ActorRef stateStoreActor) {
        this.gatewayMap = gateway;
        this.monitorUnitStateStore = stateStoreActor;
        this.getProbe().addRateCalculator("monitorUnitStateRateIn",60);
    }

    public static Props props(ForeignGateway gateway, ActorRef monitorUnitStateStoreActor) {
        return Props.create(GatewayChangeAdapter.class, () -> new GatewayChangeAdapter(gateway, monitorUnitStateStoreActor));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(ForeignGatewayChange.class, this::onForeignGatewayChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            gatewayMap = (ForeignGateway) needUpdateAction.getConfig();
        }
    }

    //map to monitorunitID and send to monitorunit
    private void onForeignGatewayChange(ForeignGatewayChange gatewayChange) {
        //如果无法找到monitorUnit，将无法找到本adapter
        getProbe().info("onForeignGatewayChange, id=" + gatewayChange.getID() + ", ip=" + gatewayChange.getIP());
        getProbe().info("onForeignGatewayChange, EnumGatewayConnectState" + gatewayChange.getConnectState());
        var gatewayExist = downSampleGatewayChange(gatewayChange);
        getProbe().info("downSampleGatewayChange," + gatewayExist);
        if (gatewayExist != null) {
            // 并根据GatewayChange转为MonitorUnitChange, 并发送给GatewayChangeSpout
            var muChange = convertToMonitorUnitChange(gatewayExist);
            // 告诉mu状态管理器更新状态
            monitorUnitStateStore.tell(muChange, ActorRef.noSender());
            getProbe().incrementRateSource("monitorUnitStateRateIn");
        }

    }

    private MonitorUnitChange convertToMonitorUnitChange(ForeignGatewayChange foreignGatewayChange) {
        MonitorUnitChange monitorUnitChange = new MonitorUnitChange();
        monitorUnitChange.setMonitorUnitId(gatewayMap.getMonitorUnitID());//h: 这个Map里应该是有gatewayid与monitorUnitid的映射
        monitorUnitChange.setIP(foreignGatewayChange.getIP());
        monitorUnitChange.setPort(foreignGatewayChange.getPort());
        monitorUnitChange.setConnectState(mapConnectState(foreignGatewayChange.getConnectState()));
        monitorUnitChange.setFirmWareVersion(foreignGatewayChange.getFirmWareVersion());
        monitorUnitChange.setSoftWareVersion(foreignGatewayChange.getSoftWareVersion());
        monitorUnitChange.setVendor(foreignGatewayChange.getVendor());
        monitorUnitChange.setModal(foreignGatewayChange.getModal());
        monitorUnitChange.setTimeStamp(foreignGatewayChange.getTimestamp());

        return monitorUnitChange;
    }

    private int mapConnectState(EnumGatewayConnectState enumConnectState) {
        return switch (enumConnectState) {
            case ONLINE -> 1;
            case OFFLINE -> 0;
        };
    }

    private ForeignGatewayChange downSampleGatewayChange(ForeignGatewayChange gatewayChange) {//h: 保存上个GatewayChange的时间戳和connectState状态
        //利用类变量暂存上个GatewayChange的时间戳和connectState状态
        //对GatewayChange进行数据降采样，与上个connectState相同的且gatewayChange时间戳间隔小于2秒的，则丢弃
        //对过滤后的GatewayChange，转为MonitorUnitChange, 并发送给GatewayChangeSpout

        if (lastTimestamp == null) {
            lastConnectState = gatewayChange.getConnectState();
            lastTimestamp = gatewayChange.getTimestamp();
            return gatewayChange;
        }
        if (lastConnectState != gatewayChange.getConnectState()) {
            lastConnectState = gatewayChange.getConnectState();
            lastTimestamp = gatewayChange.getTimestamp();
            return gatewayChange;
        }

        if (gatewayChange.getTimestamp().isAfter(lastTimestamp.plus(Duration.ofSeconds(2)))) {
            lastConnectState = gatewayChange.getConnectState();
            lastTimestamp = gatewayChange.getTimestamp();
            return gatewayChange;
        }

        return null;

    }


}

