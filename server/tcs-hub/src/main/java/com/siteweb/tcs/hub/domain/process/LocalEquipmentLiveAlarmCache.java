package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.hub.domain.letter.EquipmentAlarmChange;
import com.siteweb.tcs.hub.domain.letter.LiveAlarm;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
public class LocalEquipmentLiveAlarmCache {
    private static ConcurrentHashMap<String, LiveAlarm> cache = new ConcurrentHashMap<>();

    public static List<LiveAlarm> queryLiveAlarm(boolean getAllData, List<Integer> equipmentIds)
    {
        List<LiveAlarm> liveAlarms = new ArrayList<>();
        if (getAllData) {
            liveAlarms = new ArrayList<>(cache.values());
        } else {
            //如果liveAlarms的equipmentId在equipmentIds中的某个值，则添加到liveAlarms中
            liveAlarms = cache.values().stream().filter(
                    liveAlarm -> equipmentIds.contains(liveAlarm.getEquipmentId()))
                    .collect(Collectors.toList());
        }

        return liveAlarms;
    }

    public static void saveEquipmentLiveAlarm(List<EquipmentAlarmChange> equipmentAlarmChangeList) {
        if (equipmentAlarmChangeList == null || equipmentAlarmChangeList.isEmpty()) {
            return;
        }
        //h: hub中在内存中保存的活动告警
        //遍历equipmentAlarmChangeList，如果EquipmentAlarmChange的UniqueId不存在，则转换为LiveAlarm直接加入cache
        //如果cache中存在UniqueId的对应的LiveAlarm，则如果EquipmentAlarmChange的end time不为null，则LiveAlarm结束，从cache删除本节点
        //如果endTime为null，则认为是重复告警Change，丢弃，不做任何动作
        for (EquipmentAlarmChange equipmentAlarmChange : equipmentAlarmChangeList) {
            String uniqueId = equipmentAlarmChange.getUniqueId();
            if (cache.containsKey(uniqueId)) {
                //LiveAlarm liveAlarm = cache.get(uniqueId);
                if (equipmentAlarmChange.getEndTime() != null) {
                    cache.remove(uniqueId);
                }
            } else {
                if (equipmentAlarmChange.getStartTime() != null && equipmentAlarmChange.getEndTime() != null) {//如果缓存里不存在，但是本条告警开始与结束时间都不为空，则不进缓存了
                    log.warn("[SendAlarm] Alarm does not exist in cache, but has ended. Alarm info: {}", equipmentAlarmChange.getWindowLogString());
                    continue;
                }
                LiveAlarm liveAlarm = new LiveAlarm(equipmentAlarmChange);
                cache.put(uniqueId, liveAlarm);
            }
        }
    }
}
