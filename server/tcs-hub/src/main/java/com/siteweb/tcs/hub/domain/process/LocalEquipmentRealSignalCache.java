package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.hub.domain.letter.EquipmentRealSignal;
import com.siteweb.tcs.hub.domain.letter.GetEquipmentRealSignalAction;
import com.siteweb.tcs.hub.domain.letter.RealSignal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class LocalEquipmentRealSignalCache {
    private static final ConcurrentHashMap<Integer, HashMap<Integer, RealSignal>> realSignalCache = new ConcurrentHashMap<>();

    public static void saveEquipmentRealSignal(EquipmentRealSignal equipmentRealSignal) {
        if (equipmentRealSignal == null || equipmentRealSignal.getEquipmentId() == null || equipmentRealSignal.getRealSignalList().isEmpty()) {
            return;
        }

        var equipmentId = equipmentRealSignal.getEquipmentId();
        var signalMap = realSignalCache.computeIfAbsent(equipmentId, k -> new HashMap<>());

        equipmentRealSignal.getRealSignalList().forEach(realSignal -> {
            signalMap.compute(realSignal.getSignalId(), (id, oldRealSignal) -> {
                if (oldRealSignal != null) {
                    oldRealSignal.update(realSignal);
                    return oldRealSignal;
                } else {
                    return realSignal;
                }
            });
        });
    }

    public static List<EquipmentRealSignal> queryEquipmentRealSignal(GetEquipmentRealSignalAction action) {
        List<EquipmentRealSignal> equipmentRealSignalList;

        if (action.isGetAllData()) {
            // 收集所有设备的信号信息
            equipmentRealSignalList = realSignalCache.entrySet().stream()
                    .map(entry -> {
                        EquipmentRealSignal equipmentSignal = new EquipmentRealSignal();
                        equipmentSignal.setEquipmentId(entry.getKey());
                        equipmentSignal.setRealSignalList(new ArrayList<>(entry.getValue().values()));
                        return equipmentSignal;
                    })
                    .collect(Collectors.toList());
        } else {
            // 针对指定设备ID收集信号信息
            equipmentRealSignalList = action.getEquipmentIds().stream()
                    .map(realSignalCache::get)
                    .filter(Objects::nonNull)
                    .map(signalMap -> {
                        EquipmentRealSignal equipmentSignal = new EquipmentRealSignal();
                        equipmentSignal.setEquipmentId(signalMap.keySet().iterator().next()); // 假设每个map只有一个键，即equipmentId
                        equipmentSignal.setRealSignalList(new ArrayList<>(signalMap.values()));
                        return equipmentSignal;
                    })
                    .collect(Collectors.toList());
        }

        return equipmentRealSignalList;
    }

}
