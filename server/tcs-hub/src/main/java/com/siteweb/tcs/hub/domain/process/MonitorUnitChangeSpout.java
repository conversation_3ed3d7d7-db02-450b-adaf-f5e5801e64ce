package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.SpringBeanUtil;
import com.siteweb.tcs.hub.domain.letter.HandlerRegisterAction;
import com.siteweb.tcs.hub.domain.letter.MonitorUnitChange;
import com.siteweb.tcs.hub.domain.process.lifecycle.GatewayPipelinePublisher;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.List;

public class MonitorUnitChangeSpout extends ProbeActor {

    private final ActorRef gatewayPipelinePublisher;
    private final List<ActorRef> routees = new ArrayList<ActorRef>();
    private final LocalMonitorUnitStateCache localMonitorUnitStateCache;

    public MonitorUnitChangeSpout(ActorRef gatewayPipelinePublisher) {
        this.gatewayPipelinePublisher = gatewayPipelinePublisher;
        localMonitorUnitStateCache = SpringBeanUtil.getBean("localMonitorUnitStateCache",LocalMonitorUnitStateCache.class);
        getProbe().addRateCalculator("monitorUnitStateRateOut",60);
    }

    public static Props props(ActorRef gatewayPipelinePublisher) {

        return Props.create(MonitorUnitChangeSpout.class, gatewayPipelinePublisher);
    }


    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(HandlerRegisterAction.class, this::registerActor)
                .match(MonitorUnitChange.class, this::broadcastMessage)
                .build()
                .orElse(super.createReceive());
                //not need update Action
    }

    private void registerActor(HandlerRegisterAction msg) {
        routees.add(msg.getActorRef());
    }

    private void broadcastMessage(MonitorUnitChange message) {
        localMonitorUnitStateCache.saveMonitorUnitState(message);
        gatewayPipelinePublisher.tell(message, getSelf());

        routees.forEach(r -> r.forward(message, getContext()));
        getProbe().incrementRateSource("monitorUnitStateRateOut");
        getProbe().info("broadcastMessage, " + message.getMonitorUnitId() + ", " + message.getConnectState() + ", " + message.getTimestamp());
    }


}

