package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.MonitorUnitChange;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.time.LocalDateTime;

public class MonitorUnitStateStore extends ProbeActor {

    private final ActorRef spout;
    private ForeignGateway gateway;
    private Integer connectState;
    private LocalDateTime timestamp;

    public MonitorUnitStateStore(ForeignGateway gateway, ActorRef spout ) {
        connectState = 0;
        this.gateway = gateway;
        this.spout = spout;
    }

    public static Props props(ForeignGateway gateway, ActorRef spout) {
        return Props.create(MonitorUnitStateStore.class, () -> new MonitorUnitStateStore(gateway, spout));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(MonitorUnitChange.class, this::saveState)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            gateway = (ForeignGateway) needUpdateAction.getConfig();
        }
    }

    private void saveState(MonitorUnitChange muChange) {
        this.getProbe().info("saveState, " + muChange.getConnectState());
        connectState = muChange.getConnectState();
        timestamp = muChange.getTimestamp();
        spout.tell(muChange, getSelf());
    }



}

