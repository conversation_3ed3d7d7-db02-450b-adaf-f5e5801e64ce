package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.process.DeviceAlarmAdapter;
import com.siteweb.tcs.hub.domain.process.EquipmentAlarmSpout;
import com.siteweb.tcs.hub.domain.process.EquipmentAlarmStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * 设备告警管道
 * 负责处理设备告警数据的数据流
 */
public class DeviceAlarmPipeline extends DataPipeline {
    private final ForeignDevice device;
    
    /**
     * 构造函数
     * @param context Actor上下文
     * @param device 外部设备实体
     */
    public DeviceAlarmPipeline(ActorContext context, ForeignDevice device, ActorRef pipelinePublisher) {
        super(context, pipelinePublisher);
        this.device = device;
    }

    @Override
    public void create() {
        // 创建告警发送Actor
        ActorRef spoutActor = getContext().actorOf(
                Props.create(EquipmentAlarmSpout.class, device, getPipelinePublisher()),
                "EquipmentAlarmSpout"
        );
        setSpoutActor(spoutActor);
        
        // 创建告警存储Actor
        ActorRef storeActor = getContext().actorOf(
                EquipmentAlarmStateStore.props(spoutActor),
                "EquipmentAlarmStateStore"
        );
        setStoreActor(storeActor);
        
        // 创建告警适配器Actor
        ActorRef adapterActor = getContext().actorOf(
                DeviceAlarmAdapter.props(device, storeActor),
                "DeviceAlarmAdapter"
        );

        setAdapterActor(adapterActor);
    }
}

