package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentChange;
import com.siteweb.tcs.hub.domain.letter.ForeignDeviceChange;
import com.siteweb.tcs.hub.domain.process.DeviceChangeAdapter;
import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import com.siteweb.tcs.hub.domain.process.EquipmentChangeSpout;
import com.siteweb.tcs.hub.domain.process.EquipmentStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.time.LocalDateTime;

/**
 * 设备变更管道
 * 负责处理设备状态变更的数据流
 */
public class DeviceChangePipeline extends DataPipeline {
    private final ForeignDevice device;
    
    /**
     * 构造函数
     * @param context Actor上下文
     * @param device 外部设备实体
     */
    public DeviceChangePipeline(ActorContext context, ForeignDevice device, ActorRef pipelinePublisher) {
        super(context, pipelinePublisher);
        this.device = device;
    }

    @Override
    public void create() {
        // 创建状态存储Actor
        ActorRef spoutActor = getContext().actorOf(
                Props.create(EquipmentChangeSpout.class, device, getPipelinePublisher()),
                "EquipmentChangeSpout"
        );
        setSpoutActor(spoutActor);
        
        // 创建状态存储Actor
        ActorRef storeActor = getContext().actorOf(
                EquipmentStateStore.props(device, spoutActor),
                "EquipmentStateStore"
        );
        setStoreActor(storeActor);
        
        // 创建适配器Actor
        ActorRef adapterActor = getContext().actorOf(
                DeviceChangeAdapter.props(device, storeActor),
                "DeviceChangeAdapter"
        );
        setAdapterActor(adapterActor);

        // 发送设备上线消息
        ForeignDeviceChange foreignDeviceChange = new ForeignDeviceChange()
                .setForeignDeviceId(this.device.getForeignDeviceID())
                .setConnectState(EnumDeviceConnectState.ONLINE)
                .setTimeStamp(LocalDateTime.now())
                .setForeignGatewayId(this.device.getForeignGatewayID());

        getAdapterActor().tell(foreignDeviceChange, getContext().self());
    }
}

