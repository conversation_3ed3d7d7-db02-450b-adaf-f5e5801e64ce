package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.LogUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.ForeignDeviceSignalChange;
import com.siteweb.tcs.hub.domain.letter.ForeignSignalChange;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.PoisonPill;
import org.apache.pekko.actor.Props;

/**
 * 设备生命周期管理器
 * 负责处理设备的生命周期事件，管理设备的CRUD操作
 * 并维护设备相关的数据管道
 */
@Slf4j
@Deprecated
public class DeviceLCM extends ProbeActor {

    // 设备变更管道
    private final DeviceChangePipeline deviceChangePipeline;

    // 实时数据管道
    private final DeviceRealSignalPipeline deviceRealSignalPipeline;

    // 告警管道
    private final DeviceAlarmPipeline deviceAlarmPipeline;

    // 历史数据管道
    private final DeviceHistoryDataPipeline deviceHistoryDataPipeline;

    // 控制命令管道
    private final DeviceControlCommandPipeline deviceControlCommandPipeline;

    // 日志事件管道
    private final DeviceLoggingEventPipeline deviceLoggingEventPipeline;

    // 设备缓存
    private ForeignDevice foreignDevice;

    private final DevicePersistHandler devicePersistHandler = new DevicePersistHandler();

    // 设备信息
    private final String gatewayId;
    private final String deviceId;

    /**
     * 构造函数
     * @param gatewayId 网关ID
     * @param foreignDevice 设备
     */
    public DeviceLCM(String gatewayId, ForeignDevice foreignDevice, ActorRef pipelinePublisher) {
        this.gatewayId = gatewayId;
        this.deviceId = foreignDevice.getForeignDeviceID();
        this.foreignDevice = foreignDevice;

        // 初始化pipes
        this.deviceChangePipeline = new DeviceChangePipeline(getContext(), foreignDevice, pipelinePublisher);
        this.deviceRealSignalPipeline = new DeviceRealSignalPipeline(getContext(), foreignDevice, pipelinePublisher);
        this.deviceAlarmPipeline = new DeviceAlarmPipeline(getContext(), foreignDevice, pipelinePublisher);
        this.deviceHistoryDataPipeline = new DeviceHistoryDataPipeline(getContext(), foreignDevice, pipelinePublisher);
        this.deviceControlCommandPipeline = new DeviceControlCommandPipeline(getContext(), foreignDevice, pipelinePublisher);
        this.deviceLoggingEventPipeline = new DeviceLoggingEventPipeline(getContext(), foreignDevice, pipelinePublisher);

        LogUtil.info(log, "device.lcm.create", gatewayId, deviceId);
    }

    /**
     * 创建Props
     */
    public static Props props(String gatewayId, ForeignDevice foreignDevice,ActorRef pipelinePublisher) {
        return Props.create(DeviceLCM.class, gatewayId, foreignDevice,pipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .match(PoisonPill.class, this::onTermination)
                .match(ForeignSignalChange.class, this::onForeignSignalChange)
                //todo: add all data to pipline
                .build()
                .orElse(super.createReceive());
    }

    private void onForeignSignalChange(ForeignSignalChange foreignSignalChange) {
        deviceRealSignalPipeline.process(foreignSignalChange);
    }

    @Override
    public void postRestart(final Throwable reason){
        synConfig();
    }

    private void synConfig() {
        ForeignDevice newDevice = devicePersistHandler.getForeignDevice(gatewayId, deviceId);
        if (newDevice != null) {
            onNeedUpdate(new NeedUpdateAction(newDevice));
        }
    }

    private void onTermination(PoisonPill poisonPill) {
        try {
            // 销毁设备变更管道
            deviceChangePipeline.destroy();
            // 销毁实时数据管道
            deviceRealSignalPipeline.destroy();
            // 销毁告警管道
            deviceAlarmPipeline.destroy();
            // 销毁历史数据管道
            deviceHistoryDataPipeline.destroy();
            // 销毁控制命令管道
            deviceControlCommandPipeline.destroy();
            // 销毁日志事件管道
            deviceLoggingEventPipeline.destroy();
        } catch (Exception e) {
            LogUtil.error(log, "device.lcm.destroy.failed", e, gatewayId, deviceId);
        }

        // 停止自身
        getContext().stop(getSelf());

        log.info("设备生命周期管理器终止: gatewayId={}, deviceId={}", gatewayId, deviceId);
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            ForeignDevice newForeignDevice = (ForeignDevice) needUpdateAction.getConfig();
            // 更新设备
            deviceChangePipeline.update(needUpdateAction);//adapter 需要更新
            // 更新设备信号
            deviceRealSignalPipeline.update(needUpdateAction); //adapter,store 需要更新
            // 更新设备告警
            deviceAlarmPipeline.update(needUpdateAction);//adapter,store 需要更新(有todo)
            // 更新设备历史数据
            deviceHistoryDataPipeline.update(needUpdateAction); //adapter 需要更新
            // 更新设备控制命令
            deviceControlCommandPipeline.update(needUpdateAction); //req/resp adapter有更新,store有todo
            // 更新设备日志事件
            deviceLoggingEventPipeline.update(needUpdateAction); //adapter 需要更新
            // 更新设备
            this.foreignDevice = newForeignDevice.clone();
        }
    }

}
