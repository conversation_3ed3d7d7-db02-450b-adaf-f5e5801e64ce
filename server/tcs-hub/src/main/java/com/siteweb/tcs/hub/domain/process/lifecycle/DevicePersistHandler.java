package com.siteweb.tcs.hub.domain.process.lifecycle;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.util.SpringBeanUtil;
import com.siteweb.tcs.hub.dal.dto.AlarmDTO;
import com.siteweb.tcs.hub.dal.dto.ControlDTO;
import com.siteweb.tcs.hub.dal.dto.EquipmentDTO;
import com.siteweb.tcs.hub.dal.dto.SignalDTO;
import com.siteweb.tcs.hub.dal.entity.*;
import com.siteweb.tcs.hub.domain.letter.*;
import com.siteweb.tcs.hub.domain.letter.enums.EquipmentTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.process.LocalEquipmentStateStore;
import com.siteweb.tcs.hub.service.impl.*;
import com.siteweb.tcs.siteweb.provider.EquipmentTemplateProvider;
import lombok.extern.slf4j.Slf4j;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;

import java.util.*;

@Slf4j
public class DevicePersistHandler {

    private final ForeignDeviceService foreignDeviceService = SpringBeanUtil.getBean("foreignDeviceService",ForeignDeviceService.class);
    private final ForeignSignalService foreignSignalService = SpringBeanUtil.getBean("foreignSignalService", ForeignSignalService.class);
    private final ForeignAlarmService foreignAlarmService = SpringBeanUtil.getBean("foreignAlarmService", ForeignAlarmService.class);
    private final ForeignControlService foreignControlService =  SpringBeanUtil.getBean("foreignControlService", ForeignControlService.class);
    private final PlatformDefaultIdService platformDefaultIdService = SpringBeanUtil.getBean("platformDefaultIdService",PlatformDefaultIdService.class);
    private final EquipmentService equipmentService = SpringBeanUtil.getBean("equipmentService",EquipmentService.class);
    private final SignalService signalService = SpringBeanUtil.getBean("signalService",SignalService.class);
    private final EventService eventService = SpringBeanUtil.getBean("eventService",EventService.class);
    private final ControlService controlService = SpringBeanUtil.getBean("controlService",ControlService.class);
    private final EquipmentTemplateProvider equipmentTemplateProvider = SpringBeanUtil.getBean("equipmentTemplateProvider",EquipmentTemplateProvider.class);
    private final DoorService doorService = SpringBeanUtil.getBean("doorService",DoorService.class);

    public List<ForeignDevice> handleCreateDeviceEvent(LifeCycleEvent lifeCycleEvent,Integer monitorUnitId) {

        List<ForeignDevice> returnDeviceList = new ArrayList<>();
        String pluginId = lifeCycleEvent.getPluginInstanceId();

        List<ForeignDeviceConfigChange> foreignDeviceConfigChangeList = (List<ForeignDeviceConfigChange>) lifeCycleEvent.getForeignConfigChange();

        if(CollectionUtil.isEmpty(foreignDeviceConfigChangeList)){
            log.error("CREATE DEVICE: device info list is empty, skip the life cycle action {}",lifeCycleEvent);
            return returnDeviceList;
        }


        for (ForeignDeviceConfigChange e : foreignDeviceConfigChangeList) {
            if(foreignDeviceService.judgeDeviceExist(e.getForeignDeviceId(), monitorUnitId)){
                log.error("CREATE DEVICE: device {} info has existed, skip the life cycle action {}",e,lifeCycleEvent);
                continue;
            }

            List<ForeignSignal> foreignSignalList = new ArrayList<>();
            List<ForeignAlarm> foreignAlarmList = new ArrayList<>();
            List<ForeignControl> foreignControlList = new ArrayList<>();

            EquipmentDTO equipmentDTO = equipmentService.createEquipment(monitorUnitId, e);
            if(ObjectUtil.isEmpty(equipmentDTO)) {
                log.error("CREATE DEVICE: equipment create failed, skip the life cycle action {}",lifeCycleEvent);
                continue;
            }

            ForeignDevice foreignDevice = equipmentDTO.toForeignDevice(e.getForeignGatewayId(),e.getForeignDeviceId(), pluginId);
            foreignDeviceService.save(foreignDevice);
            returnDeviceList.add(foreignDevice);

            Map<String,Integer> signalIdMap = new HashMap<>();

            //信号
            if(CollectionUtil.isNotEmpty(e.getForeignSignalConfigChangeList())){
                List<SignalDTO> signalDTOList = new ArrayList<>();
                e.getForeignSignalConfigChangeList().forEach(s -> {
                    SignalDTO signal = signalService.createSignal(equipmentDTO.getEquipmentTemplateId(),s);
                    if(ObjectUtil.isNotEmpty(signal)){
                        foreignSignalList.add(signal.toForeignSignal(e.getForeignGatewayId(), monitorUnitId,e.getForeignDeviceId(),equipmentDTO.getEquipmentId(),s.getForeignSignalId()));
                        signalDTOList.add(signal);
                        signalIdMap.put(s.getForeignSignalId(),signal.getSignalId());
                    }
                });
                foreignSignalService.saveBatch(foreignSignalList);
                equipmentDTO.setSignalDTOList(signalDTOList);
            }

            //事件
            if(CollectionUtil.isNotEmpty(e.getForeignAlarmConfigChangeList())){
                List<AlarmDTO> alarmDTOList = new ArrayList<>();
                e.getForeignAlarmConfigChangeList().forEach(a -> {
                    AlarmDTO alarm = eventService.createAlarm(equipmentDTO.getEquipmentTemplateId(),signalIdMap.get(a.getForeignSignalId()),a);
                    if(ObjectUtil.isNotEmpty(alarm)){
                        foreignAlarmList.add(alarm.toForeignAlarm(e.getForeignGatewayId(),monitorUnitId,e.getForeignDeviceId(),equipmentDTO.getEquipmentId(),a.getForeignAlarmId()));
                        alarmDTOList.add(alarm);
                    }
                });
                foreignAlarmService.saveBatch(foreignAlarmList);
                equipmentDTO.setAlarmDTOList(alarmDTOList);
            }

            //控制
            if(CollectionUtil.isNotEmpty(e.getForeignControlConfigChangeList())){
                List<ControlDTO> controlDTOList = new ArrayList<>();
                e.getForeignControlConfigChangeList().forEach(c -> {
                    ControlDTO control = controlService.createControl(equipmentDTO.getEquipmentTemplateId(),signalIdMap.get(c.getForeignSignalId()),c);
                    if(ObjectUtil.isNotEmpty(control)){
                        foreignControlList.add(control.toForeignControl(e.getForeignGatewayId(), monitorUnitId,e.getForeignDeviceId(),equipmentDTO.getEquipmentId(),c.getForeignControlId()));
                        controlDTOList.add(control);
                    }
                });
                foreignControlService.saveBatch(foreignControlList);
                equipmentDTO.setControlDTOList(controlDTOList);
            }

            //门禁
            if(ObjectUtil.equal(e.getEquipmentCategory(), EquipmentTypeEnum.DOOR_EQUIPMENT.getValue())){
                doorService.createDoor(equipmentDTO, (ForeignDoorDeviceConfigChange) e);
                //创建39通道信号
                signalService.createDoorCategorySignal(equipmentDTO,(ForeignDoorDeviceConfigChange) e);
            }

            foreignDevice.setForeignSignalList(foreignSignalList).setForeignAlarmList(foreignAlarmList)
                    .setForeignControlList(foreignControlList).setStationId(equipmentDTO.getStationId());
        };

        return returnDeviceList;
    }

    public ForeignDevice getForeignDevice(String gatewayId, String deviceId) {
        ForeignDevice foreignDevice = foreignDeviceService.getForeignDeviceById(gatewayId, deviceId);

        if (ObjectUtil.isEmpty(foreignDevice)) {
            return null;
        }

        return foreignDevice;
    }

    public List<ForeignDevice> handleDeleteDevice(LifeCycleEvent lifeCycleEvent) {
        List<ForeignDevice> returnDeviceList = new ArrayList<>();
        HandleDeviceLifeCycleRequest handleDeviceLifeCycleRequest = (HandleDeviceLifeCycleRequest)lifeCycleEvent.getForeignConfigChange();
        List<ForeignDeviceConfigChange> foreignDeviceConfigChangeList = handleDeviceLifeCycleRequest.getForeignDeviceInfoList();
        if(CollectionUtil.isEmpty(foreignDeviceConfigChangeList)){
            return returnDeviceList;
        }
        /*
           1. 删除S6数据库
           2. 删除本地映射信息数据库
           3. 删除本地缓存
        */
        for (ForeignDeviceConfigChange device : foreignDeviceConfigChangeList) {
            ForeignDevice foreignDevice = foreignDeviceService.getById(device.getForeignDeviceId());
            foreignDeviceService.deleteForeignDevice(foreignDevice);
            returnDeviceList.add(foreignDevice);

            equipmentService.deleteEquipment(foreignDevice.getEquipmentId());
            //设备模板
            Boolean delFlag = equipmentTemplateProvider.deleteByEquipmentTemplateId(foreignDevice.getEquipmentTemplateId());
            foreignSignalService.deleteByEquipmentId(foreignDevice.getEquipmentId());
            foreignAlarmService.deleteByEquipmentId(foreignDevice.getEquipmentId());
            foreignControlService.deleteByEquipmentId(foreignDevice.getEquipmentId());

            //删除状态缓存
            LocalEquipmentStateStore.deleteEquipmentState(foreignDevice.getEquipmentId());
            //s6，无需删门禁设备，配置服务已经自己删除了
        };

        return returnDeviceList;
    }

    public List<ForeignDevice> handleUpdateDevice(LifeCycleEvent lifeCycleEvent, ForeignGateway foreignGateway) {
        List<ForeignDevice> returnDeviceList = new ArrayList<>();

        HandleDeviceLifeCycleRequest handleDeviceLifeCycleRequest = (HandleDeviceLifeCycleRequest)lifeCycleEvent.getForeignConfigChange();
        List<ForeignDeviceConfigChange> foreignDeviceConfigChangeList = handleDeviceLifeCycleRequest.getForeignDeviceInfoList();
        if(CollectionUtil.isEmpty(foreignDeviceConfigChangeList)){
            return returnDeviceList;
        }

        List<EquipmentDTO> updateEquipmentList = new ArrayList<>();
        for(ForeignDeviceConfigChange device : foreignDeviceConfigChangeList){
            //从foreignGateway找到ForeignDeviceConfigChange device的monitorUnitId,equipmentId
            Integer monitorUnitId = foreignGateway.getMonitorUnitID();
            Integer equipmentId = null;
            ForeignDevice currentForeignDevice = null;
            for (ForeignDevice foreignDevice : foreignGateway.getForeignDeviceList()){
                if(ObjectUtil.equal(device.getForeignDeviceId(), foreignDevice.getForeignDeviceID())){
                    equipmentId = foreignDevice.getEquipmentId();
                    currentForeignDevice = foreignDevice;
                    break;
                }
            }

            if (equipmentId == null) {
                continue;
            }

            //逻辑梳理澄清,ForeignDevice没什么变化,所以不更新, 对alarm等的更新如下：
            //1 保持gateway的当前foreignDevice作为参数(引用关系,函数内更新此对象可直接影响gateway对象内的数据更新)
            //2 传参给handleAlarmUpdate,更新本对象就等于更新cache(不需要deviceCache)
            returnDeviceList.add(currentForeignDevice);

            EquipmentDetailDTO equipmentDetailDTO = device.toEquipmentDetailDTO(monitorUnitId,equipmentId);
            //equipmentDetailDTO.setStationId(foreignDeviceCache.getStationId()); ??
            //TODO: 这句话看不太懂

            equipmentService.updateEquipment(equipmentDetailDTO);
            EquipmentDTO equipmentDTO = equipmentService.getEquipment(equipmentId);

            //信号删改加
            if(CollectionUtil.isNotEmpty(device.getForeignSignalConfigChangeList())) {
                List<SignalDTO> updateSignalRes = handleSignalUpdate(device.getForeignDeviceId(),device.getForeignSignalConfigChangeList(),
                        currentForeignDevice);
                equipmentDTO.setSignalDTOList(updateSignalRes);
            }
            //告警删改加
            if(CollectionUtil.isNotEmpty(device.getForeignAlarmConfigChangeList())){
                List<AlarmDTO> updateEventRes = handleAlarmUpdate(
                        device.getForeignDeviceId(), device.getForeignAlarmConfigChangeList(), currentForeignDevice);
                equipmentDTO.setAlarmDTOList(updateEventRes);
            }
            //控制删改加
            if(CollectionUtil.isNotEmpty(device.getForeignControlConfigChangeList())){
                List<ControlDTO> updateControlRes = handleControlUpdate(device.getForeignDeviceId(),device.getForeignControlConfigChangeList(),
                        currentForeignDevice);
                equipmentDTO.setControlDTOList(updateControlRes);
            }
            //门禁设备
            if(ObjectUtil.equal(device.getEquipmentCategory(),EquipmentTypeEnum.DOOR_EQUIPMENT.getValue())){
                doorService.updateDoor(equipmentId, (ForeignDoorDeviceConfigChange) device);
            }
        }

        return returnDeviceList;
    }

    //处理告警更新
    private List<AlarmDTO> handleAlarmUpdate(String foreignDeviceId, List<ForeignAlarmConfigChange> foreignAlarmChangeList,
                                             ForeignDevice foreignDevice){

        if(CollectionUtil.isEmpty(foreignAlarmChangeList)) return Collections.emptyList();

        Map<String,ForeignAlarm> alarmMap = new HashMap<>();
        Map<String,Integer> signalIdMap = new HashMap<>();
        foreignDevice.getForeignAlarmList().forEach(e->alarmMap.put(e.getForeignAlarmID(),e));
        foreignDevice.getForeignSignalList().forEach(e -> signalIdMap.put(e.getForeignSignalID(),e.getSignalId()));
        List<AlarmDTO> alarmDTOList = new ArrayList<>();
        for(ForeignAlarmConfigChange alarmChange : foreignAlarmChangeList){
            AlarmDTO alarmDTO = null;
            ForeignAlarm foreignAlarm = alarmMap.get(alarmChange.getForeignAlarmId());
            try {
                switch (alarmChange.getEventType()){
                    case CREATE:
                        alarmDTO = eventService.createAlarm(foreignDevice.getEquipmentTemplateId(), signalIdMap.get(alarmChange.getForeignSignalId()), alarmChange);
                        if (ObjectUtil.isEmpty(alarmDTO)) continue;
                        alarmDTO.setEventType(LifeCycleEventType.CREATE);
                        ForeignAlarm addAlarm = alarmDTO.toForeignAlarm(foreignDevice.getForeignGatewayID(), foreignDevice.getMonitorUnitId(), foreignDeviceId, foreignDevice.getEquipmentId(), alarmChange.getForeignAlarmId());
                        foreignAlarmService.save(addAlarm);

                        foreignDevice.getForeignAlarmList().add(addAlarm);
                        break;
                    case FIELD_UPDATE:
                        eventService.updateEvent(foreignDevice.getEquipmentTemplateId(),foreignAlarm, alarmChange);
                        alarmDTO = eventService.getAlarmInfo(foreignDevice.getEquipmentTemplateId(),foreignAlarm.getEventId());
                        alarmDTO.setEventType(LifeCycleEventType.FIELD_UPDATE);
                        break;
                    case DELETE:
                        //todo 确认告警条件
                        boolean delFlag = eventService.batchDeleteEvent(foreignDevice.getEquipmentTemplateId(), Collections.singletonList(foreignAlarm.getEventId()));
                        if(delFlag){
                            foreignAlarmService.batchDeleteForeignAlarm(foreignDevice.getEquipmentId(),Collections.singletonList(alarmChange.getForeignAlarmId()));
                            alarmDTO = new AlarmDTO().setEventId(foreignAlarm.getEventId()).setEventType(LifeCycleEventType.DELETE);

                            foreignDevice.getForeignAlarmList().removeIf(alarm -> alarm.getForeignAlarmID().equals(alarmChange.getForeignAlarmId()));
                        }
                        break;

                }
            }catch (Exception ex){
                log.error(ex.getMessage());
                continue;
            }
            alarmDTOList.add(alarmDTO);
        }
        return alarmDTOList;
    }

    //处理实时数据更新
    private List<SignalDTO> handleSignalUpdate(String foreignDeviceId,List<ForeignSignalConfigChange> foreignSignalConfigChangeList,
        ForeignDevice foreignDevice){

        if(CollectionUtil.isEmpty(foreignSignalConfigChangeList)) return Collections.emptyList();
        Map<String,Integer> signalIdMap = new HashMap<>();
        foreignDevice.getForeignSignalList().forEach(e->signalIdMap.put(e.getForeignSignalID(),e.getSignalId()));
        List<SignalDTO> signalDTOList = new ArrayList<>();
        for(ForeignSignalConfigChange signalConfigChange:foreignSignalConfigChangeList){
            SignalDTO signalDTO = null;
            Integer signalId = signalIdMap.get(signalConfigChange.getForeignSignalId());
            try {
                switch (signalConfigChange.getEventType()){
                    case CREATE:
                        try{
                            signalDTO = signalService.createSignal(foreignDevice.getEquipmentTemplateId(), signalConfigChange);
                            if (ObjectUtil.isEmpty(signalDTO)) continue;
                            foreignSignalService.save(signalDTO.toForeignSignal(foreignDevice.getForeignGatewayID()
                                    ,foreignDevice.getMonitorUnitId(),foreignDeviceId
                                    ,foreignDevice.getEquipmentId(),signalConfigChange.getForeignSignalId()));
                            signalDTO.setEventType(LifeCycleEventType.CREATE);
                            ForeignSignal foreignSignal = signalDTO.toForeignSignal(foreignDevice.getForeignGatewayID(), foreignDevice.getMonitorUnitId(), foreignDevice.getForeignDeviceID(), foreignDevice.getEquipmentId(), signalConfigChange.getForeignSignalId());

                            foreignDevice.getForeignSignalList().add(foreignSignal);
                        }catch (Exception ex){
                            log.error("创建信号错误", ex);
                        }
                        break;
                    case FIELD_UPDATE:
                        signalService.updateSignal(foreignDevice.getEquipmentTemplateId(), signalId, signalConfigChange);
                        signalDTO = signalService.getSignalInfo(foreignDevice.getEquipmentTemplateId(),signalId);
                        signalDTO.setEventType(LifeCycleEventType.FIELD_UPDATE);
                        break;
                    case DELETE:
                        boolean delFlag = signalService.batchDeleteSignal(foreignDevice.getEquipmentTemplateId(), Collections.singletonList(signalId));
                        if(delFlag){
                            foreignSignalService.batchDeleteForeignSignal(foreignDevice.getEquipmentId(),Collections.singletonList(signalConfigChange.getForeignSignalId()));
                            signalDTO = new SignalDTO().setSignalId(signalId).setEventType(LifeCycleEventType.DELETE);

                            foreignDevice.getForeignSignalList().removeIf(signal -> signal.getForeignSignalID().equals(signalConfigChange.getForeignSignalId()));
                        }
                        break;
                }
            }catch (Exception ex){
                log.error(ex.getMessage());
                continue;
            }
            signalDTOList.add(signalDTO);
        }
        return signalDTOList;
    }

    //处理控制更新
    private List<ControlDTO> handleControlUpdate(String foreignDeviceId, List<ForeignControlConfigChange> foreignControlConfigChangeList,
                                                 ForeignDevice foreignDevice){

        if(CollectionUtil.isEmpty(foreignControlConfigChangeList)) return Collections.emptyList();
        Map<String,ForeignControl> controlMap = new HashMap<>();
        Map<String,Integer> signalIdMap = new HashMap<>();
        foreignDevice.getForeignControlList().forEach(c->controlMap.put(c.getForeignControlId(),c));
        foreignDevice.getForeignSignalList().forEach(s -> signalIdMap.put(s.getForeignSignalID(),s.getSignalId()));
        List<ControlDTO> controlDTOList = new ArrayList<>();
        for (ForeignControlConfigChange controlConfigChange : foreignControlConfigChangeList){
            ControlDTO controlDTO = null;
            ForeignControl foreignControl = controlMap.get(controlConfigChange.getForeignControlId());
            try {
                switch (controlConfigChange.getEventType()){
                    case CREATE:
                        controlDTO = controlService.createControl(foreignDevice.getEquipmentTemplateId(),signalIdMap.get(controlConfigChange.getForeignSignalId()),controlConfigChange);
                        if (ObjectUtil.isEmpty(controlDTO)) continue;
                        controlDTO.setEventType(LifeCycleEventType.CREATE);
                        ForeignControl addObj = controlDTO.toForeignControl(foreignDevice.getForeignGatewayID(), foreignDevice.getMonitorUnitId(), foreignDeviceId, foreignDevice.getEquipmentId(), controlConfigChange.getForeignControlId());
                        foreignControlService.save(addObj);

                        foreignDevice.getForeignControlList().add(foreignControl);
                        break;
                    case FIELD_UPDATE:
                        controlService.updateControl(foreignDevice.getEquipmentTemplateId(),foreignControl,controlConfigChange);
                        controlDTO = controlService.getControlInfo(foreignDevice.getEquipmentTemplateId(),foreignControl.getControlId());
                        controlDTO.setEventType(LifeCycleEventType.FIELD_UPDATE);
                        break;
                    case DELETE:
                        //todo 确认控制含义
                        boolean delFlag = controlService.batchDeleteControl(foreignDevice.getEquipmentTemplateId(),Collections.singletonList(foreignControl.getControlId()));
                        if(delFlag){
                            foreignControlService.batchDeleteForeignControl(foreignDevice.getEquipmentId(),Collections.singletonList(controlConfigChange.getForeignControlId()));
                            controlDTO = new ControlDTO().setControlId(foreignControl.getControlId());

                            foreignDevice.getForeignControlList().removeIf(c -> c.getForeignControlId().equals(controlConfigChange.getForeignControlId()));
                        }
                        break;
                }
            }catch (Exception ex){
                log.error(ex.getMessage());
                continue;
            }
            controlDTOList.add(controlDTO);
        }
        return controlDTOList;
    }


}
