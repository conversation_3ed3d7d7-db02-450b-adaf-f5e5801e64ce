package com.siteweb.tcs.hub.domain.process.lifecycle;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.LogUtil;
import com.siteweb.tcs.common.util.SpringBeanUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.*;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.domain.process.LocalMonitorUnitStateCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 网关生命周期管理器
 * 负责处理网关的生命周期事件，管理网关的CRUD操作
 * 并将设备相关事件转发给对应的DeviceLCM处理
 */
@Slf4j
@Deprecated
public class GatewayLCM extends ProbeActor {
    private final GatewayChangePipeline gatewayChangePipeline;
    private final ActorRef pipelinePublisher;
    private final Map<String, ActorRef> deviceLCMMap = new HashMap<>();
    private String gatewayId;
    private ForeignGateway foreignGateway;
    private final GatewayPersistHandler gatewayPersistHandler;
    private final DevicePersistHandler devicePersistHandler = new DevicePersistHandler();
    private final LocalMonitorUnitStateCache localMonitorUnitStateCache;
    private final Integer monitorUnitId;

    /**
     * 构造函数
     * @param foreignGateway 网关
     */
    public GatewayLCM(ForeignGateway foreignGateway, ActorRef pipelinePublisher) {
        this.gatewayId = foreignGateway.getForeignGatewayID();
        this.gatewayPersistHandler = SpringBeanUtil.getBean("gatewayPersistHandler",GatewayPersistHandler.class);
        this.localMonitorUnitStateCache = SpringBeanUtil.getBean("localMonitorUnitStateCache",LocalMonitorUnitStateCache.class);
        this.foreignGateway = foreignGateway;
        this.pipelinePublisher = pipelinePublisher;
        this.gatewayChangePipeline = new GatewayChangePipeline(getContext(), pipelinePublisher);
        this.monitorUnitId = foreignGateway.getMonitorUnitID();
        load();
    }

    private void load() {
        for(ForeignDevice foreignDevice : foreignGateway.getForeignDeviceList()){
            createDeviceLCM(foreignDevice);
        }

        gatewayChangePipeline.setForeignGateway(foreignGateway);
        gatewayChangePipeline.create();
    }

    public static Props props(ForeignGateway foreignGateway, ActorRef pipelinePublisher) {
        return Props.create(GatewayLCM.class, foreignGateway, pipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        // 1. 获取父类的 Receive 逻辑
        Receive parentReceive = super.createReceive();

        // 2. 构建子类自己的 Receive 逻辑，这里只包含 matchAny 回退
        Receive childFallbackReceive = receiveBuilder()
                .match(LifeCycleEvent.class, this::onLifeCycleEvent)
                .match(TerminationMessage.class, this::onTerminationMessage)
                .match(NeedUpdateAction.class, this::synConfig)
                .match(UpdateGatewayIdAction.class, this::onGatewayIdUpdate)
                .match(ForeignGatewayChange.class, this::onForeignGatewayChange)
                // matchAny 必须放在子类 builder 的最后，用来捕获未被子类自身特定 match 匹配的消息(DTO)
                .matchAny(this::onUnhandledMessage)
                .build();

        // 3. 组合 Receive 逻辑：先尝试父类的处理，如果父类未处理，再尝试子类的回退处理
        // 这确保了父类的 onString/onInteger 会先被尝试
        return parentReceive.orElse(childFallbackReceive);

    }

    private void onForeignGatewayChange(ForeignGatewayChange foreignGatewayChange) {
        gatewayChangePipeline.process(foreignGatewayChange);
    }

    private void onUnhandledMessage(Object object) {
        if (object.getClass().equals(ForeignDeviceChange.class)) {
            sendForeignMessageToDevice(object, ((ForeignDeviceChange) object).getForeignDeviceId());
        } else if (object.getClass().equals(ForeignAlarmChange.class)) {
            sendForeignMessageToDevice(object, ((ForeignAlarmChange) object).getForeignDeviceId());
        } else if (object.getClass().equals(ForeignDeviceControlCommandResponse.class)) {
            sendForeignMessageToDevice(object, ((ForeignDeviceControlCommandResponse) object).getForeignDeviceId());
        } else if (object.getClass().equals(ForeignDeviceSignalChange.class)) {
            sendForeignMessageToDevice(object, ((ForeignDeviceSignalChange) object).getForeignDeviceId());
        }//todo: add all data type
        else {
            LogUtil.warn(log, "hub.lifecycle.gateway.unhandled.message", gatewayId, object.getClass().getName());
        }
    }

    /**
     * Generic method to forward foreign messages to the appropriate device LCM
     * @param message The message to forward
     * @param foreignDeviceId The ID of the target device
     */
    private void sendForeignMessageToDevice(Object message, String foreignDeviceId) {
        ActorRef deviceLCM = deviceLCMMap.get(foreignDeviceId);
        if (deviceLCM != null) {
            deviceLCM.tell(message, getSelf());
        }
    }

    private void onGatewayIdUpdate(UpdateGatewayIdAction updateGatewayIdAction) {
        this.foreignGateway.setForeignGatewayID(updateGatewayIdAction.getNewGatewayId());
        this.gatewayId = updateGatewayIdAction.getNewGatewayId();

        for (ActorRef deviceLCM : deviceLCMMap.values()) {
            deviceLCM.tell(updateGatewayIdAction, getSelf());
        }
    }

    private void onTerminationMessage(TerminationMessage terminationMessage) {
        localMonitorUnitStateCache.deleteMonitorUnitState(foreignGateway.getMonitorUnitID());
        this.deviceLCMMap.forEach((deviceId, deviceLCM) -> {
            deviceLCM.tell(new TerminationMessage(), getSelf());
        });
        deviceLCMMap.clear();
        this.gatewayChangePipeline.destroy();
        getContext().stop(getSelf());
    }

    @Override
    public void postRestart(final Throwable reason){
        LogUtil.warn(log, "hub.lifecycle.gateway.manager.restart", gatewayId);
        synConfig(null);
    }

    //同步gateway配置
    private void synConfig(NeedUpdateAction needUpdateAction) {
        ForeignGateway newForeignGateway = null;

        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            newForeignGateway = (ForeignGateway) needUpdateAction.getConfig();
        } else {
            //DB查找配置
            newForeignGateway = gatewayPersistHandler.getForeignGateway(gatewayId,foreignGateway.getPluginId());
            if (ObjectUtil.isEmpty(newForeignGateway)) {
                LogUtil.warn(log, "hub.lifecycle.gateway.config.not.found", gatewayId);
                return;
            }
        }

        //diff device and refresh deviceLCMMap
        diffDevices(newForeignGateway,foreignGateway);

        //对象覆盖
        foreignGateway = newForeignGateway;
    }

    private void diffDevices(ForeignGateway newGateway, ForeignGateway oldGateway) {
        //对比newForeignGateway和foreignGateway，分析设备列表的各设备ID
        //1 遍历foreignGateway的设备,如果有设备ID不存在newForeignGateway中,则删除对应的deviceLCM,否则发送updateAction
        for(ForeignDevice oldDevice : oldGateway.getForeignDeviceList()) {
            String deviceId = oldDevice.getForeignDeviceID();
            ForeignDevice newDevice = newGateway.getForeignDeviceList().stream()
                    .filter(foreignDevice -> foreignDevice.getForeignDeviceID().equals(deviceId))
                    .findFirst()
                    .orElse(null);

            if (ObjectUtil.isEmpty(newDevice)) {
                destroyDeviceLCM(deviceId);
            } else {
                //否则发送新设备配置给现有设备LCM,让其核对是否需要更新内部alarm/signal/control等数据
                this.deviceLCMMap.get(deviceId).tell(new NeedUpdateAction(newDevice), getSelf());
            }
        }

        //2 遍历newForeignGateway的设备， 关注foreignGateway的device集合，如果foreignGateway缺失此ID设备，则createDeviceLCM并加入deviceLCMMap
        for(ForeignDevice newDevice : newGateway.getForeignDeviceList()) {
            if (!deviceLCMMap.containsKey(newDevice.getForeignDeviceID())) {
                createDeviceLCM(newDevice);
            }
        }
    }

    private void destroyDeviceLCM(String deviceId) {
        if (deviceLCMMap.containsKey(deviceId)) {
            deviceLCMMap.get(deviceId).tell(new TerminationMessage(), getSelf());
            deviceLCMMap.remove(deviceId);
        } else {
            LogUtil.warn(log, "hub.lifecycle.device.manager.not.found", deviceId);
        }
    }

    private void createDeviceLCM(ForeignDevice foreignDevice) {
        ActorRef deviceLCM = getContext().actorOf(
                DeviceLCM.props(gatewayId, foreignDevice,pipelinePublisher),
                "deviceLCM-" + foreignDevice.getForeignDeviceID());
        deviceLCMMap.put(foreignDevice.getForeignDeviceID(), deviceLCM);

        LogUtil.info(log, "hub.lifecycle.device.manager.created", gatewayId, foreignDevice.getForeignDeviceID());
    }

    /**
     * 处理生命周期事件
     */
    private void onLifeCycleEvent(LifeCycleEvent event) {
        try {
            if (event.getThingType() == ThingType.GATEWAY) {
                // 处理网关事件
                handleGatewayLifeCycleEvent(event);

            } else if (event.getThingType() == ThingType.DEVICE) {
                switch (event.getEventType()) {
                    case CREATE:
                        handleCreateDevice(event);
                        break;
                    case DELETE:
                        handleDeleteDevice(event);
                        break;
                    case FIELD_UPDATE:
                        handleUpdateDevice(event);
                        break;
                    default:
                        log.warn("未知的生命周期事件类型: {}", event.getEventType());
                }
            }
        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.event.process.failed", e);
            getProbe().error("Failed to process lifecycle event: " + e.getMessage());
        }
    }

    private void handleUpdateDevice(LifeCycleEvent event) {
        try {
            List<ForeignDevice> updatedDevices = devicePersistHandler.handleUpdateDevice(event,foreignGateway);

            for (ForeignDevice foreignDevice : updatedDevices){
                if (deviceLCMMap.containsKey(foreignDevice.getForeignDeviceID())) {
                    deviceLCMMap.get(foreignDevice.getForeignDeviceID()).tell(new NeedUpdateAction(foreignDevice), getSelf());
                }
            }

            //notify
            pipelinePublisher.tell(event, getSelf());

        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.device.update.failed", e);
        }

    }

    private void handleDeleteDevice(LifeCycleEvent event) {
        try {
            List<ForeignDevice> foreignDevices = devicePersistHandler.handleDeleteDevice(event);
            //load
            for (ForeignDevice foreignDevice : foreignDevices) {
                destroyDeviceLCM(foreignDevice.getForeignDeviceID());
            }

            //notify
            pipelinePublisher.tell(event, getSelf());

        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.device.delete.failed", e);
        }

    }

    private void handleCreateDevice(LifeCycleEvent event) {
        try {
            //persist
            List<ForeignDevice> foreignDevices = devicePersistHandler.handleCreateDeviceEvent(event,monitorUnitId);

            //load
            for (ForeignDevice foreignDevice : foreignDevices) {
                createDeviceLCM(foreignDevice);
            }

            //notify
            pipelinePublisher.tell(event, getSelf());

        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.device.create.failed", e);
        }
    }

    /**
     * 处理网关生命周期事件
     */
    private void handleGatewayLifeCycleEvent(LifeCycleEvent event) {
        try {
            // 根据事件类型处理
            if (Objects.requireNonNull(event.getEventType()) == LifeCycleEventType.FIELD_UPDATE) {
                handleUpdateGateway(event);
            } else {
                log.warn("未处理的生命周期事件类型: {}", event.getEventType());
            }
        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.gateway.event.process.failed", e);
            getProbe().error("Failed to handle gateway lifecycle event: " + e.getMessage());
        }
    }

    /**
     * 处理更新网关事件
     */
    private void handleUpdateGateway(LifeCycleEvent event) {
        ForeignGateway foreignGateway = ((HandleGatewayLifeCycleResponse) event.getForeignConfigChange()).getForeignGateway();
        NeedUpdateAction action = new NeedUpdateAction(foreignGateway);
        synConfig(action);
        this.gatewayChangePipeline.update(action);
        pipelinePublisher.tell(event, getSelf());
        log.info("网关更新成功: pluginId={}, gatewayId={}", foreignGateway.getForeignGatewayID(), gatewayId);
        getProbe().info("Gateway updated: " + foreignGateway.getForeignGatewayID() + "-" + gatewayId);
    }

    /**
     * 转发设备事件给对应的DeviceLCM
     */
    private void forwardToDeviceLCM(LifeCycleEvent event) {
        String deviceId = event.getForeignDeviceId();
        ActorRef deviceLCM = deviceLCMMap.get(deviceId);

        if (deviceLCM != null) {
            deviceLCM.tell(event, getSelf());
        } else {
            LogUtil.warn(log, "hub.lifecycle.device.manager.not.found", gatewayId, deviceId);
        }
    }

}
