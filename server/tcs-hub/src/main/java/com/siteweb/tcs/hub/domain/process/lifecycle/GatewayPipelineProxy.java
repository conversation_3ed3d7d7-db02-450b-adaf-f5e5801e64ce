package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.SpringBeanUtil;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.util.LogUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 集群下，网关数据管道管理器集群入口proxy
 */
@Slf4j
@Deprecated
public class GatewayPipelineProxy extends ProbeActor {

    private ForeignGateway foreignGateway;

    private ActorRef pipelinePublisher;
    private final GatewayPersistHandler gatewayPersistHandler;

    // 网关生命周期管理器，用于处理网关相关的生命周期事件
    private ActorRef gatewayLCM;

    public GatewayPipelineProxy(String shardEntityId,String pluginInstanceId) {

        this.gatewayPersistHandler = SpringBeanUtil.getBean("gatewayPersistHandler",GatewayPersistHandler.class);

        loadGateway(shardEntityId,pluginInstanceId);
    }

    private Boolean isGatewayReady() {
        return foreignGateway != null;
    }

    private void loadGateway(String foreignGatewayId,String pluginInstanceId) {
        this.foreignGateway = gatewayPersistHandler.getForeignGateway(foreignGatewayId,pluginInstanceId);

        if (!isGatewayReady()) return;

        this.pipelinePublisher = getContext().actorOf(GatewayPipelinePublisher.props(foreignGateway), "pipelinePublisher");

        // 创建网关生命周期管理器
        this.gatewayLCM = getContext().actorOf(GatewayLCM.props(foreignGateway, pipelinePublisher),"gatewayLCM");
    }

    private void createGateway(LifeCycleEvent event){
        try {
            //persist
            gatewayPersistHandler.handleCreateGateway(event);

            //load
            loadGateway(event.getForeignGatewayId(),event.getPluginInstanceId());

            //notify
            pipelinePublisher.tell(event, getSelf());

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void deleteGateway(LifeCycleEvent event) {
        try {
            gatewayPersistHandler.handleDeleteGateway(event.getPluginInstanceId(), event.getForeignGatewayId());

            this.foreignGateway = null;
            getContext().stop(this.pipelinePublisher);
            getContext().stop(this.gatewayLCM);

            //notify
            pipelinePublisher.tell(event, getSelf());

        }catch (Exception e){
            throw new RuntimeException(e);
        }

    }

    private void updateGatewayId(LifeCycleEvent event) {
        try {
            if (!gatewayPersistHandler.handleGatewayIdChange(event)) {
                log.error("hub.lifecycle.gateway.id.update.failed: {}", event.getForeignGatewayId());
                return;
            }

            ForeignGatewayConfigChange config = (ForeignGatewayConfigChange) event.getForeignConfigChange();
            String newForeignGatewayId = config.getForeignGatewayID();

            this.gatewayLCM.tell(new UpdateGatewayIdAction(newForeignGatewayId), getSelf()); ///update id

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateGatewayField(LifeCycleEvent event) {
        try {
            gatewayPersistHandler.handleUpdateGateway(event); //only monitor unit table
            this.gatewayLCM.tell(event, getSelf());

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Props props(String shardEntityId) {
        return Props.create(GatewayPipelineProxy.class, shardEntityId);
    }

    @Override
    public Receive createReceive() {
        // 1. 获取父类的 Receive 逻辑
        Receive parentReceive = super.createReceive();

        // 2. 构建子类自己的 Receive 逻辑，这里只包含 matchAny 回退
        Receive childFallbackReceive = receiveBuilder()
                .match(LifeCycleEvent.class, this::onLifeCycleEvent)
                .match(NeedUpdateAction.class, this::onUpdateAction)
                // matchAny 必须放在子类 builder 的最后，用来捕获未被子类自身特定 match 匹配的消息(DTO)
                .matchAny(this::onUnhandledMessage)
                .build();

        // 3. 组合 Receive 逻辑：先尝试父类的处理，如果父类未处理，再尝试子类的回退处理
        // 这确保了父类的 onString/onInteger 会先被尝试
        return parentReceive.orElse(childFallbackReceive);

    }

    private void onUnhandledMessage(Object o) {
        if (!isGatewayReady()) return;
        this.gatewayLCM.tell(o, getSelf());
    }

    /**
     * 处理生命周期事件
     * 根据事件类型和目标对象，将事件转发给对应的GatewayLCM处理
     */
    private void onLifeCycleEvent(LifeCycleEvent event) {
        if (!isGatewayReady()) return;

        try {
            // 如果是网关创建事件，需要先创建对应的GatewayLCM
            if (event.getThingType() == ThingType.GATEWAY && event.getEventType() == LifeCycleEventType.CREATE) {
                createGateway(event);
            }

            if (event.getThingType() == ThingType.GATEWAY && event.getEventType() == LifeCycleEventType.DELETE) {
                // 删除网关生命周期管理器
                deleteGateway(event);
            }

            if (event.getThingType() == ThingType.GATEWAY && event.getEventType() == LifeCycleEventType.FIELD_UPDATE) {
                // 更新网关生命周期管理器(property)
                updateGatewayField(event);
            }

            if (event.getThingType() == ThingType.GATEWAY && event.getEventType() == LifeCycleEventType.GATEWAY_ID_CHANGE) {
                // 更新网关生命周期管理器(id)
                updateGatewayId(event);
            }

            // 只有设备事件且对应网关存在才转发
            if (event.getThingType() == ThingType.DEVICE) {
                this.gatewayLCM.tell(event, getSelf());
            }
        } catch (Exception e) {
            LogUtil.error(log, "hub.lifecycle.event.process.failed", e);
            getProbe().error("Failed to process lifecycle event: " + e.getMessage());
        }
    }

    /**
     * 处理配置更新事件
     * 转发给GatewayLCM处理
     */
    private void onUpdateAction(NeedUpdateAction action) {
        if (!isGatewayReady()) return;
        // 转发给GatewayLCM处理
        gatewayLCM.tell(action, getSelf());
    }

}