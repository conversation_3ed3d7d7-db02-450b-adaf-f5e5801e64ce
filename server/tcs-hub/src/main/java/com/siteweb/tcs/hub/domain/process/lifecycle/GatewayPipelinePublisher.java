package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.LifeCycleEvent;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;

public class GatewayPipelinePublisher extends ProbeActor {
    // 分布式发布/订阅主题前缀
    private static final String TOPIC_PREFIX = "pipeline";

    // 分布式发布/订阅主题格式：pipeline.{gatewayId}.{type}
    private static final String TOPIC_FORMAT = TOPIC_PREFIX + ".%s.%s";

    private final ForeignGateway foreignGateway;
    private final ActorRef mediator;

    public GatewayPipelinePublisher(ForeignGateway foreignGateway) {
        this.foreignGateway = foreignGateway;

        // 初始化分布式发布/订阅中介器
        mediator = DistributedPubSub.get(getContext().system()).mediator();
    }

    public static Props props(ForeignGateway foreignGateway) {
        return Props.create(GatewayPipelinePublisher.class, foreignGateway);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DistributedPubSubMediator.SubscribeAck.class, this::onSubscribeAck)
                .match(DistributedPubSubMediator.UnsubscribeAck.class, this::onUnsubscribeAck)
                .match(LifeCycleEvent.class, this::onLifeCycleEvent)
                .build()
                .orElse(super.createReceive());
    }

    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        publish(SubscriptionTypeEnum.LIFECYCLE_EVENT, lifeCycleEvent);
    }

    // 使用分布式发布/订阅中介器发布消息
    public void publish(SubscriptionTypeEnum subscriptionTypeEnum, Object message) {
        String topic = String.format(TOPIC_FORMAT, foreignGateway.getMonitorUnitID(), subscriptionTypeEnum.toString().toLowerCase());

        mediator.tell(
                new DistributedPubSubMediator.Publish(topic, message, true), getSelf());
    }

    public void onSubscribeAck(DistributedPubSubMediator.SubscribeAck ack) {
    }

    public void onUnsubscribeAck(DistributedPubSubMediator.UnsubscribeAck ack) {
    }
}
