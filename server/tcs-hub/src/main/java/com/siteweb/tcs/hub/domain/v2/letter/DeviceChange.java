package com.siteweb.tcs.hub.domain.v2.letter;

import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: tcs2
 * @description: 设备变化消息
 * @author: xsx
 * @create: 2025-07-12 10:53
 **/
@Data
public class DeviceChange {
    private Long gatewayId;
    private Long deviceId;
    private String foreignDeviceName;
    private EnumDeviceConnectState connectState;
    private LocalDateTime timeStamp;
}
