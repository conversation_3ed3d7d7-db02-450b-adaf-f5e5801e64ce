package com.siteweb.tcs.hub.domain.v2.letter;

import com.siteweb.tcs.hub.domain.letter.LoggingEvent;
import com.siteweb.tcs.hub.domain.letter.enums.LoggingEventEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: tcs2
 * @description: 日志型事件变化消息
 * @author: xsx
 * @create: 2025-07-12 10:54
 **/
@Data
public class DeviceLoggingEventChange {
    private Long gatewayId;
    private Long deviceId;
    private LocalDateTime recordTime;
    private LoggingEventEnum loggingEventEnum;
    private List<LoggingEvent> loggingEvent;
}
