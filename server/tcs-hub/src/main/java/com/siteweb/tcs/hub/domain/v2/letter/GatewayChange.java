package com.siteweb.tcs.hub.domain.v2.letter;

import com.siteweb.tcs.hub.domain.letter.enums.EnumGatewayConnectState;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: tcs2
 * @description: 网关变化消息
 * @author: xsx
 * @create: 2025-07-12 10:54
 **/
@Data
public class GatewayChange {
    private Long gatewayId;
    private String name;
    private String ip;
    private String port;
    private EnumGatewayConnectState connectState;
    private String firmWareVersion;
    private String softWareVersion;
    private String vendor;
    private String modal;
    private LocalDateTime timeStamp;
}
