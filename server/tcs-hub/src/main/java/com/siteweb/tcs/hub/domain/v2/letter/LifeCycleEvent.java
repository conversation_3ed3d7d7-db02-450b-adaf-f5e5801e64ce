package com.siteweb.tcs.hub.domain.v2.letter;

import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: tcs2
 * @description: 生周期事件
 * @author: xsx
 * @create: 2025-07-15 13:07
 **/
@Data
public class LifeCycleEvent implements Serializable {
    private static final long serialVersionUID = 1L;
    
    //生周期物id
    private Long thingId;
    //物类型
    private ThingType thingType;
    //生命周期类型
    private LifeCycleEventEnum lifeCycleEventEnum;
    //变化对象
    private Object changeObject;
}
