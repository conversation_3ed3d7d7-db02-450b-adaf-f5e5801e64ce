package com.siteweb.tcs.hub.domain.v2.letter;

import com.siteweb.tcs.hub.domain.letter.enums.OperationTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @program: tcs2
 * @description: 操作事件
 * @author: xsx
 * @create: 2025-07-11 10:36
 **/
@Data
@Accessors(chain = true)
public class OperationEvent implements Serializable {
    private Long thingId;
    private ThingType thingType;
    private OperationTypeEnum operationType;
}
