package com.siteweb.tcs.hub.domain.v2.process;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.StoreDeadLetter;
import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 11:04
 **/

public class DeviceChangeProcessor extends ProbeActor {
    private TcsDevice device;
    private ActorRef stateStore;
    private LocalDateTime lastTimestamp;
    private EnumDeviceConnectState lastConnectState;

    private DeviceChangeProcessor(TcsDevice device, ActorRef stateStore){
        this.device = device;
        this.stateStore = stateStore;
        this.getProbe().addRateCalculator("deviceStateRateIn", 60);
        getContext().watchWith(stateStore,new StoreDeadLetter());
    }

    public static Props props(TcsDevice device, ActorRef stateStore){
        return Props.create(DeviceChangeProcessor.class,device,stateStore);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(DeviceChange.class,this::onDeviceChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    // 处理设备自诊断，在线离线
    private void onDeviceChange(DeviceChange deviceChange) {
        if(ObjectUtil.isEmpty(deviceChange))return;
        var deviceExist = downSampleDeviceChange(deviceChange);
        if(ObjectUtil.isNotEmpty(deviceExist)){
            stateStore.tell(deviceExist,getSelf());
        }
        this.getProbe().incrementRateSource("deviceStateRateIn");
        this.getProbe().info(" onForeignDeviceChange: DeviceId=" + deviceChange.getDeviceId()+ ", GatewayId=" + deviceChange.getGatewayId());
    }

    private DeviceChange downSampleDeviceChange(DeviceChange deviceChange) {
        //h: 保存上个DeviceChange的时间戳和connectState状态
        //利用类变量暂存上个DeviceChange的时间戳和connectState状态
        //对DeviceChange进行数据降采样，与上个connectState相同的且DeviceChange时间戳间隔小于2秒的，则丢弃

        if (lastTimestamp == null) {
            lastConnectState = deviceChange.getConnectState();
            lastTimestamp = deviceChange.getTimeStamp();
            return deviceChange;
        }
        if (lastConnectState != deviceChange.getConnectState()) {
            lastConnectState = deviceChange.getConnectState();
            lastTimestamp = deviceChange.getTimeStamp();
            return deviceChange;
        }

        if (deviceChange.getTimeStamp().isAfter(lastTimestamp.plus(Duration.ofSeconds(2)))) {
            lastConnectState = deviceChange.getConnectState();
            lastTimestamp = deviceChange.getTimeStamp();
            return deviceChange;
        }

        return null;

    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }
}
