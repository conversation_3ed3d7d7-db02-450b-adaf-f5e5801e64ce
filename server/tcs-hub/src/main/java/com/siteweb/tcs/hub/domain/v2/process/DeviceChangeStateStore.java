package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 11:04
 **/

public class DeviceChangeStateStore extends ProbeActor {
    private TcsDevice device;
    private ActorRef pipelinePublisher;

    private DeviceChangeStateStore(TcsDevice device, ActorRef pipelinePublisher){
        this.device = device;
        this.pipelinePublisher = pipelinePublisher;
    }

    public static Props props(TcsDevice device, ActorRef pipelinePublisher){
        return Props.create(DeviceChangeStateStore.class,device,pipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(DeviceChange.class,this::onDeviceChangeStore)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onDeviceChangeStore(DeviceChange deviceChange) {
        pipelinePublisher.tell(deviceChange,getSelf());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }

}
