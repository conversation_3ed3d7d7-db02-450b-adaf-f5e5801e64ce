package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandRequestChange;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandResponseChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-14 09:26
 **/

public class DeviceCommandControlStateStore extends ProbeActor {

    private TcsDevice device;

    private ActorRef controlCommandRequestProcessor;

    private ActorRef pipelinePublisher;

    private DeviceCommandControlStateStore(TcsDevice device,ActorRef controlCommandRequestProcessor,ActorRef pipelinePublisher){
        this.device = device;
        this.pipelinePublisher = pipelinePublisher;
        this.controlCommandRequestProcessor = controlCommandRequestProcessor;
    }

    public static Props props(TcsDevice device,ActorRef controlCommandRequestProcessor,ActorRef pipelinePublisher){
        return Props.create(DeviceCommandControlStateStore.class,device,controlCommandRequestProcessor,pipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DeviceControlCommandRequestChange.class,this::onDeviceControlCommandRequestChange)
                .match(DeviceControlCommandResponseChange.class,this::onDeviceControlCommandResponseChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build();
    }

    // 接受控制请求转发给controlCommandRequestProcessor
    private void onDeviceControlCommandRequestChange(DeviceControlCommandRequestChange deviceControlCommandRequestChange) {
        controlCommandRequestProcessor.tell(deviceControlCommandRequestChange,getSelf());
    }

    // 接受控制响应处理后发布到pipelinePublisher
    private void onDeviceControlCommandResponseChange(DeviceControlCommandResponseChange deviceControlCommandResponseChange) {
        pipelinePublisher.tell(deviceControlCommandResponseChange,getSelf());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }
}
