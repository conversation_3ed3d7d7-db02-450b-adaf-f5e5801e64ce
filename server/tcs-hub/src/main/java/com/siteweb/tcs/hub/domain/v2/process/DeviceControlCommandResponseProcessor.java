package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandResponseChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-14 09:27
 **/

public class DeviceControlCommandResponseProcessor extends ProbeActor {

    private TcsDevice device;

    private ActorRef commandControlStateStore;

    private DeviceControlCommandResponseProcessor(TcsDevice device,ActorRef commandControlStateStore){
        this.device = device;
        this.commandControlStateStore = commandControlStateStore;
    }

    public static Props props(TcsDevice device, ActorRef commandControlStateStore){
        return Props.create(DeviceControlCommandResponseProcessor.class,device,commandControlStateStore);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DeviceControlCommandResponseChange.class,this::onDeviceControlCommandResponseChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build();
    }

    private void onDeviceControlCommandResponseChange(DeviceControlCommandResponseChange deviceControlCommandResponseChange) {
        commandControlStateStore.tell(deviceControlCommandResponseChange,getSelf());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }

}
