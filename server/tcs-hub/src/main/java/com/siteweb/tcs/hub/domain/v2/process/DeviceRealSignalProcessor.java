package com.siteweb.tcs.hub.domain.v2.process;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.dal.entity.TcsSignal;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceSignalChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.Map;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:56
 **/

public class DeviceRealSignalProcessor extends ProbeActor {

    private Map<Long, TcsSignal> signalMap;

    private ActorRef storeActor;

    private DeviceRealSignalProcessor(TcsDevice device,ActorRef storeActor){
        this.storeActor = storeActor;
        if(CollectionUtil.isNotEmpty(device.getSignals())){
            device.getSignals().forEach(e -> signalMap.put(e.getDeviceId(),e));
        }
    }

    public static Props props(TcsDevice device,ActorRef storeActor){
        return Props.create(DeviceRealSignalProcessor.class,device,storeActor);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(DeviceSignalChange.class,this::onDeviceSignalChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    //处理实时信号
    private void onDeviceSignalChange(DeviceSignalChange deviceSignalChange) {
        /**
         * 1.判断信号的id都存不存在，不存在证明可能配置不同步
         * 2.判断信号时间是不是异常
         * 其他过滤逻辑
         * ......
         */
        deviceSignalChange.getSignalChangeList().removeIf(
                change -> !signalMap.containsKey(change.getSignalId())
        );

        storeActor.tell(deviceSignalChange,getSelf());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            TcsDevice device = (TcsDevice) needUpdateAction.getConfig();
            this.signalMap.clear();
            if(CollectionUtil.isNotEmpty(device.getSignals())){
                device.getSignals().forEach(e -> signalMap.put(e.getDeviceId(),e));
            }
        }
    }
}
