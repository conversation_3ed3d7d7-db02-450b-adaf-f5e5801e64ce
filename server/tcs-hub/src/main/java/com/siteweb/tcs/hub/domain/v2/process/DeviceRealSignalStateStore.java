package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.common.o11y.ActorParameter;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceSignalChange;
import com.siteweb.tcs.hub.domain.v2.letter.SignalChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:57
 **/

public class DeviceRealSignalStateStore extends ProbeActor {

    private TcsDevice device;

    private ActorRef pipelinePublisher;

    private final DeviceSignalActiveState state = new DeviceSignalActiveState();
    private static final String MISFIRE_DEVICE = "MISFIRE_DEVICE";
    private static final String MISFIRE_SIGNAL = "MISFIRE_SIGNAL";
    private final String REAL_DATA = "real_data";


    private DeviceRealSignalStateStore(TcsDevice device,ActorRef  pipelinePublisher){
        this.device = device;
        this.pipelinePublisher = pipelinePublisher;
        getProbe().addWindowLog(MISFIRE_DEVICE);
        getProbe().addWindowLog(MISFIRE_SIGNAL);
        getProbe().addWindowLog(REAL_DATA);
        getProbe().setHasBypassFunctionality(true);
        this.state.setDeviceId(device.getId());
        getProbe().newParameter("MinInterval","MinInterval", ActorParameter.ActorParameterDataType.INTEGER, false, 2000);
    }

    public static Props props(TcsDevice device,ActorRef  pipelinePublisher){
        return Props.create(DeviceRealSignalStateStore.class,device,pipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(DeviceSignalChange.class,this::onDeviceSignalChangeStore)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onDeviceSignalChangeStore(DeviceSignalChange deviceSignalChange) {
        getProbe().bypass(deviceSignalChange);
        if (deviceSignalChange == null) return;

        Integer saveInterval = getProbe().getParameter("MinInterval").getIntValue();

        HashMap<Long, SignalChange> signalMap = this.state.getCache();
        //不直接在循环里面remove
        List<SignalChange> filterSignal = new ArrayList<>();
        for (SignalChange signal : deviceSignalChange.getSignalChangeList()) {

            if (signalMap.containsKey(signal.getSignalId())) {
                SignalChange oldSignal = signalMap.get(signal.getSignalId());
                //如果有超过1s的新数据，则更新（down sample，降低无效写次数）
                Duration duration = Duration.between(oldSignal.getTimestamp(), signal.getTimestamp());
                if (duration.toMillis() > saveInterval) {
                    signalMap.put(signal.getSignalId(), signal);
                } else {
                    getProbe().enqueueWindowLogItem(MISFIRE_SIGNAL, signal);
                    filterSignal.add(signal);
                }
            } else {
                this.state.getCache().put(signal.getSignalId(),signal);
//                this.state.getMetricInstrument().enqueueWindowLogItem(MISFIRE_SIGNAL, signal);
            }
        }

        //tell spout to send change of device signals to watchers
        deviceSignalChange.getSignalChangeList().removeAll(filterSignal);

        if (!deviceSignalChange.getSignalChangeList().isEmpty()) {
            getProbe().enqueueWindowLogItem(REAL_DATA, deviceSignalChange);
            //发布出去
            pipelinePublisher.tell(deviceSignalChange,getSelf());
        }
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
            this.state.setDeviceId(device.getId());
        }
    }
}