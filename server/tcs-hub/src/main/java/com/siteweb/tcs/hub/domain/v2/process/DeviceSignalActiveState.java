package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.hub.domain.v2.letter.SignalChange;
import lombok.Data;

import java.util.HashMap;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-12 14:08
 **/
@Data
public class DeviceSignalActiveState {
    private Long deviceId;
    private final HashMap<Long, SignalChange> cache = new HashMap<>();

    public void reset() {
        cache.clear();
    }
}
