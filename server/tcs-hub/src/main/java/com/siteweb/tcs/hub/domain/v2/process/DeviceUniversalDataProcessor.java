package com.siteweb.tcs.hub.domain.v2.process;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceSignalChange;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceUniversalDataChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description: 设备通用数据通道处理器
 * @author: xsx
 * @create: 2025-07-16 14:13
 **/

public class DeviceUniversalDataProcessor extends ProbeActor {
    private ActorRef storeActor;
    private TcsDevice device;

    private DeviceUniversalDataProcessor(TcsDevice device,ActorRef storeActor){
        this.device = device;
        this.storeActor = storeActor;
    }

    public static Props props(TcsDevice device,ActorRef storeActor){
        return Props.create(DeviceUniversalDataProcessor.class,device,storeActor);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(DeviceUniversalDataChange.class,this::onDeviceUniversalDataChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }

    private void onDeviceUniversalDataChange(DeviceUniversalDataChange deviceUniversalDataChange) {
        if(ObjectUtil.isEmpty(deviceUniversalDataChange)) return;
        storeActor.tell(deviceUniversalDataChange,getSelf());
    }
}
