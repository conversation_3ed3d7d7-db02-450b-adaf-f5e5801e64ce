package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.GatewayChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 11:11
 **/

public class GatewayChangeStateStore  extends ProbeActor {
    private TcsGateway gateway;
    private ActorRef pipelinePublisher;

    private GatewayChangeStateStore(TcsGateway gateway,ActorRef pipelinePublisher){
        this.gateway = gateway;
        this.pipelinePublisher = pipelinePublisher;
    }

    public static Props props(TcsGateway gateway,ActorRef pipelinePublisher){
        return Props.create(GatewayChangeStateStore.class,gateway,pipelinePublisher);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(GatewayChange.class,this::onGatewayChangeStateStore)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onGatewayChangeStateStore(GatewayChange gatewayChange) {
        pipelinePublisher.tell(gatewayChange,getSelf());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.gateway = (TcsGateway) needUpdateAction.getConfig();
        }
    }
}

