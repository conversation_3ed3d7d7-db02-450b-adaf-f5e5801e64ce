package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import lombok.Getter;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.PoisonPill;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:45
 **/
public class DataPipeline<PipelineConfigEntity>{

    protected PipelineConfigEntity configEntity;
    @Getter
    private final ActorContext context;
    protected ActorRef processorActor;
    protected ActorRef storeActor;
    protected ActorRef  pipelinePublisher;

    public DataPipeline(ActorContext context, ActorRef pipelinePublisher,PipelineConfigEntity configEntity){
        this.context = context;
        this.pipelinePublisher = pipelinePublisher;
    }

    //创建，子类实现
    public void create() {

    }

    public void update(NeedUpdateAction action){
        Class<?> aClass = configEntity.getClass();
        Object config = action.getConfig();
        if(action.getConfig() != null && aClass.isInstance(config)) {
            configEntity = (PipelineConfigEntity) config;
            processorActor.tell(action,ActorRef.noSender());
            storeActor.tell(action,ActorRef.noSender());
        }
    }

    //销毁
    public void destroy() {
        if(ObjectUtil.isNotEmpty(processorActor)){
            processorActor.tell(PoisonPill.getInstance(), ActorRef.noSender());
        }
        if(ObjectUtil.isNotEmpty(processorActor)){
            storeActor.tell(PoisonPill.getInstance(), ActorRef.noSender());
        }
    }

    public void process(Object message) {
        processorActor.tell(message, context.self());
    }

}
