package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.v2.process.DeviceAlarmProcessor;
import com.siteweb.tcs.hub.domain.v2.process.DeviceAlarmStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:44
 **/

public class DeviceAlarmPipeline extends DataPipeline<TcsDevice>{

    public DeviceAlarmPipeline(ActorContext context, ActorRef pipelinePublisher,TcsDevice device) {
        super(context, pipelinePublisher,device);
    }

    @Override
    public void create() {
        storeActor = getContext().actorOf(DeviceAlarmStateStore.props(configEntity,pipelinePublisher),"DeviceAlarmStateStore");
        processorActor = getContext().actorOf(DeviceAlarmProcessor.props(configEntity,storeActor),"DeviceAlarmProcessor");
    }
}
