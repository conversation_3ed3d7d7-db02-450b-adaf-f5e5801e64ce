package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.v2.process.DeviceChangeProcessor;
import com.siteweb.tcs.hub.domain.v2.process.DeviceChangeStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:43
 **/

public class DeviceChangePipeline extends DataPipeline<TcsDevice>{

    public DeviceChangePipeline(ActorContext context, ActorRef pipelinePublisher,TcsDevice device) {
        super(context, pipelinePublisher,device);
    }

    @Override
    public void create() {
        storeActor = getContext().actorOf(DeviceChangeStateStore.props(configEntity,pipelinePublisher),"DeviceChangeStateStore");
        processorActor = getContext().actorOf(DeviceChangeProcessor.props(configEntity,storeActor),"DeviceChangeProcessor");
    }
}
