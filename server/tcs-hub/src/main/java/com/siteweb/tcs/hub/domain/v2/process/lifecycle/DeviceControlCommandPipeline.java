package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandRequestChange;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandResponseChange;
import com.siteweb.tcs.hub.domain.v2.process.DeviceCommandControlStateStore;
import com.siteweb.tcs.hub.domain.v2.process.DeviceControlCommandRequestProcessor;
import com.siteweb.tcs.hub.domain.v2.process.DeviceControlCommandResponseProcessor;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:44
 **/

public class DeviceControlCommandPipeline extends DataPipeline<TcsDevice>{

    private ActorRef deviceCommandControlStateStore;
    private ActorRef deviceControlCommandRequestProcessor;
    private ActorRef deviceControlCommandResponseProcessor;

    private ActorRef controlCommandHandlerActor;

    public DeviceControlCommandPipeline(ActorContext context, ActorRef pipelinePublisher,TcsDevice device,ActorRef controlCommandHandlerActor) {
        super(context, pipelinePublisher,device);
        this.controlCommandHandlerActor = controlCommandHandlerActor;
    }

    // 请求 Subscriber -> GatewayLCM(转发) -> DeviceLCM(转发) -> DeviceControlCommandPipeline -> deviceCommandControlStateStore -> deviceControlCommandRequestProcessor -> controlCommandHandlerActor
    // 响应 南向 ->  GatewayLCM(转发) -> DeviceLCM(转发) -> DeviceControlCommandPipeline -> deviceControlCommandResponseProcessor -> deviceCommandControlStateStore -> pipelinePublisher
    @Override
    public void create() {
        this.deviceControlCommandRequestProcessor = getContext().actorOf(DeviceControlCommandRequestProcessor.props(configEntity,controlCommandHandlerActor));
        this.deviceCommandControlStateStore = getContext().actorOf(DeviceCommandControlStateStore.props(configEntity,deviceControlCommandRequestProcessor,pipelinePublisher));
        this.deviceControlCommandResponseProcessor = getContext().actorOf(DeviceControlCommandResponseProcessor.props(configEntity,deviceCommandControlStateStore));
    }

    public void processDeviceControlCommandRequestChange(DeviceControlCommandRequestChange message) {
        deviceCommandControlStateStore.tell(message,getContext().self());
    }

    public void processDeviceControlCommandResponseChange(DeviceControlCommandResponseChange message) {
        deviceControlCommandResponseProcessor.tell(message,getContext().self());
    }
}
