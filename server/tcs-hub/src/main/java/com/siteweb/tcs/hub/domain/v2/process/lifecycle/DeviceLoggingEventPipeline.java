package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.v2.process.DeviceLoggingEventProcessor;
import com.siteweb.tcs.hub.domain.v2.process.DeviceLoggingEventStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:45
 **/

public class DeviceLoggingEventPipeline  extends DataPipeline<TcsDevice>{

    public DeviceLoggingEventPipeline(ActorContext context, ActorRef pipelinePublisher,TcsDevice device) {
        super(context, pipelinePublisher,device);
    }

    @Override
    public void create() {
        storeActor = getContext().actorOf(DeviceLoggingEventStateStore.props(configEntity,pipelinePublisher),"DeviceLoggingEventStateStore");
        processorActor = getContext().actorOf(DeviceLoggingEventProcessor.props(configEntity,storeActor),"DeviceLoggingEventProcessor");
    }
}
