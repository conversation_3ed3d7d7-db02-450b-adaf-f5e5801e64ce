package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.v2.process.DeviceRealSignalProcessor;
import com.siteweb.tcs.hub.domain.v2.process.DeviceRealSignalStateStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 10:44
 **/

public class DeviceRealSignalPipeline extends DataPipeline<TcsDevice>{

    public DeviceRealSignalPipeline(ActorContext context, ActorRef pipelinePublisher,TcsDevice device) {
        super(context, pipelinePublisher,device);
    }

    @Override
    public void create() {
        storeActor = getContext().actorOf(DeviceRealSignalStateStore.props(configEntity,pipelinePublisher),"DeviceRealSignalStateStore");
        processorActor = getContext().actorOf(DeviceRealSignalProcessor.props(configEntity,storeActor),"DeviceRealSignalProcessor");
    }
}
