package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.LogUtil;
import com.siteweb.tcs.common.util.SpringBeanUtil;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.letter.enums.OperationTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.domain.v2.letter.*;
import com.siteweb.tcs.hub.exception.ThingRestartException;
import com.siteweb.tcs.hub.service.ITcsDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.*;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import scala.concurrent.duration.Duration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 09:38
 **/
@Slf4j
public class GatewayLCM extends ProbeActor {

    private final GatewayChangePipeline gatewayChangePipeline;
    private final ActorRef pipelinePublisher;
    private final Map<Long, ActorRef> deviceLCMMap = new HashMap<>();
    private TcsGateway gateway;
    private final ITcsDeviceService deviceService;
    // 分布式发布/订阅主题前缀
    private static final String TOPIC_PREFIX = "pipeline";

    // 分布式发布/订阅主题格式：pipeline.{gatewayId}.{type}
    private static final String TOPIC_FORMAT = TOPIC_PREFIX + ".%s.%s";

    private final ActorRef mediator;


    private GatewayLCM(TcsGateway gateway, ActorRef pipelinePublisher) {

        this.deviceService = SpringBeanUtil.getBean(ITcsDeviceService.class);
        this.gateway = gateway;
        this.pipelinePublisher = pipelinePublisher;
        this.gatewayChangePipeline = new GatewayChangePipeline(getContext(), pipelinePublisher,gateway);
        mediator = DistributedPubSub.get(getContext().system()).mediator();
        //订阅主题，pipeline.gatewayId.control_command_request
        mediator.tell(new DistributedPubSubMediator.Subscribe(String.format(TOPIC_FORMAT,gateway.getId(), SubscriptionTypeEnum.CONTROL_COMMAND_REQUEST.toString().toLowerCase()), getSelf()), getSelf());
        load();
    }

    private void load() {
        for(TcsDevice device : gateway.getDevices()){
            createDeviceLCM(device);
        }
        gatewayChangePipeline.create();
    }

    private void createDeviceLCM(TcsDevice device) {
        //从数据库拉回来设备最新的信息
        ActorRef deviceLCM = getContext().actorOf(
                DeviceLCM.props( device,pipelinePublisher),
                "deviceLCM-" + device.getId());
        deviceLCMMap.put(device.getId(), deviceLCM);

        LogUtil.info(log, "hub.lifecycle.device.manager.created", device.getGatewayId(), device.getId());
    }

    public static Props props(TcsGateway gateway, ActorRef pipelinePublisher) {
        return Props.create(GatewayLCM.class,gateway,pipelinePublisher);
    }

    @Override
    public AbstractActor.Receive createReceive() {
        // 1. 获取父类的 Receive 逻辑
        AbstractActor.Receive parentReceive = super.createReceive();

        // 2. 构建子类自己的 Receive 逻辑，这里只包含 matchAny 回退
        AbstractActor.Receive childFallbackReceive = receiveBuilder()
                .match(OperationEvent.class, this::onOperationEvent)
                .match(LifeCycleEvent.class,this::onLifeCycleEvent)
                .match(NeedUpdateAction.class,this::onNeedUpdateAction)
                // matchAny 必须放在子类 builder 的最后，用来捕获未被子类自身特定 match 匹配的消息(DTO)
                .matchAny(this::onUnhandledMessage)
                .build();

        // 3. 组合 Receive 逻辑：先尝试父类的处理，如果父类未处理，再尝试子类的回退处理
        // 这确保了父类的 onString/onInteger 会先被尝试
        return parentReceive.orElse(childFallbackReceive);

    }

    private void onNeedUpdateAction(NeedUpdateAction needUpdateAction) {
        gateway = (TcsGateway) needUpdateAction.getConfig();
        gatewayChangePipeline.update(needUpdateAction);
    }


    /**
     * 1.收到生命周期变化，如果物类型为网关，只处理更新操作
     * 2.如果是设备，增删改都需要处理
     * 3.reload整个网关配置
     * 4.网关配置转发给gatewayLCM，gatewayLCM更新配置，并且更新GatewayChangePipeline配置
     * 5.如果是设备的生命周期转发给gatewayLCM
     */
    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        switch (lifeCycleEvent.getThingType()){
            case GATEWAY :
                handleGatewayLifeCycleEvent(lifeCycleEvent);
                break;
            case DEVICE:
                handleDeviceLifeCycleEvent(lifeCycleEvent);
                break;
        }
    }

    private void handleDeviceLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        DeviceConfigChangeDto deviceConfigChangeDto = (DeviceConfigChangeDto) lifeCycleEvent.getChangeObject();
        handleGatewayConfigChangeDto(deviceConfigChangeDto);
    }

    private void handleGatewayLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        GatewayConfigChangeDto gatewayConfigChangeDto = (GatewayConfigChangeDto) lifeCycleEvent.getChangeObject();
        if(CollectionUtil.isNotEmpty(gatewayConfigChangeDto.getDevices())){
            List<DeviceConfigChangeDto> configChangeDtoList = gatewayConfigChangeDto.getDevices();
            configChangeDtoList.forEach(this::handleGatewayConfigChangeDto);
        }
    }

    private void handleGatewayConfigChangeDto(DeviceConfigChangeDto deviceConfigChangeDto){
        if(deviceConfigChangeDto.getLifeCycleEvent() == LifeCycleEventEnum.UPDATE){
            TcsDevice updateObj = gateway.getDevices().stream().filter(device -> Objects.equals(device.getId(), deviceConfigChangeDto.getId())).findFirst().get();
            NeedUpdateAction needUpdateAction = new NeedUpdateAction(updateObj);
            deviceLCMMap.get(deviceConfigChangeDto.getId()).tell(needUpdateAction,getSelf());
        }
        OperationEvent operationEvent = new OperationEvent();
        operationEvent.setThingType(ThingType.DEVICE);
        operationEvent.setOperationType(OperationTypeEnum.getByLifeCycleEventEnum(deviceConfigChangeDto.getLifeCycleEvent()));
        operationEvent.setThingId(deviceConfigChangeDto.getId());
        onOperationEvent(operationEvent);
    }

    //转发
    private void onUnhandledMessage(Object o) {
        // todo 还少控制
        if(o instanceof DeviceChange deviceChange){
            //设备在线离线
            sendMessageToDevice(deviceChange,deviceChange.getDeviceId());
        }if(o instanceof DeviceSignalChange deviceSignalChange){
            //设备实时信号
            sendMessageToDevice(deviceSignalChange,deviceSignalChange.getDeviceId());
        }if(o instanceof DeviceAlarmChange deviceAlarmChange){
            //设备实时告警
            sendMessageToDevice(deviceAlarmChange,deviceAlarmChange.getDeviceId());
        }if(o instanceof DeviceHistorySignalChange deviceHistorySignalChange){
            //设备历史数据
            sendMessageToDevice(deviceHistorySignalChange,deviceHistorySignalChange.getDeviceId());
        }if(o instanceof DeviceLoggingEventChange deviceLoggingEventChange){
            sendMessageToDevice(deviceLoggingEventChange,deviceLoggingEventChange.getDeviceId());
            //设备日志型事件
        }if (o instanceof DeviceControlCommandRequestChange deviceControlCommandRequestChange){
            sendMessageToDevice(deviceControlCommandRequestChange,deviceControlCommandRequestChange.getDeviceId());
        }if(o instanceof DeviceUniversalDataChange deviceUniversalDataChange){
            sendMessageToDevice(deviceUniversalDataChange,deviceUniversalDataChange.getDeviceId());
        }
        if(o instanceof GatewayChange gatewayChange){
            //网关在线离线状态
            gatewayChangePipeline.process(gatewayChange);
        }
    }

    /**
     * Generic method to forward foreign messages to the appropriate device LCM
     * @param message The message to forward
     * @param deviceId The ID of the target device
     */
    private void sendMessageToDevice(Object message, Long deviceId) {
        ActorRef deviceLCM = deviceLCMMap.get(deviceId);
        if (deviceLCM != null) {
            deviceLCM.tell(message, getSelf());
        }
    }

    //处理设备的操作
    private void onOperationEvent(OperationEvent operationEvent) {
        if(operationEvent.getThingType() == ThingType.DEVICE && operationEvent.getOperationType() == OperationTypeEnum.STOP){
            onDeviceOperationStop(operationEvent);
        }
        if(operationEvent.getThingType() == ThingType.DEVICE && operationEvent.getOperationType() == OperationTypeEnum.START){
            onDeviceOperationStart(operationEvent);
        }
        if(operationEvent.getThingType() == ThingType.DEVICE && operationEvent.getOperationType() == OperationTypeEnum.RESTART){
            onDeviceOperationReStart(operationEvent);
        }
        if(operationEvent.getThingType() == ThingType.GATEWAY && operationEvent.getOperationType() == OperationTypeEnum.RESTART){
            onGatewayOperationReStart(operationEvent);
        }
    }

    private void onGatewayOperationReStart(OperationEvent operationEvent) {
        throw new ThingRestartException(ThingType.GATEWAY, gateway.getId(), null);
    }

    private void onDeviceOperationReStart(OperationEvent operationEvent) {
        Long deviceId = operationEvent.getThingId();
        if(deviceLCMMap.containsKey(deviceId)){
            deviceLCMMap.get(deviceId).tell(operationEvent, getSelf());
        }
    }

    private void onDeviceOperationStart(OperationEvent operationEvent) {
        if(isDeviceReady(operationEvent.getThingId())) return;
        TcsDevice device = gateway.getDevices().stream().filter(e -> ObjectUtil.equals(e.getId(), operationEvent.getThingId())).findFirst().get();
        createDeviceLCM(device);
    }

    private void onDeviceOperationStop(OperationEvent operationEvent) {
        Long deviceId = operationEvent.getThingId();
        if(!isDeviceReady(deviceId)) return;
        deviceLCMMap.get(deviceId).tell(PoisonPill.getInstance(),getSelf());
        deviceLCMMap.remove(deviceId);
    }

    private boolean isDeviceReady(Long deviceId){
        return deviceLCMMap.containsKey(deviceId);
    }

    // 自定义监督策略
    @Override
    public SupervisorStrategy supervisorStrategy() {
        return new OneForOneStrategy(
                10, // 最大重试次数
                Duration.create(1, TimeUnit.MINUTES), // 时间窗口
                throwable -> {
                    if (throwable instanceof ThingRestartException thingRestartException) {
                        // 重启子Actor，并使用新的配置
                        TcsDevice device = gateway.getDevices().stream().filter(e -> ObjectUtil.equals(e.getId(), thingRestartException.getDeviceId())).findFirst().get();
                        createDeviceLCM(device);
                        return SupervisorStrategy.restart();
                    } else {
                        //处理不了向上传播
                        return SupervisorStrategy.escalate();
                    }
                });
    }
}
