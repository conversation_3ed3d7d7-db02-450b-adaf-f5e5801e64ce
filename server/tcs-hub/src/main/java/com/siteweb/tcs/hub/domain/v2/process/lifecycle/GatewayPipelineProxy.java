package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.util.SpringBeanUtil;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.v2.letter.OperationEvent;
import com.siteweb.tcs.hub.domain.letter.enums.OperationTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.domain.v2.letter.LifeCycleEvent;
import com.siteweb.tcs.hub.exception.ThingRestartException;
import com.siteweb.tcs.hub.service.ITcsGatewayService;
import org.apache.pekko.actor.*;
import scala.concurrent.duration.Duration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/11 09:17
 * @description:
 */
public class GatewayPipelineProxy  extends ProbeActor {

    private TcsGateway gateway;

    private ActorRef pipelinePublisher;

    private final ITcsGatewayService gatewayService;

    // 网关生命周期管理器，用于处理网关相关的生命周期事件
    private ActorRef gatewayLCM;

    public static Props props(Long gatewayId){
        return Props.create(GatewayPipelineProxy.class,gatewayId);
    }

    private GatewayPipelineProxy(Long gatewayId) {

        this.gatewayService = SpringBeanUtil.getBean(ITcsGatewayService.class);
        // 先加载网关信息，再创建pipelinePublisher
        loadGateway(gatewayId);
    }

    private void loadGateway(Long gatewayId) {
        this.gateway = gatewayService.getGatewayWithFullInfo(gatewayId);

        if (!isGatewayReady()) return;

        // 在网关信息加载完成后创建pipelinePublisher
        this.pipelinePublisher = getContext().actorOf(GatewayPipelinePublisher.props(gateway), "pipelinePublisher");

        // 创建网关生命周期管理器
        this.gatewayLCM = getContext().actorOf(GatewayLCM.props(gateway, pipelinePublisher),"gatewayLCM");
    }

    private Boolean isGatewayReady() {
        return gateway != null;
    }

    @Override
    public Receive createReceive() {
        // 1. 获取父类的 Receive 逻辑
        Receive parentReceive = super.createReceive();

        // 2. 构建子类自己的 Receive 逻辑，这里只包含 matchAny 回退
        Receive childFallbackReceive = receiveBuilder()
                .match(LifeCycleEvent.class,this::onLifeCycleEvent)
                .match(OperationEvent.class, this::onOperationEvent)
                // matchAny 必须放在子类 builder 的最后，用来捕获未被子类自身特定 match 匹配的消息(DTO)
                .matchAny(this::onUnhandledMessage)
                .build();

        // 3. 组合 Receive 逻辑：先尝试父类的处理，如果父类未处理，再尝试子类的回退处理
        // 这确保了父类的 onString/onInteger 会先被尝试
        return parentReceive.orElse(childFallbackReceive);

    }

    //收到生命周期，将其转换成操作事件，并且发布到南向
    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        //解析做更新操作
        /**
         * 1.收到生命周期变化，如果物类型为网关，只处理更新操作
         * 2.如果物类型是设备，转发给GatewayLCM
         */
        if(
                (lifeCycleEvent.getThingType() == ThingType.GATEWAY && lifeCycleEvent.getLifeCycleEventEnum() == LifeCycleEventEnum.UPDATE)
                || lifeCycleEvent.getThingType() == ThingType.DEVICE
        ) {
            gateway = gatewayService.getGatewayWithFullInfo(gateway.getId());
            NeedUpdateAction needUpdateAction = new NeedUpdateAction(gateway);
            gatewayLCM.tell(needUpdateAction,getSelf());
            gatewayLCM.tell(lifeCycleEvent,getSelf());
        }
        pipelinePublisher.tell(lifeCycleEvent,getSelf());
    }

    //无法处理的数据转发到gatewayLCM
    private void onUnhandledMessage(Object o) {
        gatewayLCM.forward(o,getContext());
    }

    private void onOperationEvent(OperationEvent operationEvent) {
        /**
         * 1.如果是设备的操作，直接转发给GatewayLCM
         * 2.如果时网关的操作那就是自己
         * 3.目前GatewayPipelineProxy不再管生命周期，每次发生loadGateway重新装载配置
         */
        if(operationEvent.getThingType() == ThingType.DEVICE){
            gatewayLCM.forward(operationEvent,getContext());
        }
        if(operationEvent.getThingType() == ThingType.GATEWAY && operationEvent.getOperationType() == OperationTypeEnum.STOP){
            handleGatewayStart(operationEvent);
        }
        if(operationEvent.getThingType() == ThingType.GATEWAY && operationEvent.getOperationType() == OperationTypeEnum.START){
            handleGatewayStop(operationEvent);
        }
        if(operationEvent.getThingType() == ThingType.GATEWAY && operationEvent.getOperationType() == OperationTypeEnum.RESTART){
            handleGatewayRestart(operationEvent);
        }
    }

    private void handleGatewayRestart(OperationEvent operationEvent) {
        if(!isGatewayReady()) return;
        gatewayLCM.tell(operationEvent,getSelf());
    }

    private void handleGatewayStop(OperationEvent operationEvent) {
        gatewayLCM.tell(PoisonPill.getInstance(),getSelf());
    }

    private void handleGatewayStart(OperationEvent operationEvent) {
        if(isGatewayReady()) return;
        loadGateway(operationEvent.getThingId());
    }

    // 自定义监督策略
    @Override
    public SupervisorStrategy supervisorStrategy() {
        return new OneForOneStrategy(
                10, // 最大重试次数
                Duration.create(1, TimeUnit.MINUTES), // 时间窗口
                throwable -> {
                    if (throwable instanceof ThingRestartException thingRestartException) {
                        // 重启子Actor，并使用新的配置
                        loadGateway(thingRestartException.getGatewayId());
                        return SupervisorStrategy.restart();
                    } else {
                        //处理不了向上传播
                        return SupervisorStrategy.escalate();
                    }
                });
    }
}
