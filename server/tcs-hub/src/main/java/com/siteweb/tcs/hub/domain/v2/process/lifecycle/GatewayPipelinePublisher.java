package com.siteweb.tcs.hub.domain.v2.process.lifecycle;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.domain.v2.letter.*;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;

/**
 * <AUTHOR>
 * @date 2025/7/11 09:37
 * @description:
 */

public class GatewayPipelinePublisher extends ProbeActor {
    // 分布式发布/订阅主题前缀
    private static final String TOPIC_PREFIX = "pipeline";

    // 分布式发布/订阅主题格式：pipeline.{gatewayId}.{type}
    private static final String TOPIC_FORMAT = TOPIC_PREFIX + ".%s.%s";

    private static final String GATEWAY_CREATE_TOPIC = TOPIC_PREFIX+"GatewayCreate";

    private final TcsGateway gateway;
    private final ActorRef mediator;

    public GatewayPipelinePublisher(TcsGateway gateway) {
        this.gateway = gateway;

        // 初始化分布式发布/订阅中介器
        mediator = DistributedPubSub.get(getContext().system()).mediator();
        System.out.println("GatewayPipelinePublisher: 创建成功，网关ID: " + gateway.getId());
        System.out.println("GatewayPipelinePublisher: mediator初始化成功: " + (mediator != null));
    }

    public static Props props(TcsGateway gateway) {
        return Props.create(GatewayPipelinePublisher.class, gateway);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DistributedPubSubMediator.SubscribeAck.class, this::onSubscribeAck)
                .match(DistributedPubSubMediator.UnsubscribeAck.class, this::onUnsubscribeAck)
                .match(LifeCycleEvent.class, this::onLifeCycleEvent)
                //其他类型的发布消息
                .match(DeviceChange.class,this::onDeviceChange)
                .match(AlarmChange.class,this::onAlarmChange)
                .match(DeviceHistorySignalChange.class,this::onDeviceHistorySignalChange)
                .match(DeviceLoggingEventChange.class,this::onDeviceLoggingEventChange)
                .match(DeviceSignalChange.class,this::onDeviceSignalChange)
                .match(DeviceUniversalDataChange.class,this::onDeviceUniversalDataChange)
                .match(GatewayChange.class,this::onGatewayChange)
                .build()
                .orElse(super.createReceive());
    }

    private void onDeviceUniversalDataChange(DeviceUniversalDataChange deviceUniversalDataChange) {
        publish(SubscriptionTypeEnum.UNIVERSAL_DATA, deviceUniversalDataChange);
    }

    private void onGatewayChange(GatewayChange gatewayChange) {
        publish(SubscriptionTypeEnum.GATEWAY_STATE_CHANGE, gatewayChange);
    }

    private void onDeviceSignalChange(DeviceSignalChange deviceSignalChange) {
        publish(SubscriptionTypeEnum.REAL_DATA, deviceSignalChange);
    }

    private void onDeviceLoggingEventChange(DeviceLoggingEventChange deviceLoggingEventChange) {
        publish(SubscriptionTypeEnum.LOGGING_EVENT, deviceLoggingEventChange);
    }

    private void onDeviceHistorySignalChange(DeviceHistorySignalChange deviceHistorySignalChange) {
        publish(SubscriptionTypeEnum.HISTORY_DATA,deviceHistorySignalChange);
    }

    private void onAlarmChange(AlarmChange alarmChange) {
        publish(SubscriptionTypeEnum.ALARM,alarmChange);
    }

    private void onDeviceChange(DeviceChange deviceChange) {
        publish(SubscriptionTypeEnum.DEVICE_STATE_CHANGE,deviceChange);
    }

    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        if(lifeCycleEvent.getLifeCycleEventEnum() == LifeCycleEventEnum.CREATE && lifeCycleEvent.getThingType() == ThingType.GATEWAY){
            System.out.println("GatewayPipelinePublisher: 发布网关创建事件到主题: " + GATEWAY_CREATE_TOPIC);
            System.out.println("GatewayPipelinePublisher: 事件内容: " + lifeCycleEvent);
            mediator.tell(
                    new DistributedPubSubMediator.Publish(GATEWAY_CREATE_TOPIC, lifeCycleEvent, true), getSelf());
        }
        publish(SubscriptionTypeEnum.LIFECYCLE_EVENT, lifeCycleEvent);
    }

    // 使用分布式发布/订阅中介器发布消息
    public void publish(SubscriptionTypeEnum subscriptionTypeEnum, Object message) {
        String topic = String.format(TOPIC_FORMAT, gateway.getId(), subscriptionTypeEnum.toString().toLowerCase());

        mediator.tell(
                new DistributedPubSubMediator.Publish(topic, message, true), getSelf());
    }

    public void onSubscribeAck(DistributedPubSubMediator.SubscribeAck ack) {
    }

    public void onUnsubscribeAck(DistributedPubSubMediator.UnsubscribeAck ack) {
    }
}