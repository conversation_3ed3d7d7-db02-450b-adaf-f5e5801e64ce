package com.siteweb.tcs.hub.exception;

import com.siteweb.tcs.common.exception.code.BusinessErrorCode;

public enum HubBusinessErrorCode implements BusinessErrorCode {
    THING_RESTART("THING_RESTART_EXCEPTION","物对象重启");

    private final String code;
    private final String message;

    HubBusinessErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
