package com.siteweb.tcs.hub.security;

import cn.hutool.core.text.CharSequenceUtil;
import io.jsonwebtoken.Claims;

public class ClaimsUtil {
    private ClaimsUtil(){}
    public static Integer getUserId(Claims claims) {
        return claims.get("userId",Integer.class);
    }

    public static String getLoginId(Claims claims) {
        String loginId = claims.get("loginId", String.class);
        if (CharSequenceUtil.isBlank(loginId)) {
            return claims.get("userName", String.class);
        }
        return loginId;
    }
    public static String getRoles(Claims claims){
        String roles = claims.get("role", String.class);
        if (CharSequenceUtil.isBlank(roles)) {
            return claims.get("roles", String.class);
        }
        return roles;
    }
    public static String getLoginType(Claims claims){
        String loginType = claims.get("loginType", String.class);
        if (CharSequenceUtil.isBlank(loginType)) {
            return "web";
        }
        return loginType;
    }

    public static String getTheme(Claims claims){
        String themeName = claims.get("themeName", String.class);
        if (CharSequenceUtil.isBlank(themeName)) {
            return "default";
        }
        return themeName;
    }

    public static String getUkey(Claims claims){
        String ukey = claims.get("ukey", String.class);
        if (CharSequenceUtil.isBlank(ukey)) {
            return "";
        }
        return ukey;
    }

    public static String getUserName(Claims claims){
        // 首先尝试从claims中获取userName字段
        String userName = claims.get("userName", String.class);
        if (CharSequenceUtil.isNotBlank(userName)) {
            return userName;
        }
        // 如果没有userName字段，从subject中获取（JWT创建时用户名存储在subject中）
        String subject = claims.getSubject();
        if (CharSequenceUtil.isNotBlank(subject)) {
            return subject;
        }
        // 都没有的话返回空字符串
        return "";
    }

    public static String getType(Claims claims){
        return claims.get("type", String.class);
    }
}
