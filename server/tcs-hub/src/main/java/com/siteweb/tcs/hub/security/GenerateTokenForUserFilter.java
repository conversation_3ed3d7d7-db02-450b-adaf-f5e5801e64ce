package com.siteweb.tcs.hub.security;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.tcs.hub.security.util.RequestParamUtil;
import com.siteweb.tcs.hub.security.util.ResponseUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
public class GenerateTokenForUserFilter extends AbstractAuthenticationProcessingFilter {

    private final TokenUtil tokenUtil;

    protected GenerateTokenForUserFilter(UserAuthenticationManager authenticationManager, TokenUtil tokenUtil) {
        super(new AntPathRequestMatcher("/api/thing/login", HttpMethod.POST.name()));
        setAuthenticationManager(authenticationManager);
        this.tokenUtil = tokenUtil;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException, ServletException {

        try {
            String ipAddr = IpUtil.getIpAddr(request);
            // 解密后的用户名密码
            String tmpStr = new String(Base64.getDecoder().decode(getRequestId(request)), StandardCharsets.UTF_8);

            UsernamePasswordAuthenticationToken authToken = null;
            int index;
            if (StringUtils.isNotEmpty(tmpStr) && (index = tmpStr.indexOf(":")) >= 1) {
                String username = tmpStr.substring(0, index);
                String password = tmpStr.substring(index + 1);
                authToken = new UsernamePasswordAuthenticationToken(URLDecoder.decode(username, StandardCharsets.UTF_8), password);
            }
            return getAuthenticationManager().authenticate(authToken);
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            throw new AuthenticationServiceException(e.getMessage());
        }
    }

    /**
     * 处理成功认证的请求
     * 生成新的token并返回用户信息
     *
     * @param req HTTP请求
     * @param res HTTP响应
     * @param chain 过滤器链
     * @param authToken 认证信息
     */
    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain, Authentication authToken) {
        try {
            String loginType = getTextParameter(req, "loginType");
            if ("".equals(loginType)) {
                loginType = "web";
            }
            SecurityContextHolder.getContext().setAuthentication(authToken);
            
            ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
            TokenUser tokenUser = (TokenUser) authToken.getPrincipal();
            String newToken = this.tokenUtil.createTokenForUser(tokenUser, loginType);
            
            // 设置响应数据
            jsonResp.put("token", newToken);
            jsonResp.put("UserName", tokenUser.getUser().getLoginId());
            jsonResp.put("LoginId", tokenUser.getUser().getLoginId());
            jsonResp.put("role", tokenUser.getRole());
            jsonResp.put("userId", tokenUser.getUserId());
            
            ResponseUtil.setArrayJsonResponse(res, HttpServletResponse.SC_OK, jsonResp);
        } catch (Exception e) {
            log.error("登录异常:{}", ExceptionUtil.stacktraceToString(e));
            throw new AuthenticationServiceException(e.getMessage());
        }
    }

    /**
     * 处理认证失败的请求
     * 根据不同的失败原因返回相应的错误信息
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @param failed 认证异常
     * @throws IOException IO异常
     */
    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response, AuthenticationException failed) throws IOException {
        ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
        String message = failed.getMessage();
        jsonResp.put("token", "");
        jsonResp.put("error", message);
        String respCode = "errorcode";
        
        // 根据不同的认证失败原因设置不同的错误码
        if (AuthenticationMessageEnum.ACCOUNT_EXPIRED.getMsg().equals(message) ||
                AuthenticationMessageEnum.PASSWORD_EXPIRED.getMsg().equals(message)) {
            jsonResp.put(respCode, 7); // 账号或密码过期
        } else if (AuthenticationMessageEnum.LOCKED.getMsg().equals(message)) {
            jsonResp.put(respCode, 11); // 账号锁定
        } else if (AuthenticationMessageEnum.DISABLED.getMsg().equals(message)) {
            jsonResp.put(respCode, 12); // 账号禁用
        } else if (AuthenticationMessageEnum.WRONG_PASSWORD.getMsg().equals(message) ||
                AuthenticationMessageEnum.USERNAME_NOT_FOUND.getMsg().equals(message) ||
                AuthenticationMessageEnum.USER_NOT_EXIST.getMsg().equals(message)) {
            jsonResp.put(respCode, 13); // 用户名或密码错误
            jsonResp.put("pwdErrCount", "");
            jsonResp.put("error", AuthenticationMessageEnum.USERNAME_OR_PWD_ERR.getMsg());
        } else if (CharSequenceUtil.contains(message, AuthenticationMessageEnum.FREEZE.getMsg())) {
            jsonResp.put(respCode, 15); // 账号冻结
        } else if (CharSequenceUtil.contains(message, AuthenticationMessageEnum.VIOLENT_HACK.getMsg())) {
            jsonResp.put(respCode, 14); // 暴力破解
        } else {
            jsonResp.put(respCode, 3); // 其他错误
        }
        
        ResponseUtil.setArrayJsonResponse(response, HttpServletResponse.SC_UNAUTHORIZED, jsonResp);
    }


    /**
     * 获取并验证请求ID参数
     *
     * @param request HTTP请求对象
     * @return 经过验证的请求ID，如果验证失败返回空字符串
     */
    private String getRequestId(HttpServletRequest request) {
        return RequestParamUtil.getBase64Parameter(request, "requestId");
    }

    /**
     * 获取并验证文本参数
     *
     * @param request HTTP请求对象
     * @param parameterName 参数名称
     * @return 经过验证的参数值，如果验证失败返回空字符串
     */
    private String getTextParameter(HttpServletRequest request, String parameterName) {
        return RequestParamUtil.getAlphanumericParameter(request, parameterName);
    }
}
