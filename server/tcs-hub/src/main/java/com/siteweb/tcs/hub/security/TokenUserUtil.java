package com.siteweb.tcs.hub.security;


import cn.hutool.core.util.ObjectUtil;

import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component
public class TokenUserUtil {
    
    private static boolean skipTokenValidation;
    
    @Value("${webAuth.development.skipTokenValidation:false}")
    public void setSkipTokenValidation(boolean skipTokenValidation) {
        TokenUserUtil.skipTokenValidation = skipTokenValidation;
    }


    /**
     * 获取Authentication
     */
    private static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 获取用户
     */
    public static TokenUser getUser() {
        Authentication authentication = getAuthentication();
        if (ObjectUtil.isNotNull(authentication) && authentication.isAuthenticated()) {
            Object details = authentication.getDetails();
            if (details instanceof TokenUser tokenUser) {
                return tokenUser;
            }
        }
        
        // 开发模式下，如果没有认证信息，返回默认用户
        if (skipTokenValidation) {
            return createDefaultDevelopmentUser();
        }
        
        throw StandardBusinessErrorCode.AUTHENTICATION_FAILED.toException("用户ID: ");
    }
    
    /**
     * 创建开发模式下的默认用户
     * 使用数据库初始化脚本中的超管用户信息
     */
    private static TokenUser createDefaultDevelopmentUser() {
        // 创建开发模式下的默认AccountDTO，使用超管用户信息
        com.siteweb.tcs.hub.dal.dto.AccountDTO defaultAccount = new com.siteweb.tcs.hub.dal.dto.AccountDTO();
        defaultAccount.setUserId(-1); // 超管用户ID
        defaultAccount.setUserName("系统管理员"); // 超管用户名
        defaultAccount.setLoginId("admin"); // 超管登录ID
        defaultAccount.setPassword(""); // 开发模式下密码为空
        defaultAccount.setEnable(true);
        defaultAccount.setLocked(false);
        defaultAccount.setRoleIds("-1"); // 超管角色ID
        defaultAccount.setAlias("系统管理员");
        defaultAccount.setLoginType("local");
        defaultAccount.setDepartmentId(1L);
        defaultAccount.setDepartmentName("系统默认部门");
        defaultAccount.setDescription("系统默认超级管理员");
        
        return new TokenUser(defaultAccount);
    }

    /*
     * 获取当前登录用户ID
     */
    public static Integer getLoginUserId() {
         return getUser().getUserId();
    }

    public static String getLoginUserName() {
        return getUser().getUsername();
    }

    /**
     * 获取用户角色信息
     *
     * @return 角色集合
     */
    public static List<Integer> getRoles() {
        List<Integer> roleIds = new ArrayList<>();
        TokenUser user = getUser();
        String role = user.getRole();
        String[] roleArray = role.split(",");
        for (String s : roleArray) {
            roleIds.add(Integer.valueOf(s));
        }
        return roleIds;
    }

    /**
     * 获取用户登录类型
     * @return
     */
    public static String getLoginType() {
        TokenUser user = getUser();
        return user.getUser().getLoginType();
    }
}
