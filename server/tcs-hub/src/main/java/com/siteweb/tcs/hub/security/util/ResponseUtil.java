package com.siteweb.tcs.hub.security.util;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.tcs.hub.security.JacksonUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;

import java.io.IOException;

/**
 * HTTP响应处理工具类
 * 提供统一的HTTP响应处理方法，减少代码重复
 */
public class ResponseUtil {

    /**
     * 设置标准JSON响应
     *
     * @param response HTTP响应对象
     * @param statusCode HTTP状态码
     * @param code 业务状态码
     * @param message 响应消息
     * @throws IOException IO异常
     */
    public static void setJsonResponse(HttpServletResponse response, int statusCode, int code, String message) throws IOException {
        ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
        jsonResp.put("code", code);
        jsonResp.put("msg", message);
        writeJsonResponse(response, statusCode, jsonResp);
    }

    /**
     * 设置数组类型的JSON响应
     *
     * @param response HTTP响应对象
     * @param statusCode HTTP状态码
     * @param jsonResp JSON响应内容
     * @throws IOException IO异常
     */
    public static void setArrayJsonResponse(HttpServletResponse response, int statusCode, ObjectNode jsonResp) throws IOException {
        ArrayNode jsonArray = JacksonUtil.getInstance().createArrayNode();
        jsonArray.add(jsonResp);
        writeJsonResponse(response, statusCode, jsonArray);
    }

    /**
     * 写入JSON响应
     *
     * @param response HTTP响应对象
     * @param statusCode HTTP状态码
     * @param jsonNode JSON响应内容
     * @throws IOException IO异常
     */
    private static void writeJsonResponse(HttpServletResponse response, int statusCode, Object jsonNode) throws IOException {
        response.setHeader(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        response.setStatus(statusCode);
        response.getWriter().write(jsonNode.toString());
        response.getWriter().flush();
        response.getWriter().close();
    }
}