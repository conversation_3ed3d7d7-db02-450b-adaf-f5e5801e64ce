package com.siteweb.tcs.hub.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.dto.ControlConfigChangeDto;
import com.siteweb.tcs.hub.dal.entity.TcsControl;

import java.util.List;

/**
 * 控制表服务接口
 */
public interface ITcsControlService extends IService<TcsControl> {

    /**
     * 根据设备ID查询控制列表
     *
     * @param deviceId 设备ID
     * @return 控制列表
     */
    List<TcsControl> listByDeviceId(Long deviceId);

    /**
     * 根据南向控制ID查询控制
     *
     * @param southControlId 南向控制ID
     * @return 控制信息
     */
    TcsControl getBySouthControlId(Long deviceId, String southControlId);

    /**
     * 根据关联信号ID查询控制列表
     *
     * @param relatedSignalId 关联信号ID
     * @return 控制列表
     */
    List<TcsControl> listByRelatedSignalId(Long relatedSignalId);

    /**
     * 保存或更新控制信息
     *
     * @param control 控制信息
     * @return 是否成功
     */
    boolean saveOrUpdateControl(TcsControl control);

    /**
     * 处理控制配置变更
     *
     * @param configDto 控制配置变更DTO
     * @return 配置变更结果
     */
    ConfigChangeResult handleControlConfigChange(ControlConfigChangeDto configDto);
} 