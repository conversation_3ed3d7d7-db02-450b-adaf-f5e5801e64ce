package com.siteweb.tcs.hub.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;

import java.util.List;

/**
 * 设备表服务接口
 */
public interface ITcsDeviceService extends IService<TcsDevice> {

    /**
     * 根据网关ID查询设备列表
     * @param gatewayId 网关ID
     * @return 设备列表
     */
    List<TcsDevice> listByGatewayId(Long gatewayId);

    /**
     * 根据南向设备ID查询设备
     * @param southDeviceId 南向设备ID
     * @return 设备信息
     */
    TcsDevice getBySouthDeviceId(String southDeviceId);

    /**
     * 查询设备及其信号、告警、控制信息
     * @param deviceId 设备ID
     * @return 设备信息（包含信号、告警、控制列表）
     */
    TcsDevice getDeviceWithDetails(Long deviceId);

    /**
     * 根据设备类型查询设备列表
     * @param deviceType 设备类型
     * @return 设备列表
     */
    List<TcsDevice> listByDeviceType(Integer deviceType);

    /**
     * 保存或更新设备信息
     * @param device 设备信息
     * @return 是否成功
     */
    boolean saveOrUpdateDevice(TcsDevice device);

    /**
     * 处理设备配置变更
     * @param configDto 设备配置变更DTO
     * @return 配置变更结果
     */
    ConfigChangeResult handleDeviceConfigChange(DeviceConfigChangeDto configDto);
}