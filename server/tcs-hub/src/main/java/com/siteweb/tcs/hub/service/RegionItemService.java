package com.siteweb.tcs.hub.service;

import com.siteweb.tcs.hub.dal.entity.RegionItem;

import java.util.List;


public interface RegionItemService {
    List<RegionItem> getRegionItemsByPluginId(String pluginId);

    List<RegionItem> getRegionItemsByRegionId(Integer regionId);

    boolean createRegionItem(RegionItem regionItem);

    boolean updateRegionItem(RegionItem regionItem);

    boolean deleteRegionItem(Integer regionItemId);

    boolean deleteRegionItems(Integer regionId);

    RegionItem getRegionItemById(Integer regionId);

    List<RegionItem> getAllRegionItems();

    RegionItem getByRegionIdAndItemId(Integer regionId, String itemId, String pluginId);
}
