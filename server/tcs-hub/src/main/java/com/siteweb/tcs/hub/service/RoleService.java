package com.siteweb.tcs.hub.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.tcs.hub.dal.dto.RoleQueryDTO;
import com.siteweb.tcs.hub.dal.entity.Role;

import java.util.List;

public interface RoleService {
    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 创建的角色信息
     */
    Role createRole(Role roleDTO);

    /**
     * 更新角色
     *
     * @param roleDTO 角色信息
     * @return 更新后的角色信息
     */
    Role updateRole(Role roleDTO);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    boolean removeRole(Integer roleId);

    /**
     * 获取角色信息
     *
     * @param roleId 角色ID
     * @return 角色信息
     */
    Role getById(Integer roleId);

    /**
     * 获取所有角色
     *
     * @return 角色列表
     */
    List<Role> getAllRoles();

    /**
     * 获取用户的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getRolesByUserId(Integer userId);

    /**
     * 分页查询角色列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<Role> getRolesPage(RoleQueryDTO query);

    /**
     * 更新角色状态
     *
     * @param roleId 角色ID
     * @param status 状态：1=启用，0=停用
     * @return 是否更新成功
     */
    boolean updateRoleStatus(Integer roleId, Integer status);

    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID列表
     * @return 删除的数量
     */
    int batchDeleteRoles(List<Integer> roleIds);

    /**
     * 检查角色名称是否已存在
     *
     * @param roleName  角色名称
     * @param excludeId 排除的角色ID（用于编辑时排除自己）
     * @return 是否存在
     */
    boolean checkRoleNameExists(String roleName, Integer excludeId);

    /**
     * 检查角色代码是否已存在
     *
     * @param roleCode  角色代码
     * @param excludeId 排除的角色ID（用于编辑时排除自己）
     * @return 是否存在
     */
    boolean checkRoleCodeExists(String roleCode, Integer excludeId);
}
