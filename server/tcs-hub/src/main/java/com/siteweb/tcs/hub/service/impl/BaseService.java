package com.siteweb.tcs.hub.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Collections;
import java.util.List;

/**
 * 基础Service类，提供通用的删除方法
 * @param <M> Mapper类型
 * @param <T> 实体类型
 */
public abstract class BaseService<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements IService<T> {

    /**
     * 根据条件删除记录
     * @param field 字段名
     * @param value 字段值
     * @return 是否删除成功
     */
    protected boolean deleteByField(String field, Object value) {
        if (ObjectUtil.isEmpty(value)) {
            return false;
        }
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(field, value);
        return this.remove(queryWrapper);
    }

    /**
     * 根据ID列表批量删除记录
     * @param field 字段名
     * @param idList ID列表
     * @return 是否删除成功
     */
    protected boolean deleteByIdList(String field, List<?> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return false;
        }
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, idList);
        return this.remove(queryWrapper);
    }

    /**
     * 根据多个条件删除记录
     * @param conditions 条件Map，key为字段名，value为字段值
     * @return 是否删除成功
     */
    protected boolean deleteByConditions(java.util.Map<String, Object> conditions) {
        if (ObjectUtil.isEmpty(conditions)) {
            return false;
        }
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        conditions.forEach(queryWrapper::eq);
        return this.remove(queryWrapper);
    }

    /**
     * 根据条件查询记录列表
     * @param field 字段名
     * @param value 字段值
     * @return 记录列表
     */
    protected List<T> selectByField(String field, Object value) {
        if (ObjectUtil.isEmpty(value)) {
            return Collections.emptyList();
        }
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(field, value);
        return this.list(queryWrapper);
    }
}