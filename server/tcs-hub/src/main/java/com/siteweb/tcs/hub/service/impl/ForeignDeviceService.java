package com.siteweb.tcs.hub.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.dal.mapper.ForeignDeviceMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外部设备服务实现类
 * 提供外部设备的增删改查等基础操作
 */
@Service
public class ForeignDeviceService extends BaseService<ForeignDeviceMapper, ForeignDevice> implements IService<ForeignDevice> {

    private static final Logger logger = LoggerFactory.getLogger(ForeignDeviceService.class);

    @Autowired
    private ForeignDeviceMapper foreignDeviceMapper;

    @Autowired
    private ForeignSignalService foreignSignalService;

    @Autowired
    private ForeignAlarmService foreignAlarmService;

    @Autowired
    private ForeignControlService foreignControlService;

    /**
     * 根据监控单元ID查询外部设备列表
     * @param monitorUnitId 监控单元ID
     * @return 外部设备列表
     * @throws IllegalArgumentException 当monitorUnitId为空时抛出
     */
    public List<ForeignDevice> selectForeignDeviceByMonitorUnitId(Integer monitorUnitId) {
        if (monitorUnitId == null) {
            logger.error("监控单元ID不能为空");
            throw new IllegalArgumentException("监控单元ID不能为空");
        }
        return selectByField("MonitorUnitId", monitorUnitId);
    }

    /**
     * 判断设备是否存在
     * @param foreignDeviceId 外部设备ID
     * @param monitorUnitId 监控单元ID
     * @return 设备是否存在
     * @throws IllegalArgumentException 当参数为空时抛出
     */
    public boolean judgeDeviceExist(String foreignDeviceId, Integer monitorUnitId) {
        if (ObjectUtil.isEmpty(foreignDeviceId) || ObjectUtil.isEmpty(monitorUnitId)) {
            logger.error("外部设备ID或监控单元ID不能为空");
            throw new IllegalArgumentException("外部设备ID或监控单元ID不能为空");
        }
        QueryWrapper<ForeignDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ForeignDeviceId", foreignDeviceId);
        queryWrapper.eq("MonitorUnitId", monitorUnitId);
        return foreignDeviceMapper.exists(queryWrapper);
    }

    /**
     * 删除设备
     * @param device 外部设备对象
     * @return 是否删除成功
     * @throws IllegalArgumentException 当device为空或必要字段为空时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteForeignDevice(ForeignDevice device) {
        if (ObjectUtil.isEmpty(device) || ObjectUtil.isEmpty(device.getMonitorUnitId()) 
            || ObjectUtil.isEmpty(device.getForeignDeviceID())) {
            logger.error("删除设备参数不完整: {}", device);
            throw new IllegalArgumentException("删除设备参数不完整");
        }
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("MonitorUnitId", device.getMonitorUnitId());
        conditions.put("ForeignDeviceId", device.getForeignDeviceID());
        boolean result = deleteByConditions(conditions);
        if (!result) {
            logger.warn("删除设备失败: {}", device);
        }
        return result;
    }

    /**
     * 根据ID获取外部设备
     * @param foreignGatewayId 外部网关ID
     * @param foreignDeviceId 外部设备ID
     * @return 外部设备对象
     * @throws IllegalArgumentException 当任一参数为空时抛出
     */
    public ForeignDevice getForeignDeviceById( String foreignGatewayId, String foreignDeviceId) {
//        if (ObjectUtil.isEmpty(pluginId) || ObjectUtil.isEmpty(foreignGatewayId)
//            || ObjectUtil.isEmpty(foreignDeviceId)) {
//            logger.error("获取设备参数不完整: pluginId={}, foreignGatewayId={}, foreignDeviceId={}",
//                pluginId, foreignGatewayId, foreignDeviceId);
//            throw new IllegalArgumentException("获取设备参数不完整");
//        }
        QueryWrapper<ForeignDevice> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("PluginId", pluginId);
        queryWrapper.eq("ForeignGatewayId", foreignGatewayId);
        queryWrapper.eq("ForeignDeviceId", foreignDeviceId);
        return foreignDeviceMapper.selectOne(queryWrapper);
    }

    /**
     * 根据监控单元ID删除外部设备及其关联数据
     * @param monitorUnitId 监控单元ID
     * @return 是否删除成功
     * @throws IllegalArgumentException 当monitorUnitId为空时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteForeignDeviceByMonitorUnitId(Integer monitorUnitId) {
        if (ObjectUtil.isEmpty(monitorUnitId)) {
            logger.error("删除设备的监控单元ID不能为空");
            throw new IllegalArgumentException("监控单元ID不能为空");
        }
        List<ForeignDevice> foreignDeviceList = selectByField("MonitorUnitId", monitorUnitId);
        if (CollectionUtil.isNotEmpty(foreignDeviceList)) {
            List<Integer> equipmentIdList = foreignDeviceList.stream().map(ForeignDevice::getEquipmentId).toList();
            try {
                foreignSignalService.deleteByEquipmentIdList(equipmentIdList);
                foreignAlarmService.deleteByEquipmentIdList(equipmentIdList);
                foreignControlService.deleteByEquipmentIdList(equipmentIdList);
            } catch (Exception e) {
                logger.error("删除设备关联数据失败: monitorUnitId={}, error={}", monitorUnitId, e.getMessage());
                throw e;
            }
        }
        boolean result = deleteByField("MonitorUnitId", monitorUnitId);
        if (!result) {
            logger.warn("删除监控单元下的设备失败: monitorUnitId={}", monitorUnitId);
        }
        return result;
    }
}
