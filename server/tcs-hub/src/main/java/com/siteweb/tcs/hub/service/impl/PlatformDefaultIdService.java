package com.siteweb.tcs.hub.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.siteweb.dto.ResourceStructureDTO;
import com.siteweb.tcs.siteweb.dto.StationDTO;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.enums.StructureTypeEnum;
import com.siteweb.tcs.siteweb.provider.EquipmentTemplateProvider;
import com.siteweb.tcs.siteweb.provider.ResourceStructureProvider;
import com.siteweb.tcs.siteweb.provider.SamplerProvider;
import com.siteweb.tcs.siteweb.provider.SamplerUnitProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 平台侧默认id查询业务类
 */
@Service
@Slf4j
public class PlatformDefaultIdService {
    @Autowired
    private EquipmentTemplateProvider equipmentTemplateProvider;
    @Autowired
    private ResourceStructureProvider resourceStructureProvider;
    @Autowired
    private SamplerUnitProvider samplerUnitProvider;
    @Autowired
    private SamplerProvider samplerProvider;

    private Integer resourceStructureRootId;

    private Integer tcsDefaultResourceStructureId;

    private static Integer bInterfaceDeviceTemplateRootId;

    private static Integer bInterfaceSamplerId;

    private static final String bInterfaceSamplerProtocolCode = "BInterface-HOST设备6-00";

    private static final String defaultTCSResourceStructureName = "TCS默认层级";

    private static final String tcsResourceStructureFlag = "TCS";


//    @PostConstruct
    private void initId(){
        bInterfaceDeviceTemplateRootId = equipmentTemplateProvider.getBInterfaceDeviceTemplateRootId();
        try {
            resourceStructureRootId = resourceStructureProvider.getTreeRootId();
        }catch (Exception ex){
            log.error(ex.getMessage());
        }
        try {
            bInterfaceSamplerId = samplerProvider.getSamplerByProtocolCode(bInterfaceSamplerProtocolCode).getSamplerId();
        }catch (Exception ex){
           log.error(ex.getMessage());
        }
    }

    public Integer getResourceStructureRootId(){
        if(ObjectUtil.isEmpty(resourceStructureRootId)){
            try{
                resourceStructureRootId = resourceStructureProvider.getTreeRootId();
            }catch(Exception ex){
               log.error(ex.getMessage());
            }
        }
        return resourceStructureRootId;
    }

    public Integer getTCSDefaultResourceStructureId(){
        if(ObjectUtil.isEmpty(tcsDefaultResourceStructureId)){
            try{
                tcsDefaultResourceStructureId = resourceStructureProvider.getTCSDefaultResourceStructureId();
                if(ObjectUtil.isEmpty(tcsDefaultResourceStructureId)){
                    //创建
                    ResourceStructureDTO resourceStructureDTO = new ResourceStructureDTO();
                    resourceStructureDTO.setParentResourceStructureId(getResourceStructureRootId());
                    resourceStructureDTO.setPhoto(tcsResourceStructureFlag);
                    resourceStructureDTO.setResourceStructureName(defaultTCSResourceStructureName);
                    resourceStructureDTO.setCreateStation(true);
                    resourceStructureDTO.setStructureTypeId(StructureTypeEnum.STATION.getValue());
                    StationDTO stationDTO = new StationDTO();
                    stationDTO.setStationName(defaultTCSResourceStructureName);
                    stationDTO.setEnable(true);
                    resourceStructureDTO.setStation(stationDTO);
                    ResourceStructure resourceStructure = resourceStructureProvider.createResourceStructure(resourceStructureDTO);
                    if(ObjectUtil.isNotEmpty(resourceStructure)) {
                        tcsDefaultResourceStructureId = resourceStructure.getResourceStructureId();
                        return resourceStructure.getResourceStructureId();
                    }
                }
            }catch (Exception ex){
                log.error(ex.getMessage());
            }
        }
        return tcsDefaultResourceStructureId;
    }

    public Integer getBInterfaceDeviceTemplateRootId(){
        if(ObjectUtil.isEmpty(bInterfaceDeviceTemplateRootId)){
            bInterfaceDeviceTemplateRootId = equipmentTemplateProvider.getBInterfaceDeviceTemplateRootId();
        }
        return bInterfaceDeviceTemplateRootId;
    }

    public Integer getBInterfaceSampleUnitRootId(Integer monitorUnitId){
        if(ObjectUtil.isEmpty(monitorUnitId)){
            return null;
        }
        List<SamplerUnit> samplerUnitList = samplerUnitProvider.selectSamplerUnitWithPort(monitorUnitId);
        if(CollectionUtil.isEmpty(samplerUnitList)){
            return null;
        }
        Integer bInterfaceSampleUnitRootId = samplerUnitList.stream().map(SamplerUnit::getSamplerUnitId).findFirst().get();
        return bInterfaceSampleUnitRootId;
    }

    public Integer getDefaultSamplerIdByProtocolCode(){
        if(ObjectUtil.isEmpty(bInterfaceSamplerId)){
            Sampler tslSampler = samplerProvider.getSamplerByProtocolCode(bInterfaceSamplerProtocolCode);
            bInterfaceSamplerId = tslSampler.getSamplerId();
            return bInterfaceSamplerId;
        }else {
            return bInterfaceSamplerId;
        }
    }
}
