package com.siteweb.tcs.hub.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.provider.SamplerUnitProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: SamplerUnitService
 * @descriptions: 采集单元业务处理类
 * @author: xsx
 * @date: 2024/8/23 10:44
 **/
@Service
public class SamplerUnitService {
    @Autowired
    private SamplerUnitProvider samplerUnitProvider;
    @Autowired
    private PlatformDefaultIdService platformDefaultIdService;

    public SamplerUnit createDefaultSamplerUnit(Integer monitorUnitId,String equipmentName,Integer portId){
        if(ObjectUtil.isEmpty(monitorUnitId) || ObjectUtil.isEmpty(portId)) return null;
        SamplerUnit samplerUnit = new SamplerUnit();
        samplerUnit.setMonitorUnitId(monitorUnitId);
        samplerUnit.setPortId(portId);
        Integer samplerId = platformDefaultIdService.getDefaultSamplerIdByProtocolCode();
        samplerUnit.setSamplerId(samplerId);
        samplerUnit.setParentSamplerUnitId(0);
        samplerUnit.setSamplerUnitName(equipmentName);
        samplerUnit.setAddress(1);
        samplerUnit.setSamplerType((short) 18);
        samplerUnit.setSpUnitInterval(2.0);
        samplerUnit.setDllPath("IDUHOST.so");
        samplerUnit.setConnectState(0);
        samplerUnit.setDescription("TCS create");
        SamplerUnit result = samplerUnitProvider.createConfig(samplerUnit);
        return result;
    }
}
