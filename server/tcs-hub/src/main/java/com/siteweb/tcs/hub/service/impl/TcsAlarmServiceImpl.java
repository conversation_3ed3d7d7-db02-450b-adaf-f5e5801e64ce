package com.siteweb.tcs.hub.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.common.util.LocaleMessageSourceUtil;
import com.siteweb.tcs.hub.dal.dto.AlarmConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.entity.TcsAlarm;
import com.siteweb.tcs.hub.dal.entity.TcsSignal;
import com.siteweb.tcs.hub.dal.mapper.TcsAlarmMapper;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.service.ITcsAlarmService;
import com.siteweb.tcs.hub.service.ITcsSignalService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 告警表服务实现类
 */
@Service
public class TcsAlarmServiceImpl extends ServiceImpl<TcsAlarmMapper, TcsAlarm> implements ITcsAlarmService {

    @Resource
    private TcsAlarmMapper tcsAlarmMapper;

    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    private ITcsSignalService signalService;

    @Override
    public List<TcsAlarm> listByDeviceId(Long deviceId) {
        return tcsAlarmMapper.selectByDeviceId(deviceId);
    }

    @Override
    public TcsAlarm getBySouthAlarmId(Long deviceId, String southAlarmId) {
        return tcsAlarmMapper.selectBySouthAlarmId(deviceId, southAlarmId);
    }

    @Override
    public List<TcsAlarm> listByRelatedSignalId(Long relatedSignalId) {
        return tcsAlarmMapper.selectByRelatedSignalId(relatedSignalId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateAlarm(TcsAlarm alarm) {
        return this.saveOrUpdate(alarm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConfigChangeResult handleAlarmConfigChange(AlarmConfigChangeDto configDto) {
        try {
            // 参数验证
            if (configDto == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.data.empty"),
                        ThingType.ALARM);
            }

            if (configDto.getLifeCycleEvent() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.lifecycle.empty"),
                        ThingType.ALARM);
            }

            // 根据生命周期事件类型处理
            switch (configDto.getLifeCycleEvent()) {
                case CREATE:
                    return handleAlarmCreate(configDto);
                case UPDATE:
                    return handleAlarmUpdate(configDto);
                case DELETE:
                    return handleAlarmDelete(configDto);
                default:
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("config.lifecycle.unsupported", new Object[]{configDto.getLifeCycleEvent()}),
                            ThingType.ALARM);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.change.failed"),
                    e.getMessage(), ThingType.ALARM);
        }
    }

    /**
     * 处理告警创建
     */
    private ConfigChangeResult handleAlarmCreate(AlarmConfigChangeDto configDto) {
        try {
            // 检查南向告警ID是否已存在
            if (configDto.getSouthAlarmId() != null) {
                TcsAlarm existingAlarm = getBySouthAlarmId(configDto.getDeviceId(),configDto.getSouthAlarmId());
                if (existingAlarm != null) {
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("alarm.south.id.exists", new Object[]{configDto.getSouthAlarmId()}),
                            ThingType.ALARM);
                }
            }

            // 处理相关信号ID
            processRelatedSignalId(configDto);

            // 创建告警实体
            TcsAlarm alarm = new TcsAlarm();
            BeanUtils.copyProperties(configDto, alarm);
            alarm.setDeleted(false);

            // 保存告警
            boolean success = this.save(alarm);
            if (success) {
                // 创建返回的ConfigChangeDto，包含southId和id
                AlarmConfigChangeDto resultDto = new AlarmConfigChangeDto();
                BeanUtils.copyProperties(alarm, resultDto);
                resultDto.setLifeCycleEvent(configDto.getLifeCycleEvent());

                return ConfigChangeResult.success(alarm.getSouthAlarmId(), ThingType.ALARM, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.create.failed"),
                        ThingType.ALARM);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.create.failed"),
                    e.getMessage(), ThingType.ALARM);
        }
    }

    /**
     * 处理告警更新
     */
    private ConfigChangeResult handleAlarmUpdate(AlarmConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.update.id.required"),
                        ThingType.ALARM);
            }

            // 检查告警是否存在
            TcsAlarm existingAlarm = this.getById(configDto.getId());
            if (existingAlarm == null) {
                return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("alarm.not.exists", new Object[]{configDto.getId()}),
                        ThingType.ALARM);
            }

            // 检查南向告警ID是否与其他告警冲突
            if (configDto.getSouthAlarmId() != null &&
                    !configDto.getSouthAlarmId().equals(existingAlarm.getSouthAlarmId())) {
                TcsAlarm conflictAlarm = getBySouthAlarmId(configDto.getDeviceId(),configDto.getSouthAlarmId());
                if (conflictAlarm != null && !conflictAlarm.getId().equals(configDto.getId())) {
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("alarm.south.id.conflict", new Object[]{configDto.getSouthAlarmId()}),
                            ThingType.ALARM);
                }
            }

            // 处理相关信号ID
            processRelatedSignalId(configDto);

            // 更新告警实体
            BeanUtils.copyProperties(configDto, existingAlarm, "id");

            // 保存更新
            boolean success = this.updateById(existingAlarm);
            if (success) {
                // 创建返回的ConfigChangeDto，包含更新后的数据
                AlarmConfigChangeDto resultDto = new AlarmConfigChangeDto();
                BeanUtils.copyProperties(existingAlarm, resultDto);
                resultDto.setLifeCycleEvent(LifeCycleEventEnum.UPDATE);

                return ConfigChangeResult.success(existingAlarm.getSouthAlarmId(), ThingType.ALARM, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.update.failed"),
                        ThingType.ALARM);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.update.failed"),
                    e.getMessage(), ThingType.ALARM);
        }
    }

    /**
     * 处理告警删除
     */
    private ConfigChangeResult handleAlarmDelete(AlarmConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.delete.id.required"),
                        ThingType.ALARM);
            }

            // 检查告警是否存在
            TcsAlarm existingAlarm = this.getById(configDto.getId());
            if (existingAlarm == null) {
                return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("alarm.not.exists", new Object[]{configDto.getId()}),
                        ThingType.ALARM);
            }

            // 逻辑删除告警
            boolean success = this.removeById(configDto.getId());
            if (success) {
                // 创建返回的ConfigChangeDto，包含要删除的告警ID
                AlarmConfigChangeDto resultDto = new AlarmConfigChangeDto();
                BeanUtils.copyProperties(configDto,resultDto);
                resultDto.setId(existingAlarm.getId());
                resultDto.setSouthAlarmId(existingAlarm.getSouthAlarmId());
                resultDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);

                return ConfigChangeResult.success(existingAlarm.getSouthAlarmId(), ThingType.ALARM, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.delete.failed"),
                        ThingType.ALARM);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("alarm.delete.failed"),
                    e.getMessage(), ThingType.ALARM);
        }
    }

    /**
     * 处理相关信号ID
     * 根据deviceId和southRelatedSignalId查询signal的id，设置到relatedSignalId
     */
    private void processRelatedSignalId(AlarmConfigChangeDto configDto) {
        if (configDto.getSouthRelatedSignalId() != null && configDto.getDeviceId() != null) {
            // 根据deviceId和southRelatedSignalId查询信号
            List<TcsSignal> signals = signalService.listByDeviceId(configDto.getDeviceId());
            for (TcsSignal signal : signals) {
                if (configDto.getSouthRelatedSignalId().equals(signal.getSouthSignalId())) {
                    configDto.setRelatedSignalId(signal.getId());
                    break;
                }
            }
        }
    }
} 