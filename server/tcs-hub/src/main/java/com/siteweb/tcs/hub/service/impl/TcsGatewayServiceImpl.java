package com.siteweb.tcs.hub.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.dal.mapper.TcsGatewayMapper;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.service.ITcsDeviceService;
import com.siteweb.tcs.hub.service.ITcsGatewayService;
import com.siteweb.tcs.common.util.LocaleMessageSourceUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 网关表服务实现类
 */
@Service
public class TcsGatewayServiceImpl extends ServiceImpl<TcsGatewayMapper, TcsGateway> implements ITcsGatewayService {

    @Resource
    private TcsGatewayMapper tcsGatewayMapper;

    @Autowired
    private ITcsDeviceService deviceService;

    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public List<TcsGateway> listByPluginId(String pluginId) {
        return tcsGatewayMapper.selectByPluginId(pluginId);
    }

    @Override
    public TcsGateway getBySouthGatewayId(String southGatewayId) {
        return tcsGatewayMapper.selectBySouthGatewayId(southGatewayId);
    }

    @Override
    public TcsGateway getGatewayWithDevices(Long gatewayId) {
        return tcsGatewayMapper.selectGatewayWithDevices(gatewayId);
    }

    @Override
    public TcsGateway getGatewayWithFullInfo(Long gatewayId) {
        return tcsGatewayMapper.selectGatewayWithFullInfo(gatewayId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateGateway(TcsGateway gateway) {
        return this.saveOrUpdate(gateway);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConfigChangeResult handleGatewayConfigChange(GatewayConfigChangeDto configDto) {
        try {
            // 参数验证
            if (configDto == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.data.empty"), 
                    ThingType.GATEWAY);
            }

            if (configDto.getLifeCycleEvent() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.lifecycle.empty"), 
                    ThingType.GATEWAY);
            }

            // 根据生命周期事件类型处理
            ConfigChangeResult result;
            switch (configDto.getLifeCycleEvent()) {
                case CREATE:
                    result = handleGatewayCreate(configDto);
                    break;
                case UPDATE:
                    result = handleGatewayUpdate(configDto);
                    break;
                case DELETE:
                    result = handleGatewayDelete(configDto);
                    break;
                case NOT_CHANGE:
                    result = handleGatewayNotChange(configDto);
                    break;
                default:
                    return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("config.lifecycle.unsupported", new Object[]{configDto.getLifeCycleEvent()}), 
                        ThingType.GATEWAY);
            }

            // 处理设备配置变更（如果有且不是CREATE操作）
            // CREATE操作的设备已经在handleGatewayCreate中处理过了
            if (result.isSuccess() && !CollectionUtils.isEmpty(configDto.getDevices()) && 
                configDto.getLifeCycleEvent() != LifeCycleEventEnum.CREATE) {
                // 收集设备配置变更的结果
                List<DeviceConfigChangeDto> changedDevices = new ArrayList<>();
                
                for (DeviceConfigChangeDto deviceDto : configDto.getDevices()) {
                    deviceDto.setGatewayId(configDto.getId());
                    ConfigChangeResult deviceResult = deviceService.handleDeviceConfigChange(deviceDto);
                    if (!deviceResult.isSuccess()) {
                        throw new RuntimeException(messageSourceUtil.getMessage("device.signal.change.failed", 
                            new Object[]{deviceResult.getErrorMessage()}));
                    }
                    if (deviceResult.getConfigData() != null) {
                        changedDevices.add((DeviceConfigChangeDto) deviceResult.getConfigData());
                    }
                }
                
                // 将设备配置变更结果设置到返回的GatewayConfigChangeDto中
                if (result.getConfigData() != null) {
                    GatewayConfigChangeDto gatewayResultDto = (GatewayConfigChangeDto) result.getConfigData();
                    gatewayResultDto.setDevices(changedDevices.isEmpty() ? null : changedDevices);
                }
            }

            return result;
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.change.failed"), 
                e.getMessage(), ThingType.GATEWAY);
        }
    }

    private ConfigChangeResult handleGatewayNotChange(GatewayConfigChangeDto configDto) {
        GatewayConfigChangeDto result = new GatewayConfigChangeDto();
        result.setId(configDto.getId());
        result.setLifeCycleEvent(LifeCycleEventEnum.NOT_CHANGE);
        ConfigChangeResult configChangeResult = new ConfigChangeResult();
        configChangeResult.setSuccess(true);
        configChangeResult.setConfigData(result);
        configChangeResult.setEntityType(ThingType.GATEWAY);
        configChangeResult.setEntityId(configDto.getSouthGatewayId());
        return configChangeResult;
    }

    /**
     * 处理网关创建
     */
    private ConfigChangeResult handleGatewayCreate(GatewayConfigChangeDto configDto) {
        try {
            // 检查南向网关ID是否已存在
            if (configDto.getSouthGatewayId() != null) {
                TcsGateway existingGateway = getBySouthGatewayId(configDto.getSouthGatewayId());
                if (existingGateway != null) {
                    return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("gateway.south.id.exists", new Object[]{configDto.getSouthGatewayId()}), 
                        ThingType.GATEWAY);
                }
            }

            // 创建网关实体
            TcsGateway gateway = new TcsGateway();
            BeanUtils.copyProperties(configDto, gateway);
            gateway.setDeleted(false);

            // 保存网关
            boolean success = this.save(gateway);
            if (success) {
                // 创建返回的ConfigChangeDto，包含southId和id
                GatewayConfigChangeDto resultDto = new GatewayConfigChangeDto();
                BeanUtils.copyProperties(gateway, resultDto);
                resultDto.setLifeCycleEvent(configDto.getLifeCycleEvent());
                
                // 如果有设备配置变更，需要处理并收集CREATE的设备数据
                if (!CollectionUtils.isEmpty(configDto.getDevices())) {
                    List<DeviceConfigChangeDto> createdDevices = new ArrayList<>();
                    for (DeviceConfigChangeDto deviceDto : configDto.getDevices()) {
                        if (deviceDto.getLifeCycleEvent() == LifeCycleEventEnum.CREATE) {
                            // 设置设备的网关ID为新创建的网关ID
                            deviceDto.setGatewayId(gateway.getId());
                            ConfigChangeResult deviceResult = deviceService.handleDeviceConfigChange(deviceDto);
                            if (deviceResult.isSuccess() && deviceResult.getConfigData() != null) {
                                createdDevices.add((DeviceConfigChangeDto) deviceResult.getConfigData());
                            }
                        }
                    }
                    resultDto.setDevices(createdDevices.isEmpty() ? null : createdDevices);
                }
                
                return ConfigChangeResult.success(gateway.getSouthGatewayId(), ThingType.GATEWAY, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.create.failed"), 
                    ThingType.GATEWAY);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.create.failed"), 
                e.getMessage(), ThingType.GATEWAY);
        }
    }

    /**
     * 处理网关更新
     */
    private ConfigChangeResult handleGatewayUpdate(GatewayConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.update.id.required"), 
                    ThingType.GATEWAY);
            }

            // 检查网关是否存在
            TcsGateway existingGateway = this.getById(configDto.getId());
            if (existingGateway == null) {
                return ConfigChangeResult.failure(
                    messageSourceUtil.getMessage("gateway.not.exists", new Object[]{configDto.getId()}), 
                    ThingType.GATEWAY);
            }

            // 检查南向网关ID是否与其他网关冲突
            if (configDto.getSouthGatewayId() != null && 
                !configDto.getSouthGatewayId().equals(existingGateway.getSouthGatewayId())) {
                TcsGateway conflictGateway = getBySouthGatewayId(configDto.getSouthGatewayId());
                if (conflictGateway != null && !conflictGateway.getId().equals(configDto.getId())) {
                    return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("gateway.south.id.conflict", new Object[]{configDto.getSouthGatewayId()}), 
                        ThingType.GATEWAY);
                }
            }

            // 更新网关实体
            BeanUtils.copyProperties(configDto, existingGateway, "id");

            // 保存更新
            boolean success = this.updateById(existingGateway);
            if (success) {
                return ConfigChangeResult.success(existingGateway.getSouthGatewayId(), ThingType.GATEWAY);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.update.failed"), 
                    ThingType.GATEWAY);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.update.failed"), 
                e.getMessage(), ThingType.GATEWAY);
        }
    }

    /**
     * 处理网关删除
     */
    private ConfigChangeResult handleGatewayDelete(GatewayConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.delete.id.required"), 
                    ThingType.GATEWAY);
            }

            // 检查网关是否存在
            TcsGateway existingGateway = this.getById(configDto.getId());
            if (existingGateway == null) {
                return ConfigChangeResult.failure(
                    messageSourceUtil.getMessage("gateway.not.exists", new Object[]{configDto.getId()}), 
                    ThingType.GATEWAY);
            }

            // 级联删除该网关下的所有设备
            List<TcsDevice> devicesUnderGateway = deviceService.listByGatewayId(existingGateway.getId());
            List<DeviceConfigChangeDto> deletedDevices = new ArrayList<>();
            
            for (TcsDevice device : devicesUnderGateway) {
                DeviceConfigChangeDto deviceDeleteDto = new DeviceConfigChangeDto();
                deviceDeleteDto.setId(device.getId());
                deviceDeleteDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);
                
                ConfigChangeResult deviceResult = deviceService.handleDeviceConfigChange(deviceDeleteDto);
                if (!deviceResult.isSuccess()) {
                    throw new RuntimeException(messageSourceUtil.getMessage("gateway.device.delete.failed", 
                        new Object[]{device.getId(), deviceResult.getErrorMessage()}));
                }
                if (deviceResult.getConfigData() != null) {
                    deletedDevices.add((DeviceConfigChangeDto) deviceResult.getConfigData());
                }
            }

            // 逻辑删除网关
            boolean success = this.removeById(configDto.getId());
            if (success) {
                // 创建返回的ConfigChangeDto，包含要删除的网关ID和删除的设备列表
                GatewayConfigChangeDto resultDto = new GatewayConfigChangeDto();
                resultDto.setId(existingGateway.getId());
                resultDto.setSouthGatewayId(existingGateway.getSouthGatewayId());
                resultDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);
//                resultDto.setDevices(deletedDevices.isEmpty() ? null : deletedDevices);
                
                return ConfigChangeResult.success(existingGateway.getSouthGatewayId(), ThingType.GATEWAY, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.delete.failed"), 
                    ThingType.GATEWAY);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("gateway.delete.failed"), 
                e.getMessage(), ThingType.GATEWAY);
        }
    }
} 