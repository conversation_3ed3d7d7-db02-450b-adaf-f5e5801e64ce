# Configuration Change Messages
config.data.empty=配置数据不能为空
config.lifecycle.empty=生命周期事件类型不能为空
config.lifecycle.unsupported=不支持的生命周期事件类型: {0}
config.change.failed=配置变更失败

# Gateway Messages
gateway.south.id.exists=南向网关ID已存在: {0}
gateway.create.failed=网关创建失败
gateway.update.id.required=更新操作必须提供网关ID
gateway.not.exists=网关不存在: {0}
gateway.south.id.conflict=南向网关ID已被其他网关使用: {0}
gateway.update.failed=网关更新失败
gateway.delete.id.required=删除操作必须提供网关ID
gateway.delete.failed=网关删除失败

# Device Messages
device.south.id.exists=南向设备ID已存在: {0}
device.create.failed=设备创建失败
device.update.id.required=更新操作必须提供设备ID
device.not.exists=设备不存在: {0}
device.south.id.conflict=南向设备ID已被其他设备使用: {0}
device.update.failed=设备更新失败
device.delete.id.required=删除操作必须提供设备ID
device.delete.failed=设备删除失败
device.signal.change.failed=信号配置变更失败: {0}
device.alarm.change.failed=告警配置变更失败: {0}
device.control.change.failed=控制配置变更失败: {0}

# Signal Messages
signal.south.id.exists=南向信号ID已存在: {0}
signal.create.failed=信号创建失败
signal.update.id.required=更新操作必须提供信号ID
signal.not.exists=信号不存在: {0}
signal.south.id.conflict=南向信号ID已被其他信号使用: {0}
signal.update.failed=信号更新失败
signal.delete.id.required=删除操作必须提供信号ID
signal.delete.failed=信号删除失败

# Alarm Messages
alarm.south.id.exists=南向告警ID已存在: {0}
alarm.create.failed=告警创建失败
alarm.update.id.required=更新操作必须提供告警ID
alarm.not.exists=告警不存在: {0}
alarm.south.id.conflict=南向告警ID已被其他告警使用: {0}
alarm.update.failed=告警更新失败
alarm.delete.id.required=删除操作必须提供告警ID
alarm.delete.failed=告警删除失败

# Control Messages
control.south.id.exists=南向控制ID已存在: {0}
control.create.failed=控制创建失败
control.update.id.required=更新操作必须提供控制ID
control.not.exists=控制不存在: {0}
control.south.id.conflict=南向控制ID已被其他控制使用: {0}
control.update.failed=控制更新失败
control.delete.id.required=删除操作必须提供控制ID
control.delete.failed=控制删除失败 