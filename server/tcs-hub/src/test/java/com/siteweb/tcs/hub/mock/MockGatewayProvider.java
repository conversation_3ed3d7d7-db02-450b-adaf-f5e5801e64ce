package com.siteweb.tcs.hub.mock;

import com.siteweb.tcs.hub.dal.entity.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class MockGatewayProvider {
    //private final SitewebConfigurationServiceClient client = new SitewebConfigurationServiceClient();

    public List<ForeignGateway> getMockGateways(int gatewayCount) {
        var gateways = new ArrayList<ForeignGateway>();
        for (int i = 0; i < gatewayCount; i++) {
            String gatewayId = "MockGateway" + (i+1);
            int monitorUnitID = i + 1;

            var gateway = new ForeignGateway(gatewayId, monitorUnitID,"ctcc",null, new ArrayList<>());
            gateway.setForeignDeviceList(getMockDevicesForGateway(gatewayId, monitorUnitID, i+1, 20));

            gateways.add(gateway);
        }

        return gateways;
    }

    private List<ForeignControl> getMockControlsForDevice(String deviceId, int controlCount) {
        var controls = new ArrayList<ForeignControl>();
        for (int m = 0; m < controlCount; m++) {
            int controlId = m + 1;

            var control = new ForeignControl();
            control.setForeignGatewayId(deviceId.substring(0, 10)); // 假设前10个字符是gateway ID
            control.setForeignDeviceId(deviceId);
            control.setForeignControlId("MockControl" + deviceId + "_" + m);
            control.setMonitorUnitId(m % 20 + 1); // 随机分配控制监控单元ID
            control.setEquipmentId(m + 1);
            control.setControlId(controlId);

            controls.add(control);
        }

        return controls;
    }

    private List<ForeignDevice> getMockDevicesForGateway(String gatewayId, int monitorUnitId,int gatewayIndex, int deviceCount) {
        var devices = new ArrayList<ForeignDevice>();
        for (int j = 0; j < deviceCount; j++) {
            String deviceId = "MockDevice" + gatewayIndex + "_" + (j+1);
            int equipmentId = j + 1;

            var device = new ForeignDevice(gatewayId, deviceId,monitorUnitId,(monitorUnitId*100)+equipmentId,1, null, new ArrayList<>(), new ArrayList<>(),null,null);
            device.setForeignSignalList(getMockSignalsForDevice(deviceId, 200));
            device.setForeignAlarmList(getMockAlarmsForDevice(j, 100)); // 添加模拟的alarms
            device.setForeignControlList(getMockControlsForDevice(deviceId, 10)); // 添加模拟的controls

            devices.add(device);
        }

        return devices;
    }

    private List<ForeignAlarm> getMockAlarmsForDevice(int deviceId, int alarmCount) {
        var alarms = new ArrayList<ForeignAlarm>();
        for (int l = 0; l < alarmCount; l++) {
            int alarmEventId = l + 1;
            int alarmEventConditionId = l * 2 + 1;

            var alarm = new ForeignAlarm();
            alarm.setForeignGatewayID("MockGateway");
            alarm.setForeignDeviceID("MockDevice" + deviceId);
            alarm.setForeignAlarmID("MockAlarm" + deviceId + "_" + l);
            alarm.setMonitorUnitId(l % 20 + 1); // 随机分配报警监控单元ID
            alarm.setEquipmentId(l + 1);
            alarm.setEventId(alarmEventId);
            alarm.setEventConditionId(alarmEventConditionId);

            alarms.add(alarm);
        }

        return alarms;
    }

    private List<ForeignSignal> getMockSignalsForDevice(String deviceId, int signalCount) {
        var signals = new ArrayList<ForeignSignal>();
        for (int k = 0; k < signalCount; k++) {
            String signalId = "MockSignal" + deviceId + "_" + k;
            int signalMonitorUnitId = k % 20 + 1; // 随机分配信号监控单元ID
            int signalEquipmentId = k + 1;

            var signal = new ForeignSignal();
            signal.setForeignGatewayID(deviceId.substring(0, 10)); // 假设前10个字符是gateway ID
            signal.setForeignDeviceID(deviceId);
            signal.setForeignSignalID(signalId);
            signal.setMonitorUnitId(signalMonitorUnitId);
           signal.setEquipmentId(signalEquipmentId);
            signal.setSignalId(k + 1);

            signals.add(signal);
        }

        return signals;
    }

//    public Integer createMonitorUnit(MonitorUnit monitorUnit) {
//       return client.createMonitorUnit(monitorUnit);
//    }
//
//    public ForeignGateway getForeignGatewayByMonitorUnitId(Integer muId,String pluginId) {
//        return client.createForeignGatewayByMonitorUnitId(muId,pluginId);
//    }
}