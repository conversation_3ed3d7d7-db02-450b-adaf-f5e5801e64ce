package com.siteweb.tcs.hub.mock;

import lombok.Data;

/**
 * 监控单元模拟数据类
 * 用于测试环境的监控单元数据模拟
 */
@Data
public class MonitorUnit {
    private String id;
    private String name;
    private String equipmentId;
    private String status;
    private String dataType;
    
    // 测试数据构造方法
    public static MonitorUnit createMockMonitorUnit() {
        MonitorUnit unit = new MonitorUnit();
        unit.setId("MOCK-MU-001");
        unit.setName("Mock Monitor Unit");
        unit.setEquipmentId("MOCK-EQ-001");
        unit.setStatus("Active");
        unit.setDataType("Temperature");
        return unit;
    }
}