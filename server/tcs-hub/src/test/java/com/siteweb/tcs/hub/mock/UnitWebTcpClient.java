package com.siteweb.tcs.hub.mock;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-18 14:09
 **/
@Slf4j
public class UnitWebTcpClient {
    private String ip;
    private int port;
    private NettyTcpClient nettyTcpClient;

    public UnitWebTcpClient(String ip, int port) throws  Exception{
        this.ip = ip;
        this.port = port;
        this.nettyTcpClient = new NettyTcpClient(ip, port, res -> parseResponse(res));
        nettyTcpClient.start();
    }

    public void send(UnitWebCmdEnum cmdEnum, String sendData) throws Exception{
        UnitWebMessage unitWebMessage = new UnitWebMessage(cmdEnum.getCmdId(), sendData.getBytes(StandardCharsets.UTF_8));
        nettyTcpClient.send(unitWebMessage);
    }

    private UnitWebMessage parseResponse(UnitWebMessage responseData){

        // 输出或写入缓存
        System.out.println("收到响应数据内容: " + responseData);
        return responseData;
    }
}
