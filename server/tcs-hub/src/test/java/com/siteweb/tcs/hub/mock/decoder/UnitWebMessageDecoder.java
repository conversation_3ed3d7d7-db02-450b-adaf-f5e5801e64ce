package com.siteweb.tcs.hub.mock.decoder;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @program: tcs2
 * @description: String消息解码器
 * @author: xsx
 * @create: 2025-06-19 09:37
 **/

public class UnitWebMessageDecoder extends ByteToMessageDecoder {

    private static final byte EOT = 0x04;
    private static final int COMMAND_ID = 202;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        while (in.readableBytes() >= 8) { // 使用while循环处理多个完整消息
            // 标记当前位置，用于回退
            in.markReaderIndex();
            
            // 读取commandId（4字节，网络字节序）
            int commandId = in.readInt();
            if (commandId != COMMAND_ID) {
                in.resetReaderIndex();
                throw new IOException("Invalid command ID: " + commandId + ", expected: " + COMMAND_ID);
            }

            // 读取msglen（4字节，网络字节序）
            int length = in.readInt();
            
            // 防止异常长度
            if (length < 0 || length > 1024 * 1024) {
                in.resetReaderIndex();
                throw new IOException("Invalid response length: " + length);
            }

            // 根据 msglen 处理
            if (length > 0) {
                // 检查是否有足够的字节来读取完整的消息内容
                if (in.readableBytes() < length) {
                    // 数据不完整，重置读取位置，等待更多数据到达
                    in.resetReaderIndex();
                    return;
                }
                
                // 读取消息内容并转换为String
                byte[] content = new byte[length];
                in.readBytes(content);
                String message = new String(content, StandardCharsets.UTF_8);
                String res = message .replace(String.valueOf((char)0x00), "")
                        .replace(String.valueOf((char)0x04), "");
                out.add(res);
                
            } else {
                // msglen == 0，需要读取 EOT 字节
                if (in.readableBytes() < 1) {
                    // 没有足够的字节读取EOT，重置位置等待更多数据
                    in.resetReaderIndex();
                    return;
                }
                
                byte eot = in.readByte();
                if (eot != EOT) {
                    in.resetReaderIndex();
                    throw new IOException("Invalid EOT byte: 0x" + String.format("%02X", eot & 0xFF) + ", expected: 0x" + String.format("%02X", EOT & 0xFF));
                }
                out.add(""); // 空字符串
            }
            
            // 成功解码一个消息，继续处理缓冲区中的下一个消息
        }
    }
}
