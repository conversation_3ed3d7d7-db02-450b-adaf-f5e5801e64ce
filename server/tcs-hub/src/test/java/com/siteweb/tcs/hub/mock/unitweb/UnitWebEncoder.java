package com.siteweb.tcs.hub.mock.unitweb;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;

import java.nio.charset.StandardCharsets;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-19 10:41
 **/

public class UnitWebEncoder extends MessageToByteEncoder<String> {
    @Override
    protected void encode(ChannelHandlerContext ctx, String msg, ByteBuf out) {
        out.writeInt(202); // cmdid
        byte[] msgBytes = msg.getBytes(StandardCharsets.UTF_8);
        out.writeInt(msgBytes.length); // msglen
        out.writeBytes(msgBytes); // msg
        if (msgBytes.length == 0) {
            out.writeByte(0x04); // EOT
        }
    }
}
