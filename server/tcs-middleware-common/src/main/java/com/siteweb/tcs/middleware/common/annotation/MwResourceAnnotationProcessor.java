package com.siteweb.tcs.middleware.common.annotation;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

/**
 * 资源注解处理器
 */
@Component
public class MwResourceAnnotationProcessor implements BeanPostProcessor {

    private static final Logger logger = LoggerFactory.getLogger(MwResourceAnnotationProcessor.class);

    @Autowired
    @Lazy
    private ResourceRegistry resourceRegistry;


    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        processFields(bean);
        processMethods(bean);
        return bean;
    }

    private void processFields(Object bean) {
        ReflectionUtils.doWithFields(bean.getClass(), field -> {
            MwResource annotation = field.getAnnotation(MwResource.class);
            if (annotation != null) {
                Resource resource = getResource(annotation);
                if (resource != null) {
                    // 检查字段类型与资源类型的兼容性
                    Class<?> fieldType = field.getType();
                    if (fieldType.isAssignableFrom(resource.getClass())) {
                        // 类型兼容，直接注入
                        ReflectionUtils.makeAccessible(field);
                        ReflectionUtils.setField(field, bean, resource);
                        logger.debug("Injected resource {} into field {} of bean {}", resource.getId(), field.getName(), bean.getClass().getName());
                    } else {
                        // 类型不兼容，尝试特殊处理
                        if (isCompatibleResourceType(fieldType, resource)) {
                            // 类型兼容（通过特殊规则），注入资源
                            ReflectionUtils.makeAccessible(field);
                            ReflectionUtils.setField(field, bean, resource);
                            logger.debug("Injected compatible resource {} into field {} of bean {}", resource.getId(), field.getName(), bean.getClass().getName());
                        } else {
                            // 类型不兼容，记录错误
                            logger.error("Resource type mismatch: field {} of type {} cannot be assigned from resource {} of type {}",
                                field.getName(), fieldType.getName(), resource.getId(), resource.getClass().getName());
                            if (annotation.required()) {
                                throw new MiddlewareTechnicalException(
                                    MiddlewareTechnicalErrorCode.ANNOTATION_PROCESSING_FAILED,
                                    String.format("Resource type mismatch: field %s of type %s cannot be assigned from resource %s of type %s",
                                    field.getName(), fieldType.getName(), resource.getId(), resource.getClass().getName())
                                );
                            }
                        }
                    }
                }
            }
        });
    }

    private void processMethods(Object bean) {
        ReflectionUtils.doWithMethods(bean.getClass(), method -> {
            MwResource annotation = method.getAnnotation(MwResource.class);
            if (annotation != null && method.getParameterCount() == 1) {
                Resource resource = getResource(annotation);
                if (resource != null) {
                    // 检查方法参数类型与资源类型的兼容性
                    Class<?> paramType = method.getParameterTypes()[0];
                    if (paramType.isAssignableFrom(resource.getClass())) {
                        // 类型兼容，直接注入
                        ReflectionUtils.makeAccessible(method);
                        ReflectionUtils.invokeMethod(method, bean, resource);
                        logger.debug("Injected resource {} into method {} of bean {}", resource.getId(), method.getName(), bean.getClass().getName());
                    } else {
                        // 类型不兼容，尝试特殊处理
                        if (isCompatibleResourceType(paramType, resource)) {
                            // 类型兼容（通过特殊规则），注入资源
                            ReflectionUtils.makeAccessible(method);
                            ReflectionUtils.invokeMethod(method, bean, resource);
                            logger.debug("Injected compatible resource {} into method {} of bean {}", resource.getId(), method.getName(), bean.getClass().getName());
                        } else {
                            // 类型不兼容，记录错误
                            logger.error("Resource type mismatch: method parameter {} of type {} cannot be assigned from resource {} of type {}",
                                method.getName(), paramType.getName(), resource.getId(), resource.getClass().getName());
                            if (annotation.required()) {
                                throw new MiddlewareTechnicalException(
                                    MiddlewareTechnicalErrorCode.ANNOTATION_PROCESSING_FAILED,
                                    String.format("Resource type mismatch: method parameter %s of type %s cannot be assigned from resource %s of type %s",
                                    method.getName(), paramType.getName(), resource.getId(), resource.getClass().getName())
                                );
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 检查资源类型是否与目标类型兼容
     * 这里可以添加特殊的类型兼容规则，例如接口实现关系等
     */
    private boolean isCompatibleResourceType(Class<?> targetType, Resource resource) {
        // 检查是否是Resource的子类型
        if (Resource.class.isAssignableFrom(targetType)) {
            // 如果目标类型是Resource的子类型，检查资源是否实现了该接口
            return targetType.isInstance(resource);
        }

        // 可以在这里添加更多特殊的类型兼容规则
        // 例如，如果目标类型是特定的接口，检查资源是否实现了该接口

        // 默认不兼容
        return false;
    }

    private Resource getResource(MwResource annotation) {
        String resourceId = annotation.value();
        String referenceId = annotation.referenceId();
        boolean required = annotation.required();

        Resource resource = null;

        if (StringUtils.hasText(resourceId)) {
            // ResourceRegistry.get() 方法会尝试初始化资源
            resource = resourceRegistry.get(resourceId, referenceId);
            if (resource == null && required) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                    "Resource not found: " + resourceId
                );
            }
        }
        return resource;
    }
}
