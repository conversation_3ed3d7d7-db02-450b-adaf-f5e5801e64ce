package com.siteweb.tcs.middleware.common.config;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.config.*;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.resource.*;
import com.siteweb.tcs.middleware.common.service.FileSystemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.util.Map;

/**
 * 文件系统自动配置类
 * 根据配置自动创建文件系统资源和服务
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(FileSystemProperties.class)
@ConditionalOnProperty(prefix = "tcs.filesystem", name = "enabled", havingValue = "true", matchIfMissing = true)
public class FileSystemAutoConfiguration {

    @Autowired
    private FileSystemProperties fileSystemProperties;

    @Autowired
    private ServiceRegistry serviceRegistry;

    @Autowired
    private FileSystemConfigValidator configValidator;

    /**
     * 自动配置文件系统服务
     */
    @PostConstruct
    public void configureFileSystems() {
        log.info("开始自动配置文件系统服务...");

        if (fileSystemProperties == null) {
            log.warn("未配置文件系统属性");
            return;
        }

        String type = fileSystemProperties.getType();
        if (!"local".equals(type) && !"minio".equals(type) &&
            !"zookeeper".equals(type) && !"consul".equals(type)) {
            log.warn("不支持的文件系统类型: {}", type);
            return;
        }

        try {
            createFileSystemService(fileSystemProperties);
            log.info("成功创建文件系统服务，类型: {}", type);
        } catch (Exception e) {
            log.error("创建文件系统服务失败，类型: {}", type, e);
        }

        log.info("文件系统自动配置完成");
    }

    /**
     * 创建文件系统服务
     */
    private void createFileSystemService(FileSystemProperties config)
            throws MiddlewareTechnicalException {

        log.info("开始创建文件系统服务，类型: {}", config.getType());

        // 验证并转换配置
        Object specificConfig = configValidator.validateAndConvert(config);

        // 生成资源和服务ID
        String resourceId = "auto-filesystem-resource-main";
        String serviceId = "auto-filesystem-service-main";

        // 创建资源
        Resource resource = createFileSystemResource(resourceId, config.getType(), specificConfig);

        // 初始化并启动资源
        resource.initialize();
        resource.start();

        // 创建服务配置
        FileSystemServiceConfig serviceConfig = new FileSystemServiceConfig();
        serviceConfig.setAsyncMode(false);
        serviceConfig.setThreadPoolSize(5);

        // 创建文件系统服务
        FileSystemService fileSystemService = new FileSystemService(
            serviceId,
            "自动配置文件系统服务",
            "根据配置自动创建的" + config.getType() + "文件系统服务",
            resource,
            serviceConfig
        );

        // 初始化并启动服务
        fileSystemService.initialize();
        fileSystemService.start();

        // 注册服务
        serviceRegistry.save(fileSystemService);

        log.info("文件系统服务创建成功: serviceId={}, resourceId={}, type={}",
                 serviceId, resourceId, config.getType());
    }

    /**
     * 根据配置类型创建对应的文件系统资源
     */
    private Resource createFileSystemResource(String resourceId, String type, Object specificConfig)
            throws MiddlewareTechnicalException {

        switch (type.toLowerCase()) {
            case "local":
                return new LocalFileSystemResource(
                    resourceId,
                    "自动配置本地文件系统",
                    "根据配置自动创建的本地文件系统资源",
                    (LocalFileSystemConfig) specificConfig
                );
            case "minio":
                return new MinioFileSystemResource(
                    resourceId,
                    "自动配置Minio文件系统",
                    "根据配置自动创建的Minio文件系统资源",
                    (MinioFileSystemConfig) specificConfig
                );
            case "zookeeper":
                return new ZookeeperFileSystemResource(
                    resourceId,
                    "自动配置Zookeeper文件系统",
                    "根据配置自动创建的Zookeeper文件系统资源",
                    (ZookeeperFileSystemConfig) specificConfig
                );
            case "consul":
                return new ConsulFileSystemResource(
                    resourceId,
                    "自动配置Consul文件系统",
                    "根据配置自动创建的Consul文件系统资源",
                    (ConsulFileSystemConfig) specificConfig
                );
            default:
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                    "不支持的文件系统类型: " + type
                );
        }
    }


}
