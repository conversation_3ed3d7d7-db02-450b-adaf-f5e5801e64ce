package com.siteweb.tcs.middleware.common.config;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.config.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件系统配置验证和转换工具类
 * 根据type验证必需参数并转换为具体的配置对象
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Slf4j
@Component
public class FileSystemConfigValidator {

    /**
     * 验证并转换文件系统配置
     *
     * @param config 简化配置对象
     * @return 具体的配置对象
     * @throws MiddlewareTechnicalException 验证失败时抛出
     */
    public Object validateAndConvert(FileSystemProperties config)
            throws MiddlewareTechnicalException {

        if (config == null) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "文件系统配置不能为空"
            );
        }

        String type = config.getType();
        if (!StringUtils.hasText(type)) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "文件系统类型不能为空"
            );
        }

        FileSystemProperties.FileSystemType fileSystemType;
        try {
            fileSystemType = FileSystemProperties.FileSystemType.fromCode(type);
        } catch (IllegalArgumentException e) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                e.getMessage()
            );
        }

        switch (fileSystemType) {
            case LOCAL:
                return convertToLocalConfig(config);
            case MINIO:
                return convertToMinioConfig(config);
            case ZOOKEEPER:
                return convertToZookeeperConfig(config);
            case CONSUL:
                return convertToConsulConfig(config);
            default:
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "未实现的文件系统类型: " + type
                );
        }
    }

    /**
     * 转换为本地文件系统配置
     */
    private LocalFileSystemConfig convertToLocalConfig(FileSystemProperties config)
            throws MiddlewareTechnicalException {

        LocalFileSystemConfig localConfig = new LocalFileSystemConfig();

        // 使用rootPath作为根目录路径
        String rootDirectory = config.getRootPath();
        if (!StringUtils.hasText(rootDirectory)) {
            rootDirectory = "./"; // 默认路径
        }
        localConfig.setRootDirectory(rootDirectory);
        localConfig.setCreateDirectories(true); // 默认自动创建目录

        log.debug("本地文件系统配置转换完成: rootDirectory={}", rootDirectory);
        return localConfig;
    }

    /**
     * 转换为Minio文件系统配置
     */
    private MinioFileSystemConfig convertToMinioConfig(FileSystemProperties config)
            throws MiddlewareTechnicalException {

        // 验证必需参数
        if (!StringUtils.hasText(config.getUrl())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Minio文件系统必须配置url"
            );
        }
        if (!StringUtils.hasText(config.getUsername())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Minio文件系统必须配置username (accessKey)"
            );
        }
        if (!StringUtils.hasText(config.getPassword())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Minio文件系统必须配置password (secretKey)"
            );
        }

        MinioFileSystemConfig minioConfig = new MinioFileSystemConfig();

        // 解析URL，提取endpoint和bucketName
        String url = config.getUrl();
        String endpoint = url;
        String bucketName = "default";

        try {
            if (url.contains("?bucketName=")) {
                String[] parts = url.split("\\?bucketName=");
                endpoint = parts[0];
                if (parts.length > 1) {
                    bucketName = URLDecoder.decode(parts[1], StandardCharsets.UTF_8);
                    // 如果还有其他参数，只取bucketName部分
                    if (bucketName.contains("&")) {
                        bucketName = bucketName.split("&")[0];
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析Minio URL失败，使用默认bucket: {}", e.getMessage());
        }

        minioConfig.setEndpoint(endpoint);
        minioConfig.setAccessKey(config.getUsername());
        minioConfig.setSecretKey(config.getPassword());
        minioConfig.setBucketName(bucketName);

        log.debug("Minio文件系统配置转换完成: endpoint={}, bucket={}", endpoint, bucketName);
        return minioConfig;
    }

    /**
     * 转换为Zookeeper文件系统配置
     */
    private ZookeeperFileSystemConfig convertToZookeeperConfig(FileSystemProperties config)
            throws MiddlewareTechnicalException {

        // 验证必需参数
        if (!StringUtils.hasText(config.getUrl())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Zookeeper文件系统必须配置url (connectString)"
            );
        }

        ZookeeperFileSystemConfig zkConfig = new ZookeeperFileSystemConfig();
        zkConfig.setConnectString(config.getUrl());

        // 设置根路径
        String rootPath = config.getRootPath();
        if (!StringUtils.hasText(rootPath)) {
            rootPath = "/filesystem";
        }
        // 确保根路径以/开头
        if (!rootPath.startsWith("/")) {
            rootPath = "/" + rootPath;
        }
        zkConfig.setRootPath(rootPath);

        // 如果启用认证且配置了用户名密码，启用ACL
        if (config.isEnableAuth() &&
            StringUtils.hasText(config.getUsername()) &&
            StringUtils.hasText(config.getPassword())) {
            zkConfig.setEnableAcl(true);
            zkConfig.setAclUsername(config.getUsername());
            zkConfig.setAclPassword(config.getPassword());
        }

        log.debug("Zookeeper文件系统配置转换完成: connectString={}, rootPath={}, enableAcl={}",
                 config.getUrl(), rootPath, zkConfig.isEnableAcl());
        return zkConfig;
    }

    /**
     * 转换为Consul文件系统配置
     */
    private ConsulFileSystemConfig convertToConsulConfig(FileSystemProperties config)
            throws MiddlewareTechnicalException {

        // 验证必需参数
        if (!StringUtils.hasText(config.getUrl())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Consul文件系统必须配置url"
            );
        }

        ConsulFileSystemConfig consulConfig = new ConsulFileSystemConfig();

        // 解析URL设置主机和端口
        try {
            URI uri = URI.create(config.getUrl());
            consulConfig.setHost(uri.getHost());
            if (uri.getPort() > 0) {
                consulConfig.setPort(uri.getPort());
            }
            if (StringUtils.hasText(uri.getScheme())) {
                consulConfig.setScheme(uri.getScheme());
            }
        } catch (Exception e) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Consul URL格式错误: " + config.getUrl() + ", 正确格式: http://localhost:8500"
            );
        }

        // 设置根路径
        String rootPath = config.getRootPath();
        if (!StringUtils.hasText(rootPath)) {
            rootPath = "filesystem";
        }
        // 确保根路径不以/开头（Consul KV特性）
        if (rootPath.startsWith("/")) {
            rootPath = rootPath.substring(1);
        }
        consulConfig.setRootPath(rootPath);

        // 如果启用认证且配置了密码（token），设置token
        if (config.isEnableAuth() && StringUtils.hasText(config.getPassword())) {
            consulConfig.setToken(config.getPassword());
        }

        log.debug("Consul文件系统配置转换完成: host={}, port={}, rootPath={}, hasToken={}",
                 consulConfig.getHost(), consulConfig.getPort(), rootPath, StringUtils.hasText(consulConfig.getToken()));
        return consulConfig;
    }
}
