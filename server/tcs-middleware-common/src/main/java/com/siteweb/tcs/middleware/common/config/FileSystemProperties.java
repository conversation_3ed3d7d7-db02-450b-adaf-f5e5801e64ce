package com.siteweb.tcs.middleware.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件系统配置属性类
 * 简化配置，只需要配置基本参数
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tcs.filesystem")
public class FileSystemProperties {

    /**
     * 文件系统类型：local, minio, zookeeper, consul
     * 支持环境变量：TCS_FILESYSTEM_TYPE
     */
    private String type = "${TCS_FILESYSTEM_TYPE:local}";

    /**
     * 根路径/目录路径
     * - local: 本地目录路径，如 "./" 或 "/data/files"
     * - zookeeper: 根路径，如 "/filesystem"
     * - consul: 根路径，如 "filesystem"
     * 支持环境变量：TCS_FILESYSTEM_ROOTPATH
     */
    private String rootPath = "${TCS_FILESYSTEM_ROOTPATH:./}";

    /**
     * 是否启用认证
     * - local: 不需要，忽略此参数
     * - minio: 必须启用
     * - zookeeper: 可选，启用ACL时设为true
     * - consul: 可选，使用Token时设为true
     * 支持环境变量：TCS_FILESYSTEM_ENABLEAUTH
     */
    private boolean enableAuth = Boolean.parseBoolean("${TCS_FILESYSTEM_ENABLEAUTH:false}");

    /**
     * 用户名
     * - local: 不需要
     * - minio: AccessKey
     * - zookeeper: ACL用户名
     * - consul: 不需要
     * 支持环境变量：TCS_FILESYSTEM_USERNAME
     */
    private String username = "${TCS_FILESYSTEM_USERNAME:}";

    /**
     * 密码
     * - local: 不需要
     * - minio: SecretKey
     * - zookeeper: ACL密码
     * - consul: Token
     * 支持环境变量：TCS_FILESYSTEM_PASSWORD
     */
    private String password = "${TCS_FILESYSTEM_PASSWORD:}";

    /**
     * 服务器URL
     * - local: 不需要
     * - minio: 服务器地址，如 "http://localhost:9000?bucketName=mybucket"
     * - zookeeper: 连接字符串，如 "localhost:2181" 或 "zk1:2181,zk2:2181"
     * - consul: 服务器地址，如 "http://localhost:8500"
     * 支持环境变量：TCS_FILESYSTEM_URL
     */
    private String url = "${TCS_FILESYSTEM_URL:}";

    /**
     * 文件系统类型枚举
     */
    public enum FileSystemType {
        LOCAL("local", "本地文件系统"),
        MINIO("minio", "MinIO对象存储"),
        ZOOKEEPER("zookeeper", "Zookeeper分布式存储"),
        CONSUL("consul", "Consul键值存储");

        private final String code;
        private final String description;

        FileSystemType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static FileSystemType fromCode(String code) {
            for (FileSystemType type : values()) {
                if (type.code.equalsIgnoreCase(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("不支持的文件系统类型: " + code + ", 支持的类型: local, minio, zookeeper, consul");
        }
    }
}


