package com.siteweb.tcs.middleware.common.exception;

import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.TechnicalException;

/**
 * 中间件技术异常类
 * 继承自tcs-common中的TechnicalException
 */
public class MiddlewareTechnicalException extends TechnicalException {
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     */
    public MiddlewareTechnicalException(String code, String message) {
        super(code, message);
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原因
     */
    public MiddlewareTechnicalException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param details 详情
     */
    public MiddlewareTechnicalException(String code, String message, Object details) {
        super(code, message, details);
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param details 详情
     * @param cause 原因
     */
    public MiddlewareTechnicalException(String code, String message, Object details, Throwable cause) {
        super(code, message, details, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param component 组件
     */
    public MiddlewareTechnicalException(String code, String message, String component) {
        super(code, message, component);
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param details 详情
     * @param cause 原因
     * @param component 组件
     */
    public MiddlewareTechnicalException(String code, String message, Object details, Throwable cause, String component) {
        super(code, message, details, cause, component);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     */
    public MiddlewareTechnicalException(TechnicalErrorCode errorCode) {
        super(errorCode);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param component 组件
     */
    public MiddlewareTechnicalException(TechnicalErrorCode errorCode, String component) {
        super(errorCode, component);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param details 详情
     */
    public MiddlewareTechnicalException(TechnicalErrorCode errorCode, Object details) {
        super(errorCode, details);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param cause 原因
     */
    public MiddlewareTechnicalException(TechnicalErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param details 详情
     * @param cause 原因
     */
    public MiddlewareTechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause) {
        super(errorCode, details, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param details 详情
     * @param cause 原因
     * @param component 组件
     */
    public MiddlewareTechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause, String component) {
        super(errorCode, details, cause, component);
    }
}
