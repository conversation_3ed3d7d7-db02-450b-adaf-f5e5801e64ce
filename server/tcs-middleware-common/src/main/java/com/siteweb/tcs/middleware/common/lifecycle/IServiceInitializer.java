package com.siteweb.tcs.middleware.common.lifecycle;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;

/**
 * 服务初始化器接口
 * 用于初始化服务实例
 */
public interface IServiceInitializer {

    /**
     * 通过服务配置ID初始化服务
     *
     * @param serviceConfigurationId 服务配置ID
     * @return 服务实例
     * @throws Exception 初始化异常
     */
    Service initializeServiceById(String serviceConfigurationId) throws Exception;


    Service initializeSitewebPersistentService(String serviceId,String pluginId, String cmbPluginId) throws Exception;
}
