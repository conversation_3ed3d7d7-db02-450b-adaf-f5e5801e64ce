package com.siteweb.tcs.middleware.common.lifecycle;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.service.Service;

/**
 * 服务生命周期管理器接口
 */
public interface ServiceLifecycleManager {

    /**
     * 创建服务实例
     *
     * @param serviceId 服务ID，对应数据库中的服务配置
     * @return 服务实例
     * @throws MiddlewareTechnicalException 创建服务实例失败时抛出异常
     */
    Service createService(String serviceId) throws MiddlewareTechnicalException;

    /**
     * 检查服务健康状态
     *
     * @param service 服务实例
     * @return 健康状态
     */
    HealthStatus checkServiceHealth(Service service);

    /**
     * 销毁服务实例
     *
     * @param service 服务实例
     * @throws MiddlewareTechnicalException 销毁服务实例失败时抛出异常
     */
    void destroyService(Service service) throws MiddlewareTechnicalException;

    /**
     * 执行服务健康检查
     * 定期检查所有服务的健康状态
     */
    void performHealthCheck();
}
