package com.siteweb.tcs.middleware.common.model;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 连接测试结果类
 */
public class ConnectionTestResult {
    
    private final boolean success;
    private final String message;
    private final Map<String, Object> details;
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param message 消息
     */
    public ConnectionTestResult(boolean success, String message) {
        this(success, message, Collections.emptyMap());
    }
    
    /**
     * 构造函数
     * 
     * @param success 是否成功
     * @param message 消息
     * @param details 详情
     */
    public ConnectionTestResult(boolean success, String message, Map<String, Object> details) {
        this.success = success;
        this.message = message;
        this.details = details != null ? new HashMap<>(details) : new HashMap<>();
    }
    
    /**
     * 获取是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * 获取消息
     * 
     * @return 消息
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 获取详情
     * 
     * @return 详情
     */
    public Map<String, Object> getDetails() {
        return Collections.unmodifiableMap(details);
    }
    
    /**
     * 创建成功的连接测试结果
     * 
     * @param message 消息
     * @return 连接测试结果
     */
    public static ConnectionTestResult success(String message) {
        return new ConnectionTestResult(true, message);
    }
    
    /**
     * 创建成功的连接测试结果
     * 
     * @param message 消息
     * @param details 详情
     * @return 连接测试结果
     */
    public static ConnectionTestResult success(String message, Map<String, Object> details) {
        return new ConnectionTestResult(true, message, details);
    }
    
    /**
     * 创建失败的连接测试结果
     * 
     * @param message 消息
     * @return 连接测试结果
     */
    public static ConnectionTestResult failure(String message) {
        return new ConnectionTestResult(false, message);
    }
    
    /**
     * 创建失败的连接测试结果
     * 
     * @param message 消息
     * @param details 详情
     * @return 连接测试结果
     */
    public static ConnectionTestResult failure(String message, Map<String, Object> details) {
        return new ConnectionTestResult(false, message, details);
    }
}
