package com.siteweb.tcs.middleware.common.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文件信息数据模型
 */
public class FileInfo {
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 文件大小（字节）
     */
    private long fileSize;
    
    /**
     * 是否为目录
     */
    private boolean isDirectory;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
    
    /**
     * 文件类型
     */
    private String contentType;
    
    /**
     * 文件MD5值
     */
    private String md5Hash;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    // 构造函数
    public FileInfo() {
    }

    public FileInfo(String fileName, String filePath, long fileSize, boolean isDirectory) {
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.isDirectory = isDirectory;
    }

    // Getter和Setter方法
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public boolean isDirectory() {
        return isDirectory;
    }

    public void setDirectory(boolean directory) {
        isDirectory = directory;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getMd5Hash() {
        return md5Hash;
    }

    public void setMd5Hash(String md5Hash) {
        this.md5Hash = md5Hash;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    @Override
    public String toString() {
        return "FileInfo{" +
                "fileName='" + fileName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileSize=" + fileSize +
                ", isDirectory=" + isDirectory +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                ", contentType='" + contentType + '\'' +
                ", md5Hash='" + md5Hash + '\'' +
                ", attributes=" + attributes +
                '}';
    }

    // Builder模式
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private FileInfo fileInfo = new FileInfo();

        public Builder fileName(String fileName) {
            fileInfo.fileName = fileName;
            return this;
        }

        public Builder filePath(String filePath) {
            fileInfo.filePath = filePath;
            return this;
        }

        public Builder fileSize(long fileSize) {
            fileInfo.fileSize = fileSize;
            return this;
        }

        public Builder isDirectory(boolean isDirectory) {
            fileInfo.isDirectory = isDirectory;
            return this;
        }

        public Builder createTime(LocalDateTime createTime) {
            fileInfo.createTime = createTime;
            return this;
        }

        public Builder modifyTime(LocalDateTime modifyTime) {
            fileInfo.modifyTime = modifyTime;
            return this;
        }

        public Builder contentType(String contentType) {
            fileInfo.contentType = contentType;
            return this;
        }

        public Builder md5Hash(String md5Hash) {
            fileInfo.md5Hash = md5Hash;
            return this;
        }

        public Builder attributes(Map<String, Object> attributes) {
            fileInfo.attributes = attributes;
            return this;
        }

        public FileInfo build() {
            return fileInfo;
        }
    }
}
