package com.siteweb.tcs.middleware.common.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 配置验证结果类
 */
@Data
public class ValidationResult {
    
    private final boolean valid;
    private final List<String> errors;
    
    /**
     * 构造函数
     * 
     * @param valid 是否有效
     * @param errors 错误信息列表
     */
    public ValidationResult(boolean valid, List<String> errors) {
        this.valid = valid;
        this.errors = errors != null ? new ArrayList<>(errors) : new ArrayList<>();
    }
    
    /**
     * 获取是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return valid;
    }
    
    /**
     * 获取错误信息列表
     * 
     * @return 错误信息列表
     */
    public List<String> getErrors() {
        return Collections.unmodifiableList(errors);
    }
    
    /**
     * 创建有效的验证结果
     * 
     * @return 验证结果
     */
    public static ValidationResult valid() {
        return new ValidationResult(true, Collections.emptyList());
    }
    
    /**
     * 创建无效的验证结果
     * 
     * @param errors 错误信息列表
     * @return 验证结果
     */
    public static ValidationResult invalid(List<String> errors) {
        return new ValidationResult(false, errors);
    }
    
    /**
     * 创建无效的验证结果
     * 
     * @param error 错误信息
     * @return 验证结果
     */
    public static ValidationResult invalid(String error) {
        return new ValidationResult(false, Collections.singletonList(error));
    }
}
