package com.siteweb.tcs.middleware.common.model.config;

import lombok.Data;

/**
 * Dameng数据库配置类
 * 用于配置Dameng数据库连接参数
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
public class DamengConfig {

    /**
     * 数据库主机地址
     */
    private String host = "localhost";

    /**
     * 数据库端口
     */
    private Integer port = 5236;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 数据库模式（schema）
     * Dameng主要使用schema来组织数据，而不是database概念
     */
    private String schema = "SYSDBA";

    /**
     * 连接池最小连接数
     */
    private Integer minPoolSize = 5;

    /**
     * 连接池最大连接数
     */
    private Integer maxPoolSize = 20;

    /**
     * 连接超时时间（毫秒）
     */
    private Long connectionTimeout = 30000L;

    /**
     * 空闲连接超时时间（毫秒）
     */
    private Long idleTimeout = 600000L;

    /**
     * 连接最大生命周期（毫秒）
     */
    private Long maxLifetime = 1800000L;

    /**
     * 连接泄漏检测阈值（毫秒）
     */
    private Long leakDetectionThreshold = 60000L;

    /**
     * 是否启用SSL连接
     */
    private Boolean sslEnabled = false;

    /**
     * 应用程序名称
     */
    private String applicationName = "TCS-Middleware";

    /**
     * 连接初始化SQL
     */
    private String connectionInitSql;

    /**
     * 是否启用自动提交
     */
    private Boolean autoCommit = true;

    /**
     * 事务隔离级别
     * 可选值：TRANSACTION_READ_UNCOMMITTED, TRANSACTION_READ_COMMITTED, 
     *        TRANSACTION_REPEATABLE_READ, TRANSACTION_SERIALIZABLE
     */
    private String transactionIsolation = "TRANSACTION_READ_COMMITTED";

    /**
     * 连接验证查询
     */
    private String validationQuery = "SELECT 1 FROM DUAL";

    /**
     * 连接验证超时时间（秒）
     */
    private Integer validationTimeout = 5;

    /**
     * 是否在获取连接时验证
     */
    private Boolean testOnBorrow = true;

    /**
     * 是否在归还连接时验证
     */
    private Boolean testOnReturn = false;

    /**
     * 是否在连接空闲时验证
     */
    private Boolean testWhileIdle = true;

    /**
     * 空闲连接验证间隔时间（毫秒）
     */
    private Long timeBetweenEvictionRunsMillis = 60000L;

    /**
     * 连接池名称
     */
    private String poolName = "DamengPool";

    /**
     * 是否启用JMX监控
     */
    private Boolean jmxEnabled = false;

    /**
     * Dameng特有配置：字符集编码
     */
    private String characterEncoding = "UTF-8";

    /**
     * Dameng特有配置：是否启用批处理
     */
    private Boolean batchEnabled = true;

    /**
     * Dameng特有配置：批处理大小
     */
    private Integer batchSize = 1000;

    /**
     * Dameng特有配置：是否启用预编译语句缓存
     */
    private Boolean prepStmtCacheEnabled = true;

    /**
     * Dameng特有配置：预编译语句缓存大小
     */
    private Integer prepStmtCacheSize = 250;

    /**
     * Dameng特有配置：预编译语句SQL限制长度
     */
    private Integer prepStmtCacheSqlLimit = 2048;

    /**
     * 获取完整的JDBC URL
     *
     * @return JDBC URL
     */
    public String getJdbcUrl() {
        StringBuilder url = new StringBuilder();
        url.append("jdbc:dm://")
           .append(host)
           .append(":")
           .append(port);

        // 添加参数
        url.append("?schema=").append(schema);
        url.append("&characterEncoding=").append(characterEncoding);

        if (sslEnabled != null && sslEnabled) {
            url.append("&ssl=true");
        } else {
            url.append("&ssl=false");
        }

        if (batchEnabled != null && batchEnabled) {
            url.append("&rewriteBatchedStatements=true");
        }

        return url.toString();
    }

    /**
     * 获取驱动类名
     *
     * @return 驱动类名
     */
    public String getDriverClassName() {
        return "dm.jdbc.driver.DmDriver";
    }
}
