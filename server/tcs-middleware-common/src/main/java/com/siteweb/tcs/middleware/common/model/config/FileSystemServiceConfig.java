package com.siteweb.tcs.middleware.common.model.config;

/**
 * 文件系统服务配置类
 */
public class FileSystemServiceConfig {

    /**
     * 线程池大小
     */
    private int threadPoolSize = 10;

    /**
     * 是否启用异步模式
     * true: 启用异步模式，创建线程池，异步方法真正异步执行
     * false: 同步模式，不创建线程池，异步方法会降级为同步执行
     */
    private boolean asyncMode = true;

    // 构造函数
    public FileSystemServiceConfig() {
    }

    // Getter和Setter方法
    public int getThreadPoolSize() {
        return threadPoolSize;
    }

    public void setThreadPoolSize(int threadPoolSize) {
        this.threadPoolSize = threadPoolSize;
    }

    public boolean isAsyncMode() {
        return asyncMode;
    }

    public void setAsyncMode(boolean asyncMode) {
        this.asyncMode = asyncMode;
    }

    @Override
    public String toString() {
        return "FileSystemServiceConfig{" +
                "threadPoolSize=" + threadPoolSize +
                ", asyncMode=" + asyncMode +
                '}';
    }
}
