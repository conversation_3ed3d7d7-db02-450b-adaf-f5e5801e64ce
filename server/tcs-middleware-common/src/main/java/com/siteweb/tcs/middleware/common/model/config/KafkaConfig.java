package com.siteweb.tcs.middleware.common.model.config;

import lombok.Data;

import java.util.Map;

/**
 * Kafka配置类
 */
@Data
public class KafkaConfig {
    /**
     * 引导服务器地址
     */
    private String bootstrapServers;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 消费者组ID
     */
    private String groupId;

    /**
     * 键序列化器
     */
    private String keySerializer = "org.apache.kafka.common.serialization.StringSerializer";

    /**
     * 值序列化器
     */
    private String valueSerializer = "org.apache.kafka.common.serialization.StringSerializer";

    /**
     * 键反序列化器
     */
    private String keyDeserializer = "org.apache.kafka.common.serialization.StringDeserializer";

    /**
     * 值反序列化器
     */
    private String valueDeserializer = "org.apache.kafka.common.serialization.StringDeserializer";

    /**
     * 确认模式
     */
    private String acks = "all";

    /**
     * 重试次数
     */
    private Integer retries = 3;

    /**
     * 批处理大小
     */
    private Integer batchSize = 16384;

    /**
     * 延迟时间（毫秒）
     */
    private Integer lingerMs = 1;

    /**
     * 缓冲区大小
     */
    private Integer bufferMemory = 33554432;

    /**
     * 自动偏移量重置策略
     */
    private String autoOffsetReset = "earliest";

    /**
     * 是否启用自动提交
     */
    private Boolean enableAutoCommit = true;

    /**
     * 自动提交间隔（毫秒）
     */
    private Integer autoCommitIntervalMs = 5000;

    /**
     * 会话超时时间（毫秒）
     */
    private Integer sessionTimeoutMs = 30000;

    /**
     * 最大拉取记录数
     */
    private Integer maxPollRecords = 500;

    /**
     * 最大拉取字节数
     */
    private Integer fetchMaxBytes = 52428800;

    /**
     * 最大分区拉取字节数
     */
    private Integer maxPartitionFetchBytes = 1048576;

    /**
     * 安全协议
     */
    private String securityProtocol = "PLAINTEXT";

    /**
     * SASL机制
     */
    private String saslMechanism = "PLAIN";

    /**
     * SASL用户名
     */
    private String saslUsername;

    /**
     * SASL密码
     */
    private String saslPassword;

    /**
     * 额外的生产者配置
     */
    private Map<String, Object> additionalProducerConfig;

    /**
     * 额外的消费者配置
     */
    private Map<String, Object> additionalConsumerConfig;
}
