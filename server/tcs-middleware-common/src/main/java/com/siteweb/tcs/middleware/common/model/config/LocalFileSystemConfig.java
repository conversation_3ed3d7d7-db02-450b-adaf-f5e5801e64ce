package com.siteweb.tcs.middleware.common.model.config;

/**
 * 本地文件系统配置类
 */
public class LocalFileSystemConfig {

    /**
     * 根目录路径
     */
    private String rootDirectory = "/data/files";

    /**
     * 是否创建目录
     */
    private boolean createDirectories = true;



    // 构造函数
    public LocalFileSystemConfig() {
    }

    // Getter和Setter方法
    public String getRootDirectory() {
        return rootDirectory;
    }

    public void setRootDirectory(String rootDirectory) {
        this.rootDirectory = rootDirectory;
    }

    public boolean isCreateDirectories() {
        return createDirectories;
    }

    public void setCreateDirectories(boolean createDirectories) {
        this.createDirectories = createDirectories;
    }



    @Override
    public String toString() {
        return "LocalFileSystemConfig{" +
                "rootDirectory='" + rootDirectory + '\'' +
                ", createDirectories=" + createDirectories +
                '}';
    }
}
