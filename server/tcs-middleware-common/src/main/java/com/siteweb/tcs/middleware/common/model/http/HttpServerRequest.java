package com.siteweb.tcs.middleware.common.model.http;

import java.util.Map;

/**
 * HTTP服务器请求抽象接口
 * 提供统一的HTTP请求访问接口，屏蔽底层实现差异
 */
public interface HttpServerRequest {

    /**
     * 获取HTTP方法
     * 
     * @return HTTP方法（GET、POST、PUT、DELETE等）
     */
    String getMethod();

    /**
     * 获取请求路径
     * 
     * @return 请求路径（不包含查询参数）
     */
    String getPath();

    /**
     * 获取查询字符串
     * 
     * @return 查询字符串（如 "param1=value1&param2=value2"）
     */
    String getQuery();

    /**
     * 获取查询参数
     * 
     * @return 查询参数映射
     */
    Map<String, String> getQueryParams();

    /**
     * 获取指定查询参数值
     * 
     * @param name 参数名
     * @return 参数值，如果不存在返回null
     */
    String getQueryParam(String name);

    /**
     * 获取请求头
     * 
     * @return 请求头映射
     */
    Map<String, String> getHeaders();

    /**
     * 获取指定请求头值
     * 
     * @param name 请求头名称
     * @return 请求头值，如果不存在返回null
     */
    String getHeader(String name);

    /**
     * 获取请求体
     * 
     * @return 请求体字符串
     */
    String getBody();

    /**
     * 获取请求体字节数组
     * 
     * @return 请求体字节数组
     */
    byte[] getBodyBytes();

    /**
     * 获取远程客户端IP地址
     * 
     * @return 客户端IP地址
     */
    String getRemoteAddress();

    /**
     * 获取远程客户端端口
     * 
     * @return 客户端端口
     */
    int getRemotePort();

    /**
     * 获取本地服务器IP地址
     * 
     * @return 服务器IP地址
     */
    String getLocalAddress();

    /**
     * 获取本地服务器端口
     * 
     * @return 服务器端口
     */
    int getLocalPort();

    /**
     * 获取Content-Type
     * 
     * @return Content-Type值
     */
    String getContentType();

    /**
     * 获取Content-Length
     * 
     * @return Content-Length值，如果不存在返回-1
     */
    long getContentLength();

    /**
     * 检查是否为指定HTTP方法
     * 
     * @param method HTTP方法
     * @return 是否匹配
     */
    boolean isMethod(String method);

    /**
     * 检查是否为GET请求
     * 
     * @return 是否为GET请求
     */
    boolean isGet();

    /**
     * 检查是否为POST请求
     * 
     * @return 是否为POST请求
     */
    boolean isPost();

    /**
     * 检查是否为PUT请求
     * 
     * @return 是否为PUT请求
     */
    boolean isPut();

    /**
     * 检查是否为DELETE请求
     * 
     * @return 是否为DELETE请求
     */
    boolean isDelete();

    /**
     * 获取完整的请求URL
     * 
     * @return 完整URL
     */
    String getFullUrl();

    /**
     * 获取请求时间戳
     * 
     * @return 请求时间戳（毫秒）
     */
    long getTimestamp();
}
