package com.siteweb.tcs.middleware.common.registry;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.lifecycle.IResourceInitializer;
import com.siteweb.tcs.middleware.common.resource.*;
import com.zaxxer.hikari.HikariDataSource;
import org.eclipse.paho.client.mqttv3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.datasource.DelegatingDataSource;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 资源注册表
 * 用于管理资源实例
 */
@Component
public class ResourceRegistry {

    private static final Logger logger = LoggerFactory.getLogger(ResourceRegistry.class);

    private final ConcurrentMap<String, Resource> resources = new ConcurrentHashMap<>();

    /**
     * 资源引用计数详情
     * key: resourceId, value: Map<referenceId, 引用次数>
     */
    private final ConcurrentMap<String, ConcurrentMap<String, AtomicInteger>> resourceReferenceDetails = new ConcurrentHashMap<>();

    /**
     * 资源总引用计数（用于快速查询）
     * key: resourceId, value: 总引用计数
     */
    private final ConcurrentMap<String, AtomicInteger> resourceReferenceCounts = new ConcurrentHashMap<>();

    /**
     * 资源引用者集合（用于快速查询）
     * key: resourceId, value: 引用者ID集合
     */
    private final ConcurrentMap<String, Set<String>> resourceReferences = new ConcurrentHashMap<>();

    private IResourceInitializer resourceInitializer;

    @Autowired
    public void setResourceInitializer(IResourceInitializer resourceInitializer) {
        this.resourceInitializer = resourceInitializer;
    }

    /**
     * 保存资源到注册表
     *
     * @param resource 资源实例
     * @throws MiddlewareBusinessException 如果保存失败
     */
    public void save(Resource resource) throws MiddlewareBusinessException {
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Resource cannot be null"
            );
        }
        resources.put(resource.getId(), resource);
        logger.debug("Resource saved to registry: {}", resource.getId());
    }

    /**
     * 根据ID获取资源（不增加引用计数）
     * 如果资源不存在，可能会尝试创建
     *
     * ⚠️ 注意：此方法不会增加引用计数，仅供前端查看资源运行状况、是否实例化等场景使用
     * 在后端代码中使用中间件资源时，必须使用 get(resourceId, referenceId) 方法
     *
     * @param resourceId 资源ID
     * @return 资源实例，如果不存在则返回null
     */
    public Resource get(String resourceId) {
        return get(resourceId, null);
    }

    /**
     * 根据ID获取资源（带引用者ID）
     * 如果资源不存在，可能会尝试创建
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 资源实例，如果不存在则返回null
     */
    public Resource get(String resourceId, String referenceId) {
        Resource resource = resources.get(resourceId);

        // 如果资源不存在，尝试从资源初始化器获取
        if (resource == null && resourceInitializer != null) {
            try {
                logger.debug("Resource not found in registry, trying to initialize: {}", resourceId);
                resource = resourceInitializer.initializeResourceById(resourceId);
            } catch (Exception e) {
                logger.warn("Failed to initialize resource: {}", resourceId, e);
            }
        }

        // 添加引用计数
        if (resource != null && referenceId != null) {
            addResourceReference(resourceId, referenceId);
            logger.debug("Added reference for resource {}: {}", resourceId, referenceId);
        }

        return resource;
    }

    /**
     * 获取所有资源
     *
     * @return 所有资源的集合
     */
    public Collection<Resource> getAll() {
        return Collections.unmodifiableCollection(resources.values());
    }

    /**
     * 根据类型获取资源
     *
     * @param type 资源类型
     * @return 指定类型的资源集合
     */
    public Collection<Resource> getByType(String type) {
        return resources.values().stream()
                .filter(resource -> resource.getType().equals(type))
                .collect(Collectors.toList());
    }

    /**
     * 从注册表中移除资源
     *
     * @param resourceId 资源ID
     * @return 被移除的资源，如果不存在则返回null
     */
    public Resource remove(String resourceId) {
        logger.debug("Removing resource from registry: {}", resourceId);
        return resources.remove(resourceId);
    }

    public void removeAll(List<String> resourceIds) {
        resourceIds.forEach(this::remove);
    }

    /**
     * 检查资源是否存在于注册表中
     * 与get方法不同，此方法不会尝试创建不存在的资源
     *
     * @param resourceId 资源ID
     * @return 如果资源存在则返回true，否则返回false
     */
    public boolean contains(String resourceId) {
        return resources.containsKey(resourceId);
    }

    /**
     * 添加资源引用
     * 当插件开始使用资源时调用
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     */
    public void addResourceReference(String resourceId, String referenceId) {
        // 更新详细引用计数
        int newReferenceCount = resourceReferenceDetails
            .computeIfAbsent(resourceId, k -> new ConcurrentHashMap<>())
            .computeIfAbsent(referenceId, k -> new AtomicInteger(0))
            .incrementAndGet();

        // 更新总引用计数
        resourceReferenceCounts.computeIfAbsent(resourceId, k -> new AtomicInteger(0)).incrementAndGet();

        // 更新引用者集合
        resourceReferences.computeIfAbsent(resourceId, k -> ConcurrentHashMap.newKeySet()).add(referenceId);

        logger.debug("Added reference for resource {}: {} (reference count: {}, total references: {})",
                    resourceId, referenceId, newReferenceCount, resourceReferenceCounts.get(resourceId).get());
    }

    /**
     * 根据引用者ID获取所有引用的资源
     *
     * @param referenceId
     * @return
     */
    public Set<String> getResourcesByReferenceId(String referenceId) {
        Set<String> result = new HashSet<>();
        for (Map.Entry<String, Set<String>> entry : resourceReferences.entrySet()) {
            String resourceId = entry.getKey();
            Set<String> refIds = entry.getValue();
            if (refIds.contains(referenceId)) {
                result.add(resourceId);
            }
        }
        return result;
    }
    /**
     * 批量移除资源引用（移除该引用者的所有引用）
     * 当插件停止时，可以一次性移除该插件对所有资源的引用
     *
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 移除的资源数量
     */
    public int batchRemoveResourceReferences(String referenceId) {
        Set<String> resourceIds = getResourcesByReferenceId(referenceId);
        int removedResourceCount = 0;

        for (String resourceId : resourceIds) {
            try {
                // 移除该引用者对该资源的所有引用
                boolean canDestroy = removeResourceReference(resourceId, referenceId, true);
                removedResourceCount++;
                if (canDestroy) {
                    logger.info("Resource {} has no more references and was destroyed", resourceId);
                } else {
                    logger.debug("Resource {} still has other references", resourceId);
                }
            } catch (Exception e) {
                logger.error("Failed to remove reference for resource {}: {}", resourceId, e.getMessage(), e);
            }
        }

        logger.info("Batch removed references for {} resources from reference: {}", removedResourceCount, referenceId);
        return removedResourceCount;
    }

    /**
     * 批量停止资源
     * @deprecated 建议使用 batchRemoveResourceReferences(String referenceId)
     *
     * @param referenceId 引用者ID（通常是插件ID）
     */
    @Deprecated
    public void batchStopResources(String referenceId) {
        batchRemoveResourceReferences(referenceId);
    }

    /**
     * 移除资源引用（单次）
     * 当插件停止使用资源时调用
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 如果引用计数为0，返回true表示可以安全销毁资源
     */
    public boolean removeResourceReference(String resourceId, String referenceId) {
        return removeResourceReference(resourceId, referenceId, false);
    }

    /**
     * 移除资源引用
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @param removeAll 是否移除该引用者的所有引用
     * @return 如果引用计数为0，返回true表示可以安全销毁资源
     */
    public boolean removeResourceReference(String resourceId, String referenceId, boolean removeAll) {
        ConcurrentMap<String, AtomicInteger> referenceDetails = resourceReferenceDetails.get(resourceId);
        if (referenceDetails == null) {
            logger.debug("No reference details found for resource: {}", resourceId);
            return false;
        }

        AtomicInteger referenceCount = referenceDetails.get(referenceId);
        if (referenceCount == null) {
            logger.debug("No reference found for resource {} from reference {}", resourceId, referenceId);
            return false;
        }

        int removedCount;
        if (removeAll) {
            // 移除该引用者的所有引用
            removedCount = referenceCount.getAndSet(0);
            referenceDetails.remove(referenceId);
        } else {
            // 只移除一次引用
            removedCount = 1;
            int newReferenceCount = referenceCount.decrementAndGet();
            if (newReferenceCount <= 0) {
                referenceDetails.remove(referenceId);
            }
        }

        // 更新总引用计数
        AtomicInteger totalCount = resourceReferenceCounts.get(resourceId);
        if (totalCount != null) {
            int newTotalCount = totalCount.addAndGet(-removedCount);
            logger.debug("Removed {} references for resource {}: {} (remaining total references: {})",
                        removedCount, resourceId, referenceId, newTotalCount);

            if (newTotalCount <= 0) {
                // 清理所有引用数据
                resourceReferenceCounts.remove(resourceId);
                resourceReferenceDetails.remove(resourceId);
                resourceReferences.remove(resourceId);

                logger.info("Resource {} has no more references, destroying resource", resourceId);

                // 自动销毁没有引用的资源
                try {
                    Resource resource = resources.get(resourceId);
                    if (resource != null) {
                        resource.stop();
                        logger.info("Destroying resource with no references: {}", resourceId);
                        resource.destroy();
                        resources.remove(resourceId);
                        logger.info("Resource destroyed successfully: {}", resourceId);
                    }
                } catch (Exception e) {
                    logger.error("Failed to destroy resource {}: {}", resourceId, e.getMessage(), e);
                }

                return true;
            } else {
                // 如果该引用者没有更多引用，从引用者集合中移除
                if (!referenceDetails.containsKey(referenceId)) {
                    Set<String> references = resourceReferences.get(resourceId);
                    if (references != null) {
                        references.remove(referenceId);
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取资源引用计数
     *
     * @param resourceId 资源ID
     * @return 引用计数
     */
    public int getResourceReferenceCount(String resourceId) {
        AtomicInteger count = resourceReferenceCounts.get(resourceId);
        return count != null ? count.get() : 0;
    }

    /**
     * 获取资源引用者列表
     *
     * @param resourceId 资源ID
     * @return 引用者ID集合
     */
    public Set<String> getResourceReferences(String resourceId) {
        Set<String> references = resourceReferences.get(resourceId);
        return references != null ? Collections.unmodifiableSet(references) : Collections.emptySet();
    }


    /**
     * 获取数据源（带引用者ID）
     * 支持所有能够提供DataSource的资源类型，如MySQLResource、H2Resource等
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return 数据源
     * @throws MiddlewareBusinessException 如果资源不存在或不能提供DataSource
     */
    public DataSource getDataSource(String resourceId, String referenceId) throws MiddlewareBusinessException {
        // 使用带引用计数的get方法，引用计数逻辑在get()中统一处理
        Resource resource = get(resourceId, referenceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        Object nativeResource = resource.getNativeResource();
        if (nativeResource instanceof HikariDataSource) {
            return (DataSource) nativeResource;
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource cannot provide DataSource: " + resourceId
            );
        }
    }


    /**
     * 获取Redis模板（带引用者ID）
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return Redis模板，泛型类型为<String, Object>
     * @throws MiddlewareBusinessException 如果资源不存在或不是Redis资源
     */
    public RedisTemplate<String, Object> getRedisTemplate(String resourceId, String referenceId) throws MiddlewareBusinessException {
        // 使用带引用计数的get方法，引用计数逻辑在get()中统一处理
        Resource resource = get(resourceId, referenceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        if (!(resource instanceof RedisResource)) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource is not a Redis resource: " + resourceId
            );
        }

        return ((RedisResource) resource).getRedisTemplate();
    }


    /**
     * 获取Kafka模板（带引用者ID）
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return Kafka模板
     * @throws MiddlewareBusinessException 如果资源不存在或不是Kafka资源
     */
    public KafkaTemplate<String, String> getKafkaTemplate(String resourceId, String referenceId) throws MiddlewareBusinessException {
        // 使用带引用计数的get方法，引用计数逻辑在get()中统一处理
        Resource resource = get(resourceId, referenceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        if (!(resource instanceof KafkaResource)) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource is not a Kafka resource: " + resourceId
            );
        }

        return ((KafkaResource) resource).getKafkaTemplate();
    }


    /**
     * 获取MQTT客户端（带引用者ID）
     *
     * @param resourceId 资源ID
     * @param referenceId 引用者ID（通常是插件ID）
     * @return MQTT客户端
     * @throws MiddlewareBusinessException 如果资源不存在或不是MQTT资源
     */
    public MqttClient getMQTTClient(String resourceId, String referenceId) throws MiddlewareBusinessException {
        // 使用带引用计数的get方法，引用计数逻辑在get()中统一处理
        Resource resource = get(resourceId, referenceId);
        if (resource == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                "Resource not found: " + resourceId
            );
        }

        if (!(resource instanceof MosMQTTResource)) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "Resource is not a MQTT resource: " + resourceId
            );
        }

        return  (MqttClient) ((MosMQTTResource) resource).getMqttClient();

    }

}
