package com.siteweb.tcs.middleware.common.resource;

import com.ecwid.consul.v1.ConsulClient;
import com.ecwid.consul.v1.Response;
import com.ecwid.consul.v1.kv.model.GetValue;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.FileInfo;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.config.ConsulFileSystemConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * Consul文件系统资源实现
 * 使用Consul KV存储作为分布式文件存储
 */
public class ConsulFileSystemResource extends FileSystemResource {

    private static final Logger logger = LoggerFactory.getLogger(ConsulFileSystemResource.class);

    private final ConsulFileSystemConfig config;
    private ConsulClient consulClient;

    public ConsulFileSystemResource(String id, String name, String description, ConsulFileSystemConfig config) {
        super(id, ResourceType.CONSUL_FILESYSTEM.getCode(), name, description);
        this.config = config;
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化Consul文件系统资源: {}", getId());

        try {
            // 创建Consul客户端
            this.consulClient = new ConsulClient(config.getHost(), config.getPort());

            logger.info("Consul文件系统资源初始化成功: {}", getId());

        } catch (Exception e) {
            logger.error("初始化Consul文件系统资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "初始化Consul文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动Consul文件系统资源: {}", getId());

        try {
            // 测试连接
            Response<List<String>> response = consulClient.getKVKeysOnly(config.getRootPath());
            if (response == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                    "无法连接到Consul服务器"
                );
            }

            logger.info("Consul文件系统资源启动成功: {}", getId());

        } catch (Exception e) {
            logger.error("启动Consul文件系统资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动Consul文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止Consul文件系统资源: {}", getId());
        // Consul客户端不需要特殊停止操作
        logger.info("Consul文件系统资源停止成功: {}", getId());
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁Consul文件系统资源: {}", getId());
        this.consulClient = null;
        logger.info("Consul文件系统资源销毁成功: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            if (consulClient != null) {
                Response<List<String>> response = consulClient.getKVKeysOnly(config.getRootPath());
                if (response != null) {
                    return HealthStatus.up("Consul文件系统正常");
                }
            }
            return HealthStatus.down("Consul连接失败");
        } catch (Exception e) {
            return HealthStatus.down("Consul健康检查失败: " + e.getMessage());
        }
    }

    @Override
    public <T> T getNativeResource() {
        return (T) consulClient;
    }

    @Override
    public boolean writeFile(String filePath, String fileName, byte[] content) throws MiddlewareTechnicalException {
        try {
            logger.debug("写入文件到Consul: {}/{}", filePath, fileName);

            // 检查文件大小
            if (content.length > config.getMaxDataSize()) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "文件大小超过限制: " + content.length + " > " + config.getMaxDataSize()
                );
            }

            String key = buildKey(filePath, fileName);
            
            // 将字节数组编码为Base64字符串存储
            String encodedContent = Base64.getEncoder().encodeToString(content);
            
            Response<Boolean> response = consulClient.setKVValue(key, encodedContent);
            
            if (response.getValue()) {
                logger.debug("文件写入Consul成功: {}", key);
                return true;
            } else {
                logger.error("文件写入Consul失败: {}", key);
                return false;
            }

        } catch (Exception e) {
            logger.error("写入文件到Consul失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "写入文件到Consul失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public byte[] readFile(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("从Consul读取文件: {}/{}", filePath, fileName);

            String key = buildKey(filePath, fileName);
            
            Response<GetValue> response = consulClient.getKVValue(key);
            
            if (response.getValue() == null) {
                return null; // 文件不存在
            }

            String encodedContent = response.getValue().getDecodedValue();
            if (encodedContent == null) {
                return null;
            }

            // 从Base64字符串解码为字节数组
            byte[] content = Base64.getDecoder().decode(encodedContent);
            
            logger.debug("从Consul读取文件成功: {}, size: {}", key, content.length);
            return content;

        } catch (Exception e) {
            logger.error("从Consul读取文件失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "从Consul读取文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean deleteFile(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("从Consul删除文件: {}/{}", filePath, fileName);

            String key = buildKey(filePath, fileName);
            
            Response<Void> response = consulClient.deleteKVValue(key);
            
            // Consul删除操作总是返回成功，即使key不存在
            logger.debug("从Consul删除文件成功: {}", key);
            return true;

        } catch (Exception e) {
            logger.error("从Consul删除文件失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "从Consul删除文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public List<FileInfo> listFiles(String directoryPath) throws MiddlewareTechnicalException {
        try {
            logger.debug("列出Consul目录文件: {}", directoryPath);

            String prefix = buildDirectoryKey(directoryPath);
            List<FileInfo> fileInfos = new ArrayList<>();

            Response<List<String>> response = consulClient.getKVKeysOnly(prefix);

            if (response.getValue() != null) {
                for (String key : response.getValue()) {
                    // 只返回直接子文件，不包括子目录中的文件
                    String relativePath = key.substring(prefix.length());
                    if (!relativePath.contains("/")) {
                        String fileName = relativePath;
                        
                        // 获取文件详细信息
                        Response<GetValue> valueResponse = consulClient.getKVValue(key);
                        if (valueResponse.getValue() != null) {
                            GetValue getValue = valueResponse.getValue();
                            
                            FileInfo fileInfo = FileInfo.builder()
                                .fileName(fileName)
                                .filePath(directoryPath)
                                .fileSize(getValue.getValue() != null ? getValue.getValue().length() : 0)
                                .isDirectory(false)
                                .modifyTime(LocalDateTime.ofInstant(
                                    java.time.Instant.ofEpochSecond(getValue.getModifyIndex()), 
                                    ZoneId.systemDefault()))
                                .build();
                            fileInfos.add(fileInfo);
                        }
                    }
                }
            }

            logger.debug("列出Consul目录文件成功: {}, 文件数: {}", directoryPath, fileInfos.size());
            return fileInfos;

        } catch (Exception e) {
            logger.error("列出Consul目录文件失败: {}", directoryPath, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "列出Consul目录文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean fileExists(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            String key = buildKey(filePath, fileName);
            Response<GetValue> response = consulClient.getKVValue(key);
            return response.getValue() != null;
        } catch (Exception e) {
            logger.error("检查Consul文件存在失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "检查Consul文件存在失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean createDirectory(String directoryPath) throws MiddlewareTechnicalException {
        // Consul KV存储不需要显式创建目录
        // 目录会在存储文件时自动创建
        logger.debug("Consul不需要显式创建目录: {}", directoryPath);
        return true;
    }

    private String buildKey(String filePath, String fileName) {
        // 如果是绝对路径（Windows或Unix），转换为相对路径
        String normalizedPath = normalizeToRelativePath(filePath);

        if (normalizedPath.isEmpty() || normalizedPath.equals("/")) {
            return config.getRootPath() + "/" + fileName;
        }
        normalizedPath = normalizedPath.endsWith("/") ? normalizedPath : normalizedPath + "/";
        return config.getRootPath() + "/" + normalizedPath + fileName;
    }

    private String buildDirectoryKey(String directoryPath) {
        // 如果是绝对路径（Windows或Unix），转换为相对路径
        String normalizedPath = normalizeToRelativePath(directoryPath);

        if (normalizedPath.isEmpty() || normalizedPath.equals("/")) {
            return config.getRootPath();
        }
        return config.getRootPath() + "/" + normalizedPath;
    }

    /**
     * 将绝对路径转换为相对路径，用于键值存储
     */
    private String normalizeToRelativePath(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }

        // 处理Windows绝对路径 (如 C:\path\to\file)
        if (path.length() > 1 && path.charAt(1) == ':') {
            // 移除驱动器字母和冒号，转换反斜杠为正斜杠
            path = path.substring(2).replace('\\', '/');
        }

        // 移除开头的斜杠
        String normalizedPath = path.startsWith("/") ? path.substring(1) : path;

        return normalizedPath;
    }

    @Override
    public boolean deleteDirectory(String directoryPath, boolean recursive) throws MiddlewareTechnicalException {
        try {
            logger.debug("删除Consul目录: {}, recursive: {}", directoryPath, recursive);

            if (recursive) {
                String prefix = buildDirectoryKey(directoryPath);

                // 获取所有以该前缀开头的key
                Response<List<String>> response = consulClient.getKVKeysOnly(prefix);

                if (response.getValue() != null) {
                    for (String key : response.getValue()) {
                        consulClient.deleteKVValue(key);
                    }
                }
            }

            logger.debug("删除Consul目录成功: {}", directoryPath);
            return true;

        } catch (Exception e) {
            logger.error("删除Consul目录失败: {}", directoryPath, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "删除Consul目录失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public FileInfo getFileInfo(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("获取Consul文件信息: {}/{}", filePath, fileName);

            String key = buildKey(filePath, fileName);
            Response<GetValue> response = consulClient.getKVValue(key);

            if (response.getValue() == null) {
                return null; // 文件不存在
            }

            GetValue getValue = response.getValue();

            return FileInfo.builder()
                .fileName(fileName)
                .filePath(filePath)
                .fileSize(getValue.getValue() != null ? getValue.getValue().length() : 0)
                .isDirectory(false)
                .modifyTime(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochSecond(getValue.getModifyIndex()),
                    ZoneId.systemDefault()))
                .build();

        } catch (Exception e) {
            logger.error("获取Consul文件信息失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "获取Consul文件信息失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean copyFile(String sourcePath, String sourceFileName,
                          String targetPath, String targetFileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("复制Consul文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            // 读取源文件
            byte[] data = readFile(sourcePath, sourceFileName);
            if (data == null) {
                return false; // 源文件不存在
            }

            // 写入目标文件
            return writeFile(targetPath, targetFileName, data);

        } catch (Exception e) {
            logger.error("复制Consul文件失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "复制Consul文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean moveFile(String sourcePath, String sourceFileName,
                          String targetPath, String targetFileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("移动Consul文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            // 先复制文件
            boolean copySuccess = copyFile(sourcePath, sourceFileName, targetPath, targetFileName);
            if (!copySuccess) {
                return false;
            }

            // 再删除源文件
            boolean deleteSuccess = deleteFile(sourcePath, sourceFileName);
            if (!deleteSuccess) {
                // 如果删除失败，尝试删除已复制的目标文件
                try {
                    deleteFile(targetPath, targetFileName);
                } catch (Exception e) {
                    logger.warn("回滚复制的文件失败: {}/{}", targetPath, targetFileName, e);
                }
                return false;
            }

            logger.debug("移动Consul文件成功: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);
            return true;

        } catch (Exception e) {
            logger.error("移动Consul文件失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "移动Consul文件失败: " + e.getMessage(),
                e
            );
        }
    }

}
