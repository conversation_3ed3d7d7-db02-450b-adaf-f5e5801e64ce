package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.http.HttpServerRequest;
import com.siteweb.tcs.middleware.common.model.http.HttpServerResponse;
import com.siteweb.tcs.middleware.common.resource.http.StatisticsManager;

import java.net.InetSocketAddress;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

/**
 * HTTP服务器资源抽象
 * 定义所有HTTP服务器资源必须实现的方法，支持多种底层实现（Akka HTTP、Netty、Undertow等）
 */
public abstract class HttpServerResource extends BaseResource {

    /**
     * 构造函数
     *
     * @param id          资源ID
     * @param type        资源类型
     * @param name        资源名称
     * @param description 资源描述
     */
    protected HttpServerResource(String id, String type, String name, String description) {
        super(id, type, name, description);
    }

    /**
     * 绑定路由处理器（异步版本）
     *
     * @param path 路径（如 "/api/test"）
     * @param handler 异步处理器函数，接收请求返回响应的Future
     * @return 绑定结果的Future
     * @throws MiddlewareTechnicalException 如果绑定失败
     */
    public abstract CompletableFuture<Void> bindRoute(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler)
            throws MiddlewareTechnicalException;

    /**
     * 绑定POST路由（异步版本）
     *
     * @param path 路径
     * @param handler 异步处理器函数，返回响应的Future
     * @return 绑定结果的Future
     * @throws MiddlewareTechnicalException 如果绑定失败
     */
    public abstract CompletableFuture<Void> bindPost(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler)
            throws MiddlewareTechnicalException;

    /**
     * 绑定GET路由（异步版本）
     *
     * @param path 路径
     * @param handler 异步处理器函数，返回响应的Future
     * @return 绑定结果的Future
     * @throws MiddlewareTechnicalException 如果绑定失败
     */
    public abstract CompletableFuture<Void> bindGet(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler)
            throws MiddlewareTechnicalException;

    /**
     * 移除路由
     * 
     * @param path 路径
     * @return 移除结果的Future
     * @throws MiddlewareTechnicalException 如果移除失败
     */
    public abstract CompletableFuture<Void> unbindRoute(String path) throws MiddlewareTechnicalException;

    /**
     * 获取所有已绑定的路由路径
     * 
     * @return 路由路径列表
     */
    public abstract List<String> getBoundPaths();

    /**
     * 获取服务器地址
     * 
     * @return 服务器地址
     */
    public abstract InetSocketAddress getServerAddress();

    /**
     * 获取请求总数统计
     * 
     * @return 请求总数
     */
    public abstract long getRequestCount();

    /**
     * 获取按来源IP分组的请求统计
     * 
     * @return IP到请求次数的映射
     */
    public abstract Map<String, Long> getRequestCountBySourceIP();

    /**
     * 获取所有来源IP列表
     * 
     * @return 来源IP列表
     */
    public abstract List<String> getSourceIPs();

    /**
     * 获取指定IP的请求次数
     * 
     * @param sourceIP 来源IP
     * @return 请求次数，如果IP不存在返回0
     */
    public abstract long getRequestCountBySourceIP(String sourceIP);

    // 注意：解析失败记录功能已移至Service层的ParsingFailureManager
    // 这里不再提供相关方法

    /**
     * 获取原生服务器对象（用于特定实现的高级操作）
     * 
     * @param <T> 原生服务器对象类型
     * @return 原生服务器对象
     */
    public abstract <T> T getNativeServer();

    /**
     * 清空统计数据
     */
    public abstract void clearStatistics();

    /**
     * 清空统计数据
     */
    public abstract Map<String, Object> getStatisticsSummary() ;
    // 注意：clearParsingFailureRecords方法已移至Service层
}
