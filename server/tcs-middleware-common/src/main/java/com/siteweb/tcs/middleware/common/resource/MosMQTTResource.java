package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import org.eclipse.paho.client.mqttv3.IMqttClient;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Mosquitto MQTT资源
 * 提供MQTT客户端功能
 */
public class MosMQTTResource extends BaseResource {

    private static final Logger logger = LoggerFactory.getLogger(MosMQTTResource.class);

    /**
     * MQTT客户端
     */
    private final MqttClient mqttClient;

    /**
     * MQTT连接选项
     */
    private final MqttConnectOptions connectOptions;

    /**
     * 构造函数
     *
     * @param id             资源ID
     * @param type           资源类型
     * @param name           资源名称
     * @param description    资源描述
     * @param mqttClient     MQTT客户端
     * @param connectOptions MQTT连接选项
     */
    public MosMQTTResource(String id, String type, String name, String description,
                        MqttClient mqttClient, MqttConnectOptions connectOptions) {
        super(id, type, name, description);
        this.mqttClient = mqttClient;
        this.connectOptions = connectOptions;
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化MQTT资源: {}", getId());
        // 初始化阶段不创建连接，只在启动时连接
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动MQTT资源: {}", getId());
        try {
            // 测试连接
            if (mqttClient == null) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_CONFIG_INVALID,
                    "MQTT客户端未初始化"
                );
            }

            logger.info("MQTT客户端信息: 服务器地址={}, 客户端ID={}, 连接状态={}",
                    mqttClient.getServerURI(), mqttClient.getClientId(), mqttClient.isConnected());

            // 连接MQTT服务器
            if (!mqttClient.isConnected()) {
                logger.info("开始连接MQTT服务器...");
                mqttClient.connect(connectOptions);
                logger.info("MQTT服务器连接成功，连接状态: {}", mqttClient.isConnected());
            } else {
                logger.info("MQTT客户端已连接，无需重新连接");
            }

            logger.info("MQTT资源启动成功: {}", getId());
        } catch (MqttException e) {
            logger.error("启动MQTT资源失败: {}, 错误码={}, 原因={}, 消息={}",
                    getId(), e.getReasonCode(), e.getCause(), e.getMessage(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.MESSAGE_QUEUE_CONNECTION_FAILED,
                "启动MQTT资源失败: 错误码=" + e.getReasonCode() + ", 原因=" + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止MQTT资源: {}", getId());
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                logger.info("MQTT资源已断开连接: {}", getId());
            }
        } catch (MqttException e) {
            logger.error("停止MQTT资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止MQTT资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁MQTT资源: {}", getId());
        try {
            if (mqttClient != null) {
                if (mqttClient.isConnected()) {
                    mqttClient.disconnect();
                }
                mqttClient.close();
                logger.info("MQTT资源已关闭: {}", getId());
            }
        } catch (MqttException e) {
            logger.error("销毁MQTT资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                "销毁MQTT资源失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 测试MQTT连接
     *
     * @throws MiddlewareTechnicalException 如果连接测试失败
     */
    public void testConnection() throws MiddlewareTechnicalException {
        try {
            if (!mqttClient.isConnected()) {
                mqttClient.connect(connectOptions);
                mqttClient.disconnect();
            }
        } catch (MqttException e) {
            logger.error("测试MQTT连接失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.MESSAGE_QUEUE_CONNECTION_FAILED,
                "测试MQTT连接失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        if (mqttClient == null) {
            return HealthStatus.down("MQTT客户端未初始化");
        }

        try {
            boolean connected = mqttClient.isConnected();

            if (!connected) {
                try {
                    mqttClient.connect(connectOptions);
                    connected = true;
                } catch (MqttException e) {
                    logger.error("MQTT连接失败: {}", getId(), e);
                    return HealthStatus.down("连接失败: " + e.getMessage());
                }
            }

            // 构建健康状态详情
            Map<String, Object> details = new HashMap<>();
            details.put("clientId", mqttClient.getClientId());
            details.put("serverUri", mqttClient.getServerURI());
            details.put("connected", connected);

            return HealthStatus.up("连接正常", details);
        } catch (Exception e) {
            logger.error("检查MQTT资源健康状态异常: {}", getId(), e);
            return HealthStatus.down("连接异常: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeResource() {
        if (mqttClient == null) {
            throw new IllegalStateException("MQTT资源未启动");
        }
        return (T) mqttClient;
    }

    /**
     * 获取MQTT客户端
     *
     * @return MQTT客户端
     */
    public IMqttClient getMqttClient() {
        return mqttClient;
    }

    /**
     * 获取MQTT连接选项
     *
     * @return MQTT连接选项
     */
    public MqttConnectOptions getConnectOptions() {
        return connectOptions;
    }
}
