package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;

import java.util.Map;

/**
 * 资源提供者接口
 *
 * @param <T> 资源类型
 */
public interface ResourceProvider<T extends Resource> {

    /**
     * 获取资源类型
     *
     * @return 资源类型
     */
    String getType();

    /**
     * 验证资源配置
     *
     * @param config 资源配置
     * @return 验证结果，包含是否有效和错误信息
     */
    ValidationResult validateConfig(Map<String, Object> config);

    /**
     * 测试资源连接
     *
     * @param config 资源配置
     * @return 连接测试结果，包含是否成功和错误信息
     */
    ConnectionTestResult testConnection(Map<String, Object> config);

    /**
     * 创建资源实例
     *
     * @param id 资源ID
     * @param name 资源名称
     * @param description 资源描述
     * @param config 资源配置
     * @return 资源实例
     * @throws MiddlewareTechnicalException 创建资源实例失败时抛出异常
     */
    T createResource(String id, String name, String description, Map<String, Object> config) throws MiddlewareTechnicalException;

    /**
     * 销毁资源实例
     *
     * @param resource 资源实例
     * @throws MiddlewareTechnicalException 销毁资源实例失败时抛出异常
     */
    void destroyResource(Resource resource) throws MiddlewareTechnicalException;
}
