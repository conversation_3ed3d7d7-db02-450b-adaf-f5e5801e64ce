package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import java.util.Map;

/**
 * 资源提供者工厂接口
 */
public interface ResourceProviderFactory {

    /**
     * 获取资源提供者
     *
     * @param type 资源类型
     * @return 资源提供者
     * @throws MiddlewareBusinessException 资源类型不存在时抛出异常
     */
    ResourceProvider<? extends Resource> getProvider(String type) throws MiddlewareBusinessException;

    /**
     * 获取所有资源提供者
     *
     * @return 所有资源提供者
     */
    Map<String, ResourceProvider<? extends Resource>> getAllProviders();
}
