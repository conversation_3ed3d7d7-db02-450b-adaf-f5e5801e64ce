package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.FileInfo;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.config.ZookeeperFileSystemConfig;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.curator.framework.state.ConnectionStateListener;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * Zookeeper文件系统资源实现
 * 使用Zookeeper作为分布式文件存储
 */
public class ZookeeperFileSystemResource extends FileSystemResource {

    private static final Logger logger = LoggerFactory.getLogger(ZookeeperFileSystemResource.class);

    private final ZookeeperFileSystemConfig config;
    private CuratorFramework curatorClient;

    public ZookeeperFileSystemResource(String id, String name, String description, ZookeeperFileSystemConfig config) {
        super(id, ResourceType.ZOOKEEPER_FILESYSTEM.getCode(), name, description);
        this.config = config;
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化Zookeeper文件系统资源: {}", getId());

        try {
            // 创建重试策略
            ExponentialBackoffRetry retryPolicy = new ExponentialBackoffRetry(config.getBaseSleepTimeMs(), config.getMaxRetries());

            // 创建Curator客户端
            CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder()
                .connectString(config.getConnectString())
                .sessionTimeoutMs(60000)
                .connectionTimeoutMs(config.getConnectionTimeoutMs())
                .retryPolicy(retryPolicy);

            if (config.getNamespace() != null && !config.getNamespace().isEmpty()) {
                builder.namespace(config.getNamespace());
            }

             if (config.isEnableAcl() && config.getAclUsername() != null && config.getAclPassword() != null) {
                 builder.authorization("digest", (config.getAclUsername() + ":" + config.getAclPassword()).getBytes());
             }

            this.curatorClient = builder.build();

            logger.info("Zookeeper文件系统资源初始化成功: {}", getId());

        } catch (Exception e) {
            logger.error("初始化Zookeeper文件系统资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "初始化Zookeeper文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动Zookeeper文件系统资源: {}", getId());

        try {
            // 用于等待连接稳定的计数器
            CountDownLatch connectedLatch = new CountDownLatch(1);

            // 添加连接状态监听器，确保连接稳定后再执行后续操作
            curatorClient.getConnectionStateListenable().addListener(new ConnectionStateListener() {
                @Override
                public void stateChanged(CuratorFramework client, ConnectionState newState) {
                    if (newState == ConnectionState.CONNECTED || newState == ConnectionState.RECONNECTED) {
                        logger.info("Zookeeper连接成功: {}", config.getConnectString());
                        connectedLatch.countDown(); // 连接稳定，释放计数器
                    } else if (newState == ConnectionState.LOST) {
                        logger.warn("Zookeeper连接丢失，等待重连...");
                    }
                }
            });

            // 启动客户端
            curatorClient.start();

            // 等待连接稳定（超时时间设置为客户端会话超时时间）
            boolean connected = connectedLatch.await(config.getSessionTimeoutMs(), java.util.concurrent.TimeUnit.MILLISECONDS);
            if (!connected) {
                throw new MiddlewareTechnicalException(MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,"连接ZooKeeper超时，未达到稳定状态");
            }

            // 连接稳定后，执行节点检查/创建
            String rootPath = config.getRootPath();
            if (curatorClient.checkExists().forPath(rootPath) == null) {
                curatorClient.create()
                        .creatingParentsIfNeeded()
                        .withMode(CreateMode.PERSISTENT)
                        .forPath(rootPath);
                logger.info("创建根路径: {}", rootPath);
            }
            logger.info("Zookeeper文件系统资源启动成功: {}", getId());

        } catch (Exception e) {
            logger.error("启动Zookeeper文件系统资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动Zookeeper文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止Zookeeper文件系统资源: {}", getId());

        try {
            if (curatorClient != null) {
                curatorClient.close();
            }
            logger.info("Zookeeper文件系统资源停止成功: {}", getId());

        } catch (Exception e) {
            logger.error("停止Zookeeper文件系统资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止Zookeeper文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁Zookeeper文件系统资源: {}", getId());
        this.curatorClient = null;
        logger.info("Zookeeper文件系统资源销毁成功: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            if (curatorClient != null && curatorClient.getZookeeperClient().isConnected()) {
                return HealthStatus.up("Zookeeper文件系统正常");
            } else {
                return HealthStatus.down("Zookeeper连接断开");
            }
        } catch (Exception e) {
            return HealthStatus.down("Zookeeper健康检查失败: " + e.getMessage());
        }
    }

    @Override
    public <T> T getNativeResource() {
        return (T) curatorClient;
    }

    @Override
    public boolean writeFile(String filePath, String fileName, byte[] content) throws MiddlewareTechnicalException {
        try {
            logger.debug("写入文件到Zookeeper: {}/{}", filePath, fileName);

            // 检查文件大小
            if (content.length > config.getMaxDataSize()) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "文件大小超过限制: " + content.length + " > " + config.getMaxDataSize()
                );
            }

            String fullPath = buildFullPath(filePath, fileName);

            // 确保父路径存在
            String parentPath = getParentPath(fullPath);
            if (curatorClient.checkExists().forPath(parentPath) == null) {
                curatorClient.create()
                    .creatingParentsIfNeeded()
                    .withMode(CreateMode.PERSISTENT)
                    .forPath(parentPath);
            }

            // 写入文件数据
            if (curatorClient.checkExists().forPath(fullPath) != null) {
                // 更新现有文件
                curatorClient.setData().forPath(fullPath, content);
            } else {
                // 创建新文件
                curatorClient.create()
                    .withMode(CreateMode.PERSISTENT)
                    .forPath(fullPath, content);
            }

            logger.debug("文件写入Zookeeper成功: {}", fullPath);
            return true;

        } catch (Exception e) {
            logger.error("写入文件到Zookeeper失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "写入文件到Zookeeper失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public byte[] readFile(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("从Zookeeper读取文件: {}/{}", filePath, fileName);

            String fullPath = buildFullPath(filePath, fileName);

            if (curatorClient.checkExists().forPath(fullPath) == null) {
                return null; // 文件不存在
            }

            byte[] data = curatorClient.getData().forPath(fullPath);
            logger.debug("从Zookeeper读取文件成功: {}, size: {}", fullPath, data.length);
            return data;

        } catch (Exception e) {
            logger.error("从Zookeeper读取文件失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "从Zookeeper读取文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean deleteFile(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("从Zookeeper删除文件: {}/{}", filePath, fileName);

            String fullPath = buildFullPath(filePath, fileName);

            if (curatorClient.checkExists().forPath(fullPath) == null) {
                return false; // 文件不存在
            }

            curatorClient.delete().forPath(fullPath);
            logger.debug("从Zookeeper删除文件成功: {}", fullPath);
            return true;

        } catch (Exception e) {
            logger.error("从Zookeeper删除文件失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "从Zookeeper删除文件失败: " + e.getMessage(),
                e
            );
        }
    }

    private String buildFullPath(String filePath, String fileName) {
        // 如果是绝对路径（Windows或Unix），转换为相对路径
        String normalizedPath = normalizeToZkPath(filePath);
        if (normalizedPath.equals("/")) {
            return config.getRootPath() + "/" + fileName;
        }
        return config.getRootPath() + normalizedPath + "/" + fileName;
    }

    @Override
    public List<FileInfo> listFiles(String directoryPath) throws MiddlewareTechnicalException {
        try {
            logger.debug("列出Zookeeper目录文件: {}", directoryPath);

            String fullPath = buildDirectoryPath(directoryPath);
            List<FileInfo> fileInfos = new ArrayList<>();

            if (curatorClient.checkExists().forPath(fullPath) == null) {
                return fileInfos; // 目录不存在，返回空列表
            }

            List<String> children = curatorClient.getChildren().forPath(fullPath);

            for (String child : children) {
                String childPath = fullPath + "/" + child;
                Stat stat = curatorClient.checkExists().forPath(childPath);

                if (stat != null) {
                    FileInfo fileInfo = FileInfo.builder()
                        .fileName(child)
                        .filePath(directoryPath)
                        .fileSize(stat.getDataLength())
                        .isDirectory(stat.getNumChildren() > 0)
                        .createTime(LocalDateTime.ofInstant(
                            java.time.Instant.ofEpochMilli(stat.getCtime()), ZoneId.systemDefault()))
                        .modifyTime(LocalDateTime.ofInstant(
                            java.time.Instant.ofEpochMilli(stat.getMtime()), ZoneId.systemDefault()))
                        .build();
                    fileInfos.add(fileInfo);
                }
            }

            logger.debug("列出Zookeeper目录文件成功: {}, 文件数: {}", directoryPath, fileInfos.size());
            return fileInfos;

        } catch (Exception e) {
            logger.error("列出Zookeeper目录文件失败: {}", directoryPath, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "列出Zookeeper目录文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean fileExists(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            String fullPath = buildFullPath(filePath, fileName);
            return curatorClient.checkExists().forPath(fullPath) != null;
        } catch (Exception e) {
            logger.error("检查Zookeeper文件存在失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "检查Zookeeper文件存在失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean createDirectory(String directoryPath) throws MiddlewareTechnicalException {
        try {
            logger.debug("创建Zookeeper目录: {}", directoryPath);

            String fullPath = buildDirectoryPath(directoryPath);

            if (curatorClient.checkExists().forPath(fullPath) != null) {
                return true; // 目录已存在
            }

            curatorClient.create()
                .creatingParentsIfNeeded()
                .withMode(CreateMode.PERSISTENT)
                .forPath(fullPath);

            logger.debug("创建Zookeeper目录成功: {}", fullPath);
            return true;

        } catch (Exception e) {
            logger.error("创建Zookeeper目录失败: {}", directoryPath, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "创建Zookeeper目录失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean deleteDirectory(String directoryPath, boolean recursive) throws MiddlewareTechnicalException {
        try {
            logger.debug("删除Zookeeper目录: {}, recursive: {}", directoryPath, recursive);

            String fullPath = buildDirectoryPath(directoryPath);

            if (curatorClient.checkExists().forPath(fullPath) == null) {
                return false; // 目录不存在
            }

            if (recursive) {
                // 递归删除
                curatorClient.delete()
                    .deletingChildrenIfNeeded()
                    .forPath(fullPath);
            } else {
                // 只删除空目录
                curatorClient.delete().forPath(fullPath);
            }

            logger.debug("删除Zookeeper目录成功: {}", fullPath);
            return true;

        } catch (Exception e) {
            logger.error("删除Zookeeper目录失败: {}", directoryPath, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "删除Zookeeper目录失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public FileInfo getFileInfo(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("获取Zookeeper文件信息: {}/{}", filePath, fileName);

            String fullPath = buildFullPath(filePath, fileName);
            Stat stat = curatorClient.checkExists().forPath(fullPath);

            if (stat == null) {
                return null; // 文件不存在
            }

            return FileInfo.builder()
                .fileName(fileName)
                .filePath(filePath)
                .fileSize(stat.getDataLength())
                .isDirectory(stat.getNumChildren() > 0)
                .createTime(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(stat.getCtime()), ZoneId.systemDefault()))
                .modifyTime(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(stat.getMtime()), ZoneId.systemDefault()))
                .build();

        } catch (Exception e) {
            logger.error("获取Zookeeper文件信息失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "获取Zookeeper文件信息失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean copyFile(String sourcePath, String sourceFileName,
                          String targetPath, String targetFileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("复制Zookeeper文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            // 读取源文件
            byte[] data = readFile(sourcePath, sourceFileName);
            if (data == null) {
                return false; // 源文件不存在
            }

            // 写入目标文件
            return writeFile(targetPath, targetFileName, data);

        } catch (Exception e) {
            logger.error("复制Zookeeper文件失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "复制Zookeeper文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean moveFile(String sourcePath, String sourceFileName,
                          String targetPath, String targetFileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("移动Zookeeper文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            // 先复制文件
            boolean copySuccess = copyFile(sourcePath, sourceFileName, targetPath, targetFileName);
            if (!copySuccess) {
                return false;
            }

            // 再删除源文件
            boolean deleteSuccess = deleteFile(sourcePath, sourceFileName);
            if (!deleteSuccess) {
                // 如果删除失败，尝试删除已复制的目标文件
                try {
                    deleteFile(targetPath, targetFileName);
                } catch (Exception e) {
                    logger.warn("回滚复制的文件失败: {}/{}", targetPath, targetFileName, e);
                }
                return false;
            }

            logger.debug("移动Zookeeper文件成功: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);
            return true;

        } catch (Exception e) {
            logger.error("移动Zookeeper文件失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "移动Zookeeper文件失败: " + e.getMessage(),
                e
            );
        }
    }

    private String buildDirectoryPath(String directoryPath) {
        // 如果是绝对路径（Windows或Unix），转换为相对路径
        String normalizedPath = normalizeToZkPath(directoryPath);
        if (normalizedPath.equals("/")) {
            return config.getRootPath();
        }
        return config.getRootPath() + normalizedPath;
    }

    /**
     * 将绝对路径转换为Zookeeper路径格式
     */
    private String normalizeToZkPath(String path) {
        if (path == null || path.isEmpty()) {
            return "/";
        }

        // 处理Windows绝对路径 (如 C:\path\to\file)
        if (path.length() > 1 && path.charAt(1) == ':') {
            // 移除驱动器字母和冒号，转换反斜杠为正斜杠
            path = path.substring(2).replace('\\', '/');
        }

        // 确保以斜杠开头
        String normalizedPath = path.startsWith("/") ? path : "/" + path;

        return normalizedPath;
    }

    private String getParentPath(String fullPath) {
        int lastSlashIndex = fullPath.lastIndexOf('/');
        return lastSlashIndex > 0 ? fullPath.substring(0, lastSlashIndex) : "/";
    }
}
