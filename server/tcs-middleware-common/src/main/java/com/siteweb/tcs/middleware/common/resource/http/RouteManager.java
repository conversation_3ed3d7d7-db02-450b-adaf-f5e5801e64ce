package com.siteweb.tcs.middleware.common.resource.http;

import com.siteweb.tcs.middleware.common.model.http.HttpServerRequest;
import com.siteweb.tcs.middleware.common.model.http.HttpServerResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 路由管理器
 * 负责管理HTTP路由的注册、匹配和调用
 */
public class RouteManager {

    private static final Logger logger = LoggerFactory.getLogger(RouteManager.class);

    /**
     * 路由信息
     */
    public static class RouteInfo {
        private final String path;
        private final String method;
        private final Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> asyncHandler;

        public RouteInfo(String path, String method, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> asyncHandler) {
            this.path = path;
            this.method = method;
            this.asyncHandler = asyncHandler;
        }

        public String getPath() {
            return path;
        }

        public String getMethod() {
            return method;
        }

        public Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> getAsyncHandler() {
            return asyncHandler;
        }

        public boolean matches(String requestPath, String requestMethod) {
            return pathMatches(requestPath) && methodMatches(requestMethod);
        }

        private boolean pathMatches(String requestPath) {
            // 简单的路径匹配，支持精确匹配和通配符
            if (path.equals(requestPath)) {
                return true;
            }
            
            // 支持路径参数匹配（如 /api/{id} 匹配 /api/123）
            if (path.contains("{") && path.contains("}")) {
                return pathParameterMatches(requestPath);
            }
            
            return false;
        }

        private boolean pathParameterMatches(String requestPath) {
            String[] pathSegments = path.split("/");
            String[] requestSegments = requestPath.split("/");
            
            if (pathSegments.length != requestSegments.length) {
                return false;
            }
            
            for (int i = 0; i < pathSegments.length; i++) {
                String pathSegment = pathSegments[i];
                String requestSegment = requestSegments[i];
                
                // 如果是参数段（包含{}），则跳过匹配
                if (pathSegment.startsWith("{") && pathSegment.endsWith("}")) {
                    continue;
                }
                
                // 普通段必须精确匹配
                if (!pathSegment.equals(requestSegment)) {
                    return false;
                }
            }
            
            return true;
        }

        private boolean methodMatches(String requestMethod) {
            return method == null || method.equalsIgnoreCase("ALL") || method.equalsIgnoreCase(requestMethod);
        }
    }

    // 存储所有路由信息
    private final Map<String, RouteInfo> routes = new ConcurrentHashMap<>();

    /**
     * 注册异步路由
     *
     * @param path 路径
     * @param method HTTP方法（GET、POST等，null表示所有方法）
     * @param asyncHandler 异步处理器
     * @return 路由键值
     */
    public String registerAsyncRoute(String path, String method, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> asyncHandler) {
        String routeKey = generateRouteKey(path, method);
        RouteInfo routeInfo = new RouteInfo(path, method, asyncHandler);
        routes.put(routeKey, routeInfo);

        logger.debug("注册异步路由: {} {} -> {}", method != null ? method : "ALL", path, routeKey);
        return routeKey;
    }

    /**
     * 移除路由
     *
     * @param path 路径
     * @param method HTTP方法
     * @return 是否成功移除
     */
    public boolean unregisterRoute(String path, String method) {
        String routeKey = generateRouteKey(path, method);
        RouteInfo removed = routes.remove(routeKey);
        
        if (removed != null) {
            logger.debug("移除路由: {} {} -> {}", method != null ? method : "ALL", path, routeKey);
            return true;
        }
        
        return false;
    }

    /**
     * 移除指定路径的所有路由
     *
     * @param path 路径
     * @return 移除的路由数量
     */
    public int unregisterAllRoutes(String path) {
        int count = 0;
        Iterator<Map.Entry<String, RouteInfo>> iterator = routes.entrySet().iterator();
        
        while (iterator.hasNext()) {
            Map.Entry<String, RouteInfo> entry = iterator.next();
            if (entry.getValue().getPath().equals(path)) {
                iterator.remove();
                count++;
                logger.debug("移除路由: {} {}", entry.getValue().getMethod(), path);
            }
        }
        
        return count;
    }

    /**
     * 查找匹配的路由
     *
     * @param requestPath 请求路径
     * @param requestMethod 请求方法
     * @return 匹配的路由信息，如果没有找到返回null
     */
    public RouteInfo findRoute(String requestPath, String requestMethod) {
        // 首先尝试精确匹配
        String exactKey = generateRouteKey(requestPath, requestMethod);
        RouteInfo exactMatch = routes.get(exactKey);
        if (exactMatch != null) {
            return exactMatch;
        }

        // 尝试通用方法匹配（ALL方法）
        String generalKey = generateRouteKey(requestPath, null);
        RouteInfo generalMatch = routes.get(generalKey);
        if (generalMatch != null) {
            return generalMatch;
        }

        // 尝试模式匹配（路径参数等）
        for (RouteInfo routeInfo : routes.values()) {
            if (routeInfo.matches(requestPath, requestMethod)) {
                return routeInfo;
            }
        }

        return null;
    }

    /**
     * 获取所有已注册的路径
     *
     * @return 路径列表
     */
    public List<String> getAllPaths() {
        Set<String> paths = new HashSet<>();
        for (RouteInfo routeInfo : routes.values()) {
            paths.add(routeInfo.getPath());
        }
        return new ArrayList<>(paths);
    }

    /**
     * 获取所有路由信息
     *
     * @return 路由信息列表
     */
    public List<RouteInfo> getAllRoutes() {
        return new ArrayList<>(routes.values());
    }

    /**
     * 清空所有路由
     */
    public void clear() {
        routes.clear();
        logger.debug("清空所有路由");
    }

    /**
     * 获取路由数量
     *
     * @return 路由数量
     */
    public int size() {
        return routes.size();
    }

    /**
     * 生成路由键值
     *
     * @param path 路径
     * @param method HTTP方法
     * @return 路由键值
     */
    private String generateRouteKey(String path, String method) {
        String normalizedMethod = method != null ? method.toUpperCase() : "ALL";
        return normalizedMethod + ":" + path;
    }
}
