package com.siteweb.tcs.middleware.common.resource.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * HTTP服务器统计管理器
 * 负责收集和管理HTTP请求统计数据，包括流量统计和超时错误统计
 */
public class StatisticsManager {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsManager.class);

    // 请求总数计数器
    private final AtomicLong totalRequestCount = new AtomicLong(0);

    // 按来源IP分组的请求计数
    private final Map<String, AtomicLong> requestCountByIP = new ConcurrentHashMap<>();

    // 流量统计管理器
    private final TrafficStatisticsManager trafficManager;

    // 超时错误管理器
    private final TimeoutErrorManager timeoutErrorManager;

    /**
     * 构造函数
     */
    public StatisticsManager() {
        this.trafficManager = new TrafficStatisticsManager();
        this.timeoutErrorManager = new TimeoutErrorManager(1000, 24); // 最多1000条记录，保留24小时
    }

    /**
     * 构造函数（可配置参数）
     */
    public StatisticsManager(int maxTimeoutRecords, int timeoutRetentionHours) {
        this.trafficManager = new TrafficStatisticsManager();
        this.timeoutErrorManager = new TimeoutErrorManager(maxTimeoutRecords, timeoutRetentionHours);
    }

    /**
     * 记录一次请求
     *
     * @param sourceIP 来源IP地址
     */
    public void recordRequest(String sourceIP) {
        // 增加总请求数
        totalRequestCount.incrementAndGet();

        // 增加该IP的请求数
        if (sourceIP != null && !sourceIP.trim().isEmpty()) {
            requestCountByIP.computeIfAbsent(sourceIP, k -> new AtomicLong(0)).incrementAndGet();
        }

        logger.trace("记录请求: sourceIP={}, totalCount={}", sourceIP, totalRequestCount.get());
    }

    /**
     * 记录请求流量
     *
     * @param sourceIP 来源IP地址
     * @param requestBytes 请求字节数
     */
    public void recordRequestWithTraffic(String sourceIP, long requestBytes) {
        recordRequest(sourceIP);
        trafficManager.recordRequest(requestBytes);
    }

    /**
     * 记录响应流量
     *
     * @param responseBytes 响应字节数
     * @param responseTimeMs 响应时间（毫秒）
     */
    public void recordResponse(long responseBytes, long responseTimeMs) {
        trafficManager.recordResponse(responseBytes, responseTimeMs);
    }

    /**
     * 记录超时错误
     *
     * @param sourceIP 源IP
     * @param requestPath 请求路径
     * @param requestMethod HTTP方法
     * @param timeoutMs 超时时间（毫秒）
     * @param errorMessage 错误信息
     */
    public void recordTimeoutError(String sourceIP, String requestPath, String requestMethod,
                                 long timeoutMs, String errorMessage) {
        timeoutErrorManager.recordTimeoutError(sourceIP, requestPath, requestMethod, timeoutMs, errorMessage);
    }

    /**
     * 获取请求总数
     *
     * @return 请求总数
     */
    public long getTotalRequestCount() {
        return totalRequestCount.get();
    }

    /**
     * 获取所有来源IP列表
     *
     * @return 来源IP列表
     */
    public List<String> getSourceIPs() {
        return new ArrayList<>(requestCountByIP.keySet());
    }

    /**
     * 获取指定IP的请求次数
     *
     * @param sourceIP 来源IP
     * @return 请求次数，如果IP不存在返回0
     */
    public long getRequestCountBySourceIP(String sourceIP) {
        AtomicLong count = requestCountByIP.get(sourceIP);
        return count != null ? count.get() : 0;
    }

    /**
     * 获取按来源IP分组的请求统计
     *
     * @return IP到请求次数的映射
     */
    public Map<String, Long> getRequestCountBySourceIPMap() {
        Map<String, Long> result = new HashMap<>();
        for (Map.Entry<String, AtomicLong> entry : requestCountByIP.entrySet()) {
            result.put(entry.getKey(), entry.getValue().get());
        }
        return result;
    }

    // 注意：解析失败记录功能已移至Service层的ParsingFailureManager

    /**
     * 获取流量统计摘要
     */
    public Map<String, Object> getTrafficSummary() {
        return trafficManager.getTrafficSummary();
    }

    /**
     * 获取超时错误统计摘要
     */
    public Map<String, Object> getTimeoutErrorSummary() {
        return timeoutErrorManager.getTimeoutErrorSummary();
    }

    /**
     * 获取超时错误记录
     */
    public List<TimeoutErrorManager.TimeoutErrorRecord> getTimeoutErrorRecords() {
        return timeoutErrorManager.getTimeoutErrorRecords();
    }

    /**
     * 获取过滤后的超时错误记录
     */
    public List<TimeoutErrorManager.TimeoutErrorRecord> getTimeoutErrorRecords(String sourceIP, Long startTime, Long endTime) {
        return timeoutErrorManager.getTimeoutErrorRecords(sourceIP, startTime, endTime);
    }

    /**
     * 获取当前QPS
     */
    public double getCurrentQPS() {
        return trafficManager.getCurrentQPS();
    }

    /**
     * 获取当前请求速率（KB/s）
     */
    public double getCurrentRequestRate() {
        return trafficManager.getCurrentRequestRate();
    }

    /**
     * 获取当前响应速率（KB/s）
     */
    public double getCurrentResponseRate() {
        return trafficManager.getCurrentResponseRate();
    }

    /**
     * 获取平均响应时间（毫秒）
     */
    public double getAverageResponseTime() {
        return trafficManager.getAverageResponseTime();
    }

    /**
     * 清空所有统计数据
     */
    public void clearStatistics() {
        totalRequestCount.set(0);
        requestCountByIP.clear();
        trafficManager.clearStatistics();
        timeoutErrorManager.clearStatistics();
        logger.info("清空所有统计数据");
    }

    /**
     * 关闭统计管理器
     */
    public void shutdown() {
        trafficManager.shutdown();
        timeoutErrorManager.shutdown();
    }

    /**
     * 获取统计摘要信息
     *
     * @return 统计摘要
     */
    public Map<String, Object> getStatisticsSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalRequestCount", getTotalRequestCount());
        summary.put("uniqueSourceIPCount", requestCountByIP.size());

        // 获取请求最多的前5个IP
        List<Map.Entry<String, AtomicLong>> topIPs = requestCountByIP.entrySet().stream()
                .sorted(Map.Entry.<String, AtomicLong>comparingByValue((a, b) -> Long.compare(b.get(), a.get())))
                .limit(5)
                .toList();

        Map<String, Long> topIPsMap = new HashMap<>();
        for (Map.Entry<String, AtomicLong> entry : topIPs) {
            topIPsMap.put(entry.getKey(), entry.getValue().get());
        }
        summary.put("topSourceIPs", topIPsMap);

        // 添加流量统计
        summary.put("traffic", getTrafficSummary());

        // 添加超时错误统计
        summary.put("timeoutErrors", getTimeoutErrorSummary());

        return summary;
    }
}
