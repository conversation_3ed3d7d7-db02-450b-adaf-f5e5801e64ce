package com.siteweb.tcs.middleware.common.resource.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 超时错误管理器
 * 负责记录和统计HTTP请求超时错误
 */
public class TimeoutErrorManager {

    private static final Logger logger = LoggerFactory.getLogger(TimeoutErrorManager.class);

    // 超时错误统计
    private final AtomicLong totalTimeoutCount = new AtomicLong(0);
    private final Map<String, AtomicLong> timeoutCountByIP = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> timeoutCountByPath = new ConcurrentHashMap<>();

    // 超时错误记录（限制数量防止内存溢出）
    private final List<TimeoutErrorRecord> timeoutRecords = Collections.synchronizedList(new ArrayList<>());
    private final int maxRecords;
    private final int retentionHours;

    // 定时清理任务
    private final ScheduledExecutorService cleanupExecutor;

    public TimeoutErrorManager(int maxRecords, int retentionHours) {
        this.maxRecords = maxRecords > 0 ? maxRecords : 1000;
        this.retentionHours = retentionHours > 0 ? retentionHours : 24;
        
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "TimeoutErrorCleanup");
            t.setDaemon(true);
            return t;
        });
        
        // 启动定期清理任务
        if (retentionHours > 0) {
            cleanupExecutor.scheduleWithFixedDelay(this::cleanupExpiredRecords, 1, 1, TimeUnit.HOURS);
        }
    }

    /**
     * 记录超时错误
     *
     * @param sourceIP 源IP
     * @param requestPath 请求路径
     * @param requestMethod HTTP方法
     * @param timeoutMs 超时时间（毫秒）
     * @param errorMessage 错误信息
     */
    public void recordTimeoutError(String sourceIP, String requestPath, String requestMethod, 
                                 long timeoutMs, String errorMessage) {
        // 增加总超时计数
        totalTimeoutCount.incrementAndGet();
        
        // 按IP统计
        if (sourceIP != null && !sourceIP.trim().isEmpty()) {
            timeoutCountByIP.computeIfAbsent(sourceIP, k -> new AtomicLong(0)).incrementAndGet();
        }
        
        // 按路径统计
        if (requestPath != null && !requestPath.trim().isEmpty()) {
            timeoutCountByPath.computeIfAbsent(requestPath, k -> new AtomicLong(0)).incrementAndGet();
        }
        
        // 创建超时错误记录
        TimeoutErrorRecord record = new TimeoutErrorRecord(
            sourceIP, requestPath, requestMethod, timeoutMs, errorMessage, System.currentTimeMillis()
        );
        
        synchronized (timeoutRecords) {
            // 检查是否达到最大记录数限制
            if (timeoutRecords.size() >= maxRecords) {
                // 移除最旧的记录
                timeoutRecords.remove(0);
                logger.debug("超时错误记录数达到上限{}，移除最旧记录", maxRecords);
            }
            
            timeoutRecords.add(record);
        }
        
        logger.warn("记录超时错误: sourceIP={}, path={}, method={}, timeout={}ms, error={}", 
                   sourceIP, requestPath, requestMethod, timeoutMs, errorMessage);
    }

    /**
     * 获取总超时错误数
     */
    public long getTotalTimeoutCount() {
        return totalTimeoutCount.get();
    }

    /**
     * 获取按IP分组的超时错误统计
     */
    public Map<String, Long> getTimeoutCountByIP() {
        Map<String, Long> result = new ConcurrentHashMap<>();
        timeoutCountByIP.forEach((ip, count) -> result.put(ip, count.get()));
        return result;
    }

    /**
     * 获取按路径分组的超时错误统计
     */
    public Map<String, Long> getTimeoutCountByPath() {
        Map<String, Long> result = new ConcurrentHashMap<>();
        timeoutCountByPath.forEach((path, count) -> result.put(path, count.get()));
        return result;
    }

    /**
     * 获取指定IP的超时错误数
     */
    public long getTimeoutCountByIP(String sourceIP) {
        AtomicLong count = timeoutCountByIP.get(sourceIP);
        return count != null ? count.get() : 0;
    }

    /**
     * 获取所有超时错误记录
     */
    public List<TimeoutErrorRecord> getTimeoutErrorRecords() {
        synchronized (timeoutRecords) {
            return new ArrayList<>(timeoutRecords);
        }
    }

    /**
     * 获取过滤后的超时错误记录
     *
     * @param sourceIP 源IP过滤（可为null）
     * @param startTime 开始时间过滤（毫秒时间戳，可为null）
     * @param endTime 结束时间过滤（毫秒时间戳，可为null）
     * @return 过滤后的记录列表
     */
    public List<TimeoutErrorRecord> getTimeoutErrorRecords(String sourceIP, Long startTime, Long endTime) {
        List<TimeoutErrorRecord> allRecords = getTimeoutErrorRecords();
        List<TimeoutErrorRecord> filteredRecords = new ArrayList<>();
        
        for (TimeoutErrorRecord record : allRecords) {
            // 源IP过滤
            if (sourceIP != null && !sourceIP.equals(record.getSourceIP())) {
                continue;
            }
            
            // 时间范围过滤
            long recordTime = record.getTimestamp();
            if (startTime != null && recordTime < startTime) {
                continue;
            }
            if (endTime != null && recordTime > endTime) {
                continue;
            }
            
            filteredRecords.add(record);
        }
        
        return filteredRecords;
    }

    /**
     * 获取超时错误统计摘要
     */
    public Map<String, Object> getTimeoutErrorSummary() {
        Map<String, Object> summary = new ConcurrentHashMap<>();
        summary.put("totalTimeoutCount", getTotalTimeoutCount());
        summary.put("timeoutCountByIP", getTimeoutCountByIP());
        summary.put("timeoutCountByPath", getTimeoutCountByPath());
        summary.put("recordCount", timeoutRecords.size());
        summary.put("maxRecords", maxRecords);
        summary.put("retentionHours", retentionHours);
        return summary;
    }

    /**
     * 清空超时错误统计
     */
    public void clearStatistics() {
        totalTimeoutCount.set(0);
        timeoutCountByIP.clear();
        timeoutCountByPath.clear();
        timeoutRecords.clear();
        logger.info("超时错误统计数据已清空");
    }

    /**
     * 关闭超时错误管理器
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 清理过期的超时错误记录
     */
    private void cleanupExpiredRecords() {
        if (retentionHours <= 0) {
            return;
        }
        
        long expireThreshold = System.currentTimeMillis() - (retentionHours * 60 * 60 * 1000L);
        
        synchronized (timeoutRecords) {
            timeoutRecords.removeIf(record -> record.getTimestamp() < expireThreshold);
        }
        
        logger.debug("清理过期的超时错误记录，保留时间阈值: {}", expireThreshold);
    }

    /**
     * 超时错误记录类
     */
    public static class TimeoutErrorRecord {
        private final String sourceIP;
        private final String requestPath;
        private final String requestMethod;
        private final long timeoutMs;
        private final String errorMessage;
        private final long timestamp;

        public TimeoutErrorRecord(String sourceIP, String requestPath, String requestMethod,
                                long timeoutMs, String errorMessage, long timestamp) {
            this.sourceIP = sourceIP;
            this.requestPath = requestPath;
            this.requestMethod = requestMethod;
            this.timeoutMs = timeoutMs;
            this.errorMessage = errorMessage;
            this.timestamp = timestamp;
        }

        // Getters
        public String getSourceIP() { return sourceIP; }
        public String getRequestPath() { return requestPath; }
        public String getRequestMethod() { return requestMethod; }
        public long getTimeoutMs() { return timeoutMs; }
        public String getErrorMessage() { return errorMessage; }
        public long getTimestamp() { return timestamp; }

        @Override
        public String toString() {
            return String.format("TimeoutErrorRecord{sourceIP='%s', path='%s', method='%s', timeout=%dms, error='%s', timestamp=%d}",
                    sourceIP, requestPath, requestMethod, timeoutMs, errorMessage, timestamp);
        }
    }
}
