package com.siteweb.tcs.middleware.common.resource.http.netty;

import com.siteweb.tcs.middleware.common.model.http.HttpServerResponse;
import io.netty.buffer.Unpooled;
import io.netty.handler.codec.http.*;
import io.netty.util.CharsetUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * Netty HTTP响应适配器
 * 将通用的HttpServerResponse接口适配为Netty的FullHttpResponse
 */
public class NettyHttpResponseAdapter implements HttpServerResponse {

    private int status = 200;
    private final Map<String, String> headers = new HashMap<>();
    private String body = "";
    private byte[] bodyBytes = null;
    private boolean completed = false;

    @Override
    public HttpServerResponse setStatus(int status) {
        this.status = status;
        return this;
    }

    @Override
    public int getStatus() {
        return status;
    }

    @Override
    public HttpServerResponse setHeader(String name, String value) {
        headers.put(name, value);
        return this;
    }

    @Override
    public HttpServerResponse addHeader(String name, String value) {
        // 简化实现：直接覆盖，实际应该支持多值
        headers.put(name, value);
        return this;
    }

    @Override
    public String getHeader(String name) {
        return headers.get(name);
    }

    @Override
    public Map<String, String> getHeaders() {
        return new HashMap<>(headers);
    }

    @Override
    public HttpServerResponse setBody(String body) {
        this.body = body != null ? body : "";
        this.bodyBytes = null; // 清除字节缓存
        return this;
    }

    @Override
    public HttpServerResponse setBody(byte[] body) {
        this.bodyBytes = body != null ? body : new byte[0];
        this.body = null; // 清除字符串缓存
        return this;
    }

    @Override
    public String getBody() {
        if (body != null) {
            return body;
        } else if (bodyBytes != null) {
            return new String(bodyBytes, CharsetUtil.UTF_8);
        }
        return "";
    }

    @Override
    public byte[] getBodyBytes() {
        if (bodyBytes != null) {
            return bodyBytes;
        } else if (body != null) {
            return body.getBytes(CharsetUtil.UTF_8);
        }
        return new byte[0];
    }

    @Override
    public HttpServerResponse setContentType(String contentType) {
        return setHeader(HttpHeaderNames.CONTENT_TYPE.toString(), contentType);
    }

    @Override
    public String getContentType() {
        return getHeader(HttpHeaderNames.CONTENT_TYPE.toString());
    }

    @Override
    public HttpServerResponse setJson(String json) {
        return setContentType("application/json; charset=utf-8").setBody(json);
    }

    @Override
    public HttpServerResponse setHtml(String html) {
        return setContentType("text/html; charset=utf-8").setBody(html);
    }

    @Override
    public HttpServerResponse setText(String text) {
        return setContentType("text/plain; charset=utf-8").setBody(text);
    }

    @Override
    public HttpServerResponse setXml(String xml) {
        return setContentType("application/xml; charset=utf-8").setBody(xml);
    }

    @Override
    public HttpServerResponse ok(String body) {
        return setStatus(200).setBody(body);
    }

    @Override
    public HttpServerResponse created(String body) {
        return setStatus(201).setBody(body);
    }

    @Override
    public HttpServerResponse noContent() {
        return setStatus(204).setBody("");
    }

    @Override
    public HttpServerResponse badRequest(String message) {
        return setStatus(400).setText(message);
    }

    @Override
    public HttpServerResponse unauthorized(String message) {
        return setStatus(401).setText(message);
    }

    @Override
    public HttpServerResponse forbidden(String message) {
        return setStatus(403).setText(message);
    }

    @Override
    public HttpServerResponse notFound(String message) {
        return setStatus(404).setText(message);
    }

    @Override
    public HttpServerResponse internalServerError(String message) {
        return setStatus(500).setText(message);
    }

    @Override
    public boolean isCompleted() {
        return completed;
    }

    @Override
    public void complete() {
        completed = true;
    }

    /**
     * 转换为Netty的FullHttpResponse
     *
     * @return Netty HTTP响应对象
     */
    public FullHttpResponse toNettyResponse() {
        // 创建响应体
        byte[] responseBody = getBodyBytes();
        FullHttpResponse response = new DefaultFullHttpResponse(
            HttpVersion.HTTP_1_1,
            HttpResponseStatus.valueOf(status),
            Unpooled.wrappedBuffer(responseBody)
        );

        // 设置响应头
        HttpHeaders responseHeaders = response.headers();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            responseHeaders.set(entry.getKey(), entry.getValue());
        }

        // 设置Content-Length
        responseHeaders.setInt(HttpHeaderNames.CONTENT_LENGTH, responseBody.length);

        // 如果没有设置Content-Type，设置默认值
        if (!responseHeaders.contains(HttpHeaderNames.CONTENT_TYPE)) {
            responseHeaders.set(HttpHeaderNames.CONTENT_TYPE, "text/plain; charset=utf-8");
        }

        return response;
    }
}
