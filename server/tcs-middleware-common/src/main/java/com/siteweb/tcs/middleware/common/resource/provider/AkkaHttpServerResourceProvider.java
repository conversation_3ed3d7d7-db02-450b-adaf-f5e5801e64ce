package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.AkkaHttpServerConfig;
import com.siteweb.tcs.middleware.common.resource.AkkaHttpServerResource;
import com.siteweb.tcs.middleware.common.resource.HttpServerResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Akka HTTP服务器资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class AkkaHttpServerResourceProvider extends AbstractResourceProvider<AkkaHttpServerResource, AkkaHttpServerConfig> {

    @Override
    public String getType() {
        return ResourceType.AKKA_HTTP_SERVER.getCode();
    }

    @Override
    protected Class<AkkaHttpServerConfig> getConfigClass() {
        return AkkaHttpServerConfig.class;
    }

    @Override
    protected void validateConfigObject(AkkaHttpServerConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证主机地址
        if (!StringUtils.hasText(config.getHost())) {
            errors.add("主机地址不能为空");
        }

        // 验证端口
        if (config.getPort() <= 0 || config.getPort() > 65535) {
            errors.add("端口必须在1-65535之间");
        }

        // 验证空闲超时时间
        if (config.getIdleTimeout() <= 0) {
            errors.add("空闲超时时间必须大于0");
        }

        // 验证连接队列大小
        if (config.getBacklog() <= 0) {
            errors.add("连接队列大小必须大于0");
        }

        // 验证请求超时时间
        if (config.getRequestTimeoutMs() <= 0) {
            errors.add("请求超时时间必须大于0");
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_CONFIG_INVALID,
                "Akka HTTP服务器配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            AkkaHttpServerConfig httpConfig = convertMapToConfig(config);
            validateConfigObject(httpConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证Akka HTTP服务器配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证Akka HTTP服务器配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            AkkaHttpServerConfig httpConfig = convertMapToConfig(config);

            // 检查端口是否可用
            try (Socket socket = new Socket()) {
                // 尝试绑定端口，如果端口已被占用，则会抛出异常
                socket.bind(new InetSocketAddress(httpConfig.getHost(), httpConfig.getPort()));

                Map<String, Object> details = new HashMap<>();
                details.put("host", httpConfig.getHost());
                details.put("port", httpConfig.getPort());
                details.put("idleTimeout", httpConfig.getIdleTimeout() + "s");
                details.put("backlog", httpConfig.getBacklog());
                details.put("requestTimeoutMs", httpConfig.getRequestTimeoutMs() + "ms");

                return ConnectionTestResult.success("端口可用", details);
            } catch (Exception e) {
                return ConnectionTestResult.failure("端口已被占用: " + e.getMessage());
            }
        } catch (Exception e) {
            logger.error("测试Akka HTTP服务器连接失败", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        }
    }

    @Override
    protected AkkaHttpServerResource doCreateResource(String id, String name, String description, AkkaHttpServerConfig config)
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建Akka HTTP服务器资源: id={}, name={}, host={}:{}",
                id, name, config.getHost(), config.getPort());

            // 创建Akka HTTP服务器资源
            AkkaHttpServerResource resource = new AkkaHttpServerResource(
                id,
                getType(),
                name,
                description,
                config.getHost(),
                config.getPort(),
                config.getIdleTimeout(),
                config.getBacklog(),
                config.getRequestTimeoutMs()
            );

            logger.info("Akka HTTP服务器资源创建成功: id={}, name={}", id, name);
            return resource;

        } catch (Exception e) {
            logger.error("创建Akka HTTP服务器资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建Akka HTTP服务器资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(AkkaHttpServerConfig config) {
        return String.format("AkkaHttpServerConfig{host='%s', port=%d, idleTimeout=%d, backlog=%d, requestTimeoutMs=%d}",
            config.getHost(), config.getPort(), config.getIdleTimeout(), config.getBacklog(), config.getRequestTimeoutMs());
    }

    @Override
    public void destroyResource(Resource resource) throws MiddlewareTechnicalException {
        if (resource instanceof HttpServerResource) {
            try {
                logger.info("开始销毁Akka HTTP服务器资源: {}", resource.getId());
                // 调用资源的destroy方法
                resource.destroy();
                logger.info("Akka HTTP服务器资源销毁成功: {}", resource.getId());
            } catch (Exception e) {
                logger.error("销毁Akka HTTP服务器资源失败: {}", resource.getId(), e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTRUCTION_FAILED,
                    "销毁Akka HTTP服务器资源失败: " + e.getMessage(),
                    e
                );
            }
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "资源类型不匹配，期望HttpServerResource，实际为" + resource.getClass().getName()
            );
        }
    }
}
