package com.siteweb.tcs.middleware.common.resource.provider;

import com.ecwid.consul.v1.ConsulClient;
import com.ecwid.consul.v1.Response;
import com.siteweb.tcs.middleware.common.model.config.ConsulFileSystemConfig;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import com.siteweb.tcs.middleware.common.resource.ConsulFileSystemResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Consul文件系统资源提供者
 */
@Component
public class ConsulFileSystemResourceProvider extends AbstractResourceProvider<ConsulFileSystemResource, ConsulFileSystemConfig> {

    @Override
    public String getType() {
        return ResourceType.CONSUL_FILESYSTEM.getCode();
    }

    @Override
    protected Class<ConsulFileSystemConfig> getConfigClass() {
        return ConsulFileSystemConfig.class;
    }

    @Override
    protected void validateConfigObject(ConsulFileSystemConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);

        // 验证主机
        if (!StringUtils.hasText(config.getHost())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Consul主机不能为空"
            );
        }

        // 验证端口
        if (config.getPort() <= 0 || config.getPort() > 65535) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Consul端口必须在1-65535之间"
            );
        }

        // 验证协议方案
        if (!StringUtils.hasText(config.getScheme())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "协议方案不能为空"
            );
        }

        if (!"http".equalsIgnoreCase(config.getScheme()) && !"https".equalsIgnoreCase(config.getScheme())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "协议方案必须是http或https"
            );
        }

        // 验证数据中心
        if (!StringUtils.hasText(config.getDatacenter())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "数据中心不能为空"
            );
        }

        // 验证根路径
        if (!StringUtils.hasText(config.getRootPath())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "根路径不能为空"
            );
        }

        // 验证超时时间
        if (config.getConnectTimeout() <= 0) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "连接超时时间必须大于0"
            );
        }

        if (config.getReadTimeout() <= 0) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "读取超时时间必须大于0"
            );
        }

        if (config.getWriteTimeout() <= 0) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "写入超时时间必须大于0"
            );
        }

        // 验证最大数据大小
        if (config.getMaxDataSize() <= 0) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "最大数据大小必须大于0"
            );
        }

        // 验证SSL配置
        if (config.isEnableSsl()) {
            if (!StringUtils.hasText(config.getSslCertPath())) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "启用SSL时证书路径不能为空"
                );
            }
            if (!StringUtils.hasText(config.getSslKeyPath())) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "启用SSL时密钥路径不能为空"
                );
            }
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            ConsulFileSystemConfig consulConfig = convertMapToConfig(config);
            validateConfigObject(consulConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证Consul文件系统配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证Consul文件系统配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        ConsulClient testClient = null;
        try {
            // 转换配置
            ConsulFileSystemConfig consulConfig = convertMapToConfig(config);
            
            // 创建临时Consul客户端进行连接测试
            testClient = new ConsulClient(consulConfig.getHost(), consulConfig.getPort());

            // 测试基本操作
            Response<List<String>> response = testClient.getKVKeysOnly(consulConfig.getRootPath());
            
            if (response != null) {
                return ConnectionTestResult.success("Consul连接测试成功");
            } else {
                return ConnectionTestResult.failure("Consul连接测试失败：无响应");
            }
            
        } catch (MiddlewareTechnicalException e) {
            logger.error("Consul连接测试失败", e);
            return ConnectionTestResult.failure("连接测试失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Consul连接测试异常", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        }
    }

    @Override
    protected ConsulFileSystemResource doCreateResource(String id, String name, String description, 
                                                      ConsulFileSystemConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建Consul文件系统资源: id={}, name={}", id, name);
            
            // 记录配置信息（隐藏敏感信息）
            logConfig(config);
            
            // 创建Consul文件系统资源
            ConsulFileSystemResource resource = new ConsulFileSystemResource(
                id,
                name,
                description,
                config
            );
            
            logger.info("Consul文件系统资源创建成功: id={}, name={}, host={}, port={}, rootPath={}", 
                id, name, config.getHost(), config.getPort(), config.getRootPath());
            
            return resource;
            
        } catch (Exception e) {
            logger.error("创建Consul文件系统资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建Consul文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public void destroyResource(Resource resource) throws MiddlewareTechnicalException {
        if (resource instanceof ConsulFileSystemResource) {
            try {
                logger.info("开始销毁Consul文件系统资源: {}", resource.getId());
                // 调用资源的destroy方法
                resource.destroy();
                logger.info("Consul文件系统资源销毁成功: {}", resource.getId());
            } catch (Exception e) {
                logger.error("销毁Consul文件系统资源失败: {}", resource.getId(), e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTRUCTION_FAILED,
                    "销毁Consul文件系统资源失败: " + e.getMessage(),
                    e
                );
            }
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "资源类型不匹配，期望ConsulFileSystemResource，实际为" + resource.getClass().getName()
            );
        }
    }

    @Override
    protected String getConfigString(ConsulFileSystemConfig config) {
        if (config == null) {
            return "null";
        }
        // 隐藏敏感信息，只显示关键配置
        return String.format("ConsulFileSystemConfig{host='%s', port=%d, scheme='%s', datacenter='%s', rootPath='%s', enableSsl=%s}", 
            config.getHost(), config.getPort(), config.getScheme(), config.getDatacenter(), config.getRootPath(), config.isEnableSsl());
    }
}
