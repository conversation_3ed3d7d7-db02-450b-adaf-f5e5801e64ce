package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.MosMQTTConfig;
import com.siteweb.tcs.middleware.common.resource.MosMQTTResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * Mosquitto MQTT资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class MosMQTTResourceProvider extends AbstractResourceProvider<MosMQTTResource, MosMQTTConfig> {

    @Override
    public String getType() {
        return ResourceType.MQTT.getCode();
    }

    @Override
    protected Class<MosMQTTConfig> getConfigClass() {
        return MosMQTTConfig.class;
    }

    @Override
    protected void validateConfigObject(MosMQTTConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证服务器地址
        if (!StringUtils.hasText(config.getServerUri())) {
            errors.add("服务器地址不能为空");
        }

        // 验证QoS级别
        if (config.getDefaultQos() < 0 || config.getDefaultQos() > 2) {
            errors.add("QoS级别必须在0-2之间");
        }

        // 验证连接超时时间
        if (config.getConnectionTimeout() <= 0) {
            errors.add("连接超时时间必须大于0");
        }

        // 验证保持连接时间
        if (config.getKeepAliveInterval() <= 0) {
            errors.add("保持连接时间必须大于0");
        }

        // 验证SSL配置
        if (config.isUseSsl()) {
            if (!StringUtils.hasText(config.getTrustStore())) {
                errors.add("使用SSL时，信任库路径不能为空");
            }
            if (!StringUtils.hasText(config.getTrustStorePassword())) {
                errors.add("使用SSL时，信任库密码不能为空");
            }
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_CONFIG_INVALID,
                "MQTT配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            MosMQTTConfig mqttConfig = convertMapToConfig(config);
            validateConfigObject(mqttConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证MQTT配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证MQTT配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            MosMQTTConfig mqttConfig = convertMapToConfig(config);

            // 创建临时客户端进行连接测试
            String tempClientId = mqttConfig.getClientId();
            if (!StringUtils.hasText(tempClientId)) {
                tempClientId = "tcs-test-" + UUID.randomUUID().toString().replace("-", "");
            }

            MqttClient testClient = new MqttClient(mqttConfig.getServerUri(), tempClientId);
            MqttConnectOptions connectOptions = createConnectOptions(mqttConfig);

            try {
                testClient.connect(connectOptions);
                
                Map<String, Object> details = new HashMap<>();
                details.put("serverUri", mqttConfig.getServerUri());
                details.put("clientId", tempClientId);
                details.put("defaultQos", mqttConfig.getDefaultQos());
                details.put("useSsl", mqttConfig.isUseSsl());

                return ConnectionTestResult.success("连接成功", details);
            } finally {
                if (testClient.isConnected()) {
                    testClient.disconnect();
                }
                testClient.close();
            }
        } catch (MqttException e) {
            logger.error("测试MQTT连接失败: 错误码={}", e.getReasonCode(), e);
            return ConnectionTestResult.failure("连接失败: 错误码=" + e.getReasonCode() + ", 原因=" + e.getMessage());
        } catch (Exception e) {
            logger.error("测试MQTT连接失败", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        }
    }

    @Override
    protected MosMQTTResource doCreateResource(String id, String name, String description, MosMQTTConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建MQTT资源: id={}, name={}, serverUri={}", 
                id, name, config.getServerUri());

            // 生成客户端ID（如果未提供）
            String clientId = config.getClientId();
            if (!StringUtils.hasText(clientId)) {
                clientId = "tcs-" + id + "-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                config.setClientId(clientId);
            }

            // 创建MQTT客户端
            MqttClient mqttClient = new MqttClient(config.getServerUri(), clientId);
            MqttConnectOptions connectOptions = createConnectOptions(config);

            // 创建MQTT资源实例
            MosMQTTResource resource = new MosMQTTResource(id, getType(), name, description, mqttClient, connectOptions);
            
            logger.info("MQTT资源创建成功: id={}, name={}, clientId={}", id, name, clientId);
            return resource;
            
        } catch (MqttException e) {
            logger.error("创建MQTT资源失败: 错误码={}, 原因={}", e.getReasonCode(), e.getMessage(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.MESSAGE_QUEUE_CONNECTION_FAILED,
                "创建MQTT资源失败: 错误码=" + e.getReasonCode() + ", 原因=" + e.getMessage(),
                e
            );
        } catch (Exception e) {
            logger.error("创建MQTT资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建MQTT资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(MosMQTTConfig config) {
        // 隐藏敏感信息
        return String.format("MosMQTTConfig{serverUri='%s', clientId='%s', username='%s', password='***', defaultQos=%d, useSsl=%s}",
            config.getServerUri(), config.getClientId(), config.getUsername(), config.getDefaultQos(), config.isUseSsl());
    }

    @Override
    public void destroyResource(Resource resource) throws MiddlewareTechnicalException {
        if (resource instanceof MosMQTTResource) {
            try {
                logger.info("开始销毁MQTT资源: {}", resource.getId());
                // 调用资源的destroy方法
                resource.destroy();
                logger.info("MQTT资源销毁成功: {}", resource.getId());
            } catch (Exception e) {
                logger.error("销毁MQTT资源失败: {}", resource.getId(), e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTRUCTION_FAILED,
                    "销毁MQTT资源失败: " + e.getMessage(),
                    e
                );
            }
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "资源类型不匹配，期望MosMQTTResource，实际为" + resource.getClass().getName()
            );
        }
    }

    /**
     * 创建MQTT连接选项
     *
     * @param config MQTT配置
     * @return MQTT连接选项
     */
    protected MqttConnectOptions createConnectOptions(MosMQTTConfig config) {
        MqttConnectOptions connectOptions = new MqttConnectOptions();
        
        // 基本配置
        connectOptions.setConnectionTimeout(config.getConnectionTimeout());
        connectOptions.setKeepAliveInterval(config.getKeepAliveInterval());
        connectOptions.setCleanSession(config.isCleanSession());
        connectOptions.setAutomaticReconnect(config.isAutomaticReconnect());
        connectOptions.setMaxInflight(config.getMaxInflight());

        // 认证配置
        if (StringUtils.hasText(config.getUsername())) {
            connectOptions.setUserName(config.getUsername());
        }
        if (StringUtils.hasText(config.getPassword())) {
            connectOptions.setPassword(config.getPassword().toCharArray());
        }

        // 遗嘱消息配置
        if (StringUtils.hasText(config.getWillTopic()) && StringUtils.hasText(config.getWillMessage())) {
            connectOptions.setWill(config.getWillTopic(), config.getWillMessage().getBytes(), config.getWillQos(), config.isWillRetained());
        }

        // SSL配置
        if (config.isUseSsl()) {
            try {
                Properties sslProps = new Properties();
                if (StringUtils.hasText(config.getTrustStore())) {
                    sslProps.setProperty("com.ibm.ssl.trustStore", config.getTrustStore());
                }
                if (StringUtils.hasText(config.getTrustStorePassword())) {
                    sslProps.setProperty("com.ibm.ssl.trustStorePassword", config.getTrustStorePassword());
                }
                if (StringUtils.hasText(config.getKeyStore())) {
                    sslProps.setProperty("com.ibm.ssl.keyStore", config.getKeyStore());
                }
                if (StringUtils.hasText(config.getKeyStorePassword())) {
                    sslProps.setProperty("com.ibm.ssl.keyStorePassword", config.getKeyStorePassword());
                }
                connectOptions.setSSLProperties(sslProps);
            } catch (Exception e) {
                logger.warn("配置SSL失败，将使用默认SSL配置", e);
            }
        }

        return connectOptions;
    }
}
