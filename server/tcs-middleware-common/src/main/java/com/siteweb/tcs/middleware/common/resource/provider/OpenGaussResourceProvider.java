package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.H2Config;
import com.siteweb.tcs.middleware.common.model.config.OpenGaussConfig;
import com.siteweb.tcs.middleware.common.resource.OpenGaussResource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OpenGauss数据库资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class OpenGaussResourceProvider extends AbstractResourceProvider<OpenGaussResource, OpenGaussConfig> {

    @Override
    public String getType() {
        return ResourceType.OPENGAUSS.getCode();
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            OpenGaussConfig openGaussConfig = convertMapToConfig(config);
            validateConfigObject(openGaussConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证OpenGuass配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证H2配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    protected Class<OpenGaussConfig> getConfigClass() {
        return OpenGaussConfig.class;
    }

    @Override
    protected void validateConfigObject(OpenGaussConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证必要参数
        if (!StringUtils.hasText(config.getHost())) {
            errors.add("主机地址不能为空");
        }

        if (config.getPort() == null || config.getPort() <= 0) {
            errors.add("端口必须大于0");
        }

        if (!StringUtils.hasText(config.getDatabase())) {
            errors.add("数据库名不能为空");
        }

        if (!StringUtils.hasText(config.getUsername())) {
            errors.add("用户名不能为空");
        }

        // 验证连接池参数
        if (config.getMaxPoolSize() == null || config.getMaxPoolSize() <= 0) {
            errors.add("连接池大小必须大于0");
        }

        if (config.getMinPoolSize() == null || config.getMinPoolSize() < 0) {
            errors.add("最小连接池大小不能小于0");
        }

        if (config.getMinPoolSize() != null && config.getMaxPoolSize() != null && 
            config.getMinPoolSize() > config.getMaxPoolSize()) {
            errors.add("最小连接池大小不能大于最大连接池大小");
        }

        // 验证超时参数
        if (config.getConnectionTimeout() == null || config.getConnectionTimeout() <= 0) {
            errors.add("连接超时时间必须大于0");
        }

        if (config.getIdleTimeout() == null || config.getIdleTimeout() <= 0) {
            errors.add("空闲超时时间必须大于0");
        }

        if (config.getMaxLifetime() == null || config.getMaxLifetime() <= 0) {
            errors.add("连接最大生命周期必须大于0");
        }

        // 验证SSL模式
        if (config.getSslEnabled() != null && config.getSslEnabled() && 
            StringUtils.hasText(config.getSslMode())) {
            String sslMode = config.getSslMode().toLowerCase();
            if (!sslMode.equals("disable") && !sslMode.equals("allow") && 
                !sslMode.equals("prefer") && !sslMode.equals("require") && 
                !sslMode.equals("verify-ca") && !sslMode.equals("verify-full")) {
                errors.add("SSL模式必须是以下值之一：disable, allow, prefer, require, verify-ca, verify-full");
            }
        }

        // 验证兼容模式
        if (StringUtils.hasText(config.getCompatibilityMode())) {
            String mode = config.getCompatibilityMode().toUpperCase();
            if (!mode.equals("PG") && !mode.equals("A") && !mode.equals("B") && !mode.equals("C")) {
                errors.add("兼容模式必须是以下值之一：PG, A, B, C");
            }
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.CONFIGURATION_INVALID,
                "OpenGauss配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 转换配置
            OpenGaussConfig openGaussConfig = convertMapToConfig(config);
            
            // 验证配置
            validateConfigObject(openGaussConfig);
            
            // 测试连接
            return doTestConnection(openGaussConfig);
            
        } catch (MiddlewareTechnicalException e) {
            logger.error("OpenGauss连接测试失败: {}", e.getMessage());
            return ConnectionTestResult.failure("配置验证失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("OpenGauss连接测试失败", e);
            return ConnectionTestResult.failure("连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 执行实际的连接测试
     */
    private ConnectionTestResult doTestConnection(OpenGaussConfig config) {
        Connection connection = null;
        try {
            // 加载驱动
            Class.forName(config.getDriverClassName());
            
            // 创建连接
            connection = DriverManager.getConnection(
                config.getJdbcUrl(),
                config.getUsername(),
                config.getPassword()
            );
            
            // 测试连接有效性
            if (connection.isValid(5)) {
                Map<String, Object> details = new HashMap<>();
                details.put("jdbcUrl", config.getJdbcUrl());
                details.put("driverVersion", connection.getMetaData().getDriverVersion());
                details.put("databaseProductName", connection.getMetaData().getDatabaseProductName());
                details.put("databaseProductVersion", connection.getMetaData().getDatabaseProductVersion());
                
                return ConnectionTestResult.success("OpenGauss连接测试成功", details);
            } else {
                return ConnectionTestResult.failure("OpenGauss连接无效");
            }
            
        } catch (ClassNotFoundException e) {
            logger.error("OpenGauss驱动未找到", e);
            return ConnectionTestResult.failure("OpenGauss驱动未找到: " + e.getMessage());
        } catch (SQLException e) {
            logger.error("OpenGauss连接失败", e);
            return ConnectionTestResult.failure("OpenGauss连接失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("OpenGauss连接测试异常", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    logger.warn("关闭OpenGauss测试连接失败", e);
                }
            }
        }
    }

    @Override
    protected OpenGaussResource doCreateResource(String id, String name, String description, OpenGaussConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建OpenGauss资源: id={}, name={}, host={}:{}", 
                id, name, config.getHost(), config.getPort());

            // 创建OpenGauss资源实例
            OpenGaussResource resource = new OpenGaussResource(id, name, description, config);
            
            logger.info("OpenGauss资源创建成功: id={}, name={}", id, name);
            return resource;
            
        } catch (Exception e) {
            logger.error("创建OpenGauss资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建OpenGauss资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(OpenGaussConfig config) {
        // 隐藏敏感信息
        return String.format("OpenGaussConfig{host='%s', port=%d, database='%s', username='%s', password='***', maxPoolSize=%d, compatibilityMode='%s'}",
            config.getHost(), config.getPort(), config.getDatabase(), 
            config.getUsername(), config.getMaxPoolSize(), config.getCompatibilityMode());
    }
}
