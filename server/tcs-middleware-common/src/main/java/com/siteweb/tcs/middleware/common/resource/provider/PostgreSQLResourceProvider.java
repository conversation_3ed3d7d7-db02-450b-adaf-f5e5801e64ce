package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.PostgreSQLConfig;
import com.siteweb.tcs.middleware.common.resource.PostgreSQLResource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PostgreSQL数据库资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class PostgreSQLResourceProvider extends AbstractResourceProvider<PostgreSQLResource, PostgreSQLConfig> {

    @Override
    public String getType() {
        return ResourceType.POSTGRESQL.getCode();
    }

    @Override
    protected Class<PostgreSQLConfig> getConfigClass() {
        return PostgreSQLConfig.class;
    }

    @Override
    protected void validateConfigObject(PostgreSQLConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证必要参数
        if (!StringUtils.hasText(config.getHost())) {
            errors.add("主机地址不能为空");
        }

        if (config.getPort() == null || config.getPort() <= 0) {
            errors.add("端口必须大于0");
        }

        if (!StringUtils.hasText(config.getDatabase())) {
            errors.add("数据库名不能为空");
        }

        if (!StringUtils.hasText(config.getUsername())) {
            errors.add("用户名不能为空");
        }

        // 验证连接池参数
        if (config.getMaxPoolSize() == null || config.getMaxPoolSize() <= 0) {
            errors.add("连接池大小必须大于0");
        }

        if (config.getMinPoolSize() == null || config.getMinPoolSize() < 0) {
            errors.add("最小连接池大小不能小于0");
        }

        if (config.getMinPoolSize() != null && config.getMaxPoolSize() != null && 
            config.getMinPoolSize() > config.getMaxPoolSize()) {
            errors.add("最小连接池大小不能大于最大连接池大小");
        }

        // 验证超时参数
        if (config.getConnectionTimeout() == null || config.getConnectionTimeout() <= 0) {
            errors.add("连接超时时间必须大于0");
        }

        if (config.getIdleTimeout() == null || config.getIdleTimeout() <= 0) {
            errors.add("空闲超时时间必须大于0");
        }

        if (config.getMaxLifetime() == null || config.getMaxLifetime() <= 0) {
            errors.add("连接最大生命周期必须大于0");
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_CONFIG_INVALID,
                "PostgreSQL配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            PostgreSQLConfig postgresqlConfig = convertMapToConfig(config);
            validateConfigObject(postgresqlConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证PostgreSQL配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证PostgreSQL配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            PostgreSQLConfig postgresqlConfig = convertMapToConfig(config);

            // 构建JDBC URL
            String jdbcUrl = postgresqlConfig.getJdbcUrl();
            String username = postgresqlConfig.getUsername();
            String password = postgresqlConfig.getPassword();

            // 测试连接
            try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
                if (connection.isValid(5)) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("databaseProductName", connection.getMetaData().getDatabaseProductName());
                    details.put("databaseProductVersion", connection.getMetaData().getDatabaseProductVersion());
                    details.put("driverName", connection.getMetaData().getDriverName());
                    details.put("driverVersion", connection.getMetaData().getDriverVersion());
                    details.put("schema", postgresqlConfig.getSchema());

                    return ConnectionTestResult.success("连接成功", details);
                } else {
                    return ConnectionTestResult.failure("连接无效");
                }
            }
        } catch (SQLException e) {
            logger.error("测试PostgreSQL连接失败", e);
            return ConnectionTestResult.failure("连接失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("测试PostgreSQL连接失败", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        }
    }

    @Override
    protected PostgreSQLResource doCreateResource(String id, String name, String description, PostgreSQLConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建PostgreSQL资源: id={}, name={}, host={}:{}", 
                id, name, config.getHost(), config.getPort());

            // 创建PostgreSQL资源实例
            PostgreSQLResource resource = new PostgreSQLResource(id, name, description, config);
            
            logger.info("PostgreSQL资源创建成功: id={}, name={}", id, name);
            return resource;
            
        } catch (Exception e) {
            logger.error("创建PostgreSQL资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建PostgreSQL资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(PostgreSQLConfig config) {
        // 隐藏敏感信息
        return String.format("PostgreSQLConfig{host='%s', port=%d, database='%s', username='%s', password='***', maxPoolSize=%d}",
            config.getHost(), config.getPort(), config.getDatabase(), 
            config.getUsername(), config.getMaxPoolSize());
    }
}
