package com.siteweb.tcs.middleware.common.runtime;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 组件专用Logger工具类
 * 为Resource和Service提供专用的Logger，确保日志能被正确收集
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class ComponentLogger {
    
    /**
     * 获取Resource专用Logger
     * 
     * @param resourceId 资源ID
     * @return 专用Logger
     */
    public static Logger getResourceLogger(String resourceId) {
        String loggerName = "middleware.resource." + resourceId;
        return LoggerFactory.getLogger(loggerName);
    }
    
    /**
     * 获取Service专用Logger
     * 
     * @param serviceId 服务ID
     * @return 专用Logger
     */
    public static Logger getServiceLogger(String serviceId) {
        String loggerName = "middleware.service." + serviceId;
        return LoggerFactory.getLogger(loggerName);
    }
    
    /**
     * 获取Resource实现类专用Logger
     * 
     * @param resourceId 资源ID
     * @param clazz 实现类
     * @return 专用Logger
     */
    public static Logger getResourceLogger(String resourceId, Class<?> clazz) {
        String loggerName = "middleware.resource." + resourceId + "." + clazz.getSimpleName();
        return LoggerFactory.getLogger(loggerName);
    }
    
    /**
     * 获取Service实现类专用Logger
     * 
     * @param serviceId 服务ID
     * @param clazz 实现类
     * @return 专用Logger
     */
    public static Logger getServiceLogger(String serviceId, Class<?> clazz) {
        String loggerName = "middleware.service." + serviceId + "." + clazz.getSimpleName();
        return LoggerFactory.getLogger(loggerName);
    }
    
    /**
     * 记录Resource生命周期日志
     * 
     * @param resourceId 资源ID
     * @param level 日志级别
     * @param message 消息
     * @param args 参数
     */
    public static void logResourceLifecycle(String resourceId, String level, String message, Object... args) {
        Logger logger = getResourceLogger(resourceId);
        String formattedMessage = String.format("[%s] %s", resourceId, message);
        
        switch (level.toUpperCase()) {
            case "DEBUG":
                logger.debug(formattedMessage, args);
                break;
            case "INFO":
                logger.info(formattedMessage, args);
                break;
            case "WARN":
                logger.warn(formattedMessage, args);
                break;
            case "ERROR":
                logger.error(formattedMessage, args);
                break;
            default:
                logger.info(formattedMessage, args);
                break;
        }
    }
    
    /**
     * 记录Service生命周期日志
     * 
     * @param serviceId 服务ID
     * @param level 日志级别
     * @param message 消息
     * @param args 参数
     */
    public static void logServiceLifecycle(String serviceId, String level, String message, Object... args) {
        Logger logger = getServiceLogger(serviceId);
        String formattedMessage = String.format("[%s] %s", serviceId, message);
        
        switch (level.toUpperCase()) {
            case "DEBUG":
                logger.debug(formattedMessage, args);
                break;
            case "INFO":
                logger.info(formattedMessage, args);
                break;
            case "WARN":
                logger.warn(formattedMessage, args);
                break;
            case "ERROR":
                logger.error(formattedMessage, args);
                break;
            default:
                logger.info(formattedMessage, args);
                break;
        }
    }
}
