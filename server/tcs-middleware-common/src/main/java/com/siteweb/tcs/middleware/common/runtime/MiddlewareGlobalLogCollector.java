package com.siteweb.tcs.middleware.common.runtime;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 中间件全局日志拦截器
 * 自动拦截中间件相关的日志并分发到对应的收集器
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Component
public class MiddlewareGlobalLogCollector extends AppenderBase<ILoggingEvent> {
    
    @Autowired
    private MiddlewareLogCollectorManager logCollectorManager;
    
    private PatternLayoutEncoder encoder;
    
    // 中间件相关的包名前缀
    private static final Set<String> MIDDLEWARE_PACKAGES = ConcurrentHashMap.newKeySet();
    
    static {
        MIDDLEWARE_PACKAGES.add("com.siteweb.tcs.middleware");
        MIDDLEWARE_PACKAGES.add("com.siteweb.tcs.siteweb");
        // 可以根据需要添加更多包名
    }
    
    @PostConstruct
    public void initialize() {
        log.info("Initializing MiddlewareGlobalLogCollector");
        
        // 配置编码器
        encoder = new PatternLayoutEncoder();
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        encoder.setContext(loggerContext);
        encoder.setPattern("%msg%n");
        encoder.start();
        
        // 设置 Appender 上下文
        setContext(loggerContext);
        setName("MiddlewareGlobalLogCollector");
        start();
        
        // 添加到根 Logger
        Logger rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME);
        rootLogger.addAppender(this);
        
        log.info("MiddlewareGlobalLogCollector initialized and attached to root logger");
    }
    
    @PreDestroy
    public void cleanup() {
        log.info("Cleaning up MiddlewareGlobalLogCollector");
        
        // 从根 Logger 移除
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME);
        rootLogger.detachAppender(this);
        
        // 停止编码器和 Appender
        if (encoder != null) {
            encoder.stop();
        }
        stop();
        
        log.info("MiddlewareGlobalLogCollector cleanup completed");
    }
    
    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!started || logCollectorManager == null) {
            return;
        }
        
        try {
            // 检查是否是中间件相关的日志
            if (!isMiddlewareRelatedLog(eventObject)) {
                return;
            }
            
            // 从 MDC 获取组件信息
            String componentId = eventObject.getMDCPropertyMap().get(MiddlewareContextManager.COMPONENT_ID_KEY);
            String componentType = eventObject.getMDCPropertyMap().get(MiddlewareContextManager.COMPONENT_TYPE_KEY);
            String componentName = eventObject.getMDCPropertyMap().get(MiddlewareContextManager.COMPONENT_NAME_KEY);
            String operation = eventObject.getMDCPropertyMap().get(MiddlewareContextManager.OPERATION_KEY);
            
            // 如果没有组件信息，尝试从堆栈跟踪中推断
            if (componentId == null || componentType == null) {
                return; // 无法确定组件信息，跳过
            }
            
            // 创建日志信息
            MiddlewareLogCollector.LogInfo logInfo = createLogInfo(eventObject, componentId, componentType, componentName, operation);
            
            // 分发到对应的收集器
            if (MiddlewareContextManager.TYPE_SERVICE.equals(componentType)) {
                logCollectorManager.appendServiceLog(componentId, logInfo);
            } else if (MiddlewareContextManager.TYPE_RESOURCE.equals(componentType)) {
                logCollectorManager.appendResourceLog(componentId, logInfo);
            } else if (MiddlewareContextManager.TYPE_RESOURCE_PROVIDER.equals(componentType)) {
                // 资源提供者的日志可以根据具体资源ID分发，或者创建专门的提供者日志收集器
                if (componentId != null && !componentId.equals(componentName)) {
                    // 如果有具体的资源ID，分发到该资源的日志收集器
                    logCollectorManager.appendResourceLog(componentId, logInfo);
                } else {
                    // 否则记录为通用的资源提供者日志
                    logCollectorManager.appendResourceLog("provider-" + componentName, logInfo);
                }
            } else if (MiddlewareContextManager.TYPE_SERVICE_PROVIDER.equals(componentType)) {
                // 服务提供者的日志处理类似
                if (componentId != null && !componentId.equals(componentName)) {
                    logCollectorManager.appendServiceLog(componentId, logInfo);
                } else {
                    logCollectorManager.appendServiceLog("provider-" + componentName, logInfo);
                }
            }
            
        } catch (Exception e) {
            // 避免日志拦截器本身的异常影响应用
            // 这里不能使用 log，会造成循环
            System.err.println("Error in MiddlewareGlobalLogCollector: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否是中间件相关的日志
     */
    private boolean isMiddlewareRelatedLog(ILoggingEvent eventObject) {
        // 检查 Logger 名称
        String loggerName = eventObject.getLoggerName();
        if (loggerName != null) {
            for (String packagePrefix : MIDDLEWARE_PACKAGES) {
                if (loggerName.startsWith(packagePrefix)) {
                    return true;
                }
            }
        }
        
        // 检查堆栈跟踪
        StackTraceElement[] stackTrace = new Throwable().getStackTrace();
        return Arrays.stream(stackTrace)
                .anyMatch(element -> MIDDLEWARE_PACKAGES.stream()
                        .anyMatch(packagePrefix -> element.getClassName().startsWith(packagePrefix)));
    }
    
    /**
     * 创建日志信息对象
     */
    private MiddlewareLogCollector.LogInfo createLogInfo(ILoggingEvent eventObject, String componentId, 
                                                        String componentType, String componentName, String operation) {
        MiddlewareLogCollector.LogInfo logInfo = new MiddlewareLogCollector.LogInfo();
        logInfo.setTimeStamp(LocalDateTime.ofInstant(Instant.ofEpochMilli(eventObject.getTimeStamp()), ZoneId.systemDefault()));
        logInfo.setLevel(eventObject.getLevel().levelStr);
        logInfo.setLoggerName(eventObject.getLoggerName());
        logInfo.setThread(eventObject.getThreadName());
        logInfo.setComponentId(componentId);
        logInfo.setComponentType(componentType);
        
        // 格式化消息，包含操作信息
        String originalMessage = new String(encoder.encode(eventObject)).trim();
        String formattedMessage;
        if (operation != null) {
            formattedMessage = String.format("[%s:%s] %s", componentType.toUpperCase(), operation, originalMessage);
        } else {
            formattedMessage = String.format("[%s] %s", componentType.toUpperCase(), originalMessage);
        }
        logInfo.setMessage(formattedMessage);
        
        return logInfo;
    }
    
    /**
     * 添加中间件包名前缀
     * 
     * @param packagePrefix 包名前缀
     */
    public static void addMiddlewarePackage(String packagePrefix) {
        MIDDLEWARE_PACKAGES.add(packagePrefix);
    }
    
    /**
     * 移除中间件包名前缀
     * 
     * @param packagePrefix 包名前缀
     */
    public static void removeMiddlewarePackage(String packagePrefix) {
        MIDDLEWARE_PACKAGES.remove(packagePrefix);
    }
    
    /**
     * 获取所有中间件包名前缀
     * 
     * @return 包名前缀集合
     */
    public static Set<String> getMiddlewarePackages() {
        return Set.copyOf(MIDDLEWARE_PACKAGES);
    }
}
