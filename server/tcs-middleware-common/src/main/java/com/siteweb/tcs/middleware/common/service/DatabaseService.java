package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库服务类
 * 提供数据库操作的通用方法
 */
public class DatabaseService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);

    private final DataSource dataSource;

    /**
     * 构造函数
     *
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param resource 关联的资源
     */
    public DatabaseService(String id, String name, String description, Resource resource) {
        super(id, "database", name, description, resource);

        // 使用静态工厂模式获取适当的DataSource
        this.dataSource = getDatabaseDataSource(resource);
    }

    /**
     * 静态工厂方法，根据资源类型获取对应的DataSource
     *
     * @param resource 资源
     * @return 数据源
     * @throws IllegalArgumentException 如果资源类型不支持
     */
    private static DataSource getDatabaseDataSource(Resource resource) {
        if (resource == null) {
            throw new IllegalArgumentException("资源不能为空");
        }

        String resourceType = resource.getType();

        // 使用静态工厂模式，根据资源类型返回对应的DataSource
        switch (resourceType.toLowerCase()) {
            case "mysql":
                try {
                    // 尝试获取MySQL数据源
                    return resource.getNativeResource();
                } catch (Exception e) {
                    logger.error("获取MySQL数据源失败", e);
                    throw new IllegalArgumentException("获取MySQL数据源失败: " + e.getMessage(), e);
                }
            case "h2":
                try {
                    // 尝试获取H2数据源
                    return resource.getNativeResource();
                } catch (Exception e) {
                    logger.error("获取H2数据源失败", e);
                    throw new IllegalArgumentException("获取H2数据源失败: " + e.getMessage(), e);
                }
            case "postgresql":
                try {
                    // 尝试获取PostgreSQL数据源
                    return resource.getNativeResource();
                } catch (Exception e) {
                    logger.error("获取PostgreSQL数据源失败", e);
                    throw new IllegalArgumentException("获取PostgreSQL数据源失败: " + e.getMessage(), e);
                }
            default:
                throw new IllegalArgumentException("不支持的资源类型: " + resourceType);
        }
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化数据库服务: {}", getId());
        // 初始化阶段不需要特殊操作
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动数据库服务: {}", getId());
        try {
            // 测试连接
            try (Connection connection = dataSource.getConnection()) {
                if (!connection.isValid(5)) {
                    throw new SQLException("连接无效");
                }
            }

            logger.info("数据库服务启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动数据库服务失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_START_FAILED,
                "启动数据库服务失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止数据库服务: {}", getId());
        // 停止阶段不需要特殊操作，资源会自行处理连接池的关闭
        logger.info("数据库服务停止成功: {}", getId());
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁数据库服务: {}", getId());
        // 销毁阶段不需要特殊操作，资源会自行处理连接池的关闭
        logger.info("数据库服务销毁成功: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        // 委托给资源进行健康检查
        return resource.checkHealth();
    }

    /**
     * 获取数据库连接
     *
     * @return 数据库连接
     * @throws SQLException 如果获取连接失败
     */
    public Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    /**
     * 执行查询SQL，返回结果列表
     *
     * @param sql SQL语句
     * @param params 参数列表
     * @return 查询结果列表，每行数据以Map形式返回
     * @throws MiddlewareTechnicalException 如果执行SQL出错
     */
    public List<Map<String, Object>> query(String sql, Object... params) throws MiddlewareTechnicalException {
        logger.debug("执行查询SQL: {}", sql);

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {

            // 设置参数
            setParameters(statement, params);

            // 执行查询
            try (ResultSet resultSet = statement.executeQuery()) {
                return convertResultSetToList(resultSet);
            }
        } catch (SQLException e) {
            logger.error("执行查询SQL失败: {}", sql, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.DATABASE_QUERY_FAILED,
                "执行查询SQL失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 执行查询SQL，返回单行结果
     *
     * @param sql SQL语句
     * @param params 参数列表
     * @return 单行查询结果，以Map形式返回，如果没有结果则返回null
     * @throws MiddlewareTechnicalException 如果执行SQL出错
     */
    public Map<String, Object> queryForMap(String sql, Object... params) throws MiddlewareTechnicalException {
        logger.debug("执行查询单行SQL: {}", sql);

        List<Map<String, Object>> results = query(sql, params);
        if (results.isEmpty()) {
            return null;
        }
        return results.get(0);
    }

    /**
     * 执行查询SQL，返回单个值
     *
     * @param sql SQL语句
     * @param requiredType 返回值类型
     * @param params 参数列表
     * @param <T> 返回值类型
     * @return 单个值，如果没有结果则返回null
     * @throws MiddlewareTechnicalException 如果执行SQL出错
     */
    public <T> T queryForObject(String sql, Class<T> requiredType, Object... params) throws MiddlewareTechnicalException {
        logger.debug("执行查询单值SQL: {}", sql);

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {

            // 设置参数
            setParameters(statement, params);

            // 执行查询
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return convertValue(resultSet.getObject(1), requiredType);
                }
                return null;
            }
        } catch (SQLException e) {
            logger.error("执行查询单值SQL失败: {}", sql, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.DATABASE_QUERY_FAILED,
                "执行查询单值SQL失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 执行更新SQL（INSERT、UPDATE、DELETE）
     *
     * @param sql SQL语句
     * @param params 参数列表
     * @return 影响的行数
     * @throws MiddlewareTechnicalException 如果执行SQL出错
     */
    public int update(String sql, Object... params) throws MiddlewareTechnicalException {
        logger.debug("执行更新SQL: {}", sql);

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {

            // 设置参数
            setParameters(statement, params);

            // 执行更新
            return statement.executeUpdate();
        } catch (SQLException e) {
            logger.error("执行更新SQL失败: {}", sql, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.DATABASE_UPDATE_FAILED,
                "执行更新SQL失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 执行批量更新SQL
     *
     * @param sql SQL语句
     * @param batchArgs 批量参数列表
     * @return 每个批次影响的行数数组
     * @throws MiddlewareTechnicalException 如果执行SQL出错
     */
    public int[] batchUpdate(String sql, List<Object[]> batchArgs) throws MiddlewareTechnicalException {
        logger.debug("执行批量更新SQL: {}", sql);

        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql)) {

            // 设置批量参数
            for (Object[] args : batchArgs) {
                setParameters(statement, args);
                statement.addBatch();
            }

            // 执行批量更新
            return statement.executeBatch();
        } catch (SQLException e) {
            logger.error("执行批量更新SQL失败: {}", sql, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.DATABASE_UPDATE_FAILED,
                "执行批量更新SQL失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 在事务中执行操作
     *
     * @param action 事务操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws MiddlewareTechnicalException 如果执行操作出错
     */
    public <T> T executeInTransaction(TransactionCallback<T> action) throws MiddlewareTechnicalException {
        logger.debug("在事务中执行操作");

        Connection connection = null;
        boolean originalAutoCommit = true;

        try {
            connection = dataSource.getConnection();
            originalAutoCommit = connection.getAutoCommit();
            connection.setAutoCommit(false);

            T result = action.doInTransaction(connection);

            connection.commit();
            return result;
        } catch (Exception e) {
            if (connection != null) {
                try {
                    connection.rollback();
                } catch (SQLException ex) {
                    logger.error("回滚事务失败", ex);
                }
            }
            logger.error("事务执行失败", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.DATABASE_TRANSACTION_FAILED,
                "事务执行失败: " + e.getMessage(),
                e
            );
        } finally {
            if (connection != null) {
                try {
                    connection.setAutoCommit(originalAutoCommit);
                    connection.close();
                } catch (SQLException ex) {
                    logger.error("关闭连接失败", ex);
                }
            }
        }
    }

    /**
     * 设置PreparedStatement的参数
     *
     * @param statement PreparedStatement
     * @param params 参数列表
     * @throws SQLException 如果设置参数失败
     */
    private void setParameters(PreparedStatement statement, Object... params) throws SQLException {
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                statement.setObject(i + 1, params[i]);
            }
        }
    }

    /**
     * 将ResultSet转换为List<Map<String, Object>>
     *
     * @param resultSet 结果集
     * @return 转换后的列表
     * @throws SQLException 如果转换失败
     */
    private List<Map<String, Object>> convertResultSetToList(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        while (resultSet.next()) {
            Map<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            results.add(row);
        }

        return results;
    }

    /**
     * 将值转换为指定类型
     *
     * @param value 原始值
     * @param requiredType 目标类型
     * @param <T> 目标类型
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    private <T> T convertValue(Object value, Class<T> requiredType) {
        if (value == null) {
            return null;
        }

        if (requiredType.isInstance(value)) {
            return (T) value;
        }

        if (String.class == requiredType) {
            return (T) value.toString();
        }

        if (Number.class.isAssignableFrom(requiredType)) {
            Number number = (Number) value;
            if (Integer.class == requiredType) {
                return (T) Integer.valueOf(number.intValue());
            } else if (Long.class == requiredType) {
                return (T) Long.valueOf(number.longValue());
            } else if (Double.class == requiredType) {
                return (T) Double.valueOf(number.doubleValue());
            } else if (Float.class == requiredType) {
                return (T) Float.valueOf(number.floatValue());
            } else if (Short.class == requiredType) {
                return (T) Short.valueOf(number.shortValue());
            } else if (Byte.class == requiredType) {
                return (T) Byte.valueOf(number.byteValue());
            }
        }

        if (Boolean.class == requiredType) {
            if (value instanceof Number) {
                return (T) Boolean.valueOf(((Number) value).intValue() != 0);
            } else if (value instanceof String) {
                return (T) Boolean.valueOf(Boolean.parseBoolean((String) value));
            }
        }

        throw new IllegalArgumentException("无法将 " + value.getClass().getName() + " 转换为 " + requiredType.getName());
    }

    /**
     * 事务回调接口
     *
     * @param <T> 返回值类型
     */
    public interface TransactionCallback<T> {
        /**
         * 在事务中执行的操作
         *
         * @param connection 数据库连接
         * @return 操作结果
         * @throws SQLException 如果执行操作出错
         */
        T doInTransaction(Connection connection) throws SQLException;
    }
}
