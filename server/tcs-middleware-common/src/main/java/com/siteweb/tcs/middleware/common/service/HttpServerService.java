package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.config.HttpServerServiceConfig;
import com.siteweb.tcs.middleware.common.model.http.HttpServerRequest;
import com.siteweb.tcs.middleware.common.model.http.HttpServerResponse;
import com.siteweb.tcs.middleware.common.model.http.RequestFailureRecord;
import com.siteweb.tcs.middleware.common.resource.HttpServerResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.service.http.RequestFailureManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

/**
 * HTTP服务器服务实现
 * 基于HttpServerResourceInterface的HTTP服务器服务实现
 */
public class HttpServerService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(HttpServerService.class);

    private final HttpServerResource httpServerResource;
    private final HttpServerServiceConfig config;
    private final RequestFailureManager requestFailureManager;

    /**
     * 构造函数
     *
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param resource HTTP服务器资源
     * @param config 服务配置
     */
    public HttpServerService(String id, String name, String description, Resource resource, HttpServerServiceConfig config) {
        super(id, ServiceType.HTTP_SERVER.getCode(), name, description, resource);
        this.httpServerResource = (HttpServerResource) resource;
        this.config = config != null ? config : new HttpServerServiceConfig();
        this.requestFailureManager = new RequestFailureManager(this.config);
    }

    /**
     * 兼容性构造函数（使用默认配置）
     *
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param resource HTTP服务器资源
     */
    public HttpServerService(String id, String name, String description, Resource resource) {
        this(id, name, description, resource, new HttpServerServiceConfig());
    }
    
    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化HTTP服务器服务: {}", getId());
        // 验证资源类型
        if (!(resource instanceof HttpServerResource)) {
            throw new MiddlewareTechnicalException(MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID.getCode(),
                "不支持的资源类型，需要HttpServerResourceInterface"
            );
        }
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动HTTP服务器服务: {}", getId());
        // 服务启动时不需要特殊操作，资源已经在启动时处理
        logger.info("HTTP服务器服务启动成功: {}", getId());
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止HTTP服务器服务: {}", getId());
        // 服务停止时不需要特殊操作，资源会在停止时处理
        logger.info("HTTP服务器服务停止成功: {}", getId());
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁HTTP服务器服务: {}", getId());

        // 关闭解析失败管理器
        if (requestFailureManager != null) {
            requestFailureManager.shutdown();
        }

        logger.info("HTTP服务器服务销毁成功: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            // 检查底层资源的健康状态
            HealthStatus resourceHealth = resource.checkHealth();
            if (!resourceHealth.isUp()) {
                return HealthStatus.down("底层HTTP服务器资源不健康: " + resourceHealth.getMessage());
            }

            // 检查服务自身状态
            if (getStatus() != ServiceStatus.STARTED) {
                return HealthStatus.down("HTTP服务器服务未启动");
            }

            return HealthStatus.up("HTTP服务器服务运行正常");
        } catch (Exception e) {
            logger.error("检查HTTP服务器服务健康状态失败: {}", getId(), e);
            return HealthStatus.down("检查健康状态失败: " + e.getMessage());
        }
    }

    // ==================== HttpServerService 接口实现 ====================


    public CompletableFuture<Void> bindRoute(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler) {
        try {
            return httpServerResource.bindRoute(path, handler);
        } catch (Exception e) {
            logger.error("绑定路由失败: path={}", path, e);
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }


    public CompletableFuture<Void> bindPost(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler) {
        try {
            return httpServerResource.bindPost(path, handler);
        } catch (Exception e) {
            logger.error("绑定POST路由失败: path={}", path, e);
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }


    public CompletableFuture<Void> bindGet(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler) {
        try {
            return httpServerResource.bindGet(path, handler);
        } catch (Exception e) {
            logger.error("绑定GET路由失败: path={}", path, e);
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }


    public CompletableFuture<Void> unbindRoute(String path) {
        try {
            return httpServerResource.unbindRoute(path);
        } catch (Exception e) {
            logger.error("移除路由失败: path={}", path, e);
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }


    public InetSocketAddress getServerAddress() {
        return httpServerResource.getServerAddress();
    }


    /**
     * 获取请求总数统计
     * 统计功能由Resource层管理
     *
     * @return 请求总数
     */
    public long getRequestCount() {
        return httpServerResource.getRequestCount();
    }

    /**
     * 获取所有来源IP列表
     * 统计功能由Resource层管理
     *
     * @return 来源IP列表
     */
    public List<String> getSourceIPs() {
        return httpServerResource.getSourceIPs();
    }

    /**
     * 获取指定IP的请求次数
     * 统计功能由Resource层管理
     *
     * @param sourceIP 来源IP
     * @return 请求次数
     */
    public long getRequestCountBySourceIP(String sourceIP) {
        return httpServerResource.getRequestCountBySourceIP(sourceIP);
    }


    /**
     * 记录请求处理失败信息（简化版本）
     * 直接从HttpServerRequest中提取所需信息
     *
     * @param request HTTP请求对象
     * @param errorMessage 错误信息
     * @param exceptionType 异常类型
     */
    public void recordRequestFailure(HttpServerRequest request, String errorMessage, String exceptionType) {
        RequestFailureRecord record = new RequestFailureRecord(
                request.getRemoteAddress(),
                request.getLocalAddress(),
                request.getRemotePort(),
                request.getLocalPort(),
                errorMessage,
                request.getPath(),
                request.getMethod(),
                request.getBody(),
                exceptionType
        );
        requestFailureManager.recordParsingFailure(record);
    }

    /**
     * 记录请求处理失败信息（简化版本，自动推断异常类型）
     * 直接从HttpServerRequest中提取所需信息
     *
     * @param request HTTP请求对象
     * @param errorMessage 错误信息
     */
    public void recordRequestFailure(HttpServerRequest request, String errorMessage) {
        recordRequestFailure(request, errorMessage, "RequestProcessingException");
    }

    /**
     * 记录请求处理失败信息（从异常中提取信息）
     * 直接从HttpServerRequest和Throwable中提取所需信息
     *
     * @param request HTTP请求对象
     * @param throwable 异常对象
     */
    public void recordRequestFailure(HttpServerRequest request, Throwable throwable) {
        String errorMessage = throwable.getMessage() != null ? throwable.getMessage() : "Unknown error";
        String exceptionType = throwable.getClass().getSimpleName();
        recordRequestFailure(request, errorMessage, exceptionType);
    }

    /**
     * 记录请求超时失败
     * 专门用于记录超时相关的失败
     *
     * @param request HTTP请求对象
     * @param timeoutMs 超时时间（毫秒）
     */
    public void recordRequestTimeout(HttpServerRequest request, long timeoutMs) {
        String errorMessage = String.format("Request timeout after %d ms", timeoutMs);
        recordRequestFailure(request, errorMessage, "TimeoutException");
    }

    /**
     * 记录请求验证失败
     * 专门用于记录参数验证、权限验证等失败
     *
     * @param request HTTP请求对象
     * @param validationError 验证错误信息
     */
    public void recordRequestValidationFailure(HttpServerRequest request, String validationError) {
        recordRequestFailure(request, validationError, "ValidationException");
    }

    /**
     * 记录请求解析失败
     * 专门用于记录请求体解析、参数解析等失败
     *
     * @param request HTTP请求对象
     * @param parseError 解析错误信息
     */
    public void recordRequestParseFailure(HttpServerRequest request, String parseError) {
        recordRequestFailure(request, parseError, "ParseException");
    }

    /**
     * 记录解析失败信息（保留原方法以兼容性）
     * 现在由Service层的ParsingFailureManager处理，而不是Resource层
     *
     * @param srcIP 源IP
     * @param dstIP 目标IP
     * @param srcPort 源端口
     * @param dstPort 目标端口
     * @param errorMessage 错误信息
     * @deprecated 使用 recordRequestFailure(HttpServerRequest, String, String) 替代
     */
    @Deprecated
    public void recordParsingFailure(String srcIP, String dstIP, int srcPort, int dstPort, String errorMessage) {
        RequestFailureRecord record = new RequestFailureRecord(srcIP, dstIP, srcPort, dstPort, errorMessage);
        requestFailureManager.recordParsingFailure(record);
    }

    /**
     * 记录解析失败信息（包含请求体内容）（保留原方法以兼容性）
     * 现在由Service层的ParsingFailureManager处理，而不是Resource层
     *
     * @param srcIP 源IP
     * @param dstIP 目标IP
     * @param srcPort 源端口
     * @param dstPort 目标端口
     * @param errorMessage 错误信息
     * @param requestPath 请求路径
     * @param httpMethod HTTP方法
     * @param requestBody 请求体内容
     * @param exceptionType 异常类型
     * @deprecated 使用 recordRequestFailure(HttpServerRequest, String, String) 替代
     */
    @Deprecated
    public void recordParsingFailure(String srcIP, String dstIP, int srcPort, int dstPort,
                                   String errorMessage, String requestPath, String httpMethod,
                                   String requestBody, String exceptionType) {
        RequestFailureRecord record = new RequestFailureRecord(srcIP, dstIP, srcPort, dstPort,
                                                              errorMessage, requestPath, httpMethod,
                                                              requestBody, exceptionType);
        requestFailureManager.recordParsingFailure(record);
    }

    /**
     * 获取所有解析失败记录
     *
     * @return 解析失败记录列表
     */
    public List<RequestFailureRecord> getParsingFailureRecords() {
        return requestFailureManager.getParsingFailureRecords();
    }

    /**
     * 获取过滤后的解析失败记录
     *
     * @param srcIP 源IP过滤（可为null，表示不限制）
     * @param startTime 开始时间过滤（毫秒时间戳，可为null）
     * @param endTime 结束时间过滤（毫秒时间戳，可为null）
     * @return 解析失败记录列表
     */
    public List<RequestFailureRecord> getParsingFailureRecords(String srcIP, Long startTime, Long endTime) {
        return requestFailureManager.getParsingFailureRecords(srcIP, startTime, endTime);
    }

    /**
     * 清空统计数据（仅清空Resource层的统计）
     */
    public void clearStatistics() {
        httpServerResource.clearStatistics();
    }
    public Map<String, Object> getStatisticsSummary() {
        return httpServerResource.getStatisticsSummary();
    }

    /**
     * 获取流量统计摘要
     */
    public Map<String, Object> getTrafficSummary() {
        Map<String, Object> summary = httpServerResource.getStatisticsSummary();
        return summary.get("traffic") != null ?
               (Map<String, Object>) summary.get("traffic") :
               new HashMap<>();
    }

    /**
     * 获取超时错误统计摘要
     */
    public Map<String, Object> getTimeoutErrorSummary() {
        Map<String, Object> summary = httpServerResource.getStatisticsSummary();
        return summary.get("timeoutErrors") != null ?
               (Map<String, Object>) summary.get("timeoutErrors") :
               new HashMap<>();
    }

    /**
     * 获取当前QPS
     */
    public double getCurrentQPS() {
        Map<String, Object> trafficSummary = getTrafficSummary();
        return trafficSummary.get("currentQPS") != null ?
               (Double) trafficSummary.get("currentQPS") : 0.0;
    }

    /**
     * 获取当前请求速率（KB/s）
     */
    public double getCurrentRequestRate() {
        Map<String, Object> trafficSummary = getTrafficSummary();
        return trafficSummary.get("currentRequestRateKBps") != null ?
               (Double) trafficSummary.get("currentRequestRateKBps") : 0.0;
    }

    /**
     * 获取当前响应速率（KB/s）
     */
    public double getCurrentResponseRate() {
        Map<String, Object> trafficSummary = getTrafficSummary();
        return trafficSummary.get("currentResponseRateKBps") != null ?
               (Double) trafficSummary.get("currentResponseRateKBps") : 0.0;
    }

    /**
     * 获取平均响应时间（毫秒）
     */
    public double getAverageResponseTime() {
        Map<String, Object> trafficSummary = getTrafficSummary();
        return trafficSummary.get("averageResponseTimeMs") != null ?
               (Double) trafficSummary.get("averageResponseTimeMs") : 0.0;
    }

    /**
     * 清空请求失败记录（现在由Service层管理）
     */
    public void clearRequestFailureRecords() {
        requestFailureManager.clearParsingFailureRecords();
    }


    /**
     * 获取解析失败记录数量
     *
     * @return 记录数量
     */
    public int getParsingFailureRecordCount() {
        return requestFailureManager.getParsingFailureRecordCount();
    }

    /**
     * 获取服务配置
     *
     * @return 服务配置
     */
    public HttpServerServiceConfig getConfig() {
        return config;
    }

    public List<String> getBoundPaths() {
        return httpServerResource.getBoundPaths();
    }

    public <T> T getNativeServer() {
        return httpServerResource.getNativeServer();
    }
}
