package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.resource.Resource;

/**
 * 服务接口
 * 所有服务类型都应该实现此接口
 */
public interface Service {

    /**
     * 获取服务ID
     *
     * @return 服务ID
     */
    String getId();

    /**
     * 获取服务类型
     *
     * @return 服务类型
     */
    String getType();

    /**
     * 获取服务名称
     *
     * @return 服务名称
     */
    String getName();

    /**
     * 获取服务描述
     *
     * @return 服务描述
     */
    String getDescription();

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    ServiceStatus getStatus();

    /**
     * 获取关联的资源
     *
     * @return 关联的资源
     */
    Resource getResource();

    /**
     * 初始化服务
     *
     * @throws MiddlewareTechnicalException 初始化失败时抛出异常
     */
    void initialize() throws MiddlewareTechnicalException;

    /**
     * 启动服务
     *
     * @throws MiddlewareTechnicalException 启动失败时抛出异常
     */
    void start() throws MiddlewareTechnicalException;

    /**
     * 停止服务
     *
     * @throws MiddlewareTechnicalException 停止失败时抛出异常
     */
    void stop() throws MiddlewareTechnicalException;

    /**
     * 销毁服务
     *
     * @throws MiddlewareTechnicalException 销毁失败时抛出异常
     */
    void destroy() throws MiddlewareTechnicalException;

    /**
     * 检查服务健康状态
     *
     * @return 服务是否健康
     */
    boolean isHealthy();

    /**
     * 获取服务健康状态详情
     *
     * @return 服务健康状态详情
     */
    HealthStatus checkHealth();
}
