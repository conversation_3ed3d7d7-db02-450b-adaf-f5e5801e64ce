package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;

import java.util.Map;

/**
 * 服务提供者工厂接口
 */
public interface ServiceProviderFactory {

    /**
     * 获取服务提供者
     *
     * @param type 服务类型
     * @return 服务提供者
     * @throws MiddlewareBusinessException 服务类型不存在时抛出异常
     */
    ServiceProvider<? extends Service> getProvider(String type) throws MiddlewareBusinessException;

    /**
     * 获取所有服务提供者
     *
     * @return 所有服务提供者
     */
    Map<String, ServiceProvider<? extends Service>> getAllProviders();
}
