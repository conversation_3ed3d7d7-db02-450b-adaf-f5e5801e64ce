package com.siteweb.tcs.middleware.common.service.provider;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.FileSystemServiceConfig;
import com.siteweb.tcs.middleware.common.resource.FileSystemResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import com.siteweb.tcs.middleware.common.service.FileSystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文件系统服务提供者
 */
@Component
public class FileSystemServiceProvider implements ServiceProvider<com.siteweb.tcs.middleware.common.service.FileSystemService> {

    private static final Logger logger = LoggerFactory.getLogger(FileSystemServiceProvider.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getType() {
        return ServiceType.FILESYSTEM.getCode();
    }

    /**
     * 将Map配置转换为FileSystemServiceConfig对象
     */
    private FileSystemServiceConfig convertMapToConfig(Map<String, Object> config) {
        try {
            return objectMapper.convertValue(config, FileSystemServiceConfig.class);
        } catch (Exception e) {
            logger.error("转换文件系统服务配置失败", e);
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.SERVICE_CONFIG_INVALID,
                "配置格式错误: " + e.getMessage()
            );
        }
    }

    @Override
    public String getSupportedResourceCategory() {
        return ServiceType.FILESYSTEM.getSupportedResourceCategory();
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        List<String> errors = new ArrayList<>();

        // 验证配置
        if (config == null) {
            errors.add("配置不能为空");
            return ValidationResult.invalid(errors);
        }

        // 验证最大文件大小
        Object maxFileSize = config.get("maxFileSize");
        if (maxFileSize != null) {
            try {
                long size = Long.parseLong(maxFileSize.toString());
                if (size <= 0) {
                    errors.add("最大文件大小必须大于0");
                }
            } catch (NumberFormatException e) {
                errors.add("最大文件大小格式不正确");
            }
        }

        // 验证线程池大小
        Object threadPoolSize = config.get("threadPoolSize");
        if (threadPoolSize != null) {
            try {
                int size = Integer.parseInt(threadPoolSize.toString());
                if (size <= 0) {
                    errors.add("线程池大小必须大于0");
                }
            } catch (NumberFormatException e) {
                errors.add("线程池大小格式不正确");
            }
        }

        return errors.isEmpty() ? ValidationResult.valid() : ValidationResult.invalid(errors);
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            // 对于文件系统服务，连接测试主要是验证配置的有效性
            // 实际的文件系统连接测试会在资源层面进行
            return ConnectionTestResult.success("文件系统服务配置验证通过");

        } catch (Exception e) {
            logger.error("测试文件系统服务连接失败", e);
            return ConnectionTestResult.failure("连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否支持指定的资源类型
     */
    private boolean isSupportedResourceType(String resourceType) {
        return ResourceType.LOCAL_FILESYSTEM.getCode().equals(resourceType) ||
               ResourceType.MINIO_FILESYSTEM.getCode().equals(resourceType) ||
               ResourceType.ZOOKEEPER_FILESYSTEM.getCode().equals(resourceType) ||
               ResourceType.CONSUL_FILESYSTEM.getCode().equals(resourceType);
    }

    @Override
    public com.siteweb.tcs.middleware.common.service.FileSystemService createService(String id, String name, String description,
                                                                                     Map<String, Object> config, Resource resource)
            throws MiddlewareTechnicalException {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_CONFIG_INVALID,
                    "配置验证失败: " + String.join(", ", validationResult.getErrors())
                );
            }

            // 验证资源类型
            if (!isSupportedResourceType(resource.getType())) {
                throw new MiddlewareBusinessException(
                        MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                    "不支持的资源类型: " + resource.getType()
                );
            }

            // 验证资源是否为FileSystemResource类型
            if (!(resource instanceof FileSystemResource)) {
                throw new MiddlewareBusinessException(
                        MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                    "资源必须是FileSystemResource类型，实际类型: " + resource.getClass().getName()
                );
            }

            // 转换配置对象
            FileSystemServiceConfig serviceConfig = convertMapToConfig(config);

            // 创建服务实例
            FileSystemService service = new FileSystemService(id, name, description, resource, serviceConfig);

            logger.info("文件系统服务创建成功: id={}, name={}, resource={}",
                id, name, resource.getId());

            return service;

        } catch (Exception e) {
            logger.error("创建文件系统服务失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "创建文件系统服务失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public void destroyService(Service service) throws MiddlewareTechnicalException {
        try {
            if (service != null) {
                logger.info("销毁文件系统服务: {}", service.getId());
                service.destroy();
            }
        } catch (Exception e) {
            logger.error("销毁文件系统服务失败: {}", service != null ? service.getId() : "null", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_DESTROY_FAILED,
                "销毁文件系统服务失败: " + e.getMessage(),
                e
            );
        }
    }
}
