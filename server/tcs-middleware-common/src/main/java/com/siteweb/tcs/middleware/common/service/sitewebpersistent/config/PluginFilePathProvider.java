package com.siteweb.tcs.middleware.common.service.sitewebpersistent.config;

import com.siteweb.tcs.siteweb.handler.FilePathProvider;
import lombok.extern.slf4j.Slf4j;

/**
 * 插件特定的文件路径提供者实现
 * 为每个插件提供独立的文件根路径
 * <AUTHOR>
 */
@Slf4j
public class PluginFilePathProvider implements FilePathProvider {
    
    private final String pluginId;
    private final String rootPath;
    private final PluginPathConfig pluginPathConfig;
    
    /**
     * 构造函数
     *
     * @param pluginId 插件ID
     */
    public PluginFilePathProvider(String pluginId) {
        this(new PluginPathConfig(pluginId));
    }

    /**
     * 构造函数（使用PluginPathConfig）
     *
     * @param pluginPathConfig 插件路径配置
     */
    public PluginFilePathProvider(PluginPathConfig pluginPathConfig) {
        this.pluginPathConfig = pluginPathConfig;
        this.pluginId = pluginPathConfig.getPluginId();
        this.rootPath = "plugins/" + pluginId;
    }
    
    @Override
    public String getRootPath() {
        log.debug("Using plugin-specific file path provider for plugin: {}, rootPath: {}", pluginId, rootPath);
        return rootPath;
    }

    @Override
    public String getWorkspacePath() {
        String workspacePath = rootPath + "/workspace";
        log.debug("Plugin workspace path for {}: {}", pluginId, workspacePath);
        return workspacePath;
    }

    @Override
    public String getProtocolPath() {
        String protocolPath = getWorkspacePath() + "/protocol";
        log.debug("Plugin protocol path for {}: {}", pluginId, protocolPath);
        return protocolPath;
    }

    @Override
    public String getPluginPath(String targetPluginId) {
        String pluginPath = "plugins/" + targetPluginId;
        log.debug("Target plugin path for {}: {}", targetPluginId, pluginPath);
        return pluginPath;
    }

    @Override
    public String getPluginWorkspacePath(String targetPluginId) {
        String pluginWorkspacePath = getPluginPath(targetPluginId) + "/workspace";
        log.debug("Target plugin workspace path for {}: {}", targetPluginId, pluginWorkspacePath);
        return pluginWorkspacePath;
    }

    @Override
    public String getStandardDicFilePath() {
        return pluginPathConfig.getStandardDicFilePath();
    }
    @Override
    public void setStandardDicFilePath(String standardDicFilePath) {
        pluginPathConfig.setStandardDicFilePath(standardDicFilePath);
    }

    @Override
    public String getCmbInitListFile(Integer monitorUnitId) {
        return pluginPathConfig.getCmbInitListFilePath(monitorUnitId);
    }

    @Override
    public String getCmbSignalIdMapFile() {
        return pluginPathConfig.getCmbSignalIdMapFilePath();
    }


    /**
     * 获取插件ID
     *
     * @return 插件ID
     */
    public String getPluginId() {
        return pluginId;
    }
}
