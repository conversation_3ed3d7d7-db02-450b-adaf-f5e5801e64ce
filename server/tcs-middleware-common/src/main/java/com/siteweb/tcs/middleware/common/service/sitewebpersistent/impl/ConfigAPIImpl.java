package com.siteweb.tcs.middleware.common.service.sitewebpersistent.impl;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.util.PathUtil;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.middleware.common.service.sitewebpersistent.ConfigAPI;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.*;
import com.siteweb.tcs.siteweb.manager.SamplerTreeManager;
import com.siteweb.tcs.siteweb.provider.EquipmentProvider;
import com.siteweb.tcs.siteweb.provider.SamplerProvider;
import com.siteweb.tcs.siteweb.provider.ConfigTaskProvider;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.TableExistenceChecker;
import com.siteweb.tcs.siteweb.vo.*;
import com.siteweb.tcs.siteweb.util.StrSplitUtil;
import com.alibaba.excel.EasyExcel;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * ConfigAPI实现类
 * 支持使用独立的SqlSessionTemplate进行数据库操作
 * 注意：不使用@Service注解，通过new方式创建实例，确保每个SitewebPersistentService有独立的API实例
 */
@Slf4j
public class ConfigAPIImpl implements ConfigAPI {

    private EquipmentProvider equipmentProvider;

    private IDiskfileService diskfileService;

    private ISamplerService samplerService;

    private SamplerProvider samplerProvider;

    private IEquipmentTemplateService equipmentTemplateService;

    private IDataItemService dataItemService;

    private IMonitorUnitService monitorUnitService;

    private IEquipmentService equipmentService;

    private IPortService portService;

    private ISamplerUnitService samplerUnitService;

    private IWorkStationService workStationService;

    private SamplerTreeManager samplerTreeManager;

    private IEquipmentTemplateXmlService equipmentTemplateXmlService;

    private ICategoryIdMapService categoryIdMapService;

    // ==================== 设备管理相关Service ====================

    private ITslMonitorUnitSignalService tslMonitorUnitSignalService;

    private IAcrossMonitorUnitSignalService acrossMonitorUnitSignalService;

    private ITslMonitorUnitEventService tslMonitorUnitEventService;

    private ISignalService signalService;

    private IEventService eventService;

    private IControlService controlService;

    private IOperationDetailService operationDetailService;

    private ITaskStatusService taskStatusService;

    private ICenterService centerService;

    private IExpressionService expressionService;

    private IMonitorUnitXmlService monitorUnitXmlService;

    private ICollectorDownloadService collectorDownloadService;

    private ConfigTaskProvider configTaskProvider;

    private TableExistenceChecker tableExistenceChecker;

    private IConfigImportService configImportService;

    @Override
    public void initAPI(ApplicationContext clonedContext) {
        try {
            // 从克隆的上下文获取所有需要的Bean，这些Bean的Mapper已被替换为使用独立数据源
            equipmentProvider = clonedContext.getBean(EquipmentProvider.class);
            diskfileService = clonedContext.getBean(IDiskfileService.class);
            samplerService = clonedContext.getBean(ISamplerService.class);
            samplerProvider = clonedContext.getBean(SamplerProvider.class);
            equipmentTemplateService = clonedContext.getBean(IEquipmentTemplateService.class);
            dataItemService = clonedContext.getBean(IDataItemService.class);
            monitorUnitService = clonedContext.getBean(IMonitorUnitService.class);
            equipmentService = clonedContext.getBean( IEquipmentService.class);
            portService = clonedContext.getBean( IPortService.class);
            samplerUnitService = clonedContext.getBean(ISamplerUnitService.class);
            workStationService = clonedContext.getBean(IWorkStationService.class);
            samplerTreeManager = clonedContext.getBean(SamplerTreeManager.class);
            equipmentTemplateXmlService = clonedContext.getBean(IEquipmentTemplateXmlService.class);
            categoryIdMapService = clonedContext.getBean(ICategoryIdMapService.class);
            tslMonitorUnitSignalService = clonedContext.getBean(ITslMonitorUnitSignalService.class);
            acrossMonitorUnitSignalService = clonedContext.getBean(IAcrossMonitorUnitSignalService.class);
            tslMonitorUnitEventService = clonedContext.getBean(ITslMonitorUnitEventService.class);
            signalService = clonedContext.getBean(ISignalService.class);
            eventService = clonedContext.getBean(IEventService.class);
            controlService = clonedContext.getBean(IControlService.class);
            operationDetailService = clonedContext.getBean(IOperationDetailService.class);
            taskStatusService = clonedContext.getBean(ITaskStatusService.class);
            centerService = clonedContext.getBean(ICenterService.class);
            expressionService = clonedContext.getBean(IExpressionService.class);
            monitorUnitXmlService = clonedContext.getBean(IMonitorUnitXmlService.class);
            configTaskProvider = clonedContext.getBean(ConfigTaskProvider.class);
            tableExistenceChecker = clonedContext.getBean("tableExistenceChecker",TableExistenceChecker.class);
            collectorDownloadService = clonedContext.getBean(ICollectorDownloadService.class);
            configImportService = clonedContext.getBean(IConfigImportService.class);
        } catch (Exception e) {
            log.error("Failed to initialize ConfigAPIImpl with cloned context, falling back to main application beans", e);
            // 如果从克隆上下文获取Bean失败，保持使用主程序的Bean
        }
    }

    @Override
    public void initOMC() {
        centerService.createOMCCenter();
    }


    // ==================== Sampler相关方法实现 ====================

    @Override
    public List<SamplerVO> findAllVoForSampler() {
        // 现在samplerService已经是从复制上下文获取的，其Mapper使用独立数据源
        List<SamplerVO> result = samplerService.findAllVo();
        log.debug("Using cloned context service for findAllVoForSampler, found {} samplers", result.size());
        return result;
    }

    @Override
    public List<Sampler> findByIdsForSampler(List<Integer> samplerIds) {
        // 现在samplerService已经是从复制上下文获取的，其Mapper使用独立数据源
        List<Sampler> result = samplerService.findByIds(samplerIds);
        log.debug("Using cloned context service for findByIdsForSampler, found {} samplers", result.size());
        return result;
    }

    @Override
    public Sampler findByIdForSampler(Integer samplerId) {
        // 现在samplerService已经是从复制上下文获取的，其Mapper使用独立数据源
        Sampler result = samplerService.findById(samplerId);
        log.debug("Using cloned context service for findByIdForSampler: {}", samplerId);
        return result;
    }

    @Override
    public Sampler findByProtocolCodeForSampler(String protocolCode) {
        // 现在samplerService已经是从复制上下文获取的，其Mapper使用独立数据源
        Sampler result = samplerService.getSamplerByProtocolCode(protocolCode);
        log.debug("Using cloned context service for findByProtocolCodeForSampler: {}", protocolCode);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateForSampler(Sampler sampler) {
        // 现在samplerService已经是从复制上下文获取的，其Mapper使用独立数据源
        boolean result = samplerService.update(sampler);
        log.debug("Using cloned context service for updateForSampler: {}", sampler.getSamplerId());
        return result;
    }

    @Override
    public String validateBatchDeleteForSampler(String samplerIds) {
        return samplerProvider.validateBatchDeleteForSampler(samplerIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteForSampler(String samplerIds) {
        return samplerProvider.batchDeleteForSampler(samplerIds);
    }

    @Override
    public String validateDeleteByIdForSampler(Integer samplerId) {
        // 添加调试日志，确认使用的是独立上下文的service
        log.debug("ConfigAPI validateDeleteByIdForSampler: samplerId={}, using independent context", samplerId);

        // 先直接通过samplerService验证记录是否存在
        Sampler sampler = samplerService.findById(samplerId);
        log.debug("ConfigAPI direct findById result: {}", sampler != null ? sampler.getSamplerId() : "null");

        return samplerProvider.validateDeleteByIdForSampler(samplerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIdForSampler(Integer samplerId) {
        return samplerProvider.deleteByIdForSampler(samplerId);
    }

    @Override
    public void uploadProtocolFileForSampler(Sampler sampler, ProtocolTypeEnum protocolTypeEnum, byte[] bytes, String fileName) {
        samplerService.uploadProtocolFile(sampler, protocolTypeEnum, bytes, fileName);
    }

    @Override
    public void deleteProtocolFileForSampler(String protocolCode, ProtocolTypeEnum protocolTypeEnum) {
        samplerService.deleteProtocolFile(protocolCode, protocolTypeEnum);
    }

    @Override
    public File downloadProtocolFileForSampler(String protocolCode, Integer protocolType) {
        try {
            // 1. 验证protocolType
            ProtocolTypeEnum protocolTypeEnum = ProtocolTypeEnum.getByProtocolType(protocolType);
            if (Objects.isNull(protocolTypeEnum)) {
                throw new RuntimeException("协议类型不存在");
            }

            // 2. 根据protocolCode查找sampler
            Sampler sampler = samplerProvider.getSamplerByProtocolCode(protocolCode);
            if (Objects.isNull(sampler)) {
                throw new RuntimeException("采集器不存在，协议代码: " + protocolCode);
            }


            // 3. 构建文件路径
            String filePath = PathUtil.pathJoin("protocol", sampler.getProtocolCode(), protocolTypeEnum.getLibPath());
            String fileName = sampler.getDllPath();
            return diskfileService.download(filePath, fileName);

        } catch (Exception e) {
            log.error("Failed to prepare download for protocol file: protocolCode={}, protocolType={}", protocolCode, protocolType, e);
            throw new RuntimeException("准备文件下载失败: " + e.getMessage());
        }
    }

    // ==================== EquipmentTemplate相关方法实现 ====================

    @Override
    public List<EquipmentTemplateVO> findVoAllForEquipmentTemplate() {
        return equipmentTemplateService.findVoAll();
    }

    @Override
    public EquipmentTemplate importTemplateForEquipmentTemplate(Element equipmentTemplatesElement) {
        return equipmentTemplateXmlService.importTemplate(equipmentTemplatesElement);
    }

    @Override
    public EquipmentTemplate findByIdForEquipmentTemplate(Integer equipmentTemplateId) {
        return equipmentTemplateService.findById(equipmentTemplateId);
    }

    @Override
    public List<Tree<Integer>> findTreeForEquipmentTemplate(Boolean hideDynamicConfigTemplate) {
        try {
            return equipmentTemplateService.findTree(hideDynamicConfigTemplate);
        } catch (Exception e) {
            log.error("Failed to find equipment template tree", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<EquipmentTemplateTreeDTO> findTreeExcludeCategoryForEquipmentTemplate(Integer equipmentCategory, String protocolCode, String equipmentTemplateName) {
        try {
            return equipmentTemplateService.findTree(equipmentCategory, protocolCode, equipmentTemplateName);
        } catch (Exception e) {
            log.error("Failed to find equipment template tree by condition", e);
            return Collections.emptyList();
        }
    }

    @Override
    public EquipmentTemplateVO findVoByIdForEquipmentTemplate(Integer equipmentTemplateId) {
        try {
            return equipmentTemplateService.findVoById(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to find equipment template VO by ID: {}", equipmentTemplateId, e);
            return null;
        }
    }

    @Override
    public boolean inheritUpdateForEquipmentTemplate(EquipmentTemplate equipmentTemplate) {
        try {
            return equipmentTemplateService.inheritUpdate(equipmentTemplate);
        } catch (Exception e) {
            log.error("Failed to inherit update equipment template: {}", equipmentTemplate.getEquipmentTemplateId(), e);
            return false;
        }
    }

    @Override
    public boolean updateForEquipmentTemplate(EquipmentTemplate equipmentTemplate) {
        try {
            return equipmentTemplateService.updateById(equipmentTemplate);
        } catch (Exception e) {
            log.error("Failed to update equipment template: {}", equipmentTemplate.getEquipmentTemplateId(), e);
            return false;
        }
    }

    @Override
    public int copyForEquipmentTemplate(CopyEquipmentTemplateDTO copyEquipmentTemplateDTO) {
        try {
            return equipmentTemplateService.copyTemplate(copyEquipmentTemplateDTO);
        } catch (Exception e) {
            log.error("Failed to copy equipment template", e);
            return -1;
        }
    }

    @Override
    public boolean deleteByIdForEquipmentTemplate(Integer equipmentTemplateId) {
        try {
            return equipmentTemplateService.deleteById(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to delete equipment template: {}", equipmentTemplateId, e);
            return false;
        }
    }

    @Override
    public String exportForEquipmentTemplate(Integer equipmentTemplateId) {
        try {
            return equipmentTemplateXmlService.exportEquipmentTemplate(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to export equipment template: {}", equipmentTemplateId, e);
            return "";
        }
    }

    @Override
    public boolean upgradeToRootTemplateForEquipmentTemplate(Integer equipmentTemplateId) {
        try {
            return equipmentTemplateService.upgradeToRootTemplate(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to upgrade to root template: {}", equipmentTemplateId, e);
            return false;
        }
    }

    @Override
    public EquipmentTemplate createForEquipmentTemplate(EquipmentTemplate equipmentTemplate) {
        try {
            return equipmentTemplateService.createEquipmentTemplate(equipmentTemplate);
        } catch (Exception e) {
            log.error("Failed to create equipment template", e);
            return null;
        }
    }

    @Override
    public List<String> getDllPathForEquipmentTemplate(List<Integer> equipmentTemplateIds) {
        try {
            return equipmentTemplateService.findDLlPathByEquipmentTemplateIds(equipmentTemplateIds);
        } catch (Exception e) {
            log.error("Failed to get DLL paths for equipment templates", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, Object> getAssociatedDataForEquipmentTemplate(Integer equipmentTemplateId) {
        try {
            Map<String, Object> result = new HashMap<>();
            // 这里应该获取关联的信号、事件和控制数据
            // 暂时返回空的Map
            log.warn("getAssociatedDataForEquipmentTemplate method is not fully implemented");
            return result;
        } catch (Exception e) {
            log.error("Failed to get associated data for equipment template: {}", equipmentTemplateId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public void exportExcelForEquipmentTemplate(HttpServletResponse response, Integer equipmentTemplateId) {
        try {
            equipmentTemplateService.exportExcel(response, equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to export equipment template to Excel: {}", equipmentTemplateId, e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean applyStandardizationToChildrenForEquipmentTemplate(Integer parentTemplateId) {
        try {
            return equipmentTemplateService.applyStandardizationToChildrenForEquipmentTemplate(parentTemplateId);
        } catch (Exception e) {
            log.error("Failed to apply standardization to children for equipment template: {}", parentTemplateId, e);
            return false;
        }
    }

    @Override
    public int updateEquipmentCategoryForEquipmentTemplate(Integer equipmentTemplateId, Integer equipmentCategory) {
        return equipmentTemplateService.updateEquipmentCategory(equipmentTemplateId, equipmentCategory);
    }

    // ==================== DataItem相关方法实现 ====================

    @Override
    public List<DataItem> findByEntryIdForDataItem(Integer entryId) {
        try {
            if (entryId == null) {
                log.warn("entryId is null for findByEntryIdForDataItem");
                return List.of();
            }
            DataEntryEnum dataEntryEnum = DataEntryEnum.findByValue(entryId);
            return dataItemService.findByEntryId(dataEntryEnum);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid entryId for findByEntryIdForDataItem: {}", entryId);
            return List.of();
        } catch (Exception e) {
            log.error("Failed to find data items by entryId: {}", entryId, e);
            return List.of();
        }
    }

    @Override
    public boolean updateForDataItem(DataItemUpdateDTO dataItemUpdateDTO) {
        try {
            DataItem dataItem = BeanUtil.copyProperties(dataItemUpdateDTO, DataItem.class);
            if (dataItem != null) {
                boolean result = dataItemService.update( dataItem)>0;
                log.debug("Using cloned context service for updateForDataItem: {}", dataItem.getEntryItemId());
                return result;
            } else {
                log.warn("Invalid parameter type for updateForDataItem: {}", dataItem.getClass());
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to update data item", e);
            return false;
        }
    }

    @Override
    public int createDataItemForDataItem( DataItemCreateDTO dataItemCreateDTO) {
        try {
            DataItem dataItem = new DataItem();
            BeanUtil.copyProperties(dataItemCreateDTO, dataItem);
            int result = dataItemService.createDataItem(dataItem);
            log.debug("Using cloned context service for createDataItemForDataItem: {}", (dataItem).getEntryItemId());
            return result;
        } catch (Exception e) {
            log.error("Failed to create data item", e);
            return 0;
        }
    }

    @Override
    public int deleteByEntryItemIdsForDataItem(String entryItemIds) {
        log.debug("Using cloned context service for deleteByEntryItemIdsForDataItem: {}", entryItemIds);
        return dataItemService.deleteByEntryItemIds(StrSplitUtil.splitToIntList(entryItemIds));
    }

    // ==================== CategoryIdMap相关方法实现 ====================

    @Override
    public Map<Integer, String> findBusinessCategoryFromOriginCategoryForCategoryIdMap(Integer originCategoryKey) {
        return categoryIdMapService.findBusinessCategoryFromOriginCategory(originCategoryKey);
    }

    // ==================== MonitorUnit相关方法实现 ====================

    @Override
    public MonitorUnitDTO findByIdForMonitorUnit(Integer monitorUnitId) {
        try {
            if (tableExistenceChecker.areStationTablesExist()){
                return monitorUnitService.findById(monitorUnitId);
            }
            else {
                return monitorUnitService.findByIdWithoutStation(monitorUnitId);
            }
        } catch (Exception e) {
            log.error("Failed to find monitor unit by id: {}", monitorUnitId, e);
            return null;
        }
    }

    @Override
    public boolean createForMonitorUnit(MonitorUnitDTO monitorUnit, Integer resourceStructureId) {
        try {
            return monitorUnitService.createMonitorUnit(monitorUnit, resourceStructureId);
        } catch (Exception e) {
            log.error("Failed to create monitor unit", e);
            return false;
        }
    }

    @Override
    public boolean createV3ForMonitorUnit(MonitorUnitDTO monitorUnit) {
        try {
            return monitorUnitService.createMonitorUnitV3(monitorUnit);
        } catch (Exception e) {
            log.error("Failed to create monitor unit V3", e);
            return false;
        }
    }

    @Override
    public boolean updateForMonitorUnit(MonitorUnitDTO monitorUnit) {
        try {
            return monitorUnitService.updateMonitorUnit(monitorUnit);
        } catch (Exception e) {
            log.error("Failed to update monitor unit", e);
            return false;
        }
    }

    @Override
    public boolean deleteForMonitorUnit(Integer monitorUnitId, Boolean isDelEqs) {
        try {
            boolean result;
            if (Boolean.TRUE.equals(isDelEqs)) {
                result = monitorUnitService.deleteMonitorUnitAndEqs(monitorUnitId);
            } else {
                result = monitorUnitService.deleteMonitorUnit(monitorUnitId);
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to delete monitor unit: {}", monitorUnitId, e);
            return false;
        }
    }


    @Override
    public List<TypeItemDTO> findTypesForMonitorUnit() {
        try {
            DataEntryEnum dataEntryEnum = DataEntryEnum.MONITOR_UNIT_TYPE;
            return dataItemService.findTypes(dataEntryEnum);
        } catch (Exception e) {
            log.error("Failed to find monitor unit types", e);
            return List.of();
        }
    }

    @Override
    public List<PortTreeVO> getSamplerTreeForMonitorUnit(Integer monitorUnitId) {
        try {
            return samplerTreeManager.getSamplerTree(monitorUnitId);
        } catch (Exception e) {
            log.error("Failed to get sampler tree for monitor unit: {}", monitorUnitId, e);
            return List.of();
        }
    }

    @Override
    public List<MonitorUnitDTO> findByIdsForMonitorUnit(List<Integer> monitorUnitIds) {
        try {
            List<MonitorUnitDTO> result;
            if (monitorUnitIds == null || monitorUnitIds.isEmpty()) {
                if (tableExistenceChecker.areStationTablesExist()) {
                    result = monitorUnitService.findAllMonitorUnit();
                } else {
                    result = monitorUnitService.findAllMonitorUnitWithoutStation();
                }
            } else {
                result = monitorUnitService.findByIds(monitorUnitIds);
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to find monitor units by ids", e);
            return List.of();
        }
    }

    /**
     * Find monitor units with pagination
     *
     * @param page Page object containing page number and page size
     * @return IPage containing monitor unit DTOs with pagination info
     */

    @Override
    public IPage<MonitorUnitDTO> findMonitorUnitPage(Page<MonitorUnitDTO> page, MonitorUnitQueryDTO queryDTO) {
        try {
            IPage<MonitorUnitDTO> result;

            if (tableExistenceChecker.areStationTablesExist()) {
                result = monitorUnitService.findMonitorUnitPageWithQuery(page, queryDTO);
            } else {
                result = monitorUnitService.findMonitorUnitPageWithoutStationWithQuery(page, queryDTO);
            }

            return result;
        } catch (Exception e) {
            log.error("Failed to find monitor units by ids", e);
            return Page.of(0, 0);
        }
    }

    @Override
    public IPage<MonitorUnitDTO> findNonRmuMonitorUnitPageWithoutStationWithQuery(Page<MonitorUnitDTO> page, MonitorUnitQueryDTO queryDTO) {
        try {
            return monitorUnitService.findNonRmuMonitorUnitPageWithoutStationWithQuery(page, queryDTO);
        } catch (Exception e) {
            log.error("Failed to find monitor units by ids", e);
            return Page.of(0, 0);
        }
    }

    // ==================== Equipment相关方法实现 ====================

    @Override
    public Equipment findByIdForEquipment(Integer equipmentId) {
        try {
            return equipmentService.findEquipmentById(equipmentId);
        } catch (Exception e) {
            log.error("Failed to find equipment by id: {}", equipmentId, e);
            return null;
        }
    }

    @Override
    public Equipment createForEquipment(CreateEquipmentDto info) {
        try {
            if (tableExistenceChecker.areStationTablesExist()) {
                return equipmentProvider.createEquipment(info);
            } else {
                return equipmentProvider.createEquipmentWithoutHouseStation(info);
            }
        } catch (Exception e) {
            log.error("Failed to create equipment", e);
            return null;
        }
    }


    @Override
    public Equipment updateForEquipment(EquipmentDetailDTO equipmentDetailDTO) {
        try {
            return equipmentService.updateEquipment(equipmentDetailDTO);
        } catch (Exception e) {
            log.error("Failed to update equipment", e);
            return null;
        }
    }

    @Override
    public boolean deleteForEquipment(Integer equipmentId) {
        try {
            return equipmentService.deleteEquipment(equipmentId);
        } catch (Exception e) {
            log.error("Failed to delete equipment: {}", equipmentId, e);
            return false;
        }
    }

    @Override
    public List<Equipment> findByMonitorUnitIdForEquipment(Integer monitorUnitId) {
        try {
            return equipmentService.findByMonitorUnitId(monitorUnitId);
        } catch (Exception e) {
            log.error("Failed to find equipment by monitor unit id: {}", monitorUnitId, e);
            return List.of();
        }
    }

    @Override
    public boolean switchTemplateForEquipment(SwitchTemplateDTO switchTemplateDTO) {
        try {
            if (switchTemplateDTO == null || switchTemplateDTO.getEquipmentIds() == null ||
                    switchTemplateDTO.getEquipmentIds().isEmpty() || switchTemplateDTO.getDestTemplateId() == null) {
                log.error("Invalid switch template DTO parameters");
                return false;
            }

            Integer equipmentTemplateId = switchTemplateDTO.getDestTemplateId();
            boolean allSuccess = true;

            for (Integer equipmentId : switchTemplateDTO.getEquipmentIds()) {
                boolean success = equipmentService.switchEquipmentTemplate(equipmentId, equipmentTemplateId);
                if (!success) {
                    allSuccess = false;
                    log.error("Failed to switch template for equipment: {}", equipmentId);
                }
            }

            log.info("Switch template operation completed. Success: {}", allSuccess);
            return allSuccess;
        } catch (Exception e) {
            log.error("Failed to switch equipment template", e);
            return false;
        }
    }

    @Override
    public List<Equipment> searchEquipmentList(Integer monitorUnitId, String equipmentName) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<Equipment> queryWrapper = new LambdaQueryWrapper<>();

            // 如果指定了监控单元ID，则按监控单元ID查询
            if (monitorUnitId != null) {
                queryWrapper.eq(Equipment::getMonitorUnitId, monitorUnitId);
            }

            // 如果指定了设备名称，则进行模糊查询
            if (StringUtils.isNotBlank(equipmentName)) {
                queryWrapper.like(Equipment::getEquipmentName, equipmentName);
            }

            // 按显示顺序和设备名称排序
            queryWrapper.orderByAsc(Equipment::getDisplayIndex, Equipment::getEquipmentName);

            List<Equipment> equipmentList = equipmentService.list(queryWrapper);
            log.info("Found {} equipment with monitorUnitId: {}, equipmentName: {}",
                    equipmentList.size(), monitorUnitId, equipmentName);

            return equipmentList;
        } catch (Exception e) {
            log.error("Failed to search equipment list with monitorUnitId: {}, equipmentName: {}",
                    monitorUnitId, equipmentName, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Integer equipmentInstanceForEquipment(Integer equipmentId) {
        try {
            return equipmentService.equipmentInstance(equipmentId);
        } catch (Exception e) {
            log.error("Failed to create equipment instance: {}", equipmentId, e);
            return null;
        }
    }

    // ==================== Port相关方法实现 ====================

    @Override
    public Port findByPortIdForPort(Integer portId) {
        try {
            return portService.findByPortId(portId);
        } catch (Exception e) {
            log.error("Failed to find port by port id: {}", portId, e);
            return null;
        }
    }

    @Override
    public Port createForPort(Port port) {
        try {
            // 按照原配置工具的逻辑，先验证再创建
            portService.verification(port);
            return portService.createPort(port);
        } catch (Exception e) {
            log.error("Failed to create port", e);
            return null;
        }
    }

    @Override
    public boolean updateForPort(Port port) {
        try {
            portService.verification(port);
            return portService.updatePort(port);
        } catch (Exception e) {
            log.error("Failed to update port", e);
            return false;
        }
    }

    @Override
    public boolean deleteForPort(Integer portId) {
        try {
            return portService.deleteByPortId(portId);
        } catch (Exception e) {
            log.error("Failed to delete port: {}", portId, e);
            return false;
        }
    }

    @Override
    public List<Port> findByMonitorUnitIdForPort(Integer monitorUnitId) {
        try {
            return portService.list(new LambdaQueryWrapper<Port>()
                    .eq(Port::getMonitorUnitId, monitorUnitId));
        } catch (Exception e) {
            log.error("Failed to find ports by monitor unit id: {}", monitorUnitId, e);
            return List.of();
        }
    }

    @Override
    public List<DataItem> findTypesForPort(Integer monitorUnitCategory) {
        try {
            DataEntryEnum dataEntryEnum = DataEntryEnum.PORT_TYPE;
            return dataItemService.findByEntryId(dataEntryEnum);
        } catch (Exception e) {
            log.error("Failed to find port types", e);
            return List.of();
        }
    }

    @Override
    public Integer getMaxPortNoForPort(Integer monitorUnitId) {
        try {
            // 按照原配置工具的逻辑获取最大端口号
            Integer maxPortNo = portService.getMaxPortByMonitorUnitId(monitorUnitId);
            return maxPortNo != null ? maxPortNo : 0;
        } catch (Exception e) {
            log.error("Failed to get max port no for monitor unit: {}", monitorUnitId, e);
            return 0;
        }
    }

    @Override
    public List<SamplerUnitTreeVO> getSamplerUnitsForPort(Integer portId) {
        try {
            PortTreeVO portTreeVO = samplerTreeManager.getPortVO(portId);

            if (portTreeVO != null) {
                // 获取 samplerUnits 属性
                return portTreeVO.getSamplerUnits();
            }
            return List.of();
        } catch (Exception e) {
            log.error("Failed to get sampler units for port: {}", portId, e);
            return List.of();
        }
    }

    @Override
    public Boolean checkEquipmentNameExistsForEquipment(Integer equipmentId, Integer monitorUnitId, String equipmentName) {
        try {
            return equipmentService.existsByMonitorUnitIdAndEquipmentName(equipmentId, monitorUnitId, equipmentName);
        } catch (Exception e) {
            log.error("Failed to check equipment name exists: equipmentId={}, monitorUnitId={}, equipmentName={}",
                    equipmentId, monitorUnitId, equipmentName, e);
            return false;
        }
    }

    @Override
    public EquipmentDetailDTO findEquipmentDetailForEquipment(Integer equipmentId) {
        return equipmentService.findEquipmentDetail(equipmentId);
    }

    // ==================== SamplerUnit相关方法实现 ====================

    @Override
    public SamplerUnit createForSamplerUnit(SamplerUnit samplerUnit) {
        try {
            return samplerUnitService.createSamplerUnit(samplerUnit);
        } catch (Exception e) {
            log.error("Failed to create sampler unit", e);
            return null;
        }
    }

    @Override
    public SamplerUnit findByIdForSamplerUnit(Integer samplerUnitId) {
        try {
            return samplerUnitService.findBySamplerUnitId(samplerUnitId);
        } catch (Exception e) {
            log.error("Failed to find sampler unit by id: {}", samplerUnitId, e);
            return null;
        }
    }

    @Override
    public boolean updateForSamplerUnit(SamplerUnit samplerUnit) {
        try {
            return samplerUnitService.updateSamplerUnit(samplerUnit);
        } catch (Exception e) {
            log.error("Failed to update sampler unit", e);
            return false;
        }
    }

    @Override
    public boolean deleteForSamplerUnit(Integer samplerUnitId) {
        try {
            return samplerUnitService.deleteSamplerUnit(samplerUnitId);
        } catch (Exception e) {
            log.error("Failed to delete sampler unit: {}", samplerUnitId, e);
            return false;
        }
    }

    @Override
    public List<SamplerUnit> findByMonitorUnitIdAndPortIdForSamplerUnit(Integer monitorUnitId, Integer portId) {
        try {
            return samplerUnitService.findByMonitorUnitIdAndPortId(monitorUnitId, portId);
        } catch (Exception e) {
            log.error("Failed to find sampler units by monitorUnitId: {} and portId: {}", monitorUnitId, portId, e);
            return Collections.emptyList();
        }
    }

    // ==================== Workstation相关方法实现 ====================

    @Override
    public List<ServerSourceVO> getServerSourceListForWorkstation() {
        try {
            if (tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_WORKSTATION.getTableName())) {
                return workStationService.list().stream()
                        .filter(ServerSourceVO::filter)
                        .map(ServerSourceVO::from)
                        .toList();
            }
            else {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Failed to get server source list for workstation", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<WorkStation> findAllForWorkstation() {
        try {
            if (tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_WORKSTATION.getTableName())) {
                return workStationService.list();
            }
            else {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Failed to find all workstations", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<WorkStation> findByWorkStationTypeForWorkstation(Integer workStationType) {
        try {
            if (tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_WORKSTATION.getTableName())) {
                WorkStationTypeEnum workStationTypeEnum = WorkStationTypeEnum.getByValue(workStationType);
                if (workStationTypeEnum == null) {
                    log.warn("Invalid workstation type: {}", workStationType);
                    return Collections.emptyList();
                }

                return workStationService.findByWorkStationType(workStationTypeEnum);
            }
            else {
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("Failed to find workstations by type: {}", workStationType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public WorkStation findByIdForWorkstation(Integer workStationId) {
        try {
            if (tableExistenceChecker.isTableExists(TableIdentityEnum.TBL_WORKSTATION.getTableName())) {
                return workStationService.findByWorkStationId(workStationId);
            }
            else {
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to find workstation by id: {}", workStationId, e);
            return null;
        }
    }

    // ==================== 设备管理相关方法实现 ====================

    @Override
    public List<com.siteweb.tcs.siteweb.vo.EquipmentTreeVO> getEquipmentTreeForDeviceManagement() {
        try {
            return equipmentService.getEquipmentTree();
        } catch (Exception e) {
            log.error("Failed to get equipment tree for device management", e);
            return List.of();
        }
    }

    @Override
    public Equipment getEquipmentInfoForDeviceManagement(Integer equipmentId) {
        try {
            return equipmentService.findEquipmentById(equipmentId);
        } catch (Exception e) {
            log.error("Failed to get equipment info for device management: {}", equipmentId, e);
            return null;
        }
    }

    @Override
    public boolean updateEquipmentConfigForDeviceManagement(Equipment equipment) {
        try {
            return equipmentService.updateById(equipment);
        } catch (Exception e) {
            log.error("Failed to update equipment config for device management", e);
            return false;
        }
    }

    @Override
    public EquipmentTemplate getEquipmentTemplateConfigForDeviceManagement(Integer id) {
        try {
            return equipmentTemplateService.findById(id);
        } catch (Exception e) {
            log.error("Failed to get equipment template config for device management: {}", id, e);
            return null;
        }
    }

    @Override
    public Integer addEquipmentInstanceForDeviceManagement(Integer equipmentId) {
        try {
            return equipmentService.equipmentInstance(equipmentId);
        } catch (Exception e) {
            log.error("Failed to add equipment instance for device management: {}", equipmentId, e);
            return null;
        }
    }


    @Override
    public byte[] exportReferenceByEquipmentTemplateIdForDeviceManagement(Integer equipmentTemplateId) {
        try {
            return equipmentService.exportReferenceByEquipmentTemplateId(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to export equipment reference by template ID for device management: equipmentTemplateId={}", equipmentTemplateId, e);
            throw new RuntimeException("导出设备引用信息失败", e);
        }
    }

    @Override
    public List<EquipTemplateChangeDTO> checkTemplateChangeForDeviceManagement(SwitchTemplateDTO switchTemplateDTO) {
        try {
            // 首先检查信号引用冲突
            if (equipmentTemplateService.switchTemplateSignalCheck(switchTemplateDTO)) {
                throw new RuntimeException("模板切换存在信号引用冲突");
            }

            // 比较模板变更影响
            return equipmentTemplateService.changeCompare(
                    switchTemplateDTO.getOriginTemplateId(),
                    switchTemplateDTO.getDestTemplateId(),
                    switchTemplateDTO.getEquipmentIds()
            );
        } catch (Exception e) {
            log.error("Failed to check template change for device management", e);
            throw e;
        }
    }

    @Override
    public byte[] exportTemplateChangeForDeviceManagement(SwitchTemplateDTO switchTemplateDTO) {
        try {
            List<EquipTemplateChangeDTO> changeList = checkTemplateChangeForDeviceManagement(switchTemplateDTO);

            // 使用EasyExcel导出
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, EquipTemplateChangeDTO.class)
                    .sheet("模板切换影响")
                    .doWrite(changeList);

            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("Failed to export template change for device management", e);
            throw new RuntimeException("导出模板切换影响失败", e);
        }
    }


    @Override
    public Equipment getSimplifyEquipmentForDeviceManagement(Integer equipmentId) {
        try {
            return equipmentService.findSimplifyEquipment(equipmentId);
        } catch (Exception e) {
            log.error("Failed to get simplify equipments for device management", e);
            return null;
        }
    }

    @Override
    public List<EquipmentReferenceVO> findReferenceByEquipmentTemplateIdForDeviceManagement(Integer equipmentTemplateId) {
        try {
            return equipmentService.findReferenceByEquipmentTemplateId(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to find equipment reference by template ID for device management: equipmentTemplateId={}", equipmentTemplateId, e);
            return List.of();
        }
    }

    @Override
    public EquipmentTemplate getTopParentEquipmentTemplateForDeviceManagement(Integer equipmentTemplateId) {
        try {
            return equipmentTemplateService.findTopParent(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to get top parent equipment template for device management: {}", equipmentTemplateId, e);
            return null;
        }
    }

    @Override
    public boolean updateChildrenCategoryForDeviceManagement(Integer parentId, Integer category) {
        try {
            return equipmentTemplateService.updateChildrenCategory(parentId, category) > 0;
        } catch (Exception e) {
            log.error("Failed to update children category for device management: parentId={}, category={}", parentId, category, e);
            return false;
        }
    }

    @Override
    public List<EquipmentBaseType> getEquipmentBaseTypeListForDeviceManagement() {
        try {
            // 需要实现获取设备基类的逻辑
            return equipmentTemplateService.findBaseClassAll().stream()
                    .map(template -> {
                        EquipmentBaseType baseType = new EquipmentBaseType();
                        baseType.setBaseEquipmentId(template.getEquipmentTemplateId());
                        baseType.setBaseEquipmentName(template.getEquipmentTemplateName());
                        baseType.setEquipmentTypeId(template.getEquipmentCategory());
                        return baseType;
                    })
                    .toList();
        } catch (Exception e) {
            log.error("Failed to get equipment base type list for device management", e);
            return List.of();
        }
    }

    @Override
    public List<DataItem> getEquipmentCategorysForDeviceManagement() {
        try {
            return dataItemService.findByEntryId(com.siteweb.tcs.siteweb.enums.DataEntryEnum.EQUIPMENT_CATEGORY);
        } catch (Exception e) {
            log.error("Failed to get equipment categorys for device management", e);
            return List.of();
        }
    }

    @Override
    public MonitorUnit getMonitorUnitConfigForDeviceManagement(Integer muId) {
        try {
            MonitorUnitDTO dto;
            if (tableExistenceChecker.areStationTablesExist()) {
                dto =  monitorUnitService.findById(muId);
            }
            else {
                dto =  monitorUnitService.findByIdWithoutStation(muId);
            }
            if (dto != null) {
                MonitorUnit monitorUnit = new MonitorUnit();
                BeanUtil.copyProperties(dto, monitorUnit);
                return monitorUnit;
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to get monitor unit config for device management: {}", muId, e);
            return null;
        }
    }

    @Override
    public List<TslMonitorUnitEvent> getMonitorUnitEventConditionForDeviceManagement(Integer equipmentId, Integer eventId) {
        try {
            return tslMonitorUnitEventService.findByEquipmentIdAndEventId(equipmentId, eventId);
        } catch (Exception e) {
            log.error("Failed to get monitor unit event condition for device management: equipmentId={}, eventId={}", equipmentId, eventId, e);
            return List.of();
        }
    }

    @Override
    public boolean addMonitorUnitEventForDeviceManagement(TslMonitorUnitEvent event) {
        try {
            return tslMonitorUnitEventService.createOrUpdate(event);
        } catch (Exception e) {
            log.error("Failed to add monitor unit event for device management", e);
            return false;
        }
    }

    @Override
    public boolean deleteMonitorUnitEventForDeviceManagement(Integer equipmentId, Integer eventId) {
        try {
            return tslMonitorUnitEventService.deleteByEquipmentIdAndEventId(equipmentId, eventId);
        } catch (Exception e) {
            log.error("Failed to delete monitor unit event for device management: equipmentId={}, eventId={}", equipmentId, eventId, e);
            return false;
        }
    }

    @Override
    public List<TslMonitorUnitSignal> getMonitorUnitSignalConditionForDeviceManagement(Integer equipmentId, Integer signalId) {
        try {
            return tslMonitorUnitSignalService.findByEquipmentIdAndSignalId(equipmentId, signalId);
        } catch (Exception e) {
            log.error("Failed to get monitor unit signal condition for device management: equipmentId={}, signalId={}", equipmentId, signalId, e);
            return List.of();
        }
    }

    @Override
    public boolean addMonitorUnitSignalForDeviceManagement(TslMonitorUnitSignal signal) {
        try {
            return tslMonitorUnitSignalService.createOrUpdate(signal);
        } catch (Exception e) {
            log.error("Failed to add monitor unit signal for device management", e);
            return false;
        }
    }

    @Override
    public boolean deleteMonitorUnitSignalForDeviceManagement(Integer equipmentId, Integer signalId) {
        try {
            return tslMonitorUnitSignalService.deleteByEquipmentIdAndSignalId(equipmentId, signalId);
        } catch (Exception e) {
            log.error("Failed to delete monitor unit signal for device management: equipmentId={}, signalId={}", equipmentId, signalId, e);
            return false;
        }
    }

    @Override
    public List<com.siteweb.tcs.siteweb.vo.SamplerUnitWithPortVO> getSamplerUnitWithPortForDeviceManagement() {
        try {
            return samplerUnitService.getSamplerUnitWithPort();
        } catch (Exception e) {
            log.error("Failed to get sampler unit with port for device management", e);
            return List.of();
        }
    }

    @Override
    public List<AcrossMonitorUnitSignal> getCrossMonitorUnitSignalForSignal(java.util.Map<String, Object> condition) {
        try {
            return acrossMonitorUnitSignalService.findByCondition(condition);
        } catch (Exception e) {
            log.error("Failed to get cross monitor unit signal for signal", e);
            return List.of();
        }
    }

    @Override
    public boolean createAcrossMonitorUnitSignalForSignal(AcrossMonitorUnitSignal signal) {
        try {
            return acrossMonitorUnitSignalService.createAcrossMonitorUnitSignal(signal);
        } catch (Exception e) {
            log.error("Failed to create across monitor unit signal for signal", e);
            return false;
        }
    }

    @Override
    public List<SimplifySignalDTO> getSimplifySignalsForSignal(Integer equipmentTemplateId) {
        try {
            return signalService.findSimplifySignals(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to get simplify signals for signal", e);
            return List.of();
        }
    }

    @Override
    public List<SignalConfigItem> getSignalListForSignal(Integer equipmentTemplateId, Integer equipmentId) {
        try {
            return signalService.findItemByEquipmentTemplateIdAndEquipmentId(equipmentTemplateId, equipmentId);
        } catch (Exception e) {
            log.error("Failed to get signal list for signal: equipmentTemplateId={}, equipmentId={}", equipmentTemplateId, equipmentId, e);
            return List.of();
        }
    }

    @Override
    public Signal createForSignal(SignalConfigItem signalConfigItem) {
        try {
            return signalService.createSignal(signalConfigItem);
        } catch (Exception e) {
            log.error("Failed to create signal", e);
            return null;
        }
    }

    @Override
    public Signal updateForSignal(SignalConfigItem signalConfigItem) {
        try {
            return signalService.updateSignal(signalConfigItem);
        } catch (Exception e) {
            log.error("Failed to update signal", e);
            return null;
        }
    }

    @Override
    public boolean batchDeleteForSignal(Integer equipmentTemplateId, List<Integer> signalIds) {
        try {
            return signalService.batchDeleteSignal(equipmentTemplateId, signalIds) > 0;
        } catch (Exception e) {
            log.error("Failed to batch delete signal: equipmentTemplateId={}", equipmentTemplateId, e);
            return false;
        }
    }

    @Override
    public boolean fieldCopyForSignal(List<SignalFieldCopyDTO> signalFieldCopyList) {
        try {
            return signalService.fieldCopySignal(signalFieldCopyList);
        } catch (Exception e) {
            log.error("Failed to field copy for signal", e);
            return false;
        }
    }

    @Override
    public Event createForEvent(EventConfigItem eventConfigItem) {
        try {
            return eventService.createEvent(eventConfigItem);
        } catch (Exception e) {
            log.error("Failed to create event", e);
            return null;
        }
    }

    @Override
    public Event updateForEvent(EventConfigItem eventConfigItem) {
        try {
            return eventService.updateEvent(eventConfigItem);
        } catch (Exception e) {
            log.error("Failed to update event", e);
            return null;
        }
    }

    @Override
    public boolean batchUpdateForEvent(BatchEventConfigItem batchEventConfigItem) {
        try {
            return eventService.batchUpdateEvent(batchEventConfigItem);
        } catch (Exception e) {
            log.error("Failed to batch update event", e);
            return false;
        }
    }

    @Override
    public boolean deleteForEvent(Integer equipmentTemplateId, Integer eventId) {
        try {
            int result = eventService.deleteEvent(equipmentTemplateId, eventId);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to delete event: equipmentTemplateId={}, eventId={}", equipmentTemplateId, eventId, e);
            return false;
        }
    }

    @Override
    public List<EventConfigItem> findEventItemByEquipmentTemplateIdForEvent(Integer equipmentTemplateId){
        try {
            return eventService.findEventItemByEquipmentTemplateId(equipmentTemplateId);
        }
        catch (Exception e){
            log.error("Failed to find event item by equipment template id: {}", equipmentTemplateId, e);
            return List.of();
        }
    }

    @Override
    public boolean fieldCopyForEvent(List<EventFieldCopyDTO> eventFieldCopyList) {
        try {
            return eventService.fieldCopyEvent(eventFieldCopyList);
        } catch (Exception e) {
            log.error("Failed to field copy for event", e);
            return false;
        }
    }

    @Override
    public boolean linkEventForEvent(Integer equipmentTemplateId, Integer signalId) {
        try {
            return eventService.linkEvent(equipmentTemplateId, signalId);
        } catch (Exception e) {
            log.error("Failed to link event for event: equipmentTemplateId={}, signalId={}", equipmentTemplateId, signalId, e);
            return false;
        }
    }




    @Override
    public Page<OperationDetailVO> getEquipmentTemplateLogPageForDeviceManagement(Map<String, Object> params) {
        try {
            return operationDetailService.findEquipmentTemplateLogPage(params);
        } catch (Exception e) {
            log.error("Failed to get equipment template log page for device management", e);
            return new Page<>();
        }
    }

    @Override
    public Page<OperationDetailVO> getEquipmentLogPageForDeviceManagement(Map<String, Object> params) {
        try {
            return operationDetailService.findEquipmentLogPage(params);
        } catch (Exception e) {
            log.error("Failed to get equipment log page for device management", e);
            return new Page<>();
        }
    }


    @Override
    public List<IdValueDTO<Integer, String>> getOperationTypesForDeviceManagement() {
        try {
            return operationDetailService.findOperationTypes();
        } catch (Exception e) {
            log.error("Failed to get operation types for device management", e);
            return List.of();
        }
    }

    @Override
    public EquipmentCategoryMapDTO findEquipmentCategoryMapDTOForEquipmentCategoryMap(Integer originCategory) {
        try {
            return categoryIdMapService.findEquipmentCategoryMapDTO(originCategory);
        } catch (Exception e) {
            log.error("Failed to get operation types for device management", e);
            return null;
        }
    }

    // ==================== 控制管理相关方法实现 ====================

    @Override
    public boolean createForControl(ControlConfigItem control) {
        try {
            controlService.createControl(control);
            return true;
        } catch (Exception e) {
            log.error("Failed to create control", e);
            return false;
        }
    }

    @Override
    public boolean updateForControl(ControlConfigItem control) {
        try {
            controlService.updateControlByControl(control);
            return true;
        } catch (Exception e) {
            log.error("Failed to update control", e);
            return false;
        }
    }

    @Override
    public boolean deleteForControl(Integer equipmentTemplateId, Integer controlId) {
        try {
            controlService.deleteControl(equipmentTemplateId, controlId);
            return true;
        } catch (Exception e) {
            log.error("Failed to delete control: equipmentTemplateId={}, controlId={}", equipmentTemplateId, controlId, e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteForControl(Integer equipmentTemplateId, List<Integer> controlIds) {
        try {
            controlService.batchDeleteControl(equipmentTemplateId, controlIds);
            return true;
        } catch (Exception e) {
            log.error("Failed to batch delete control: equipmentTemplateId={}", equipmentTemplateId, e);
            return false;
        }
    }

    @Override
    public List<ControlConfigItem> findControlListForControl(Integer equipmentTemplateId) {
        try {
            return controlService.findItemByEquipmentTemplateId(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to find control list for control: equipmentTemplateId={}", equipmentTemplateId, e);
            return List.of();
        }
    }

    @Override
    public List<ControlConfigPointDTO> findControlPointsForControl(Integer equipmentTemplateId) {
        try {
            return controlService.findControlPoints(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to find control points for control: equipmentTemplateId={}", equipmentTemplateId, e);
            return List.of();
        }
    }

    @Override
    public ControlConfigItem getControlInfoForControl(Integer equipmentTemplateId, Integer controlId) {
        try {
            return controlService.getControlInfo(equipmentTemplateId, controlId);
        } catch (Exception e) {
            log.error("Failed to get control info for control: equipmentTemplateId={}, controlId={}", equipmentTemplateId, controlId, e);
            return null;
        }
    }

    @Override
    public ControlConfigItem getControlCategoryForControl(Integer stationId, Integer equipmentId, Integer controlId) {
        try {
            // 首先根据设备ID获取设备信息
            Equipment equipment = equipmentService.findEquipmentById(equipmentId);
            if (equipment == null) {
                log.warn("Equipment not found for ID: {}", equipmentId);
                return null;
            }

            // 验证站点ID（如果提供）
            if (stationId != null && !stationId.equals(equipment.getStationId())) {
                log.warn("Invalid station ID: {} for equipment: {}", stationId, equipmentId);
                return null;
            }

            // 获取控制信息
            return controlService.getControlInfo(equipment.getEquipmentTemplateId(), controlId);
        } catch (Exception e) {
            log.error("Failed to get control category for control: stationId={}, equipmentId={}, controlId={}", stationId, equipmentId, controlId, e);
            return null;
        }
    }

    @Override
    public boolean fieldCopyForControl(List<CommandFieldCopyDTO> commandFieldCopyList) {
        try {
            return controlService.fieldCopy(commandFieldCopyList);
        } catch (Exception e) {
            log.error("Failed to copy fields for control", e);
            return false;
        }
    }

    @Override
    public void disposeSimilarControlForControl(SimilarDataDTO similarDataDTO) {
        try {
            controlService.disposeSimilarControl(similarDataDTO);
        } catch (Exception e) {
            log.error("Failed to dispose similar control for control", e);
            throw e;
        }
    }

    // ==================== 操作日志相关方法实现（按tcs-config原始接口） ====================

    @Override
    public Page<OperationDetailVO> findPageForOperationDetail(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO) {
        try {
            return operationDetailService.findPage(page, operationDetailDTO);
        } catch (Exception e) {
            log.error("Failed to find page for operation detail", e);
            return new Page<>();
        }
    }

    @Override
    public Page<OperationDetail> findPageByObjectTypeAndObjectIdForOperationDetail(Page<OperationDetail> page, String objectType, String objectId) {
        try {
            return operationDetailService.findPageByObjectTypeAndObjectId(page, objectType, objectId);
        } catch (Exception e) {
            log.error("Failed to find page by object type and object id for operation detail", e);
            return new Page<>();
        }
    }

    @Override
    public Page<OperationDetailVO> findEquipmentTemplateLogPageForOperationDetail(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO) {
        try {
            return operationDetailService.findEquipmentTemplateLogPage(page, operationDetailDTO);
        } catch (Exception e) {
            log.error("Failed to find equipment template log page for operation detail", e);
            return new Page<>();
        }
    }

    @Override
    public Page<OperationDetailVO> findEquipmentLogPageForOperationDetail(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO) {
        try {
            return operationDetailService.findEquipmentLogPage(page, operationDetailDTO);
        } catch (Exception e) {
            log.error("Failed to find equipment log page for operation detail", e);
            return new Page<>();
        }
    }


    // ==================== 表达式相关方法实现 ====================

    @Override
    public List<SignalExpressionDTO> findSignalExpressionForExpression(Integer equipmentTemplateId) {
        try {
            return expressionService.findSignalExpression(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to find signal expression for expression: equipmentTemplateId={}", equipmentTemplateId, e);
            return List.of();
        }
    }

    @Override
    public List<SignalExpressionDTO> findSignalExpressionByEquipmentIdForExpression(Integer equipmentId) {
        try {
            Equipment equipment = equipmentService.findEquipmentById(equipmentId);
            if (equipment == null) {
                log.warn("Equipment not found for ID: {}", equipmentId);
                return List.of();
            }
            return expressionService.findSignalExpression(equipment.getEquipmentTemplateId());
        } catch (Exception e) {
            log.error("Failed to find signal expression by equipment id for expression: equipmentId={}", equipmentId, e);
            return List.of();
        }
    }

    @Override
    public List<EventExpressionDTO> findEventExpressionForExpression(Integer equipmentTemplateId) {
        try {
            return expressionService.findEventExpression(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to find event expression for expression: equipmentTemplateId={}", equipmentTemplateId, e);
            return List.of();
        }
    }

    @Override
    public List<EventExpressionDTO> findEventExpressionByEquipmentIdForExpression(Integer equipmentId) {
        try {
            Equipment equipment = equipmentService.findEquipmentById(equipmentId);
            if (equipment == null) {
                log.warn("Equipment not found for ID: {}", equipmentId);
                return List.of();
            }
            return expressionService.findEventExpression(equipment.getEquipmentTemplateId());
        } catch (Exception e) {
            log.error("Failed to find event expression by equipment id for expression: equipmentId={}", equipmentId, e);
            return List.of();
        }
    }

    @Override
    public boolean validateExpressionForExpression(String expression) {
        try {
            return expressionService.validateExpression(expression);
        } catch (Exception e) {
            log.error("Failed to validate expression for expression: expression={}", expression, e);
            return false;
        }
    }

    @Override
    public String expressionAnalysisForExpression(ExpressionDTO expressionDTO) {
        try {
            return expressionService.expressionAnalysis(expressionDTO);
        } catch (Exception e) {
            log.error("Failed to analysis expression for expression", e);
            return "";
        }
    }

    @Override
    public ExpressionValidationResult validateComplexIndexExpressionForExpression(String expression) {
        try {
            return expressionService.validateComplexIndexExpression(expression);
        } catch (Exception e) {
            log.error("Failed to validate complex index expression for expression: expression={}", expression, e);
            return new ExpressionValidationResult(false, "验证过程中发生错误: " + e.getMessage());
        }
    }

    // ==================== 配置任务管理相关方法实现 ====================

    @Override
    public TaskStatus createConfigGenerationTaskForConfigTask(String taskId, List<Integer> monitorUnitIds, String userId) {
        return configTaskProvider.createConfigGenerationTask(taskId, monitorUnitIds, userId);
    }

    @Override
    public TaskStatus createConfigDistributionTaskForConfigTask(String taskId, List<Integer> monitorUnitIds,
                                                                String username, String password, Integer port,
                                                                String protocol, String userId) {
        return configTaskProvider.createConfigDistributionTask(taskId, monitorUnitIds, username, password, port, protocol, userId);
    }

    @Override
    public TaskStatusVO getTaskStatusForConfigTask(String taskId) {
        return configTaskProvider.getTaskStatus(taskId);
    }

    @Override
    public Page<TaskStatusVO> getTaskHistoryForConfigTask(int page, int size, String taskType) {
        return configTaskProvider.getTaskHistory(page, size, taskType);
    }

    @Override
    public void cancelTaskForConfigTask(String taskId) {
        configTaskProvider.cancelTask(taskId);
    }


    @Override
    public TaskStatus createRemoteConfigDownloadTaskForConfigTask(String taskId, Integer monitorUnitId,
                                                                  String protocol, Integer port,
                                                                  String username, String password, String userId) {
        return configTaskProvider.createRemoteConfigDownloadTask(taskId, monitorUnitId, protocol, port, username, password, userId);
    }

    @Override
    public File getLatestRemoteConfigFileForMonitorUnitXml(Integer monitorUnitId) {
        return collectorDownloadService.getLatestRemoteConfigFile(monitorUnitId);
    }

    @Override
    public TaskStatus createConfigBackupTaskForConfigTask(String taskId, Integer monitorUnitId, String userId) {
        return configTaskProvider.createConfigBackupTask(taskId, monitorUnitId, userId);
    }

    @Override
    public File getLatestBackupFileForConfigTask(Integer monitorUnitId) {
        return configTaskProvider.getLatestBackupFile(monitorUnitId);
    }

    @Override
    public boolean hasRecentBackupForConfigTask(Integer monitorUnitId, int minutes) {
        return configTaskProvider.hasRecentBackup(monitorUnitId, minutes);
    }

    // ==================== 监控单元配置文件下载相关方法实现 ====================

    @Override
    public byte[] downloadMultipleMonitorUnitConfigXMLForMonitorUnitXml(String monitorUnitIds) {
        try {
            String[] ids = monitorUnitIds.split(",");
            List<Integer> monitorUnitIdList = java.util.Arrays.stream(ids)
                    .map(Integer::parseInt)
                    .collect(java.util.stream.Collectors.toList());

            return monitorUnitXmlService.zipMultipleMonitorUnitConfigXML(monitorUnitIdList);
        } catch (Exception e) {
            log.error("Failed to download multiple monitor unit config XML", e);
            throw new RuntimeException("下载监控单元配置文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public TaskStatus createConfigImportTaskForConfigTask(String taskId,
                                                          MultipartFile file,
                                                          ConfigImportTaskRequest request) {
        try {
            // 创建任务状态记录
            TaskStatus  taskStatus = taskStatusService.createTask(taskId, TaskTypeEnum.CONFIGURATION_IMPORT, null);

            // 先保存文件到临时位置，避免异步任务中MultipartFile失效
            // 使用绝对路径，基于当前工作目录
            String workingDir = System.getProperty("user.dir");
            java.io.File tempDir = new java.io.File(workingDir, "plugins/south-omc-siteweb/workspace/temp");
            tempDir.mkdirs();

            String fileName = "config_import_" + taskId + ".zip";
            java.io.File savedFile = new java.io.File(tempDir, fileName);
            file.transferTo(savedFile);

            // 异步执行导入任务
            CompletableFuture.runAsync(() -> {
                try {
                    configImportService.executeImportFromFile(taskId, savedFile.getPath(), request);
                } catch (Exception e) {
                    log.error("配置导入任务执行失败: {}", taskId, e);
                    taskStatusService.setTaskError(taskId, "配置导入任务执行失败: " + e.getMessage());
                } finally {
                    // 清理保存的文件
                    if (savedFile.exists()) {
                        savedFile.delete();
                    }
                }
            });

            return taskStatus;

        } catch (Exception e) {
            log.error("Failed to create config import task", e);
            throw new RuntimeException("创建配置导入任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ConfigUploadAndDiffResult uploadAndCompareConfigForConfigTask(
            MultipartFile file) {
        try {
            return configImportService.uploadAndCompareConfig(file);
        } catch (Exception e) {
            log.error("Failed to upload and compare config", e);
            throw new RuntimeException("上传和对比配置失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ConfigImportReport confirmImportFromUploadForConfigTask(
            String uploadTaskId, ConfigImportTaskRequest request) {
        try {
            return configImportService.confirmImportFromUpload(uploadTaskId, request);
        } catch (Exception e) {
            log.error("Failed to confirm import from upload, taskId: {}", uploadTaskId, e);
            throw new RuntimeException("确认导入失败: " + e.getMessage(), e);
        }
    }


    @Override
    public String findMonitorUnitLogForMonitorUnit(Integer monitorUnitId) {
        try {
            return monitorUnitService.findMonitorUnitLog(monitorUnitId);
        } catch (Exception e) {
            log.error("Failed to find monitor unit log for monitor unit: {}", monitorUnitId, e);
            return "";
        }
    }
}
