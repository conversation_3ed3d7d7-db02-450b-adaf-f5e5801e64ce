package com.siteweb.tcs.middleware.controller.crud;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.i18n.NamespacedMessageSource;
import com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity;
import com.siteweb.tcs.middleware.service.ResourceConfigurationService;
import com.siteweb.tcs.middleware.service.ResourceRuntimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资源配置控制器
 * 提供资源配置的CRUD接口
 */
@RestController
@RequestMapping("/middleware/resource-configs")
@Tag(name = "资源配置管理", description = "资源配置的CRUD接口")
public class ResourceConfigController {

    @Autowired
    private ResourceConfigurationService resourceConfigurationService;
    @Autowired
    private ResourceRuntimeService resourceRuntimeService;

    @Autowired
    @Qualifier("middlewareMessageSource")
    private NamespacedMessageSource messageSource;
    /**
     * 分页查询资源配置
     *
     * @param current 当前页
     * @param size 每页大小
     * @param name 资源配置名称（可选，用于模糊查询）
     * @param resourceTypeId 资源类型ID（可选，用于筛选）
     * @return 分页结果
     */
    @GetMapping(value = "/page")
    @Operation(summary = "分页查询资源配置", description = "分页查询资源配置，支持按ID或名称关键字搜索和资源类型筛选")
    public ResponseResult<IPage<ResourceConfigurationEntity>> page(
            @Parameter(description = "当前页码", required = true) @RequestParam(defaultValue = "1") long current,
            @Parameter(description = "每页大小", required = true) @RequestParam(defaultValue = "10") long size,
            @Parameter(description = "搜索关键字（可选，支持ID或名称）") @RequestParam(required = false) String keyword,
            @Parameter(description = "资源类型ID（可选）") @RequestParam(required = false) String resourceTypeId) {

        Page<ResourceConfigurationEntity> page = new Page<>(current, size);
        IPage<ResourceConfigurationEntity> result = resourceConfigurationService.pageResourceConfigurations(page, resourceTypeId, keyword);

        return ResponseResult.success(result);
    }

    /**
     * 获取所有资源配置
     *
     * @return 所有资源配置列表
     */
    @GetMapping(value = "/list")
    @Operation(summary = "获取所有资源配置", description = "获取所有资源配置列表")
    public ResponseResult<List<ResourceConfigurationEntity>> list() {
        List<ResourceConfigurationEntity> list = resourceConfigurationService.list();
        return ResponseResult.success(list);
    }

    /**
     * 根据ID获取资源配置
     *
     * @param id 资源配置ID
     * @return 资源配置详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取资源配置", description = "根据ID获取资源配置详情")
    public ResponseResult<ResourceConfigurationEntity> getById(@PathVariable String id) {
        ResourceConfigurationEntity resourceConfig = resourceConfigurationService.getResourceConfigurationById(id);
        return ResponseResult.success(resourceConfig);
    }

    /**
     * 根据资源类型ID获取资源配置列表
     *
     * @param resourceTypeId 资源类型ID
     * @return 资源配置列表
     */
    @GetMapping("/resource-type/{resourceTypeId}")
    @Operation(summary = "根据资源类型获取资源配置", description = "根据资源类型ID获取资源配置列表")
    public ResponseResult<List<ResourceConfigurationEntity>> getByResourceTypeId(@PathVariable String resourceTypeId) {
        // 使用分页查询，但只查询指定资源类型的配置
        Page<ResourceConfigurationEntity> page = new Page<>(1, 1000); // 设置较大的页面大小
        IPage<ResourceConfigurationEntity> result = resourceConfigurationService.pageResourceConfigurations(page, resourceTypeId, null);
        List<ResourceConfigurationEntity> list = result.getRecords();
        return ResponseResult.success(list);
    }

    /**
     * 创建资源配置
     *
     * @param resourceConfig 资源配置实体
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建资源配置", description = "创建新的资源配置")
    public ResponseResult<ResourceConfigurationEntity> create(@RequestBody ResourceConfigurationEntity resourceConfig) {
        ResourceConfigurationEntity created = resourceConfigurationService.createResourceConfiguration(resourceConfig);
        boolean success = created != null;
        if (success) {
            return ResponseResult.success(created);
        } else {
            return ResponseResult.fail("创建资源配置失败");
        }
    }

    /**
     * 更新资源配置
     *
     * @param id 资源配置ID
     * @param resourceConfig 资源配置实体
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新资源配置", description = "更新指定ID的资源配置")
    public ResponseResult<ResourceConfigurationEntity> update(@PathVariable String id, @RequestBody ResourceConfigurationEntity resourceConfig) {
        if (resourceRuntimeService.isResourceInstantiated(id)){
            throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                    "Resource is instantiated, cannot update"
            );
        }
        resourceConfig.setId(id);
        ResourceConfigurationEntity updated = resourceConfigurationService.updateResourceConfiguration(id, resourceConfig);
        boolean success = updated != null;
        if (success) {
            return ResponseResult.success(updated);
        } else {
            return ResponseResult.fail("更新资源配置失败");
        }
    }

    /**
     * 删除资源配置
     *
     * @param id 资源配置ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除资源配置", description = "删除指定ID的资源配置")
    public ResponseResult<Boolean> delete(@PathVariable String id) {
        if (resourceRuntimeService.isResourceInstantiated(id)){
            throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                    "Resource is instantiated, cannot delete"
            );
        }
        boolean success = resourceConfigurationService.deleteResourceConfiguration(id);
        if (success) {
            return ResponseResult.success(true);
        } else {
            return ResponseResult.fail("删除资源配置失败");
        }
    }

    /**
     * 启用资源配置
     *
     * @param id 资源配置ID
     * @return 启用结果
     */
    @PostMapping("/enable/{id}")
    @Operation(summary = "启用资源配置", description = "启用指定资源配置")
    public ResponseResult<Boolean> enable(@PathVariable String id) {
        ResourceConfigurationEntity entity = resourceConfigurationService.getResourceConfigurationById(id);
        if (entity == null) {
            return ResponseResult.fail(messageSource.getMessage("resource.config.not_found"));
        }

        entity.setStatus("ENABLED");
        ResourceConfigurationEntity updated = resourceConfigurationService.updateResourceConfiguration(id, entity);
        boolean success = updated != null;

        if (success) {
            return ResponseResult.success(true);
        } else {
            return ResponseResult.fail(messageSource.getMessage("resource.config.update.failed"));
        }
    }

    /**
     * 禁用资源配置
     *
     * @param id 资源配置ID
     * @return 禁用结果
     */
    @PostMapping("/disable/{id}")
    @Operation(summary = "禁用资源配置", description = "禁用指定资源配置")
    public ResponseResult<Boolean> disable(@PathVariable String id) {
        if (resourceRuntimeService.isResourceInstantiated(id)){
            throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                    "Resource is instantiated, cannot disable"
            );
        }
        ResourceConfigurationEntity entity = resourceConfigurationService.getResourceConfigurationById(id);
        if (entity == null) {
            return ResponseResult.fail(messageSource.getMessage("resource.config.not_found"));
        }

        entity.setStatus("DISABLED");
        ResourceConfigurationEntity updated = resourceConfigurationService.updateResourceConfiguration(id, entity);
        boolean success = updated != null;

        if (success) {
            return ResponseResult.success(true);
        } else {
            return ResponseResult.fail(messageSource.getMessage("resource.config.update.failed"));
        }
    }
}
