package com.siteweb.tcs.middleware.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 文件上传请求DTO
 */
@Schema(description = "文件上传请求")
public class FileUploadRequest {

    @Schema(description = "服务配置ID", required = true, example = "test-minio-filesystem-service-001")
    private String serviceConfigId;

    @Schema(description = "文件路径", required = true, example = "documents/2024")
    private String filePath;

    @Schema(description = "文件名", required = true, example = "report.pdf")
    private String fileName;

    // 构造函数
    public FileUploadRequest() {
    }

    public FileUploadRequest(String serviceConfigId, String filePath, String fileName) {
        this.serviceConfigId = serviceConfigId;
        this.filePath = filePath;
        this.fileName = fileName;
    }

    // Getter和Setter方法
    public String getServiceConfigId() {
        return serviceConfigId;
    }

    public void setServiceConfigId(String serviceConfigId) {
        this.serviceConfigId = serviceConfigId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public String toString() {
        return "FileUploadRequest{" +
                "serviceConfigId='" + serviceConfigId + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileName='" + fileName + '\'' +
                '}';
    }
}
