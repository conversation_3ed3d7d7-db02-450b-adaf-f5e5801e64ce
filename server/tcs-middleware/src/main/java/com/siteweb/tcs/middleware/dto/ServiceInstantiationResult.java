package com.siteweb.tcs.middleware.dto;

import com.siteweb.tcs.middleware.common.service.ServiceStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 服务实例化结果
 * 用于避免序列化复杂的Service对象时出现循环引用
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceInstantiationResult {
    
    /**
     * 服务ID
     */
    private String id;
    
    /**
     * 服务类型
     */
    private String type;
    
    /**
     * 服务名称
     */
    private String name;
    
    /**
     * 服务描述
     */
    private String description;
    
    /**
     * 服务状态
     */
    private ServiceStatus status;
    
    /**
     * 关联的资源ID（如果有）
     */
    private String resourceId;
    
    /**
     * 是否成功实例化
     */
    private boolean success;
    
    /**
     * 错误信息（如果实例化失败）
     */
    private String errorMessage;
    
    /**
     * 创建成功的实例化结果
     *
     * @param id 服务ID
     * @param type 服务类型
     * @param name 服务名称
     * @param description 服务描述
     * @param status 服务状态
     * @param resourceId 关联的资源ID
     * @return 成功的实例化结果
     */
    public static ServiceInstantiationResult success(String id, String type, String name, String description, ServiceStatus status, String resourceId) {
        return new ServiceInstantiationResult(id, type, name, description, status, resourceId, true, null);
    }
    
    /**
     * 创建失败的实例化结果
     *
     * @param id 服务ID
     * @param errorMessage 错误信息
     * @return 失败的实例化结果
     */
    public static ServiceInstantiationResult failure(String id, String errorMessage) {
        return new ServiceInstantiationResult(id, null, null, null, null, null, false, errorMessage);
    }
}
