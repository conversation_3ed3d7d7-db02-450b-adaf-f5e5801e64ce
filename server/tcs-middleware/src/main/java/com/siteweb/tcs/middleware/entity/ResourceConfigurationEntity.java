package com.siteweb.tcs.middleware.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler;
import com.siteweb.tcs.common.util.JsonMapTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 资源配置实体�?
 */
@Data
@TableName(value = "mw_resource_configuration", autoResultMap = true)
public class ResourceConfigurationEntity {

    /**
     * 资源配置唯一标识
     */
    @TableId
    private String id;

    /**
     * 关联的资源类型ID
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 资源类型名称（非数据库字段，用于展示
     */
    @TableField(exist = false)
    private String resourceTypeName;

    /**
     * 资源配置名称
     */
    private String name;

    /**
     * 资源配置描述
     */
    private String description;

    /**
     * 资源配置详情（JSON）
     */
    @TableField(typeHandler = EnhancedJacksonTypeHandler.class)
    private Map<String, Object> config;

    /**
     * 资源配置的管理状�?
     */
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建�?
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新�?
     */
    @TableField("updated_by")
    private String updatedBy;
}

