package com.siteweb.tcs.middleware.service;

import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.dto.ResourceInstantiationResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;

import java.util.List;
import java.util.Map;

/**
 * 资源运行时服务接口
 * 提供资源的运行时操作，如连接测试、实例化、健康检查等
 */
public interface ResourceRuntimeService {

    /**
     * 测试资源连接
     *
     * @param resourceTypeId 资源类型ID
     * @param config 资源配置
     * @return 连接测试结果
     */
    ConnectionTestResult testConnection(String resourceTypeId, Map<String, Object> config);

    /**
     * 验证资源配置
     *
     * @param resourceTypeId 资源类型ID
     * @param config 资源配置
     * @return 验证结果
     */
    ValidationResult validateConfig(String resourceTypeId, Map<String, Object> config);

    /**
     * 检查资源是否已实例化
     *
     * @param resourceConfigId 资源配置ID
     * @return 是否已实例化
     */
    boolean isResourceInstantiated(String resourceConfigId);

    /**
     * 批量检查资源是否已实例化
     *
     * @param resourceConfigIds 资源配置ID列表
     * @return 资源配置ID到实例化状态的映射
     */
    Map<String, Boolean> batchCheckResourceInstantiated(List<String> resourceConfigIds);

    /**
     * 实例化资源
     *
     * @param resourceConfigId 资源配置ID
     * @return 资源实例化结果
     */
    ResourceInstantiationResult instantiateResource(String resourceConfigId);

    /**
     * 销毁资源实例
     *
     * @param resourceConfigId 资源配置ID
     * @return 是否成功销毁
     */
    boolean destroyResource(String resourceConfigId);

    /**
     * 检查资源健康状态
     *
     * @param resourceConfigId 资源配置ID
     * @return 健康状态
     */
    HealthStatus checkResourceHealth(String resourceConfigId);
}
