package com.siteweb.tcs.middleware.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.middleware.entity.ServiceTypeEntity;

import java.util.List;

/**
 * 服务类型服务接口
 */
public interface ServiceTypeService extends IService<ServiceTypeEntity> {
    
    /**
     * 分页查询服务类型
     *
     * @param page 分页参数
     * @param name 服务类型名称，可为null
     * @return 分页结果
     */
    IPage<ServiceTypeEntity> pageServiceTypes(Page<ServiceTypeEntity> page, String name);
    
    /**
     * 根据ID查询服务类型
     *
     * @param id 服务类型ID
     * @return 服务类型实体
     */
    ServiceTypeEntity getServiceTypeById(String id);
    
    /**
     * 创建服务类型
     *
     * @param serviceType 服务类型实体
     * @return 创建后的服务类型实体
     */
    ServiceTypeEntity createServiceType(ServiceTypeEntity serviceType);
    
    /**
     * 更新服务类型
     *
     * @param id 服务类型ID
     * @param serviceType 服务类型实体
     * @return 更新后的服务类型实体
     */
    ServiceTypeEntity updateServiceType(String id, ServiceTypeEntity serviceType);
    
    /**
     * 删除服务类型
     *
     * @param id 服务类型ID
     * @return 是否删除成功
     */
    boolean deleteServiceType(String id);
    
    /**
     * 根据支持的资源类别查询服务类型列表
     *
     * @param supportedResourceCategory 支持的资源类别
     * @return 服务类型列表
     */
    List<ServiceTypeEntity> listServiceTypesBySupportedResourceCategory(String supportedResourceCategory);
}
