package com.siteweb.tcs.middleware.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.middleware.entity.ResourceTypeEntity;
import com.siteweb.tcs.middleware.mapper.ResourceTypeMapper;
import com.siteweb.tcs.middleware.service.ResourceTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 资源类型服务实现类
 */
@Service
public class ResourceTypeServiceImpl extends ServiceImpl<ResourceTypeMapper, ResourceTypeEntity> implements ResourceTypeService {
    
    @Override
    public IPage<ResourceTypeEntity> pageResourceTypes(Page<ResourceTypeEntity> page, String category, String name) {
        LambdaQueryWrapper<ResourceTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(category)) {
            queryWrapper.eq(ResourceTypeEntity::getCategory, category);
        }
        if (StringUtils.hasText(name)) {
            queryWrapper.like(ResourceTypeEntity::getName, name);
        }
        queryWrapper.orderByDesc(ResourceTypeEntity::getCreateTime);
        
        return page(page, queryWrapper);
    }
    
    @Override
    public ResourceTypeEntity getResourceTypeById(String id) {
        return getById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResourceTypeEntity createResourceType(ResourceTypeEntity resourceType) {
        // 生成ID
        resourceType.setId(UUID.randomUUID().toString().replace("-", ""));
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        resourceType.setCreateTime(now);
        resourceType.setUpdateTime(now);
        
        // 保存实体
        save(resourceType);
        
        return resourceType;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResourceTypeEntity updateResourceType(String id, ResourceTypeEntity resourceType) {
        ResourceTypeEntity entity = getById(id);
        if (entity == null) {
            return null;
        }
        
        // 更新属性
        resourceType.setId(id); // 确保ID不变
        resourceType.setUpdateTime(LocalDateTime.now());
        
        // 更新实体
        updateById(resourceType);
        
        return resourceType;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteResourceType(String id) {
        return removeById(id);
    }
    
    @Override
    public List<ResourceTypeEntity> listResourceTypesByCategory(String category) {
        LambdaQueryWrapper<ResourceTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(category)) {
            queryWrapper.eq(ResourceTypeEntity::getCategory, category);
        }
        queryWrapper.orderByAsc(ResourceTypeEntity::getName);
        
        return list(queryWrapper);
    }
}
