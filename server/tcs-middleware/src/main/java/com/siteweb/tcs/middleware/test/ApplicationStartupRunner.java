package com.siteweb.tcs.middleware.test;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class ApplicationStartupRunner implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(ApplicationStartupRunner.class);

    // 注入你的资源初始化服务，这个服务本身是普通Bean，可能就不会被AOP代理
    @Autowired
    private ResourceServiceInitializerTest resourceServiceInitializerTest;


    @Override
    public void run(String... args) throws Exception {
        log.info("中间件启动Runner开始执行...");

        // 在这里调用你的初始化服务的方法
//        if (resourceServiceInitializerTest != null) {
//            resourceServiceInitializerTest.initializeResources();
//        } else {
//            log.error("ResourceInitializerService 未能成功注入！无法执行资源初始化。");
//        }

        // 测试SitewebPersistentService
//        if (sitewebPersistentServiceTest != null) {
//            log.info("开始测试SitewebPersistentService...");
//            sitewebPersistentServiceTest.runAllTests();
//        } else {
//            log.error("SitewebPersistentServiceTest 未能成功注入！");
//        }

        log.info("应用程序启动Runner执行完毕。");
    }
}