package com.siteweb.tcs.middleware.util;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 敏感信息加密工具类
 * 用于加密/解密数据库中存储的敏感信息，如密码、证书等
 */
@Component
public class EncryptionUtil {

    private static final String ENCRYPTION_PREFIX = "ENC(";
    private static final String ENCRYPTION_SUFFIX = ")";
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final int IV_LENGTH = 16;

    @Value("${middleware.encryption.secret-key:default-dev-key-not-for-production}")
    private String secretKey;

    @Value("${middleware.encryption.enabled:true}")
    private boolean encryptionEnabled;

    /**
     * 加密字符串
     *
     * @param value 需要加密的字符串
     * @return 加密后的字符串，格式为 ENC(base64编码的密文)
     */
    public String encrypt(String value) {
        if (!encryptionEnabled || value == null || value.isEmpty()) {
            return value;
        }

        // 如果已经是加密格式，则不再加密
        if (isEncrypted(value)) {
            return value;
        }

        try {
            // 生成随机IV
            byte[] iv = new byte[IV_LENGTH];
            new SecureRandom().nextBytes(iv);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);

            // 初始化加密器
            SecretKeySpec keySpec = new SecretKeySpec(getKey(), "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec);

            // 加密
            byte[] encrypted = cipher.doFinal(value.getBytes(StandardCharsets.UTF_8));

            // 将IV和密文拼接后Base64编码
            byte[] combined = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);

            return ENCRYPTION_PREFIX + Base64.getEncoder().encodeToString(combined) + ENCRYPTION_SUFFIX;
        } catch (Exception e) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Failed to encrypt value: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 解密字符串
     *
     * @param encryptedValue 加密的字符串，格式为 ENC(base64编码的密文)
     * @return 解密后的原始字符串
     */
    public String decrypt(String encryptedValue) {
        if (!encryptionEnabled || encryptedValue == null || encryptedValue.isEmpty()) {
            return encryptedValue;
        }

        // 如果不是加密格式，则直接返回
        if (!isEncrypted(encryptedValue)) {
            return encryptedValue;
        }

        try {
            // 提取密文
            String base64Value = encryptedValue.substring(
                    ENCRYPTION_PREFIX.length(),
                    encryptedValue.length() - ENCRYPTION_SUFFIX.length()
            );

            // Base64解码
            byte[] combined = Base64.getDecoder().decode(base64Value);

            // 分离IV和密文
            byte[] iv = new byte[IV_LENGTH];
            byte[] encrypted = new byte[combined.length - IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, IV_LENGTH);
            System.arraycopy(combined, IV_LENGTH, encrypted, 0, encrypted.length);

            // 初始化解密器
            SecretKeySpec keySpec = new SecretKeySpec(getKey(), "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv));

            // 解密
            byte[] original = cipher.doFinal(encrypted);
            return new String(original, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Failed to decrypt value: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 判断字符串是否已加密
     *
     * @param value 需要判断的字符串
     * @return 是否已加密
     */
    public boolean isEncrypted(String value) {
        return value != null &&
               value.startsWith(ENCRYPTION_PREFIX) &&
               value.endsWith(ENCRYPTION_SUFFIX);
    }

    /**
     * 处理配置中的敏感信息
     * 如果是加密格式，则解密；否则加密
     *
     * @param config 配置JSON字符串
     * @return 处理后的配置JSON字符串
     */
    public String processConfig(String config) {
        if (config == null || config.isEmpty()) {
            return config;
        }

        // 匹配 "password": "ENC(...)" 类似的字段
        Pattern pattern = Pattern.compile("\"(password|secret|key)\"\\s*:\\s*\"ENC\\(([^)]*)\\)\"");
        Matcher matcher = pattern.matcher(config);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            String fieldName = matcher.group(1);
            String encValue = matcher.group(2);
            String decrypted = decrypt(encValue);
            matcher.appendReplacement(sb, "\"" + fieldName + "\": \"" + decrypted + "\"");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 处理配置中需要加密的字段
     *
     * @param config 配置信息
     */
    public void encryptConfigFields(Map<String, Object> config) {
        if (config == null) {
            return;
        }

        // 需要加密的字段列表，如密码、密钥等
        String[] sensitiveFields = {"password", "secret", "key", "token", "credential"};

        for (String field : sensitiveFields) {
            if (config.containsKey(field) && config.get(field) instanceof String) {
                String value = (String) config.get(field);
                if (value != null && !value.isEmpty()) {
                    // 加密敏感字段
                    config.put(field, encrypt(value));
                }
            }
        }
    }

    /**
     * 处理配置中需要解密的字段
     *
     * @param config 配置信息
     */
    public void decryptConfigFields(Map<String, Object> config) {
        if (config == null) {
            return;
        }

        // 需要解密的字段列表，与加密字段列表相同
        String[] sensitiveFields = {"password", "secret", "key", "token", "credential"};

        for (String field : sensitiveFields) {
            if (config.containsKey(field) && config.get(field) instanceof String) {
                String value = (String) config.get(field);
                if (value != null && !value.isEmpty()) {
                    try {
                        // 解密敏感字段
                        config.put(field, decrypt(value));
                    } catch (Exception e) {
                        // 如果解密失败，可能是字段未加密，保持原值
                    }
                }
            }
        }
    }


    /**
     * 获取加密密钥
     *
     * @return 16字节的密钥
     */
    private byte[] getKey() {
        // 确保密钥长度为16字节(128位)
        byte[] key = secretKey.getBytes(StandardCharsets.UTF_8);
        byte[] result = new byte[16];

        for (int i = 0; i < 16; i++) {
            result[i] = (i < key.length) ? key[i] : 0;
        }

        return result;
    }
}
