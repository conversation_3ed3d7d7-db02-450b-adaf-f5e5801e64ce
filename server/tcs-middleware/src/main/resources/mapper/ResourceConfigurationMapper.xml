<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.middleware.mapper.ResourceConfigurationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity">
        <id column="id" property="id" />
        <result column="resource_id" property="resourceId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="config" property="config" typeHandler="com.siteweb.tcs.common.util.JsonMapTypeHandler" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 分页查询映射结果，不包含config字段 -->
    <resultMap id="PageResultMap" type="com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity">
        <id column="id" property="id" />
        <result column="resource_id" property="resourceId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 扩展的资源配置映射结果，包含资源类型名称 -->
    <resultMap id="ExtendedResultMap" type="com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity" extends="BaseResultMap">
        <result column="resource_type_name" property="resourceTypeName" />
    </resultMap>

    <!-- 分页查询扩展映射结果，包含资源类型名称但不包含config字段 -->
    <resultMap id="ExtendedPageResultMap" type="com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity" extends="PageResultMap">
        <result column="resource_type_name" property="resourceTypeName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, resource_id, name, description, config, status, create_time, update_time, created_by, updated_by
    </sql>

    <!-- 分页查询结果列，不包含config字段 -->
    <sql id="Page_Column_List">
        id, resource_id, name, description, status, create_time, update_time, created_by, updated_by
    </sql>

    <!-- 分页查询资源配置，包含资源类型名称，不包含config字段 -->
    <select id="pageResourceConfigurations" resultMap="ExtendedPageResultMap">
        SELECT
            rc.id,
            rc.resource_id,
            rt.name AS resource_type_name,
            rc.name,
            rc.description,
            rc.status,
            rc.create_time,
            rc.update_time,
            rc.created_by,
            rc.updated_by
        FROM
            mw_resource_configuration rc
        LEFT JOIN
            mw_resource_type rt ON rc.resource_id = rt.id
        <where>
            <if test="resourceId != null and resourceId != ''">
                AND rc.resource_id = #{resourceId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (rc.id LIKE CONCAT('%', #{keyword}, '%') OR rc.name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY
            rc.id DESC
    </select>

    <!-- 根据ID查询资源配置，包含资源类型名称 -->
    <select id="getResourceConfigurationById" resultMap="ExtendedResultMap">
        SELECT
            rc.id,
            rc.resource_id,
            rt.name AS resource_type_name,
            rc.name,
            rc.description,
            rc.config,
            rc.status,
            rc.create_time,
            rc.update_time,
            rc.created_by,
            rc.updated_by
        FROM
            mw_resource_configuration rc
        LEFT JOIN
            mw_resource_type rt ON rc.resource_id = rt.id
        WHERE
            rc.id = #{id}
    </select>
</mapper>
