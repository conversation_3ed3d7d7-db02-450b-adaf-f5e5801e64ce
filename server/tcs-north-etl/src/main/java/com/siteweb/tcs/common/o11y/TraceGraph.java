package com.siteweb.tcs.common.o11y;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Stub implementation of TraceGraph for compilation purposes.
 * This is a simplified version of the original class.
 */
public class TraceGraph {
    private String name;
    private List<TraceSpan> spans;
    private Map<String, String> tags;
    
    /**
     * Creates a new trace graph.
     * 
     * @param name The name of the trace graph
     */
    public TraceGraph(String name) {
        this.name = name;
        this.spans = new ArrayList<>();
        this.tags = new HashMap<>();
    }
    
    /**
     * Gets the name of the trace graph.
     * 
     * @return The name of the trace graph
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the spans in the trace graph.
     * 
     * @return The spans in the trace graph
     */
    public List<TraceSpan> getSpans() {
        return spans;
    }
    
    /**
     * Adds a span to the trace graph.
     * 
     * @param span The span to add
     */
    public void addSpan(TraceSpan span) {
        spans.add(span);
    }
    
    /**
     * Gets the tags of the trace graph.
     * 
     * @return The tags of the trace graph
     */
    public Map<String, String> getTags() {
        return tags;
    }
    
    /**
     * Adds a tag to the trace graph.
     * 
     * @param key The tag key
     * @param value The tag value
     */
    public void addTag(String key, String value) {
        tags.put(key, value);
    }
}
