package com.siteweb.tcs.north.etl;

import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.runtime.NorthPlugin;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.north.etl.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.etl.connector.process.EtlGuard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * ETL数据处理插件
 * 负责管理和处理物联网数据的提取、转换和加载
 */
@Slf4j
public class EtlPlugin extends NorthPlugin {

    private ActorRef etlGuard;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private ConnectorDataHolder dataHolder;

    /**
     * 插件构造函数
     */
    public EtlPlugin(PluginContext context) {
        super(context);
    }

    /**
     * 插件启动时执行
     */
    @Override
    public void onStart() {
        log.info("Starting ETL Data Processing Plugin...");
        
        try {
            // 设置插件ID
            dataHolder.setPluginId(this.getPluginId());
            
            // 创建根Actor
            etlGuard = getContext().getActorSystem().actorOf(
                    Props.create(EtlGuard.class, redisTemplate),
                    "EtlGuard"
            );
            
            // 注册根Actor
            dataHolder.setRootActor(etlGuard);
            getConnectorDataHolder().registerNorthEntry(etlGuard);
            
            log.info("ETL Data Processing Plugin started successfully");
        } catch (Exception e) {
            log.error("Failed to start ETL Data Processing Plugin", e);
            throw new RuntimeException("Failed to start ETL Data Processing Plugin", e);
        }
    }

    /**
     * 插件停止时执行
     */
    @Override
    public void onStop() {
        log.info("Stopping ETL Data Processing Plugin...");
        
        // 清理资源
        if (etlGuard != null) {
            getContext().getActorSystem().stop(etlGuard);
        }
        
        log.info("ETL Data Processing Plugin stopped successfully");
    }
}
