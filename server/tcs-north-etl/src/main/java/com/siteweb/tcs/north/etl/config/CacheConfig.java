package com.siteweb.tcs.north.etl.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存配置
 * 用于提高NiFi API调用性能
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    /**
     * 缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(
                "processGroups", 
                "processors", 
                "connections", 
                "clusterStatus", 
                "processGroupStatus");
    }
}
