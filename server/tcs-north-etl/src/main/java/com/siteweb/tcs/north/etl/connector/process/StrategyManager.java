package com.siteweb.tcs.north.etl.connector.process;

import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.north.etl.domain.letter.CleaningStrategyMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 策略管理器
 * 负责管理数据处理策略
 */
@Slf4j
public class StrategyManager extends AbstractActor {

    private final ActorProbe probe;
    private final ActorRef dataCleaningAdapter;
    private final Map<Integer, CleaningStrategyMessage> strategies = new HashMap<>();
    
    /**
     * 构造函数
     */
    public StrategyManager(ActorRef dataCleaningAdapter) {
        this.dataCleaningAdapter = dataCleaningAdapter;
        this.probe = createProbe(this);
        probe.addCounter("StrategyCounter");
    }
    
    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(CleaningStrategyMessage.class, this::handleCleaningStrategy)
                .matchAny(this::unhandled)
                .build();
    }
    
    /**
     * 处理处理策略消息
     */
    private void handleCleaningStrategy(CleaningStrategyMessage message) {
        probe.info("Managing strategy: " + message.getName());
        
        try {
            // 根据操作类型处理策略
            switch (message.getOperationType()) {
                case CREATE:
                case UPDATE:
                    strategies.put(message.getStrategyId(), message);
                    probe.info("Strategy created/updated: " + message.getName());
                    break;
                case DELETE:
                    strategies.remove(message.getStrategyId());
                    probe.info("Strategy deleted: " + message.getName());
                    break;
                case ACTIVATE:
                    if (strategies.containsKey(message.getStrategyId())) {
                        CleaningStrategyMessage strategy = strategies.get(message.getStrategyId());
                        strategy.setStatus("ACTIVE");
                        probe.info("Strategy activated: " + message.getName());
                    }
                    break;
                case DEACTIVATE:
                    if (strategies.containsKey(message.getStrategyId())) {
                        CleaningStrategyMessage strategy = strategies.get(message.getStrategyId());
                        strategy.setStatus("INACTIVE");
                        probe.info("Strategy deactivated: " + message.getName());
                    }
                    break;
                default:
                    probe.warn("Unknown operation type: " + message.getOperationType());
            }
            
            // 更新计数器
            probe.incrementCounterAmount("StrategyCounter", 1);
        } catch (Exception e) {
            probe.error("Error managing strategy: " + e.getMessage());
        }
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
    
    /**
     * 创建Props
     */
    public static Props props(ActorRef dataCleaningAdapter) {
        return Props.create(StrategyManager.class, dataCleaningAdapter);
    }
}
