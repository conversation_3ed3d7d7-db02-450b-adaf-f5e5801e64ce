package com.siteweb.tcs.north.etl.controller;

import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.north.etl.model.EtlTaskConfig;
import com.siteweb.tcs.north.etl.service.EtlTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ETL任务控制器，用于管理ETL任务。
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/etl")
public class EtlTaskController {

    @Autowired
    private EtlTaskService etlTaskService;

    /**
     * 获取所有ETL任务。
     *
     * @return ETL任务列表
     */
    @GetMapping("/tasks")
    public ResponseResult<List<EtlTaskConfig>> getAllTasks() {
        List<EtlTaskConfig> tasks = etlTaskService.getAllTasks();
        return ResponseResult.success(tasks);
    }

    /**
     * 根据ID获取ETL任务。
     *
     * @param id 任务ID
     * @return ETL任务
     */
    @GetMapping("/tasks/{id}")
    public ResponseResult<EtlTaskConfig> getTask(@PathVariable("id") String id) {
        EtlTaskConfig task = etlTaskService.getTask(id);
        if (task == null) {
            return ResponseResult.fail("Task not found");
        }
        return ResponseResult.success(task);
    }

    /**
     * 根据源资源获取ETL任务。
     *
     * @param type 资源类型
     * @param id   资源ID
     * @return ETL任务列表
     */
    @GetMapping("/tasks/source/{type}/{id}")
    public ResponseResult<List<EtlTaskConfig>> getTasksBySourceResource(
            @PathVariable("type") String type,
            @PathVariable("id") String id
    ) {
        List<EtlTaskConfig> tasks = etlTaskService.getTasksBySourceResource(type, id);
        return ResponseResult.success(tasks);
    }

    /**
     * 根据目标资源获取ETL任务。
     *
     * @param type 资源类型
     * @param id   资源ID
     * @return ETL任务列表
     */
    @GetMapping("/tasks/target/{type}/{id}")
    public ResponseResult<List<EtlTaskConfig>> getTasksByTargetResource(
            @PathVariable("type") String type,
            @PathVariable("id") String id
    ) {
        List<EtlTaskConfig> tasks = etlTaskService.getTasksByTargetResource(type, id);
        return ResponseResult.success(tasks);
    }

    /**
     * 创建新的ETL任务。
     *
     * @param task 要创建的任务
     * @return 创建的任务
     */
    @PostMapping("/tasks")
    public ResponseResult<EtlTaskConfig> createTask(@RequestBody EtlTaskConfig task) {
        try {
            EtlTaskConfig createdTask = etlTaskService.createTask(task);
            return ResponseResult.success(createdTask);
        } catch (Exception e) {
            log.error("Failed to create ETL task", e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 更新现有ETL任务。
     *
     * @param id   任务ID
     * @param task 更新后的任务
     * @return 更新后的任务
     */
    @PutMapping("/tasks/{id}")
    public ResponseResult<EtlTaskConfig> updateTask(
            @PathVariable("id") String id,
            @RequestBody EtlTaskConfig task
    ) {
        try {
            task.setId(id);
            EtlTaskConfig updatedTask = etlTaskService.updateTask(task);
            return ResponseResult.success(updatedTask);
        } catch (Exception e) {
            log.error("Failed to update ETL task", e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 删除ETL任务。
     *
     * @param id 任务ID
     * @return 操作结果
     */
    @DeleteMapping("/tasks/{id}")
    public ResponseResult<Boolean> deleteTask(@PathVariable("id") String id) {
        boolean result = etlTaskService.deleteTask(id);
        return ResponseResult.success(result);
    }

    /**
     * 执行ETL任务。
     *
     * @param id 任务ID
     * @return 操作结果
     */
    @PostMapping("/tasks/{id}/execute")
    public ResponseResult<Boolean> executeTask(@PathVariable("id") String id) {
        boolean result = etlTaskService.executeTask(id);
        return ResponseResult.success(result);
    }

    /**
     * 调度所有启用的ETL任务。
     *
     * @return 操作结果
     */
    @PostMapping("/tasks/schedule")
    public ResponseResult<Boolean> scheduleAllTasks() {
        try {
            etlTaskService.scheduleAllTasks();
            return ResponseResult.success(true);
        } catch (Exception e) {
            log.error("Failed to schedule ETL tasks", e);
            return ResponseResult.fail(e.getMessage());
        }
    }
}