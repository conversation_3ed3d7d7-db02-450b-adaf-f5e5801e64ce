package com.siteweb.tcs.north.etl.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 读取器实体（数据源）
 */
@Data
@TableName(value = "etl_reader", autoResultMap = true)
public class Reader {
    
    /**
     * 读取器ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 读取器名称
     */
    private String name;
    
    /**
     * 读取器描述
     */
    private String description;
    
    /**
     * 读取器类型
     */
    private String type;
    
    /**
     * 配置JSON
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> configJson = new HashMap<>();
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 读取器类型枚举
     */
    public enum ReaderType {
        /**
         * 数据库
         */
        DATABASE,
        
        /**
         * 文件
         */
        FILE,
        
        /**
         * FTP/SFTP
         */
        FTP,
        
        /**
         * Kafka
         */
        KAFKA,
        
        /**
         * HTTP API
         */
        API
    }
}
