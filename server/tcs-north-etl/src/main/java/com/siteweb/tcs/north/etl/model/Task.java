package com.siteweb.tcs.north.etl.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务实体
 */
@Data
@TableName("etl_task")
public class Task {
    
    /**
     * 任务ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 策略ID
     */
    private Integer strategyId;
    
    /**
     * 调度Cron表达式
     */
    private String scheduleCron;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * NiFi目标状态
     */
    private String nifiTargetState;
    
    /**
     * 上次运行时间
     */
    private LocalDateTime lastRunTime;
    
    /**
     * 下次运行时间
     */
    private LocalDateTime nextRunTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        /**
         * 运行中
         */
        RUNNING,
        
        /**
         * 已停止
         */
        STOPPED,
        
        /**
         * 错误
         */
        ERROR,
        
        /**
         * 已禁用
         */
        DISABLED
    }
    
    /**
     * NiFi状态枚举
     */
    public enum NifiState {
        /**
         * 运行中
         */
        RUNNING,
        
        /**
         * 已停止
         */
        STOPPED
    }
}
