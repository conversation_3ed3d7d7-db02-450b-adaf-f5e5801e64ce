package com.siteweb.tcs.north.etl.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务执行记录实体
 */
@Data
@TableName("etl_task_execution")
public class TaskExecution {
    
    /**
     * 执行记录ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行状态
     */
    private String status;
    
    /**
     * 日志消息
     */
    private String logMessage;
    
    /**
     * NiFi公告ID
     */
    private String nifiBulletinId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        /**
         * 成功
         */
        SUCCESS,
        
        /**
         * 失败
         */
        FAILURE,
        
        /**
         * 运行中
         */
        RUNNING,
        
        /**
         * 已取消
         */
        CANCELLED
    }
}
