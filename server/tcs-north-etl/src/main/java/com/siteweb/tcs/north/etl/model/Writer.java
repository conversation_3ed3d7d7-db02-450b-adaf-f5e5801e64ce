package com.siteweb.tcs.north.etl.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 存储器实体（数据目标）
 */
@Data
@TableName(value = "etl_writer", autoResultMap = true)
public class Writer {
    
    /**
     * 存储器ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 存储器名称
     */
    private String name;
    
    /**
     * 存储器描述
     */
    private String description;
    
    /**
     * 存储器类型
     */
    private String type;
    
    /**
     * 配置JSON
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> configJson = new HashMap<>();
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 存储器类型枚举
     */
    public enum WriterType {
        /**
         * 数据库
         */
        DATABASE,
        
        /**
         * 文件
         */
        FILE,
        
        /**
         * HDFS
         */
        HDFS,
        
        /**
         * Kafka
         */
        KAFKA,
        
        /**
         * HTTP API
         */
        API
    }
}
