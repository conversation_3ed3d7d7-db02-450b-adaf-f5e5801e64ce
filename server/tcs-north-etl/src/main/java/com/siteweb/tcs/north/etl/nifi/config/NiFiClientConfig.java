package com.siteweb.tcs.north.etl.nifi.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * NiFi客户端配置类
 * 用于配置NiFi客户端的依赖
 */
@Slf4j
@Configuration
public class NiFiClientConfig {

    /**
     * 创建用于NiFi通信的RestTemplate
     * 仅在非模拟模式下创建
     */
    @Bean(name = "nifiRestTemplate")
    @ConditionalOnProperty(name = "plugin.etl.nifi.mock-enabled", havingValue = "false", matchIfMissing = false)
    public RestTemplate nifiRestTemplate() {
        log.info("Creating NiFi RestTemplate for real NiFi client");
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(30000);
        requestFactory.setConnectionRequestTimeout(30000);
        return new RestTemplate(requestFactory);
    }

    /**
     * 创建用于模拟NiFi通信的RestTemplate
     * 仅在模拟模式下创建
     */
    @Bean(name = "nifiRestTemplate")
    @ConditionalOnProperty(name = "plugin.etl.nifi.mock-enabled", havingValue = "true", matchIfMissing = true)
    public RestTemplate mockNifiRestTemplate() {
        log.info("Creating mock NiFi RestTemplate");
        return new RestTemplate();
    }
}
