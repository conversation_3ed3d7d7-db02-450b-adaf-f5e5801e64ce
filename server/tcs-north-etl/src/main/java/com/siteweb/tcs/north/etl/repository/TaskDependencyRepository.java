package com.siteweb.tcs.north.etl.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.etl.model.TaskDependency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务依赖关系数据访问接口
 */
@Mapper
public interface TaskDependencyRepository extends BaseMapper<TaskDependency> {
    
    /**
     * 根据任务ID查询依赖关系列表
     */
    @Select("SELECT * FROM etl_task_dependency WHERE task_id = #{taskId}")
    List<TaskDependency> findByTaskId(@Param("taskId") Integer taskId);
    
    /**
     * 根据依赖任务ID查询依赖关系列表
     */
    @Select("SELECT * FROM etl_task_dependency WHERE depends_on_task_id = #{dependsOnTaskId}")
    List<TaskDependency> findByDependsOnTaskId(@Param("dependsOnTaskId") Integer dependsOnTaskId);
    
    /**
     * 查询任务的所有依赖任务ID
     */
    @Select("SELECT depends_on_task_id FROM etl_task_dependency WHERE task_id = #{taskId}")
    List<Integer> findDependsOnTaskIds(@Param("taskId") Integer taskId);
    
    /**
     * 查询依赖于指定任务的所有任务ID
     */
    @Select("SELECT task_id FROM etl_task_dependency WHERE depends_on_task_id = #{dependsOnTaskId}")
    List<Integer> findDependentTaskIds(@Param("dependsOnTaskId") Integer dependsOnTaskId);
}
