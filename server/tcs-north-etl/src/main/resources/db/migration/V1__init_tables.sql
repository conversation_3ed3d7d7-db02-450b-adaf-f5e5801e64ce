-- 创建清洗策略表
CREATE TABLE ai_cleaning_strategy (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL,
    data_source_id INT,
    data_type VARCHAR(50),
    cleaning_rules JSON,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- 创建数据源表
CREATE TABLE ai_data_source (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    description TEXT,
    connection_config JSON NOT NULL,
    data_format VARCHAR(50),
    sampling_interval INT,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    last_connected_at TIMESTAMP
);

-- 创建数据质量报告表
CREATE TABLE ai_data_quality_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    strategy_id INT,
    data_source_id INT,
    device_id VARCHAR(100),
    signal_id VARCHAR(100),
    report_type VARCHAR(20) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    total_count INT,
    valid_count INT,
    issue_count INT,
    quality_score DOUBLE,
    quality_stats JSON,
    quality_issues JSON,
    created_at TIMESTAMP NOT NULL,
    FOREIGN KEY (strategy_id) REFERENCES ai_cleaning_strategy(id) ON DELETE SET NULL,
    FOREIGN KEY (data_source_id) REFERENCES ai_data_source(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX idx_strategy_status ON ai_cleaning_strategy(status);
CREATE INDEX idx_strategy_type ON ai_cleaning_strategy(type);
CREATE INDEX idx_strategy_data_type ON ai_cleaning_strategy(data_type);
CREATE INDEX idx_data_source_status ON ai_data_source(status);
CREATE INDEX idx_data_source_type ON ai_data_source(type);
CREATE INDEX idx_report_strategy_id ON ai_data_quality_report(strategy_id);
CREATE INDEX idx_report_data_source_id ON ai_data_quality_report(data_source_id);
CREATE INDEX idx_report_device_id ON ai_data_quality_report(device_id);
CREATE INDEX idx_report_created_at ON ai_data_quality_report(created_at);
