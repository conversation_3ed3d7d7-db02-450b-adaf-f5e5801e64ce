export default {
  'etl': {
    'title': 'ETL Data Processing Plugin',
    'reader': {
      'title': 'Readers',
      'list': 'Reader List',
      'create': 'Create Reader',
      'edit': 'Edit Reader',
      'detail': 'Reader Details',
      'name': 'Reader Name',
      'type': 'Reader Type',
      'description': 'Description',
      'config': 'Configuration',
      'status': 'Status',
      'types': {
        'DATABASE': 'Database',
        'FILE': 'File',
        'FTP': 'FTP/SFTP',
        'KAFKA': 'Kafka',
        'API': 'API'
      },
      'actions': {
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'test': 'Test Connection'
      }
    },
    'writer': {
      'title': 'Writers',
      'list': 'Writer List',
      'create': 'Create Writer',
      'edit': 'Edit Writer',
      'detail': 'Writer Details',
      'name': 'Writer Name',
      'type': 'Writer Type',
      'description': 'Description',
      'config': 'Configuration',
      'status': 'Status',
      'types': {
        'DATABASE': 'Database',
        'FILE': 'File',
        'HDFS': 'HDFS',
        'KAFKA': 'Kafka',
        'API': 'API'
      },
      'actions': {
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'test': 'Test Connection'
      }
    },
    'strategy': {
      'title': 'ETL Strategies',
      'list': 'Strategy List',
      'create': 'Create Strategy',
      'edit': 'Edit Strategy',
      'detail': 'Strategy Details',
      'name': 'Strategy Name',
      'description': 'Description',
      'reader': 'Reader',
      'writer': 'Writer',
      'rules': 'Processing Rules',
      'nifiProcessGroupId': 'NiFi Process Group ID',
      'rule': {
        'name': 'Rule Name',
        'type': 'Rule Type',
        'description': 'Description',
        'parameters': 'Parameters',
        'order': 'Order',
        'enabled': 'Enabled',
        'types': {
          'FIELD_MAPPING': 'Field Mapping',
          'FIELD_FILTER': 'Field Filter',
          'TYPE_CONVERSION': 'Type Conversion',
          'DEFAULT_VALUE': 'Default Value',
          'SCRIPT_TRANSFORM': 'Script Transform'
        }
      },
      'actions': {
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'deploy': 'Deploy to NiFi'
      }
    },
    'task': {
      'title': 'ETL Tasks',
      'list': 'Task List',
      'create': 'Create Task',
      'edit': 'Edit Task',
      'detail': 'Task Details',
      'name': 'Task Name',
      'description': 'Description',
      'strategy': 'Strategy',
      'scheduleCron': 'Schedule Cron',
      'status': 'Status',
      'waitForDependencies': 'Wait for Dependencies',
      'timeoutMinutes': 'Timeout (minutes)',
      'lastRunTime': 'Last Run Time',
      'nextRunTime': 'Next Run Time',
      'dependencies': 'Dependencies',
      'dependencyType': 'Dependency Type',
      'dependencyTypes': {
        'STRONG': 'Strong',
        'WEAK': 'Weak',
        'CONDITIONAL': 'Conditional'
      },
      'status': {
        'RUNNING': 'Running',
        'STOPPED': 'Stopped',
        'ERROR': 'Error',
        'DISABLED': 'Disabled'
      },
      'actions': {
        'start': 'Start',
        'stop': 'Stop',
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'logs': 'View Logs',
        'status': 'View Status',
        'dependencies': 'Manage Dependencies'
      }
    },
    'cluster': {
      'title': 'NiFi Cluster',
      'overview': 'Cluster Overview',
      'nodes': 'Node List',
      'status': 'Cluster Status',
      'stats': {
        'connectedNodeCount': 'Connected Nodes',
        'totalNodeCount': 'Total Nodes',
        'activeThreadCount': 'Active Threads',
        'queuedCount': 'Queued Count',
        'queuedBytes': 'Queued Bytes'
      },
      'node': {
        'nodeId': 'Node ID',
        'address': 'Address',
        'status': 'Status',
        'activeThreadCount': 'Active Threads',
        'queued': 'Queued Data'
      }
    }
  },
  'ai-cleaner': {
    'title': 'AI Data Cleaner Plugin',
    'strategy': {
      'title': 'Cleaning Strategies',
      'list': 'Strategy List',
      'create': 'Create Strategy',
      'edit': 'Edit Strategy',
      'detail': 'Strategy Details',
      'name': 'Strategy Name',
      'type': 'Strategy Type',
      'description': 'Description',
      'status': 'Status',
      'dataSource': 'Data Source',
      'dataType': 'Data Type',
      'rules': 'Cleaning Rules',
      'rule': {
        'name': 'Rule Name',
        'type': 'Rule Type',
        'description': 'Description',
        'parameters': 'Parameters',
        'order': 'Order',
        'enabled': 'Enabled',
        'types': {
          'missing_value': 'Missing Value Handling',
          'outlier_detection': 'Outlier Detection',
          'data_normalization': 'Data Normalization',
          'time_series_interpolation': 'Time Series Interpolation'
        }
      },
      'status': {
        'ACTIVE': 'Active',
        'INACTIVE': 'Inactive'
      },
      'types': {
        'REALTIME': 'Real-time Data',
        'BATCH': 'Batch Data',
        'TIMESERIES': 'Time Series'
      },
      'actions': {
        'activate': 'Activate',
        'deactivate': 'Deactivate',
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View'
      }
    },
    'datasource': {
      'title': 'Data Sources',
      'list': 'Data Source List',
      'create': 'Create Data Source',
      'edit': 'Edit Data Source',
      'detail': 'Data Source Details',
      'name': 'Data Source Name',
      'type': 'Data Source Type',
      'description': 'Description',
      'connectionConfig': 'Connection Config',
      'dataFormat': 'Data Format',
      'samplingInterval': 'Sampling Interval (sec)',
      'status': 'Status',
      'lastConnectedAt': 'Last Connected At',
      'status': {
        'ACTIVE': 'Active',
        'INACTIVE': 'Inactive',
        'ERROR': 'Error'
      },
      'types': {
        'SPOUT': 'Real-time Data Stream',
        'MYSQL': 'MySQL Database',
        'INFLUXDB': 'InfluxDB Time Series DB',
        'HTTP_API': 'HTTP API',
        'FILE': 'File'
      },
      'actions': {
        'activate': 'Activate',
        'deactivate': 'Deactivate',
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'test': 'Test Connection'
      }
    },
    'quality': {
      'title': 'Quality Reports',
      'list': 'Report List',
      'detail': 'Report Details',
      'generate': 'Generate Report',
      'strategy': 'Strategy',
      'dataSource': 'Data Source',
      'device': 'Device',
      'signal': 'Signal',
      'reportType': 'Report Type',
      'startTime': 'Start Time',
      'endTime': 'End Time',
      'totalCount': 'Total Count',
      'validCount': 'Valid Count',
      'issueCount': 'Issue Count',
      'qualityScore': 'Quality Score',
      'qualityStats': 'Quality Statistics',
      'qualityIssues': 'Quality Issues',
      'createdAt': 'Created At',
      'reportTypes': {
        'DAILY': 'Daily Report',
        'WEEKLY': 'Weekly Report',
        'MONTHLY': 'Monthly Report',
        'REALTIME': 'Real-time Report',
        'CUSTOM': 'Custom Report'
      },
      'issueTypes': {
        'MISSING_VALUE': 'Missing Value',
        'OUTLIER': 'Outlier',
        'PATTERN_ANOMALY': 'Pattern Anomaly'
      },
      'actions': {
        'delete': 'Delete',
        'view': 'View',
        'export': 'Export'
      }
    },
    'monitor': {
      'title': 'Monitoring',
      'overview': 'Overview',
      'performance': 'Performance',
      'logs': 'Logs',
      'stats': {
        'processedCount': 'Processed Count',
        'cleanedCount': 'Cleaned Count',
        'issueCount': 'Issue Count',
        'activeStrategies': 'Active Strategies',
        'activeDataSources': 'Active Data Sources'
      }
    },
    'common': {
      'search': 'Search',
      'reset': 'Reset',
      'submit': 'Submit',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'view': 'View',
      'create': 'Create',
      'refresh': 'Refresh',
      'export': 'Export',
      'import': 'Import',
      'success': 'Success',
      'error': 'Error',
      'warning': 'Warning',
      'info': 'Info',
      'loading': 'Loading',
      'noData': 'No Data',
      'confirmDelete': 'Confirm Delete?',
      'deleteSuccess': 'Delete Success',
      'createSuccess': 'Create Success',
      'updateSuccess': 'Update Success',
      'operationSuccess': 'Operation Success',
      'operationFailed': 'Operation Failed'
    }
  }
}
