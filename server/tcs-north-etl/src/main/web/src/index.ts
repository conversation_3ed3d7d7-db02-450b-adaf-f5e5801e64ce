import { createApp, h } from 'vue'
import { createI18n } from 'vue-i18n'
import { createRouter, createWebHashHistory, RouterView } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCN from './i18n/zh-CN'
import enUS from './i18n/en-US'
import routes from './router'

// 创建国际化实例
const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  }
})

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 创建应用实例
const app = createApp({
  render() {
    return h(RouterView)
  }
})

// 使用插件
app.use(i18n)
app.use(router)
app.use(ElementPlus)

// 挂载应用
app.mount('#app')
