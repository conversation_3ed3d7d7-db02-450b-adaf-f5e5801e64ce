import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/etl',
  timeout: 30000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加认证信息等
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const res = response.data
    // 根据后端返回的状态码进行处理
    if (res.code !== 200) {
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res.data
    }
  },
  error => {
    return Promise.reject(error)
  }
)

// 读取器相关API
export const readerApi = {
  // 获取所有读取器
  getAll: () => api.get('/readers'),

  // 获取读取器详情
  getById: (id: number) => api.get(`/readers/${id}`),

  // 创建读取器
  create: (data: any) => api.post('/readers', data),

  // 更新读取器
  update: (id: number, data: any) => api.put(`/readers/${id}`, data),

  // 删除读取器
  delete: (id: number) => api.delete(`/readers/${id}`),

  // 测试读取器连接
  testConnection: (id: number) => api.post(`/readers/${id}/test`),

  // 根据类型查询读取器
  getByType: (type: string) => api.get(`/readers/type/${type}`)
}

// 存储器相关API
export const writerApi = {
  // 获取所有存储器
  getAll: () => api.get('/writers'),

  // 获取存储器详情
  getById: (id: number) => api.get(`/writers/${id}`),

  // 创建存储器
  create: (data: any) => api.post('/writers', data),

  // 更新存储器
  update: (id: number, data: any) => api.put(`/writers/${id}`, data),

  // 删除存储器
  delete: (id: number) => api.delete(`/writers/${id}`),

  // 测试存储器连接
  testConnection: (id: number) => api.post(`/writers/${id}/test`),

  // 根据类型查询存储器
  getByType: (type: string) => api.get(`/writers/type/${type}`)
}

// 策略相关API
export const strategyApi = {
  // 获取所有策略
  getAll: () => api.get('/strategies'),

  // 获取策略详情
  getById: (id: number) => api.get(`/strategies/${id}`),

  // 创建策略
  create: (data: any) => api.post('/strategies', data),

  // 更新策略
  update: (id: number, data: any) => api.put(`/strategies/${id}`, data),

  // 删除策略
  delete: (id: number) => api.delete(`/strategies/${id}`),

  // 部署策略到NiFi
  deploy: (id: number) => api.post(`/strategies/${id}/deploy`),

  // 根据读取器ID查询策略
  getByReaderId: (readerId: number) => api.get(`/strategies/reader/${readerId}`),

  // 根据存储器ID查询策略
  getByWriterId: (writerId: number) => api.get(`/strategies/writer/${writerId}`)
}

// 任务相关API
export const taskApi = {
  // 获取所有任务
  getAll: () => api.get('/tasks'),

  // 获取任务详情
  getById: (id: number) => api.get(`/tasks/${id}`),

  // 创建任务
  create: (data: any) => api.post('/tasks', data),

  // 更新任务
  update: (id: number, data: any) => api.put(`/tasks/${id}`, data),

  // 删除任务
  delete: (id: number) => api.delete(`/tasks/${id}`),

  // 启动任务
  start: (id: number) => api.post(`/tasks/${id}/start`),

  // 停止任务
  stop: (id: number) => api.post(`/tasks/${id}/stop`),

  // 启动任务及其依赖
  startWithDependencies: (id: number) => api.post(`/tasks/${id}/start-with-dependencies`),

  // 获取任务执行记录
  getExecutions: (id: number) => api.get(`/tasks/${id}/executions`),

  // 根据状态查询任务
  getByStatus: (status: string) => api.get(`/tasks/status/${status}`),

  // 获取任务依赖
  getDependencies: (id: number) => api.get(`/tasks/${id}/dependencies`),

  // 添加任务依赖
  addDependency: (id: number, dependencyId: number, type: string) =>
    api.post(`/tasks/${id}/dependencies`, { dependencyId, type }),

  // 删除任务依赖
  removeDependency: (id: number, dependencyId: number) =>
    api.delete(`/tasks/${id}/dependencies/${dependencyId}`)
}

// 集群相关API
export const clusterApi = {
  // 获取集群状态
  getStatus: () => api.get('/cluster/status'),

  // 获取流程组列表
  getProcessGroups: () => api.get('/cluster/process-groups'),

  // 获取流程组详情
  getProcessGroupById: (id: string) => api.get(`/cluster/process-groups/${id}`),

  // 获取处理器列表
  getProcessors: (processGroupId: string) => api.get(`/cluster/process-groups/${processGroupId}/processors`),

  // 获取处理器详情
  getProcessorById: (id: string) => api.get(`/cluster/processors/${id}`),

  // 启动流程组
  startProcessGroup: (id: string) => api.post(`/cluster/process-groups/${id}/start`),

  // 停止流程组
  stopProcessGroup: (id: string) => api.post(`/cluster/process-groups/${id}/stop`)
}

// 数据质量相关API
export const qualityApi = {
  // 获取所有质量报告
  getAll: () => api.get('/quality'),

  // 获取质量报告详情
  getById: (id: number) => api.get(`/quality/${id}`),

  // 删除质量报告
  delete: (id: number) => api.delete(`/quality/${id}`),

  // 根据策略ID查询报告
  getByStrategyId: (strategyId: number) => api.get(`/quality/strategy/${strategyId}`),

  // 根据数据源ID查询报告
  getByDataSourceId: (dataSourceId: number) => api.get(`/quality/datasource/${dataSourceId}`),

  // 根据设备ID查询报告
  getByDeviceId: (deviceId: string) => api.get(`/quality/device/${deviceId}`),

  // 根据时间范围查询报告
  getByTimeRange: (startTime: string, endTime: string) => api.get('/quality/timeRange', {
    params: { startTime, endTime }
  }),

  // 获取质量统计
  getStatistics: (strategyId: number, startTime: string, endTime: string) => api.get('/quality/statistics', {
    params: { strategyId, startTime, endTime }
  }),

  // 生成质量报告
  generateReport: (params: any) => api.post('/quality/generate', null, { params })
}

// 数据清洗相关API
export const cleaningApi = {
  // 清洗单条数据
  cleanData: (params: any, data: any) => api.post('/cleaning/clean', data, { params }),

  // 批量清洗数据
  cleanDataBatch: (params: any, dataList: any[]) => api.post('/cleaning/cleanBatch', dataList, { params }),

  // 从数据源提取并清洗数据
  extractAndCleanData: (params: any) => api.post('/cleaning/extract', null, { params })
}

export default {
  reader: readerApi,
  writer: writerApi,
  strategy: strategyApi,
  task: taskApi,
  cluster: clusterApi,
  quality: qualityApi,
  cleaning: cleaningApi
}
