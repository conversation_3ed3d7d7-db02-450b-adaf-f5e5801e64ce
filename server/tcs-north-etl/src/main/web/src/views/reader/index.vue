<template>
  <div class="reader-container">
    <div class="page-header">
      <h2>{{ $t('etl.reader.list') }}</h2>
      <el-button type="primary" @click="handleCreateReader">
        <el-icon><plus /></el-icon>
        {{ $t('etl.reader.create') }}
      </el-button>
    </div>

    <el-card class="reader-table-card">
      <div class="table-operations">
        <el-input
          v-model="searchQuery"
          :placeholder="$t('etl.common.search')"
          style="width: 200px"
          clearable
          @clear="fetchReaders"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
        <el-button @click="fetchReaders">
          <el-icon><refresh /></el-icon>
          {{ $t('etl.common.refresh') }}
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="filteredReaders"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" :label="$t('etl.reader.name')" min-width="150" />
        <el-table-column :label="$t('etl.reader.type')" min-width="120">
          <template #default="scope">
            <el-tag>{{ $t(`etl.reader.types.${scope.row.type}`) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('etl.reader.description')" min-width="200" show-overflow-tooltip />
        <el-table-column :label="$t('etl.common.operations')" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="handleViewReader(scope.row)"
              :title="$t('etl.reader.actions.view')"
            >
              <el-icon><view /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="handleEditReader(scope.row)"
              :title="$t('etl.reader.actions.edit')"
            >
              <el-icon><edit /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleTestConnection(scope.row)"
              :title="$t('etl.reader.actions.test')"
            >
              <el-icon><connection /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDeleteReader(scope.row)"
              :title="$t('etl.reader.actions.delete')"
            >
              <el-icon><delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalReaders"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 读取器表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? $t('etl.reader.create') : $t('etl.reader.edit')"
      width="60%"
      destroy-on-close
    >
      <el-form
        ref="readerFormRef"
        :model="readerForm"
        :rules="readerRules"
        label-width="120px"
      >
        <el-form-item :label="$t('etl.reader.name')" prop="name">
          <el-input v-model="readerForm.name" />
        </el-form-item>
        <el-form-item :label="$t('etl.reader.type')" prop="type">
          <el-select v-model="readerForm.type" style="width: 100%">
            <el-option
              v-for="type in Object.keys(readerTypes)"
              :key="type"
              :label="$t(`etl.reader.types.${type}`)"
              :value="type"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('etl.reader.description')" prop="description">
          <el-input v-model="readerForm.description" type="textarea" rows="3" />
        </el-form-item>

        <!-- 根据不同类型显示不同的配置表单 -->
        <template v-if="readerForm.type === 'DATABASE'">
          <el-form-item label="数据库类型" prop="configJson.dbType">
            <el-select v-model="readerForm.configJson.dbType" style="width: 100%">
              <el-option label="MySQL" value="mysql" />
              <el-option label="PostgreSQL" value="postgresql" />
              <el-option label="Oracle" value="oracle" />
              <el-option label="SQL Server" value="sqlserver" />
            </el-select>
          </el-form-item>
          <el-form-item label="主机地址" prop="configJson.host">
            <el-input v-model="readerForm.configJson.host" />
          </el-form-item>
          <el-form-item label="端口" prop="configJson.port">
            <el-input v-model="readerForm.configJson.port" />
          </el-form-item>
          <el-form-item label="数据库名" prop="configJson.database">
            <el-input v-model="readerForm.configJson.database" />
          </el-form-item>
          <el-form-item label="用户名" prop="configJson.username">
            <el-input v-model="readerForm.configJson.username" />
          </el-form-item>
          <el-form-item label="密码" prop="configJson.password">
            <el-input v-model="readerForm.configJson.password" type="password" show-password />
          </el-form-item>
        </template>

        <template v-else-if="readerForm.type === 'FILE'">
          <el-form-item label="文件路径" prop="configJson.filePath">
            <el-input v-model="readerForm.configJson.filePath" />
          </el-form-item>
          <el-form-item label="文件格式" prop="configJson.fileFormat">
            <el-select v-model="readerForm.configJson.fileFormat" style="width: 100%">
              <el-option label="CSV" value="csv" />
              <el-option label="JSON" value="json" />
              <el-option label="XML" value="xml" />
              <el-option label="Excel" value="excel" />
            </el-select>
          </el-form-item>
          <el-form-item label="分隔符" prop="configJson.delimiter" v-if="readerForm.configJson.fileFormat === 'csv'">
            <el-input v-model="readerForm.configJson.delimiter" />
          </el-form-item>
        </template>

        <template v-else-if="readerForm.type === 'KAFKA'">
          <el-form-item label="Kafka服务器" prop="configJson.bootstrapServers">
            <el-input v-model="readerForm.configJson.bootstrapServers" />
          </el-form-item>
          <el-form-item label="主题" prop="configJson.topic">
            <el-input v-model="readerForm.configJson.topic" />
          </el-form-item>
          <el-form-item label="消费者组" prop="configJson.consumerGroup">
            <el-input v-model="readerForm.configJson.consumerGroup" />
          </el-form-item>
        </template>

        <template v-else-if="readerForm.type === 'API'">
          <el-form-item label="API URL" prop="configJson.url">
            <el-input v-model="readerForm.configJson.url" />
          </el-form-item>
          <el-form-item label="请求方法" prop="configJson.method">
            <el-select v-model="readerForm.configJson.method" style="width: 100%">
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
              <el-option label="PUT" value="PUT" />
            </el-select>
          </el-form-item>
          <el-form-item label="请求头" prop="configJson.headers">
            <el-input v-model="readerForm.configJson.headers" type="textarea" rows="3" placeholder="JSON格式" />
          </el-form-item>
        </template>

        <template v-else-if="readerForm.type === 'FTP'">
          <el-form-item label="FTP服务器" prop="configJson.host">
            <el-input v-model="readerForm.configJson.host" />
          </el-form-item>
          <el-form-item label="端口" prop="configJson.port">
            <el-input v-model="readerForm.configJson.port" />
          </el-form-item>
          <el-form-item label="用户名" prop="configJson.username">
            <el-input v-model="readerForm.configJson.username" />
          </el-form-item>
          <el-form-item label="密码" prop="configJson.password">
            <el-input v-model="readerForm.configJson.password" type="password" show-password />
          </el-form-item>
          <el-form-item label="文件路径" prop="configJson.filePath">
            <el-input v-model="readerForm.configJson.filePath" />
          </el-form-item>
          <el-form-item label="协议类型" prop="configJson.protocol">
            <el-select v-model="readerForm.configJson.protocol" style="width: 100%">
              <el-option label="FTP" value="ftp" />
              <el-option label="SFTP" value="sftp" />
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('etl.common.cancel') }}</el-button>
          <el-button type="primary" @click="submitReaderForm">{{ $t('etl.common.save') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      :title="$t('etl.common.confirmDelete')"
      width="30%"
    >
      <span>{{ $t('etl.reader.deleteConfirm') }}</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">{{ $t('etl.common.cancel') }}</el-button>
          <el-button type="danger" @click="confirmDeleteReader">{{ $t('etl.common.delete') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search, Refresh, View, Edit, Delete, Connection } from '@element-plus/icons-vue';
import api from '@/utils/api';

// 读取器类型
const readerTypes = {
  DATABASE: 'DATABASE',
  FILE: 'FILE',
  FTP: 'FTP',
  KAFKA: 'KAFKA',
  API: 'API'
};

// 数据
const readers = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const dialogType = ref('create'); // 'create' or 'edit'
const currentReader = ref(null);
const readerFormRef = ref(null);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalReaders = ref(0);

// 表单数据
const readerForm = reactive({
  id: null,
  name: '',
  type: 'DATABASE',
  description: '',
  configJson: {
    // 数据库配置
    dbType: 'mysql',
    host: '',
    port: '',
    database: '',
    username: '',
    password: '',
    
    // 文件配置
    filePath: '',
    fileFormat: 'csv',
    delimiter: ',',
    
    // Kafka配置
    bootstrapServers: '',
    topic: '',
    consumerGroup: '',
    
    // API配置
    url: '',
    method: 'GET',
    headers: '',
    
    // FTP配置
    protocol: 'ftp'
  }
});

// 表单验证规则
const readerRules = {
  name: [
    { required: true, message: '请输入读取器名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择读取器类型', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ]
};

// 过滤后的读取器列表
const filteredReaders = computed(() => {
  if (!searchQuery.value) {
    return readers.value;
  }
  const query = searchQuery.value.toLowerCase();
  return readers.value.filter(reader => 
    reader.name.toLowerCase().includes(query) || 
    reader.description.toLowerCase().includes(query)
  );
});

// 生命周期钩子
onMounted(() => {
  fetchReaders();
});

// 获取所有读取器
const fetchReaders = async () => {
  loading.value = true;
  try {
    const response = await api.reader.getAll();
    readers.value = response.data || [];
    totalReaders.value = readers.value.length;
  } catch (error) {
    console.error('Failed to fetch readers:', error);
    ElMessage.error('获取读取器列表失败');
  } finally {
    loading.value = false;
  }
};

// 创建读取器
const handleCreateReader = () => {
  dialogType.value = 'create';
  resetForm();
  dialogVisible.value = true;
};

// 查看读取器
const handleViewReader = (row) => {
  // 跳转到详情页或显示详情对话框
  ElMessageBox.alert(JSON.stringify(row, null, 2), '读取器详情', {
    confirmButtonText: '确定'
  });
};

// 编辑读取器
const handleEditReader = (row) => {
  dialogType.value = 'edit';
  currentReader.value = row;
  
  // 复制数据到表单
  readerForm.id = row.id;
  readerForm.name = row.name;
  readerForm.type = row.type;
  readerForm.description = row.description;
  readerForm.configJson = JSON.parse(JSON.stringify(row.configJson || {}));
  
  dialogVisible.value = true;
};

// 测试连接
const handleTestConnection = async (row) => {
  try {
    const response = await api.reader.testConnection(row.id);
    ElMessage.success('连接测试成功');
  } catch (error) {
    console.error('Connection test failed:', error);
    ElMessage.error('连接测试失败: ' + (error.message || '未知错误'));
  }
};

// 删除读取器
const handleDeleteReader = (row) => {
  currentReader.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDeleteReader = async () => {
  try {
    await api.reader.delete(currentReader.value.id);
    ElMessage.success('删除成功');
    deleteDialogVisible.value = false;
    fetchReaders();
  } catch (error) {
    console.error('Failed to delete reader:', error);
    ElMessage.error('删除失败: ' + (error.message || '未知错误'));
  }
};

// 提交表单
const submitReaderForm = async () => {
  if (!readerFormRef.value) return;
  
  await readerFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'create') {
          await api.reader.create(readerForm);
          ElMessage.success('创建成功');
        } else {
          await api.reader.update(readerForm.id, readerForm);
          ElMessage.success('更新成功');
        }
        dialogVisible.value = false;
        fetchReaders();
      } catch (error) {
        console.error('Form submission failed:', error);
        ElMessage.error('操作失败: ' + (error.message || '未知错误'));
      }
    } else {
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  if (readerFormRef.value) {
    readerFormRef.value.resetFields();
  }
  
  readerForm.id = null;
  readerForm.name = '';
  readerForm.type = 'DATABASE';
  readerForm.description = '';
  readerForm.configJson = {
    dbType: 'mysql',
    host: '',
    port: '',
    database: '',
    username: '',
    password: '',
    filePath: '',
    fileFormat: 'csv',
    delimiter: ',',
    bootstrapServers: '',
    topic: '',
    consumerGroup: '',
    url: '',
    method: 'GET',
    headers: '',
    protocol: 'ftp'
  };
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchReaders();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchReaders();
};
</script>

<style scoped>
.reader-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.reader-table-card {
  margin-bottom: 20px;
}

.table-operations {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
