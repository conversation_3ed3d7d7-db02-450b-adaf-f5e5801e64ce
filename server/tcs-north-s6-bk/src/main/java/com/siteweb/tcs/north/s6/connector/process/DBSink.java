package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorProbe;
import org.apache.pekko.actor.AbstractActor;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

public class DBSink extends AbstractActor {

    private final ActorProbe probe = createProbe(this);
    @Override
    public Receive createReceive() {
        return null;
    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }
}

