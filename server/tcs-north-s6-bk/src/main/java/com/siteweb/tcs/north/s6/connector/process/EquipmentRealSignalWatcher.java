package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.hub.domain.letter.EquipmentRealSignal;
import com.siteweb.tcs.north.s6.connector.letter.SetRedisItemAction;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

public class EquipmentRealSignalWatcher extends AbstractActor {

    private final ActorProbe probe = createProbe(this);
    private final ActorRef redisSink;

    private final String REAL_DATA = "real_data";

    public EquipmentRealSignalWatcher(ActorRef redisSink) {
        this.redisSink = redisSink;
        this.probe.addWindowLog(REAL_DATA);
    }


    public static Props props(ActorRef redisSink) {
        return Props.create(EquipmentRealSignalWatcher.class, () -> new EquipmentRealSignalWatcher(redisSink));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentRealSignal.class, this::saveEquipmentRealSignal)
                .build();
    }

    private void saveEquipmentRealSignal(EquipmentRealSignal equipmentRealSignal) {
        probe.enqueueWindowLogItem(REAL_DATA, new ActorLogItem(ActorLogLevel.INFO,
                "[RealData] 收到实时信号包: " + equipmentRealSignal.getWindowLogString()));
        //todo: save change to redis
        SetRedisItemAction action = new SetRedisItemAction();
        //todo: 我不太了解redis的id规则，这里的代码需要被replace
//        equipmentRealSignalList.forEach(equipmentRealSignal -> {
            equipmentRealSignal.getRealSignalList().forEach(realSignal -> {
                action.addItem(realSignal.getRedisKey(), realSignal.getRedisValue());
            });
//        });

        redisSink.tell(action, this.getSelf());
        probe.enqueueWindowLogItem(REAL_DATA, new ActorLogItem(ActorLogLevel.INFO,
                "[RealData] 保存实时信号到redis: " + action.getWindowsLogString()));
    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }
}

