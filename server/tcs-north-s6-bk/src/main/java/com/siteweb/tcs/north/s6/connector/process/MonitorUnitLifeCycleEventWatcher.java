package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorProbe;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;


public class MonitorUnitLifeCycleEventWatcher extends AbstractActor {

    private final ActorProbe probe = createProbe(this);

    public static Props props() {
        return Props.create(MonitorUnitLifeCycleEventWatcher.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                //.match(MonitorUnitLifeCycleEvent.class, this::onLifeCycleEvent)
                .build();
    }

    // private void onLifeCycleEvent(MonitorUnitLifeCycleEvent event) {
    //     //todo: 当配置变更时，Siteweb6侦听进行更新内存,
    // }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }

}

