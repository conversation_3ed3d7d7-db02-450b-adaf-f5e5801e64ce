package com.siteweb.tcs.north.s6.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.north.s6.connector.letter.DelRedisItemAction;
import com.siteweb.tcs.north.s6.connector.letter.SetRedisItemAction;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class RedisBatchProcessor {


    private final RedisTemplate redisTemplate;

    private SetRedisItemAction lastItemAction;
    private Date lastActionTime;
    private final ScheduledExecutorService executorService;
    private ScheduledFuture<?> scheduledFuture;
    private static final int BATCH_THRESHOLD = 100;
    private static final long TIME_THRESHOLD_MS = 3000; // 3 seconds

    public RedisBatchProcessor(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        executorService = Executors.newSingleThreadScheduledExecutor();
        resetLastActionState();
    }

    private void resetLastActionState() {
        lastItemAction = new SetRedisItemAction(); // 初始化一个空的SetRedisItemAction
        lastActionTime = new Date();
        cancelScheduledFuture();
    }

    /**
     * xsx
     * todo 先不考虑分批，后续如有性能问题再分批
     * @param delRedisItemAction
     */
    public void onDelRedisItemAction(DelRedisItemAction delRedisItemAction){
        if(CollectionUtil.isEmpty(delRedisItemAction.getDelKeyList())) return;
        redisTemplate.delete(delRedisItemAction.getDelKeyList());
    }

    public void onSetRedisItemAction(SetRedisItemAction action) {
        cancelScheduledFuture();
        Date currentTime = new Date();
        long timeSinceLastAction = currentTime.getTime() - lastActionTime.getTime();

        // 如果距离上次操作超过3秒，直接存储并重置状态，把最后没有存的一起存了
        if (timeSinceLastAction > TIME_THRESHOLD_MS) {
            action.getItems().addAll(lastItemAction.getItems());
            storeAndResetIfNecessary(action, true);
            return;
        }

        // 合并当前action的items到lastItemAction
        lastItemAction.getItems().addAll(action.getItems());

        // 如果未达到100条，设置定时任务在3秒后存储
        // xsx 2024-07-02 修改存储逻辑，目前如果lastItemAction还达不到100，3s后在存，存还是会判断，如果还达不到继续不存，引用都是同一个SetRedisItemAction
        if (lastItemAction.getItems().size() < BATCH_THRESHOLD) {
            scheduledFuture = executorService.schedule(() -> storeAndResetIfNecessary(lastItemAction, false), TIME_THRESHOLD_MS, TimeUnit.MILLISECONDS);
        } else {
            storeAndResetIfNecessary(lastItemAction, false);
        }

        // 更新最后操作时间
        lastActionTime = currentTime;
    }

    private void storeAndResetIfNecessary(SetRedisItemAction action, boolean forceStore) {
        // 如果forceStore为true，或者合并后超过100条，执行存储操作
        if (forceStore || action.getItems().size() >= BATCH_THRESHOLD) {
            redisTemplate.opsForValue().multiSet(action.getPairMapForMset());
            resetLastActionState(); // 存储后重置状态
        }
    }

    private void cancelScheduledFuture() {
        if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
            scheduledFuture.cancel(false);
        }
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
        executorService.shutdownNow();
    }

}
