package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.north.s6.connector.letter.DelRedisItemAction;
import com.siteweb.tcs.north.s6.connector.letter.SetRedisItemAction;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;
import org.springframework.data.redis.core.RedisTemplate;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

public class RedisSink extends AbstractActor {

    private final ActorProbe probe = createProbe(this);

    private RedisTemplate redisTemplate;

    private RedisBatchProcessor batchProcessor;

    private final String REAL_DATA = "real_data";

    public static Props props(RedisTemplate redisTemplate) {
        return Props.create(RedisSink.class,()->new RedisSink(redisTemplate));
    }

    private RedisSink(RedisTemplate redisTemplate){
        this.redisTemplate = redisTemplate;
        probe.addWindowLog(REAL_DATA);
    }

    @Override
    public void preStart() {
        batchProcessor = new RedisBatchProcessor(this.redisTemplate);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(SetRedisItemAction.class, this::onSetRedisItemAction)
                .match(DelRedisItemAction.class,this::onDelRedisItemAction)
                .build();
    }

    private void onSetRedisItemAction(SetRedisItemAction action) {
        probe.enqueueWindowLogItem(REAL_DATA, new ActorLogItem(ActorLogLevel.INFO,action.getWindowsLogString()));
        //如果action数据和当前类变量lastItemAction数据items条数之和超过100条，则直接存储，并情况lastItemAction的Items.
        //如果不足100条，则将action的items数据加入到lastItemAction中
        //如果超过3秒钟，不管是否超过100条都批量写入redis，提高效率
        batchProcessor.onSetRedisItemAction(action);
    }

    private void onDelRedisItemAction(DelRedisItemAction delRedisItemAction){
        batchProcessor.onDelRedisItemAction(delRedisItemAction);
    }
    @Override
    public void postStop() {
        removeProbe(this.probe);
    }
}

