package com.siteweb.tcs.north.s6.dal.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@TableName(value = "tbl_signal")
public class Signal implements Serializable {
    @TableField(value = "SignalId")
    private Integer signalId;
    @TableField(value = "EquipmentTemplateId")
    private Integer equipmentTemplateId;
    @TableField(value = "BaseTypeId")
    private Long baseTypeId;


}
