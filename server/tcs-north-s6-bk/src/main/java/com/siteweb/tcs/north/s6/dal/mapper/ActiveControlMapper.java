package com.siteweb.tcs.north.s6.dal.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.mapping.StatementType;

/**
 * <AUTHOR> (2024-09-26)
 **/

@Mapper
public interface ActiveControlMapper {
    @Update(value = "UPDATE Tbl_ActiveControl SET Retry = Retry + 1, ControlPhase = 2 WHERE SerialNo = #{serialNo} AND Retry = 1;")
    @Options(statementType = StatementType.CALLABLE)
    boolean changePhase2(Integer serialNo);
}
