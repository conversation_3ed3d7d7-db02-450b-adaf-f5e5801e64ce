CREATE TABLE tcs_alarm_map (
  DeviceId INTEGER NOT NULL,
  NorthEquipmentId INTEGER NOT NULL,
  AlarmId BIGINT NOT NULL,
  NorthEventId INTEGER NOT NULL,
  AlarmMeaningId VARCHAR(128) DEFAULT NULL,
  NorthEventConditionId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (DeviceId, NorthEquipmentId, AlarmId, NorthEventId, NorthEventConditionId)
);

COMMENT ON TABLE tcs_alarm_map IS '告警映射表';
COMMENT ON COLUMN tcs_alarm_map.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_control_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INTEGER DEFAULT NULL,
  ControlId BIGINT NOT NULL,
  NorthControlId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (DeviceId, ControlId, NorthControlId)
);

COMMENT ON TABLE tcs_control_map IS '控制映射表';
COMMENT ON COLUMN tcs_control_map.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_device_map (
  GatewayId BIGINT NOT NULL,
  NorthMonitorUnitId INTEGER DEFAULT NULL,
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (GatewayId, DeviceId, NorthEquipmentId)
);

COMMENT ON TABLE tcs_device_map IS '设备映射表';
COMMENT ON COLUMN tcs_device_map.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_gateway_map (
  GatewayId BIGINT NOT NULL,
  NorthMonitorUnitId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (GatewayId, NorthMonitorUnitId)
);

COMMENT ON TABLE tcs_gateway_map IS '网关映射表';
COMMENT ON COLUMN tcs_gateway_map.GatewayId IS 'hub全局网关id';
COMMENT ON COLUMN tcs_gateway_map.NorthMonitorUnitId IS 'siteweb采集单元id';
COMMENT ON COLUMN tcs_gateway_map.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_signal_map (
  DeviceId BIGINT NOT NULL,
  NorthEquipmentId INTEGER DEFAULT NULL,
  SignalId BIGINT NOT NULL,
  NorthSignalId INTEGER NOT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (DeviceId, SignalId, NorthSignalId)
);

COMMENT ON TABLE tcs_signal_map IS '信号映射表';
COMMENT ON COLUMN tcs_signal_map.Deleted IS '逻辑删除标志';