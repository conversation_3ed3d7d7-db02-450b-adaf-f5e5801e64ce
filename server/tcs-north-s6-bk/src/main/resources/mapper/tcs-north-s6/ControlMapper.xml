<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.ControlMapper">
<!--
    private Integer stationId;
    private Integer equipmentId;
    private Integer eventId;
    private Integer eventConditionId;
    private String sequenceId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer overturn;
    private String meanings;
    private Float eventValue;
    private Integer baseTypeId;
-->
    <select id="saveControlResult" statementType="CALLABLE" resultType="int">
        CALL PBL_SaveControlResult(
        #{stationId}, #{monitorUnitId}, #{equipmentId}, #{controlId}, #{serialNo},
        #{startTime}, #{endTime}, #{userId}, #{baseTypeId},
        #{resultCode}, #{result}, #{controlPhase},
        #{ret, mode=OUT, jdbcType=INTEGER})
    </select>
</mapper>

