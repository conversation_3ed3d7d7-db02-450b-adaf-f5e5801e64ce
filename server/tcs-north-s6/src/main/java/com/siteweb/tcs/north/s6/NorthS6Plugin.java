package com.siteweb.tcs.north.s6;

import com.siteweb.tcs.common.runtime.NorthPlugin;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.north.s6.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.s6.connector.process.GatewayCreatePipelineSubscriber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;

/**
 * siteweb6北向接口插件主类
 * <p>
 * 插件的入口类，负责插件的生命周期管理
 * </p>
 */
@Slf4j
public class NorthS6Plugin extends NorthPlugin {

    @Autowired
    private ConnectorDataHolder dataHolder;

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Autowired
    private MessageSource messageSource;

    public NorthS6Plugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
            log.info("Starting NorthS6Plugin - siteweb6北向接口");

            // 设置插件ID和创建根Actor
            dataHolder.setPluginId(this.getPluginId());

            // 创建网关创建管道订阅者
            log.info("Creating GatewayCreatePipelineSubscriber...");
            ClusterContext.getActorSystem().actorOf(GatewayCreatePipelineSubscriber.props(), "gatewayCreatePipelineSubscriber");
            log.info("GatewayCreatePipelineSubscriber created successfully");

            // 初始化其他组件

            log.info("NorthS6Plugin started successfully - siteweb6北向接口启动成功");
        } catch (Exception e) {
            log.error("Error starting NorthS6Plugin - siteweb6北向接口启动失败", e);
        }
    }

    @Override
    public void onStop() {
        log.info("Stopping NorthS6Plugin - siteweb6北向接口停止");
        // Actor系统会处理停止Actor
    }
}