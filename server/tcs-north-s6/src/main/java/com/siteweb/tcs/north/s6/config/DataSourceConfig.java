package com.siteweb.tcs.north.s6.config;

import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.north.s6.dal.mapper"}, sqlSessionFactoryRef = "northS6SqlSessionFactory")
public class DataSourceConfig {

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Value("${plugin.id}")
    private String pluginId;

    @Autowired
    private ResourceRegistry resourceRegistry;

    @Autowired
    private ServiceRegistry serviceRegistry;

    @Value("${plugin.middleware.siteweb-persistent.primary}")
    private String sitewebPersistentResourceId;



    @Bean(name = "northS6DataSource")
    public DataSource northS6DataSource() {
        // 使用带引用计数的方法，传入插件ID作为引用者
        return resourceRegistry.getDataSource(dbResourceId, pluginId);
    }

    @Bean(name = "northS6TransactionManager")
    public DataSourceTransactionManager northS6TransactionManager(@Qualifier("northS6DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "northS6SqlSessionFactory")
    public SqlSessionFactory northS6SqlSessionFactory(@Qualifier("northS6DataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/tcs-north-s6/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "s6SitewebPersistentService")
    public SitewebPersistentService omcSitewebPersistentService() throws Exception {
        return serviceRegistry.getSitewebPersistentService(sitewebPersistentResourceId,pluginId,pluginId,"north-s6-plugin");
    }

} 