package com.siteweb.tcs.north.s6.connector;

import com.siteweb.tcs.common.runtime.PluginContext;
import lombok.Data;
import org.apache.pekko.actor.ActorRef;
import org.springframework.stereotype.Component;

/**
 * 连接器数据持有者
 * <p>
 * 用于在不同组件之间共享数据
 * </p>
 */
@Data
public class ConnectorDataHolder {

    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 根Actor引用
     */
    private ActorRef rootActor;

    private PluginContext pluginContext;

    private static class SingletonHolder {
        private static final ConnectorDataHolder INSTANCE = new ConnectorDataHolder();
    }

    // 提供全局访问点
    public static ConnectorDataHolder getInstance() {
        return ConnectorDataHolder.SingletonHolder.INSTANCE;
    }

    private ConnectorDataHolder(){
    }

    public <T> T getBean(Class tClass){
        T t = (T) pluginContext.getHostApplicationContext().getBean(tClass);
        return t;
    }
} 