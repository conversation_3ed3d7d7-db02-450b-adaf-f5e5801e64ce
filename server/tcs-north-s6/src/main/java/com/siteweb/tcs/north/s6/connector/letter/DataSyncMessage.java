package com.siteweb.tcs.north.s6.connector.letter;

import java.util.Map;

/**
 * 数据同步消息
 */
public class DataSyncMessage extends NorthS6Message {
    private static final long serialVersionUID = 1L;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 同步数据
     */
    private Map<String, Object> data;
    
    /**
     * 操作类型：CREATE, UPDATE, DELETE
     */
    private String operation;
    
    public DataSyncMessage() {
        super();
    }
    
    public DataSyncMessage(String deviceId, String dataType, Map<String, Object> data, String operation) {
        super();
        this.deviceId = deviceId;
        this.dataType = dataType;
        this.data = data;
        this.operation = operation;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public Map<String, Object> getData() {
        return data;
    }
    
    public void setData(Map<String, Object> data) {
        this.data = data;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public void setOperation(String operation) {
        this.operation = operation;
    }
    
    @Override
    public String toString() {
        return "DataSyncMessage{" +
                "deviceId='" + deviceId + '\'' +
                ", dataType='" + dataType + '\'' +
                ", operation='" + operation + '\'' +
                ", messageId='" + messageId + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
} 