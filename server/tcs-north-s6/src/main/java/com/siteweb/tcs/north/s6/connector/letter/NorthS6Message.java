package com.siteweb.tcs.north.s6.connector.letter;

import java.io.Serializable;

/**
 * 北向S6接口消息基类
 */
public abstract class NorthS6Message implements Serializable {
    private static final long serialVersionUID = 1L;
    
    protected long timestamp;
    protected String messageId;
    
    public NorthS6Message() {
        this.timestamp = System.currentTimeMillis();
        this.messageId = generateMessageId();
    }
    
    public NorthS6Message(String messageId) {
        this.timestamp = System.currentTimeMillis();
        this.messageId = messageId;
    }
    
    private String generateMessageId() {
        return "tcs-tcs-north-s6-" + System.currentTimeMillis() + "-" + hashCode();
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
} 