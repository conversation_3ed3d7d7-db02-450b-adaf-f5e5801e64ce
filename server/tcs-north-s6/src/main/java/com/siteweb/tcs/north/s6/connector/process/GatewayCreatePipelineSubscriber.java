package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandResponseChange;
import com.siteweb.tcs.hub.domain.v2.letter.LifeCycleEvent;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;

/**
 * @program: tcs2
 * @description: 网关创建通道订阅者
 * @author: xsx
 * @create: 2025-07-14 09:29
 **/

public class GatewayCreatePipelineSubscriber extends ProbeActor {
    // 分布式发布/订阅主题前缀
    private static final String TOPIC_PREFIX = "pipeline";

    // 分布式发布/订阅主题格式：pipeline.{gatewayId}.{type}
    private static final String GATEWAY_CREATE_TOPIC = TOPIC_PREFIX+"GatewayCreate";

    private final ActorRef mediator;

    private GatewayCreatePipelineSubscriber() {
        // 初始化分布式发布/订阅中介器
        mediator = DistributedPubSub.get(getContext().system()).mediator();

        // 订阅主题，pipelineGatewayCreate
        System.out.println("GatewayCreatePipelineSubscriber: 开始订阅主题: " + GATEWAY_CREATE_TOPIC);
        System.out.println("GatewayCreatePipelineSubscriber: Actor路径: " + getSelf().path());
        System.out.println("GatewayCreatePipelineSubscriber: Actor系统: " + getContext().system().name());
        System.out.println("GatewayCreatePipelineSubscriber: 创建时间: " + java.time.LocalDateTime.now());
        mediator.tell(new DistributedPubSubMediator.Subscribe(GATEWAY_CREATE_TOPIC, getSelf()), getSelf());

        System.out.println("GatewayCreatePipelineSubscriber: 订阅请求已发送，等待确认...");

        // 添加一个延迟测试，在订阅成功后发送一个测试消息
        getContext().system().scheduler().scheduleOnce(
            scala.concurrent.duration.Duration.create(5, java.util.concurrent.TimeUnit.SECONDS),
            () -> {
                System.out.println("GatewayCreatePipelineSubscriber: 发送测试消息...");
                mediator.tell(new DistributedPubSubMediator.Publish(GATEWAY_CREATE_TOPIC, "Test message from subscriber", true), getSelf());
            },
            getContext().dispatcher()
        );
    }

    public static Props props(){
        return Props.create(GatewayCreatePipelineSubscriber.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DistributedPubSubMediator.SubscribeAck.class, this::onSubscribeAck)
                .match(DistributedPubSubMediator.UnsubscribeAck.class, this::onUnsubscribeAck)
                .match(LifeCycleEvent.class,this::onLifeCycleEvent)
                .match(String.class, message -> {
                    System.out.println("GatewayCreatePipelineSubscriber: 收到字符串消息: " + message);
                    System.out.println("GatewayCreatePipelineSubscriber: 测试消息接收成功！");
                })
                .matchAny(message -> {
                    System.out.println("GatewayCreatePipelineSubscriber: 收到未知消息类型: " + message.getClass().getName());
                    System.out.println("GatewayCreatePipelineSubscriber: 消息内容: " + message);
                })
                .build();
    }

    /**
     * 处理订阅确认
     */
    public void onSubscribeAck(DistributedPubSubMediator.SubscribeAck ack) {
        System.out.println("GatewayCreatePipelineSubscriber: 订阅确认成功，主题: " + ack.subscribe().topic());
        System.out.println("GatewayCreatePipelineSubscriber: 订阅者: " + ack.subscribe().ref().path());
        System.out.println("GatewayCreatePipelineSubscriber: 现在已准备好接收消息!");
    }

    /**
     * 处理取消订阅确认
     */
    public void onUnsubscribeAck(DistributedPubSubMediator.UnsubscribeAck ack) {
        System.out.println("GatewayCreatePipelineSubscriber: 取消订阅确认，主题: " + ack.unsubscribe().topic());
    }

    /**
     * 创建网关消息
     * @param lifeCycleEvent
     */
    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        System.out.println("GatewayCreatePipelineSubscriber: 收到生命周期事件！");
        System.out.println("GatewayCreatePipelineSubscriber: 接收时间: " + java.time.LocalDateTime.now());
        System.out.println("GatewayCreatePipelineSubscriber: 事件类型: " + lifeCycleEvent.getLifeCycleEventEnum());
        System.out.println("GatewayCreatePipelineSubscriber: 物类型: " + lifeCycleEvent.getThingType());
        System.out.println("GatewayCreatePipelineSubscriber: 物ID: " + lifeCycleEvent.getThingId());
        System.out.println("GatewayCreatePipelineSubscriber: 完整事件: " + lifeCycleEvent);
    }


}
