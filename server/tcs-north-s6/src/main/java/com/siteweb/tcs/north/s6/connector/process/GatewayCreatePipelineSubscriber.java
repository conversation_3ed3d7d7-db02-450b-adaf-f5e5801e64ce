package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandResponseChange;
import com.siteweb.tcs.hub.domain.v2.letter.LifeCycleEvent;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;

/**
 * @program: tcs2
 * @description: 网关创建通道订阅者
 * @author: xsx
 * @create: 2025-07-14 09:29
 **/

public class GatewayCreatePipelineSubscriber extends ProbeActor {
    // 分布式发布/订阅主题前缀
    private static final String TOPIC_PREFIX = "pipeline";

    // 分布式发布/订阅主题格式：pipeline.{gatewayId}.{type}
    private static final String GATEWAY_CREATE_TOPIC = TOPIC_PREFIX+"GatewayCreate";

    private final ActorRef mediator;

    private GatewayCreatePipelineSubscriber() {
        // 初始化分布式发布/订阅中介器
        mediator = DistributedPubSub.get(getContext().system()).mediator();
        //订阅主题，pipeline.gatewayId.control_command_request
        mediator.tell(new DistributedPubSubMediator.Subscribe(GATEWAY_CREATE_TOPIC, getSelf()), getSelf());
    }

    public static Props props(){
        return Props.create(GatewayCreatePipelineSubscriber.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(LifeCycleEvent.class,this::onLifeCycleEvent)
                .build();
    }

    /**
     * 创建网关消息
     * @param lifeCycleEvent
     */
    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
    }


}
