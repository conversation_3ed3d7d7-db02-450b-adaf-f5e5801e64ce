package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandResponseChange;
import com.siteweb.tcs.hub.domain.v2.letter.LifeCycleEvent;
import com.siteweb.tcs.north.s6.web.lcm.LCMHandlerObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.Cluster;
import org.apache.pekko.cluster.Member;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;

import scala.collection.immutable.Set;

/**
 * @program: tcs2
 * @description: 网关创建通道订阅者
 * @author: xsx
 * @create: 2025-07-14 09:29
 **/
@Slf4j
public class GatewayCreatePipelineSubscriber extends ProbeActor {
    // 分布式发布/订阅主题前缀
    private static final String TOPIC_PREFIX = "pipeline";

    private static final String GATEWAY_CREATE_TOPIC = TOPIC_PREFIX+"GatewayCreate";

    private final ActorRef mediator;

    private final LCMHandlerObserver lcmHandlerObserver;

    private GatewayCreatePipelineSubscriber() {
        // 初始化分布式发布/订阅中介器
        mediator = DistributedPubSub.get(getContext().system()).mediator();
        
        // 订阅主题，pipelineGatewayCreate
        log.info("GatewayCreatePipelineSubscriber: 开始订阅主题: " + GATEWAY_CREATE_TOPIC);
        log.info("GatewayCreatePipelineSubscriber: Actor路径: " + getSelf().path());
        log.info("GatewayCreatePipelineSubscriber: Actor系统: " + getContext().system().name());
        log.info("GatewayCreatePipelineSubscriber: 创建时间: " + java.time.LocalDateTime.now());
        mediator.tell(new DistributedPubSubMediator.Subscribe(GATEWAY_CREATE_TOPIC, getSelf()), getSelf());
        lcmHandlerObserver =
        log.info("GatewayCreatePipelineSubscriber: 订阅请求已发送，等待确认...");
    }

    public static Props props(){
        return Props.create(GatewayCreatePipelineSubscriber.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DistributedPubSubMediator.SubscribeAck.class, this::onSubscribeAck)
                .match(DistributedPubSubMediator.UnsubscribeAck.class, this::onUnsubscribeAck)
                .match(LifeCycleEvent.class,this::onLifeCycleEvent)
                .build();
    }

    /**
     * 处理订阅确认
     */
    public void onSubscribeAck(DistributedPubSubMediator.SubscribeAck ack) {
        log.info("GatewayCreatePipelineSubscriber: 订阅确认成功，主题: " + ack.subscribe().topic());
        log.info("GatewayCreatePipelineSubscriber: 订阅者: " + ack.subscribe().ref().path());
        log.info("GatewayCreatePipelineSubscriber: 现在已准备好接收消息!");
    }

    /**
     * 处理取消订阅确认
     */
    public void onUnsubscribeAck(DistributedPubSubMediator.UnsubscribeAck ack) {
        log.info("GatewayCreatePipelineSubscriber: 取消订阅确认，主题: " + ack.unsubscribe().topic());
    }

    /**
     * 创建网关消息
     * @param lifeCycleEvent
     */
    private void onLifeCycleEvent(LifeCycleEvent lifeCycleEvent) {
        log.info("GatewayCreatePipelineSubscriber: 收到生命周期事件！");
        log.info("GatewayCreatePipelineSubscriber: 接收时间: " + java.time.LocalDateTime.now());
        log.info("GatewayCreatePipelineSubscriber: 事件类型: " + lifeCycleEvent.getLifeCycleEventEnum());
        log.info("GatewayCreatePipelineSubscriber: 物类型: " + lifeCycleEvent.getThingType());
        log.info("GatewayCreatePipelineSubscriber: 物ID: " + lifeCycleEvent.getThingId());
        log.info("GatewayCreatePipelineSubscriber: 完整事件: " + lifeCycleEvent);
    }
    
}
