package com.siteweb.tcs.north.s6.connector.process;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

/**
 * 北向S6代理Actor
 * <p>
 * 负责处理北向接口的消息和状态
 * </p>
 */
@Slf4j
public class NorthS6Proxy extends AbstractActor {

    /**
     * Returns the props for creating a {@link NorthS6Proxy} Actor.
     *
     * @return a Props for creating a NorthS6Proxy Actor
     */
    public static Props props() {
        return Props.create(NorthS6Proxy.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .matchAny(message -> {
                    log.debug("NorthS6Proxy received message: {}", message);
                    // 处理各种消息
                })
                .build();
    }
} 