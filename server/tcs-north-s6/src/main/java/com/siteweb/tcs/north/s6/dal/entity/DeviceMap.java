package com.siteweb.tcs.north.s6.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 设备映射表实体类
 */
@Data
@TableName("tcs_device_map")
public class DeviceMap implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 网关ID
     */
    @TableField("GatewayId")
    private Long gatewayId;

    /**
     * 北向监控单元ID
     */
    @TableField("NorthMonitorUnitId")
    private Integer northMonitorUnitId;

    /**
     * 设备ID
     */
    @TableField("DeviceId")
    private Long deviceId;

    /**
     * 北向设备ID
     */
    @TableField("NorthEquipmentId")
    private Integer northEquipmentId;


    /**
     * 设备模板id
     */
    @TableField("NorthEquipmentTemplateId")
    private Integer northEquipmentTemplateId;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("Deleted")
    private Boolean deleted;

    private static final String uniqueKey = "%s.%s";

    public String getUniqueKey(){
        return String.format(uniqueKey,gatewayId,deviceId);
    }
} 