package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC设备拓展表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("cmcc_device_ext")
public class CmccDeviceExt {

    /**
     * hub设备id
     */
    @TableId("DeviceGuid")
    private Long deviceGuid;

    /**
     * CMCC设备大类id
     */
    private Integer deviceTypeId;

    /**
     * CMCC设备子类id
     */
    private Integer deviceSubTypeId;

    /**
     * siteweb设备种类id
     */
    private Integer equipmentCategoryId;
}
