package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 移动设备类型映射表
 */
@Data
@TableName("cmcc_device_type_map")
public class CmccDeviceTypeMap implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("DeviceTypeID")
    private String deviceTypeId;

    @TableField("DeviceSubTypeID")
    private String deviceSubTypeId;

    @TableField("EquipmentCategoryID")
    private Integer equipmentCategoryId;
}
