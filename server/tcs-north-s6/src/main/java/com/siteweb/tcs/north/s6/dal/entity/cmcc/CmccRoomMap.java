package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC机房映射表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("cmcc_room_map")
public class CmccRoomMap {

    /**
     * CMCC站点id
     */
    private String cmccSiteId;

    /**
     * CMCC机房id
     */
    @TableId("CmccRoomID")
    private String cmccRoomId;

    /**
     * CMCC机房名称
     */
    private String cmccRoomName;

    /**
     * CMCC机房类型
     */
    private Integer cmccRoomType;

    /**
     * SiteWeb局站id
     */
    private Integer stationId;

    /**
     * SiteWeb机房id
     */
    private Integer houseId;
}
