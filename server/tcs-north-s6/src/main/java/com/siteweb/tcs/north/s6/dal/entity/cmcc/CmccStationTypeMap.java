package com.siteweb.tcs.north.s6.dal.entity.cmcc;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 移动标准化局站类型与Siteweb局站类型映射表
 */
@Data
@TableName("cmcc_station_type_map")
public class CmccStationTypeMap implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("StationTypeID")
    private Integer stationTypeId;

    @TableField("StationCategoryID")
    private Integer stationCategoryId;
}

