package com.siteweb.tcs.north.s6.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.CmccDeviceType;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 移动设备种类表 Mapper 接口
 */
@Mapper
public interface CmccDeviceTypeMapper extends BaseMapper<CmccDeviceType> {

    /**
     * 查询所有设备大类（去重）
     * @return 设备大类列表
     */
    List<CmccDeviceType> selectDistinctDeviceTypes();

    /**
     * 根据设备大类ID查询设备子类
     * @param deviceTypeId 设备大类ID
     * @return 设备子类列表
     */
    List<CmccDeviceType> selectSubTypesByDeviceTypeId(Integer deviceTypeId);
}

