package com.siteweb.tcs.north.s6.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.ControlMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 控制映射表Mapper接口
 */
@Mapper
@Repository
public interface ControlMapMapper extends BaseMapper<ControlMap> {

    /**
     * 根据设备ID查询控制映射
     * @param deviceId 设备ID
     * @return 控制映射列表
     */
    List<ControlMap> selectByDeviceId(@Param("deviceId") Long deviceId);

    /**
     * 根据北向设备ID查询控制映射
     * @param northEquipmentId 北向设备ID
     * @return 控制映射列表
     */
    List<ControlMap> selectByNorthEquipmentId(@Param("northEquipmentId") Integer northEquipmentId);

    /**
     * 根据控制ID查询控制映射
     * @param controlId 控制ID
     * @return 控制映射列表
     */
    List<ControlMap> selectByControlId(@Param("controlId") Long controlId);

    /**
     * 根据北向控制ID查询控制映射
     * @param northControlId 北向控制ID
     * @return 控制映射列表
     */
    List<ControlMap> selectByNorthControlId(@Param("northControlId") Integer northControlId);

    /**
     * 批量插入控制映射
     * @param controlMaps 控制映射列表
     * @return 插入成功数量
     */
    int insertBatch(@Param("list") List<ControlMap> controlMaps);

    /**
     * 根据复合主键删除控制映射
     * @param deviceId 设备ID
     * @param controlId 控制ID
     * @param northControlId 北向控制ID
     * @return 删除成功数量
     */
    int deleteByCompositeKey(@Param("deviceId") Long deviceId,
                            @Param("controlId") Long controlId,
                            @Param("northControlId") Integer northControlId);
} 