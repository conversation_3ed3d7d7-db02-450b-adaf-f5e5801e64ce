package com.siteweb.tcs.north.s6.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 网关映射表Mapper接口
 */
@Mapper
@Repository
public interface GatewayMapMapper extends BaseMapper<GatewayMap> {

    /**
     * 根据网关ID查询网关映射
     * @param gatewayId 网关ID
     * @return 网关映射列表
     */
    List<GatewayMap> selectByGatewayId(@Param("gatewayId") Long gatewayId);

    /**
     * 根据北向监控单元ID查询网关映射
     * @param northMonitorUnitId 北向监控单元ID
     * @return 网关映射列表
     */
    List<GatewayMap> selectByNorthMonitorUnitId(@Param("northMonitorUnitId") Integer northMonitorUnitId);

    /**
     * 批量插入网关映射
     * @param gatewayMaps 网关映射列表
     * @return 插入成功数量
     */
    int insertBatch(@Param("list") List<GatewayMap> gatewayMaps);

    /**
     * 根据复合主键删除网关映射
     * @param gatewayId 网关ID
     * @param northMonitorUnitId 北向监控单元ID
     * @return 删除成功数量
     */
    int deleteByCompositeKey(@Param("gatewayId") Long gatewayId,
                            @Param("northMonitorUnitId") Integer northMonitorUnitId);

    /**
     * 查询所有活跃的网关映射
     * @return 网关映射列表
     */
    List<GatewayMap> selectAllActive();
} 