package com.siteweb.tcs.north.s6.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.SignalMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 信号映射表Mapper接口
 */
@Mapper
@Repository
public interface SignalMapMapper extends BaseMapper<SignalMap> {

    /**
     * 根据设备ID查询信号映射
     * @param deviceId 设备ID
     * @return 信号映射列表
     */
    List<SignalMap> selectByDeviceId(@Param("deviceId") Long deviceId);

    /**
     * 根据北向设备ID查询信号映射
     * @param northEquipmentId 北向设备ID
     * @return 信号映射列表
     */
    List<SignalMap> selectByNorthEquipmentId(@Param("northEquipmentId") Integer northEquipmentId);

    /**
     * 根据信号ID查询信号映射
     * @param signalId 信号ID
     * @return 信号映射列表
     */
    List<SignalMap> selectBySignalId(@Param("signalId") Long signalId);

    /**
     * 根据北向信号ID查询信号映射
     * @param northSignalId 北向信号ID
     * @return 信号映射列表
     */
    List<SignalMap> selectByNorthSignalId(@Param("northSignalId") Integer northSignalId);

    /**
     * 批量插入信号映射
     * @param signalMaps 信号映射列表
     * @return 插入成功数量
     */
    int insertBatch(@Param("list") List<SignalMap> signalMaps);

    /**
     * 根据复合主键删除信号映射
     * @param deviceId 设备ID
     * @param signalId 信号ID
     * @param northSignalId 北向信号ID
     * @return 删除成功数量
     */
    int deleteByCompositeKey(@Param("deviceId") Long deviceId,
                            @Param("signalId") Long signalId,
                            @Param("northSignalId") Integer northSignalId);
} 