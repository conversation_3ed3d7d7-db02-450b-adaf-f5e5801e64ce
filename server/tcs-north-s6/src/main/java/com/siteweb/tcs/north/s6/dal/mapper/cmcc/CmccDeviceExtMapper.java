package com.siteweb.tcs.north.s6.dal.mapper.cmcc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * CMCC设备拓展表 Mapper 接口
 */
@Mapper
public interface CmccDeviceExtMapper extends BaseMapper<CmccDeviceExt> {

    /**
     * 根据设备GUID查询设备拓展信息
     * @param deviceGuid 设备GUID
     * @return 设备拓展信息
     */
    CmccDeviceExt selectByDeviceGuid(@Param("deviceGuid") Long deviceGuid);

    /**
     * 根据设备类型和子类型查询设备拓展信息
     * @param deviceTypeId 设备大类ID
     * @param deviceSubTypeId 设备子类ID
     * @return 设备拓展信息列表
     */
    java.util.List<CmccDeviceExt> selectByDeviceTypeAndSubType(
        @Param("deviceTypeId") Integer deviceTypeId, 
        @Param("deviceSubTypeId") Integer deviceSubTypeId
    );
}
