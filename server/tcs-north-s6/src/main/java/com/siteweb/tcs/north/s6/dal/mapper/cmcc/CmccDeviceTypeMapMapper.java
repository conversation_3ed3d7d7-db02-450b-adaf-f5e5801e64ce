package com.siteweb.tcs.north.s6.dal.mapper.cmcc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceTypeMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 移动设备类型映射表 Mapper 接口
 */
@Mapper
public interface CmccDeviceTypeMapMapper extends BaseMapper<CmccDeviceTypeMap> {

    /**
     * 根据设备类型ID和子类型ID查询映射
     * @param deviceTypeId 设备大类ID
     * @param deviceSubTypeId 设备子类ID
     * @return 映射记录
     */
    CmccDeviceTypeMap selectByDeviceTypeAndSubType(@Param("deviceTypeId") String deviceTypeId, 
                                                   @Param("deviceSubTypeId") String deviceSubTypeId);
}

