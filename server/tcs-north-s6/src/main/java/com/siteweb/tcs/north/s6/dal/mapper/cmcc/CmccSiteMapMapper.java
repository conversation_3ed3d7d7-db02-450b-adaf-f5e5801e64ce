package com.siteweb.tcs.north.s6.dal.mapper.cmcc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * CMCC站点映射表 Mapper 接口
 */
@Mapper
public interface CmccSiteMapMapper extends BaseMapper<CmccSiteMap> {

    /**
     * 根据CMCC站点ID查询站点映射信息
     * @param cmccSiteId CMCC站点ID
     * @return 站点映射信息
     */
    CmccSiteMap selectByCmccSiteId(@Param("cmccSiteId") String cmccSiteId);

    /**
     * 根据SiteWeb局站ID查询站点映射信息列表
     * @param stationId SiteWeb局站ID
     * @return 站点映射信息列表
     */
    java.util.List<CmccSiteMap> selectByStationId(@Param("stationId") Integer stationId);

    /**
     * 根据CMCC站点类型查询站点映射信息列表
     * @param cmccSiteType CMCC站点类型
     * @return 站点映射信息列表
     */
    java.util.List<CmccSiteMap> selectByCmccSiteType(@Param("cmccSiteType") Integer cmccSiteType);
}
