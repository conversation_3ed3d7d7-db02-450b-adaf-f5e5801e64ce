package com.siteweb.tcs.north.s6.dal.mapper.cmcc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationTypeMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 移动标准化局站类型与Siteweb局站类型映射表 Mapper 接口
 */
@Mapper
public interface CmccStationTypeMapMapper extends BaseMapper<CmccStationTypeMap> {

    /**
     * 根据局站类型ID查询映射
     * @param stationTypeId 局站类型ID
     * @return 映射记录
     */
    CmccStationTypeMap selectByStationTypeId(@Param("stationTypeId") Integer stationTypeId);
}

