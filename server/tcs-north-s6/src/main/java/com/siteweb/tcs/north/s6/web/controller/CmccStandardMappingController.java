package com.siteweb.tcs.north.s6.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.north.s6.util.ResponseUtil;
import com.siteweb.tcs.north.s6.dal.dto.CmccStandardMappingDTO;
import com.siteweb.tcs.north.s6.dal.entity.*;
import com.siteweb.tcs.north.s6.web.service.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 移动标准化映射控制器
 */
@Slf4j
@RestController
@RequestMapping("/cmcc-standard-mapping")
public class CmccStandardMappingController {

    @Resource
    private ICmccDeviceTypeService cmccDeviceTypeService;

    @Resource
    private ICmccDeviceTypeMapService cmccDeviceTypeMapService;

    @Resource
    private ICmccStationTypeService cmccStationTypeService;

    @Resource
    private ICmccStationTypeMapService cmccStationTypeMapService;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取设备类型标准化数据
     */
    @GetMapping("/device-standard-data")
    public ResponseEntity<ResponseResult> getDeviceStandardData() {
        try {
            // 获取移动设备类型映射数据
            List<CmccStandardMappingDTO.DeviceTypeMapping> deviceTypeMappings = new ArrayList<>();
            List<CmccDeviceType> allDeviceTypes = cmccDeviceTypeService.list(); // 获取所有设备类型数据
            
            for (CmccDeviceType deviceType : allDeviceTypes) {
                CmccStandardMappingDTO.DeviceTypeMapping mapping = new CmccStandardMappingDTO.DeviceTypeMapping();
                mapping.setDeviceTypeId(deviceType.getDeviceTypeId());
                mapping.setDeviceTypeName(deviceType.getDeviceTypeName());
                mapping.setDeviceSubTypeId(deviceType.getDeviceSubTypeId());
                mapping.setDeviceSubTypeName(deviceType.getDeviceSubTypeName());
                
                // 查询映射关系
                CmccDeviceTypeMap typeMap = cmccDeviceTypeMapService.getByDeviceTypeAndSubType(
                    String.valueOf(deviceType.getDeviceTypeId()), 
                    String.valueOf(deviceType.getDeviceSubTypeId())
                );
                if (typeMap != null) {
                    mapping.setEquipmentCategoryId(typeMap.getEquipmentCategoryId());
                    mapping.setMappingId(typeMap.getId());
                }
                
                deviceTypeMappings.add(mapping);
            }
            
            return ResponseHelper.successful(deviceTypeMappings);
        } catch (Exception e) {
            log.error("获取设备标准化数据失败", e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 获取局站类型标准化数据
     */
    @GetMapping("/station-standard-data")
    public ResponseEntity<ResponseResult> getStationStandardData() {
        try {
            // 获取移动局站类型映射数据
            List<CmccStandardMappingDTO.StationTypeMapping> stationTypeMappings = new ArrayList<>();
            List<CmccStationType> stationTypes = cmccStationTypeService.list();
            
            for (CmccStationType stationType : stationTypes) {
                CmccStandardMappingDTO.StationTypeMapping mapping = new CmccStandardMappingDTO.StationTypeMapping();
                mapping.setStationTypeId(stationType.getStationTypeId());
                mapping.setStationTypeName(stationType.getStationTypeName());
                
                // 查询映射关系
                CmccStationTypeMap typeMap = cmccStationTypeMapService.getByStationTypeId(stationType.getStationTypeId());
                if (typeMap != null) {
                    mapping.setStationCategoryId(typeMap.getStationCategoryId());
                    mapping.setMappingId(typeMap.getId());
                }
                
                stationTypeMappings.add(mapping);
            }
            
            return ResponseHelper.successful(stationTypeMappings);
        } catch (Exception e) {
            log.error("获取局站标准化数据失败", e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

//    /**
//     * 获取移动设备类型
//     */
//    @PostMapping("/insert-cmcc-device-types")
//    public ResponseEntity<ResponseResult> fetchCmccDeviceTypes(List<CmccDeviceType> entities) {
//        try {
//            // 清空并批量插入
//            boolean success = cmccDeviceTypeService.clearAndBatchInsert(entities);
//            return ResponseHelper.successful(success);
//        } catch (Exception e) {
//            log.error("获取移动设备类型失败", e);
//            return ResponseHelper.failed(e.getMessage());
//        }
//    }
//
//    /**
//     * 获取移动局站类型
//     */
//    @PostMapping("/insert-cmcc-station-types")
//    public ResponseEntity<ResponseResult> fetchCmccStationTypes(List<CmccStationType> entities) {
//        try {
//            // 清空并批量插入
//            boolean success = cmccStationTypeService.clearAndBatchInsert(entities);
//            return ResponseHelper.successful(success);
//        } catch (Exception e) {
//            log.error("获取移动局站类型失败", e);
//            return ResponseHelper.failed(e.getMessage());
//        }
//    }

    /**
     * 保存设备类型映射
     */
    @PostMapping("/save-device-mapping")
    public ResponseEntity<Map<String, Object>> saveDeviceMapping(@RequestBody List<CmccStandardMappingDTO.DeviceTypeMapping> mappings) {
        try {
            List<CmccDeviceTypeMap> mapEntities = new ArrayList<>();
            
            for (CmccStandardMappingDTO.DeviceTypeMapping mapping : mappings) {
                CmccDeviceTypeMap entity = new CmccDeviceTypeMap();
                entity.setId(mapping.getMappingId());
                entity.setDeviceTypeId(String.valueOf(mapping.getDeviceTypeId()));
                entity.setDeviceSubTypeId(String.valueOf(mapping.getDeviceSubTypeId()));
                entity.setEquipmentCategoryId(mapping.getEquipmentCategoryId());
                mapEntities.add(entity);
            }
            
            boolean success = cmccDeviceTypeMapService.batchUpdateMapping(mapEntities);
            if (success) {
                return ResponseUtil.success("保存设备类型映射成功");
            } else {
                return ResponseUtil.error("保存设备类型映射失败");
            }
        } catch (Exception e) {
            log.error("保存设备类型映射失败", e);
            return ResponseUtil.error("保存设备类型映射失败: " + e.getMessage());
        }
    }

    /**
     * 保存局站类型映射
     */
    @PostMapping("/save-station-mapping")
    public ResponseEntity<Map<String, Object>> saveStationMapping(@RequestBody List<CmccStandardMappingDTO.StationTypeMapping> mappings) {
        try {
            List<CmccStationTypeMap> mapEntities = new ArrayList<>();
            
            for (CmccStandardMappingDTO.StationTypeMapping mapping : mappings) {
                CmccStationTypeMap entity = new CmccStationTypeMap();
                entity.setId(mapping.getMappingId());
                entity.setStationTypeId(mapping.getStationTypeId());
                entity.setStationCategoryId(mapping.getStationCategoryId());
                mapEntities.add(entity);
            }
            
            boolean success = cmccStationTypeMapService.batchUpdateMapping(mapEntities);
            if (success) {
                return ResponseUtil.success("保存局站类型映射成功");
            } else {
                return ResponseUtil.error("保存局站类型映射失败");
            }
        } catch (Exception e) {
            log.error("保存局站类型映射失败", e);
            return ResponseUtil.error("保存局站类型映射失败: " + e.getMessage());
        }
    }

    /**
     * 持久化移动设备类型数据
     */
    @PostMapping("/insert-cmcc-device-types")
    public ResponseEntity<Map<String, Object>> insertCmccDeviceTypes(@RequestBody List<Map<String, Object>> deviceTypes) {
        try {
            List<CmccDeviceType> entities = new ArrayList<>();
            for (Map<String, Object> deviceType : deviceTypes) {
                CmccDeviceType entity = new CmccDeviceType();
                entity.setDeviceTypeId((Integer) deviceType.get("deviceTypeId"));
                entity.setDeviceTypeName((String) deviceType.get("deviceTypeName"));
                entity.setDeviceSubTypeId((Integer) deviceType.get("deviceSubTypeId"));
                entity.setDeviceSubTypeName((String) deviceType.get("deviceSubTypeName"));
                entities.add(entity);
            }
            
            // 清空并批量插入
            boolean success = cmccDeviceTypeService.clearAndBatchInsert(entities);
            if (success) {
                return ResponseUtil.success("保存移动设备类型成功");
            } else {
                return ResponseUtil.error("保存移动设备类型失败");
            }
        } catch (Exception e) {
            log.error("持久化移动设备类型失败", e);
            return ResponseUtil.error("持久化移动设备类型失败: " + e.getMessage());
        }
    }

    /**
     * 持久化移动局站类型数据
     */
    @PostMapping("/insert-cmcc-station-types")
    public ResponseEntity<Map<String, Object>> insertCmccStationTypes(@RequestBody List<Map<String, Object>> stationTypes) {
        try {
            List<CmccStationType> entities = new ArrayList<>();
            for (Map<String, Object> stationType : stationTypes) {
                CmccStationType entity = new CmccStationType();
                entity.setStationTypeId((Integer) stationType.get("stationTypeId"));
                entity.setStationTypeName((String) stationType.get("stationTypeName"));
                entities.add(entity);
            }
            
            // 清空并批量插入
            boolean success = cmccStationTypeService.clearAndBatchInsert(entities);
            if (success) {
                return ResponseUtil.success("保存移动局站类型成功");
            } else {
                return ResponseUtil.error("保存移动局站类型失败");
            }
        } catch (Exception e) {
            log.error("持久化移动局站类型失败", e);
            return ResponseUtil.error("持久化移动局站类型失败: " + e.getMessage());
        }
    }
}

