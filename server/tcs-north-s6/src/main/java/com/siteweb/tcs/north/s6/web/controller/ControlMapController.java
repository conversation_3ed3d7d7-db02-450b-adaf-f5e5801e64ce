package com.siteweb.tcs.north.s6.web.controller;

import com.siteweb.tcs.north.s6.dal.entity.ControlMap;
import com.siteweb.tcs.north.s6.util.ResponseUtil;
import com.siteweb.tcs.north.s6.web.service.IControlMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 控制映射控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/control-map")
public class ControlMapController {

    @Autowired
    private IControlMapService controlMapService;

    /**
     * 根据设备ID查询
     * @param deviceId 设备ID
     * @return 控制映射列表
     */
    @GetMapping(value = "/device/{deviceId}")
    public ResponseEntity<Map<String, Object>> getByDeviceId(@PathVariable Long deviceId) {
        try {
            List<ControlMap> controlMaps = controlMapService.getByDeviceId(deviceId);
            return ResponseUtil.success(controlMaps);
        } catch (Exception e) {
            log.error("查询控制映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 创建
     * @param controlMap 控制映射
     * @return 结果
     */
    @PostMapping(value = "/create")
    public ResponseEntity<Map<String, Object>> create(@RequestBody ControlMap controlMap) {
        try {
            controlMap.setDeleted(false);
            boolean success = controlMapService.save(controlMap);
            return success ? ResponseUtil.success("创建成功") : ResponseUtil.error("创建失败");
        } catch (Exception e) {
            log.error("创建控制映射失败", e);
            return ResponseUtil.error("创建失败");
        }
    }

    /**
     * 批量创建
     * @param controlMaps 列表
     * @return 结果
     */
    @PostMapping(value = "/batch-create")
    public ResponseEntity<Map<String, Object>> batchCreate(@RequestBody List<ControlMap> controlMaps) {
        try {
            controlMaps.forEach(cm -> cm.setDeleted(false));
            boolean success = controlMapService.saveBatch(controlMaps);
            return success ? ResponseUtil.success("批量创建成功") : ResponseUtil.error("批量创建失败");
        } catch (Exception e) {
            log.error("批量创建控制映射失败", e);
            return ResponseUtil.error("批量创建失败");
        }
    }

    /**
     * 更新
     * @param controlMap 控制映射
     * @return 结果
     */
    @PutMapping(value = "/update")
    public ResponseEntity<Map<String, Object>> update(@RequestBody ControlMap controlMap) {
        try {
            boolean success = controlMapService.updateById(controlMap);
            return success ? ResponseUtil.success("更新成功") : ResponseUtil.error("更新失败");
        } catch (Exception e) {
            log.error("更新控制映射失败", e);
            return ResponseUtil.error("更新失败");
        }
    }

    /**
     * 删除
     * @param deviceId 设备ID
     * @param controlId 控制ID
     * @param northControlId 北向控制ID
     * @return 结果
     */
    @DeleteMapping(value = "/delete")
    public ResponseEntity<Map<String, Object>> delete(
            @RequestParam Long deviceId,
            @RequestParam Long controlId,
            @RequestParam Integer northControlId) {
        try {
            boolean success = controlMapService.deleteByCompositeKey(deviceId, controlId, northControlId);
            return success ? ResponseUtil.success("删除成功") : ResponseUtil.error("删除失败");
        } catch (Exception e) {
            log.error("删除控制映射失败", e);
            return ResponseUtil.error("删除失败");
        }
    }

    /**
     * 查询所有
     * @return 列表
     */
    @GetMapping(value = "/list")
    public ResponseEntity<Map<String, Object>> list() {
        try {
            List<ControlMap> controlMaps = controlMapService.list();
            return ResponseUtil.success(controlMaps);
        } catch (Exception e) {
            log.error("查询所有控制映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }
} 