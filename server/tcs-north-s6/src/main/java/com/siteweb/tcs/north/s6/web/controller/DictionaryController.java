package com.siteweb.tcs.north.s6.web.controller;

import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.util.ResponseUtil;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.service.IDataItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @program: tcs2
 * @description: 字典控制区
 * @author: xsx
 * @create: 2025-07-22 12:57
 **/
@Slf4j
@RestController
@RequestMapping(value = "/dictionary")
public class DictionaryController {
    @Autowired
    private IDataItemService dataItemService;

    // 获取设备类型
    @GetMapping(value = "/equipment-type")
    public ResponseEntity<Map<String, Object>> getEquipmentType() {
        try {
            List<DataItem> equipmentTypeList = dataItemService.findByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY);
            return ResponseUtil.success(equipmentTypeList);
        } catch (Exception e) {
            log.error("查询设备类型", e);
            return ResponseUtil.error("查询失败");
        }
    }
}
