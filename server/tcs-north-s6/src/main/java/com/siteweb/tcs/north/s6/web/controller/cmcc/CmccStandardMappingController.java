package com.siteweb.tcs.north.s6.web.controller.cmcc;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceType;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceTypeMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationType;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationTypeMap;
import com.siteweb.tcs.north.s6.util.ResponseUtil;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccStandardMappingDTO;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.north.s6.web.service.cmcc.*;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 移动标准化映射控制器
 */
@Slf4j
@RestController
@RequestMapping("/cmcc-standard-mapping")
public class CmccStandardMappingController {

    @Resource
    private ICmccDeviceTypeService cmccDeviceTypeService;

    @Resource
    private ICmccDeviceTypeMapService cmccDeviceTypeMapService;

    @Resource
    private ICmccStationTypeService cmccStationTypeService;

    @Resource
    private ICmccStationTypeMapService cmccStationTypeMapService;

    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    private SitewebPersistentService sitewebPersistentService;

    @Autowired
    private ICmccStandardMappingService cmccStandardMappingService;

    /**
     * 获取设备类型标准化数据（支持一对一映射）
     */
    @GetMapping("/device-standard-data")
    public ResponseEntity<ResponseResult> getDeviceStandardData() {
        try {
            // 获取移动设备类型映射数据
            List<CmccStandardMappingDTO.DeviceTypeMapping> deviceTypeMappings = new ArrayList<>();
            List<CmccDeviceType> allDeviceTypes = cmccDeviceTypeService.list(); // 获取所有设备类型数据
            
            // 获取所有SiteWeb设备类型，用于查找名称
            List<DataItem> sitewebCategories = sitewebPersistentService.getConfigAPI()
                .findByEntryIdForDataItem(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());

            Map<Integer, DataItem> categoryItemMap = sitewebCategories.stream()
                    .collect(Collectors.toMap(DataItem::getItemId, Function.identity()));
            
            for (CmccDeviceType deviceType : allDeviceTypes) {
                CmccStandardMappingDTO.DeviceTypeMapping mapping = new CmccStandardMappingDTO.DeviceTypeMapping();
                mapping.setDeviceTypeId(deviceType.getDeviceTypeId());
                mapping.setDeviceTypeName(deviceType.getDeviceTypeName());
                mapping.setDeviceSubTypeId(deviceType.getDeviceSubTypeId());
                mapping.setDeviceSubTypeName(deviceType.getDeviceSubTypeName());
                
                // 查询映射关系
                CmccDeviceTypeMap typeMap = cmccDeviceTypeMapService.getByDeviceTypeAndSubType(
                    String.valueOf(deviceType.getDeviceTypeId()), 
                    String.valueOf(deviceType.getDeviceSubTypeId())
                );
                if (typeMap != null) {
                    mapping.setSitewebEquipmentCategoryId(typeMap.getEquipmentCategoryId());
                    DataItem dataItem = categoryItemMap.get(typeMap.getEquipmentCategoryId());
                    mapping.setSitewebEquipmentCategoryName(dataItem.getItemValue());
                    // 检查是否为自动创建（这里可以通过数据库字段或命名约定来判断）
                    mapping.setIsAutoCreated(!dataItem.getIsSystem());
                    mapping.setMappingId(typeMap.getId());
                }
                
                deviceTypeMappings.add(mapping);
            }
            
            return ResponseHelper.successful(deviceTypeMappings);
        } catch (Exception e) {
            log.error("获取设备标准化数据失败", e);
            return ResponseHelper.failed(e.getMessage());
        }
    }


    /**
     * 获取局站类型标准化数据
     */
    @GetMapping("/station-standard-data")
    public ResponseEntity<ResponseResult> getStationStandardData() {
        try {
            // 获取移动局站类型映射数据
            List<CmccStandardMappingDTO.StationTypeMapping> stationTypeMappings = new ArrayList<>();
            List<CmccStationType> stationTypes = cmccStationTypeService.list();
            
            for (CmccStationType stationType : stationTypes) {
                CmccStandardMappingDTO.StationTypeMapping mapping = new CmccStandardMappingDTO.StationTypeMapping();
                mapping.setStationTypeId(stationType.getStationTypeId());
                mapping.setStationTypeName(stationType.getStationTypeName());
                
                // 查询映射关系
                CmccStationTypeMap typeMap = cmccStationTypeMapService.getByStationTypeId(stationType.getStationTypeId());
                if (typeMap != null) {
                    mapping.setStationCategoryId(typeMap.getStationCategoryId());
                    mapping.setMappingId(typeMap.getId());
                }
                
                stationTypeMappings.add(mapping);
            }
            
            return ResponseHelper.successful(stationTypeMappings);
        } catch (Exception e) {
            log.error("获取局站标准化数据失败", e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 保存设备类型映射
     */
    @PostMapping("/save-device-mapping")
    public ResponseEntity<Map<String, Object>> saveDeviceMapping(@RequestBody List<CmccStandardMappingDTO.DeviceTypeMapping> mappings) {
        try {
            List<CmccDeviceTypeMap> mapEntities = new ArrayList<>();
            
            for (CmccStandardMappingDTO.DeviceTypeMapping mapping : mappings) {
                CmccDeviceTypeMap entity = new CmccDeviceTypeMap();
                entity.setId(mapping.getMappingId());
                entity.setDeviceTypeId(String.valueOf(mapping.getDeviceTypeId()));
                entity.setDeviceSubTypeId(String.valueOf(mapping.getDeviceSubTypeId()));
                entity.setEquipmentCategoryId(mapping.getEquipmentCategoryId());
                mapEntities.add(entity);
            }
            
            boolean success = cmccDeviceTypeMapService.batchUpdateMapping(mapEntities);
            if (success) {
                return ResponseUtil.success("保存设备类型映射成功");
            } else {
                return ResponseUtil.error("保存设备类型映射失败");
            }
        } catch (Exception e) {
            log.error("保存设备类型映射失败", e);
            return ResponseUtil.error("保存设备类型映射失败: " + e.getMessage());
        }
    }

    /**
     * 保存局站类型映射
     */
    @PostMapping("/save-station-mapping")
    public ResponseEntity<Map<String, Object>> saveStationMapping(@RequestBody List<CmccStandardMappingDTO.StationTypeMapping> mappings) {
        try {
            List<CmccStationTypeMap> mapEntities = new ArrayList<>();
            
            for (CmccStandardMappingDTO.StationTypeMapping mapping : mappings) {
                CmccStationTypeMap entity = new CmccStationTypeMap();
                entity.setId(mapping.getMappingId());
                entity.setStationTypeId(mapping.getStationTypeId());
                entity.setStationCategoryId(mapping.getStationCategoryId());
                mapEntities.add(entity);
            }
            
            boolean success = cmccStationTypeMapService.batchUpdateMapping(mapEntities);
            if (success) {
                return ResponseUtil.success("保存局站类型映射成功");
            } else {
                return ResponseUtil.error("保存局站类型映射失败");
            }
        } catch (Exception e) {
            log.error("保存局站类型映射失败", e);
            return ResponseUtil.error("保存局站类型映射失败: " + e.getMessage());
        }
    }

    /**
     * 持久化移动设备类型数据
     */
    @PostMapping("/insert-cmcc-device-types")
    public ResponseEntity<Map<String, Object>> insertCmccDeviceTypes(@RequestBody List<Map<String, Object>> deviceTypes) {
        try {
            List<CmccDeviceType> entities = new ArrayList<>();
            for (Map<String, Object> deviceType : deviceTypes) {
                CmccDeviceType entity = new CmccDeviceType();
                entity.setDeviceTypeId((Integer) deviceType.get("deviceTypeId"));
                entity.setDeviceTypeName((String) deviceType.get("deviceTypeName"));
                entity.setDeviceSubTypeId((Integer) deviceType.get("deviceSubTypeId"));
                entity.setDeviceSubTypeName((String) deviceType.get("deviceSubTypeName"));
                entities.add(entity);
            }
            
            // 清空并批量插入
            boolean success = cmccDeviceTypeService.clearAndBatchInsert(entities);
            if (success) {
                return ResponseUtil.success("保存移动设备类型成功");
            } else {
                return ResponseUtil.error("保存移动设备类型失败");
            }
        } catch (Exception e) {
            log.error("持久化移动设备类型失败", e);
            return ResponseUtil.error("持久化移动设备类型失败: " + e.getMessage());
        }
    }

    /**
     * 持久化移动局站类型数据
     */
    @PostMapping("/insert-cmcc-station-types")
    public ResponseEntity<Map<String, Object>> insertCmccStationTypes(@RequestBody List<Map<String, Object>> stationTypes) {
        try {
            List<CmccStationType> entities = new ArrayList<>();
            for (Map<String, Object> stationType : stationTypes) {
                CmccStationType entity = new CmccStationType();
                entity.setStationTypeId((Integer) stationType.get("stationTypeId"));
                entity.setStationTypeName((String) stationType.get("stationTypeName"));
                entities.add(entity);
            }
            
            // 清空并批量插入
            boolean success = cmccStationTypeService.clearAndBatchInsert(entities);
            if (success) {
                return ResponseUtil.success("保存移动局站类型成功");
            } else {
                return ResponseUtil.error("保存移动局站类型失败");
            }
        } catch (Exception e) {
            log.error("持久化移动局站类型失败", e);
            return ResponseUtil.error("持久化移动局站类型失败: " + e.getMessage());
        }
    }

    /**
     * 保存单个设备类型映射（一对一）
     */
    @PostMapping("/save-device-mapping-one-to-one")
    public ResponseEntity<Map<String, Object>> saveDeviceMappingOneToOne(@RequestBody Map<String, Object> request) {
        try {
            Integer deviceTypeId = (Integer) request.get("deviceTypeId");
            Integer deviceSubTypeId = (Integer) request.get("deviceSubTypeId");
            Integer sitewebEquipmentCategoryId = (Integer) request.get("sitewebEquipmentCategoryId");
            
            // 首先删除原有的映射关系
            CmccDeviceTypeMap existingMap = cmccDeviceTypeMapService.getByDeviceTypeAndSubType(
                String.valueOf(deviceTypeId), String.valueOf(deviceSubTypeId));
            if (existingMap != null) {
                cmccDeviceTypeMapService.removeById(existingMap.getId());
            }
            
            // 创建新的映射关系
            CmccDeviceTypeMap newMap = new CmccDeviceTypeMap();
            newMap.setDeviceTypeId(String.valueOf(deviceTypeId));
            newMap.setDeviceSubTypeId(String.valueOf(deviceSubTypeId));
            newMap.setEquipmentCategoryId(sitewebEquipmentCategoryId);
            
            boolean success = cmccDeviceTypeMapService.save(newMap);
            if (success) {
                // 更新设备模板表中的设备类型字段
                cmccStandardMappingService.updateDeviceTemplateCategory(sitewebEquipmentCategoryId, 
                    existingMap != null ? existingMap.getEquipmentCategoryId() : null);
                return ResponseUtil.success("保存映射成功");
            } else {
                return ResponseUtil.error("保存映射失败");
            }
        } catch (Exception e) {
            log.error("保存设备类型映射失败", e);
            return ResponseUtil.error("保存设备类型映射失败: " + e.getMessage());
        }
    }

    /**
     * 自动创建siteweb设备类型并建立映射
     */
    @PostMapping("/auto-create-and-map")
    public ResponseEntity<Map<String, Object>> autoCreateAndMapDeviceType(@RequestBody Map<String, Object> request) {
        try {
            Integer deviceTypeId = (Integer) request.get("deviceTypeId");
            Integer deviceSubTypeId = (Integer) request.get("deviceSubTypeId");
            String deviceTypeName = (String) request.get("deviceTypeName");
            String deviceSubTypeName = (String) request.get("deviceSubTypeName");
            
            boolean success = cmccStandardMappingService.autoCreateAndMapDeviceType(
                deviceTypeId, deviceSubTypeId, deviceTypeName, deviceSubTypeName);
            
            if (success) {
                return ResponseUtil.success("自动创建并映射成功");
            } else {
                return ResponseUtil.error("自动创建并映射失败");
            }
        } catch (Exception e) {
            log.error("自动创建并映射设备类型失败", e);
            return ResponseUtil.error("自动创建并映射设备类型失败: " + e.getMessage());
        }
    }

    /**
     * 批量自动创建未映射的设备类型
     */
    @PostMapping("/batch-auto-create-unmapped")
    public ResponseEntity<Map<String, Object>> batchAutoCreateUnmappedDevices() {
        try {
            int successCount = cmccStandardMappingService.batchAutoCreateUnmappedDevices();
            return ResponseUtil.success("批量自动创建成功，共处理 " + successCount + " 个设备类型");
        } catch (Exception e) {
            log.error("批量自动创建未映射设备类型失败", e);
            return ResponseUtil.error("批量自动创建失败: " + e.getMessage());
        }
    }

    /**
     * 获取未映射的CMCC设备类型
     */
    @GetMapping("/unmapped-cmcc-device-types")
    public ResponseEntity<ResponseResult> getUnmappedCmccDeviceTypes() {
        try {
            List<CmccDeviceType> allDeviceTypes = cmccDeviceTypeService.list();
            List<CmccDeviceTypeMap> allMappings = cmccDeviceTypeMapService.list();
            
            // 将已映射的设备类型ID收集到Set中
            List<String> mappedDeviceKeys = allMappings.stream()
                .map(map -> map.getDeviceTypeId() + "_" + map.getDeviceSubTypeId())
                .collect(Collectors.toList());
            
            // 过滤出未映射的设备类型
            List<CmccDeviceType> unmappedDevices = allDeviceTypes.stream()
                .filter(device -> {
                    String deviceKey = device.getDeviceTypeId() + "_" + device.getDeviceSubTypeId();
                    return !mappedDeviceKeys.contains(deviceKey);
                })
                .collect(Collectors.toList());
            
            List<CmccStandardMappingDTO.DeviceTypeMapping> result = new ArrayList<>();
            for (CmccDeviceType device : unmappedDevices) {
                CmccStandardMappingDTO.DeviceTypeMapping mapping = new CmccStandardMappingDTO.DeviceTypeMapping();
                mapping.setDeviceTypeId(device.getDeviceTypeId());
                mapping.setDeviceTypeName(device.getDeviceTypeName());
                mapping.setDeviceSubTypeId(device.getDeviceSubTypeId());
                mapping.setDeviceSubTypeName(device.getDeviceSubTypeName());
                result.add(mapping);
            }
            
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("获取未映射CMCC设备类型失败", e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 获取孤立的siteweb设备类型（自动创建但无映射的）
     */
    @GetMapping("/orphaned-siteweb-types")
    public ResponseEntity<ResponseResult> getOrphanedSitewebTypes() {
        try {
            // 获取所有SiteWeb设备类型
            List<DataItem> allCategories = sitewebPersistentService.getConfigAPI()
                .findByEntryIdForDataItem(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());
            
            // 获取所有映射关系
            List<CmccDeviceTypeMap> allMappings = cmccDeviceTypeMapService.list();
            List<Integer> mappedCategoryIds = allMappings.stream()
                .map(CmccDeviceTypeMap::getEquipmentCategoryId)
                .collect(Collectors.toList());
            
            // 找到自动创建但无映射的类型
            List<DataItem> orphanedTypes = allCategories.stream()
                .filter(category -> {
                    boolean isAutoCreated = !category.getIsSystem();
                    boolean isMapped = mappedCategoryIds.contains(category.getItemId());
                    return isAutoCreated && !isMapped;
                })
                .collect(Collectors.toList());
            
            return ResponseHelper.successful(orphanedTypes);
        } catch (Exception e) {
            log.error("获取孤立SiteWeb设备类型失败", e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 清理孤立的siteweb设备类型
     */
    @PostMapping("/cleanup-orphaned-siteweb-types")
    public ResponseEntity<Map<String, Object>> cleanupOrphanedSitewebTypes(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> sitewebEquipmentCategoryIds = (List<Integer>) request.get("sitewebEquipmentCategoryIds");
            
            int successCount = 0;
            for (Integer categoryId : sitewebEquipmentCategoryIds) {
                try {
                    // 删除SiteWeb设备类型
                    boolean deleted = cmccStandardMappingService.deleteSitewebEquipmentCategory(categoryId);
                    if (deleted) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.warn("删除SiteWeb设备类型失败: " + categoryId, e);
                }
            }
            
            return ResponseUtil.success("清理完成，共删除 " + successCount + " 个设备类型");
        } catch (Exception e) {
            log.error("清理孤立SiteWeb设备类型失败", e);
            return ResponseUtil.error("清理失败: " + e.getMessage());
        }
    }


}

