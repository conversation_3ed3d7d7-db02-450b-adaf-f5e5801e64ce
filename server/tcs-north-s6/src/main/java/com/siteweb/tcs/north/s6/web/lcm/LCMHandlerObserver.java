package com.siteweb.tcs.north.s6.web.lcm;

import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tcs2
 * @description: 生命周期处理器观察者
 * @author: xsx
 * @create: 2025-08-19 15:05
 **/
@Component
public class LCMHandlerObserver {
    private final Map<String,LCMHandler> lcmHandlerMap = new HashMap<>();

    public void registerHandler(String type,LCMHandler lcmHandler){
        lcmHandlerMap.put(type,lcmHandler);
    }

    public void handleEvent(String pluginId, GatewayConfigChangeDto gatewayConfigChangeDto){
        if (lcmHandlerMap.containsKey(pluginId)) {
            lcmHandlerMap.get(pluginId).handleConfigChange(gatewayConfigChangeDto);
        }else{
            //未识别
        }
    }
}
