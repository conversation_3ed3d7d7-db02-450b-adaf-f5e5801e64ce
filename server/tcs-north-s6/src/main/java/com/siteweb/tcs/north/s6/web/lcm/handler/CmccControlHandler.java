package com.siteweb.tcs.north.s6.web.lcm.handler;

import com.siteweb.tcs.hub.dal.dto.ControlConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.north.s6.web.lcm.handler.cmcc.CmccBaseHandler;
import com.siteweb.tcs.siteweb.entity.Equipment;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * CMCC控制处理器
 * 负责处理控制相关的创建、更新、删除逻辑
 */
@Component
public class CmccControlHandler extends CmccBaseHandler {

    /**
     * 处理控制信息
     */
    public void handleControl(DeviceConfigChangeDto deviceConfigChangeDto, Equipment equipment) {
        List<ControlConfigChangeDto> controls = deviceConfigChangeDto.getControls();
        
        for (ControlConfigChangeDto control : controls) {
            switch (control.getLifeCycleEvent()) {
                case CREATE:
                    createControl(control, equipment);
                    break;
                case DELETE:
                    deleteControl(control, equipment);
                    break;
                case UPDATE:
                    updateControl(control, equipment);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 创建控制
     */
    private void createControl(ControlConfigChangeDto control, Equipment equipment) {
        // TODO: 实现创建控制逻辑
    }

    /**
     * 删除控制
     */
    private void deleteControl(ControlConfigChangeDto control, Equipment equipment) {
        // TODO: 实现删除控制逻辑
    }

    /**
     * 更新控制
     */
    private void updateControl(ControlConfigChangeDto control, Equipment equipment) {
        // TODO: 实现更新控制逻辑
    }
}
