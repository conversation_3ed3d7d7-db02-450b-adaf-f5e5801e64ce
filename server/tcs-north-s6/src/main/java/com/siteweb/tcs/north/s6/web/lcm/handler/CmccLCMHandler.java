package com.siteweb.tcs.north.s6.web.lcm.handler;

import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.north.s6.web.lcm.LCMHandler;
import com.siteweb.tcs.north.s6.web.lcm.LCMHandlerObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-08-19 15:07
 **/
@Component
public class CmccLCMHandler implements LCMHandler<Boolean> {

    private static final String pluginId = "south-cmcc-plugin";

    @Autowired
    private LCMHandlerObserver lcmHandlerObserver;

    @PostConstruct
    private void registerHandler(){
        lcmHandlerObserver.registerHandler(pluginId,this);
    }

    @Override
    public Boolean handleConfigChange(GatewayConfigChangeDto gatewayConfigChangeDto) {
        return null;
    }
}
