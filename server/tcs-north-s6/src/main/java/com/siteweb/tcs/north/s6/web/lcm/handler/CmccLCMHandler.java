package com.siteweb.tcs.north.s6.web.lcm.handler;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.hub.dal.dto.*;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccRoomInfoDTO;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccSiteInfoDTO;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceTypeMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccRoomMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationTypeMap;
import com.siteweb.tcs.north.s6.web.lcm.LCMHandler;
import com.siteweb.tcs.north.s6.web.lcm.LCMHandlerObserver;
import com.siteweb.tcs.north.s6.web.service.IDeviceMapService;
import com.siteweb.tcs.north.s6.web.service.IGatewayMapService;
import com.siteweb.tcs.north.s6.web.service.ISignalMapService;
import com.siteweb.tcs.north.s6.web.service.cmcc.*;
import com.siteweb.tcs.north.s6.web.service.impl.SiteWebDefaultProvider;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.provider.StationProvider;
import com.siteweb.tcs.siteweb.service.IHouseService;
import com.siteweb.tcs.siteweb.service.IPortService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-08-19 15:07
 **/
@Component
public class CmccLCMHandler implements LCMHandler<Boolean> {

    private static final String pluginId = "south-cmcc-plugin";
    
    private static final String portNameTemplate = "com%s";

    @Autowired
    private LCMHandlerObserver lcmHandlerObserver;

    @Autowired
    private CmccConfigParser cmccConfigParser;

    @Autowired
    private ICmccSiteMapService cmccSiteMapService;

    @Autowired
    private ICmccDeviceTypeMapService cmccDeviceTypeMapService;

    @Autowired
    private ICmccStationTypeMapService cmccStationTypeMapService;

    @Autowired
    private ICmccStationTypeService cmccStationTypeService;

    @Autowired
    private ICmccStandardMappingService cmccStandardMappingService;

    @Autowired
    private SiteWebDefaultProvider siteWebDefaultProvider;

    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    private SitewebPersistentService sitewebPersistentService;

    @Autowired
    private IGatewayMapService gatewayMapService;

    @Autowired
    private IDeviceMapService deviceMapService;

    @Autowired
    private ICmccRoomMapService cmccRoomMapService;

    @Autowired
    private ICmccDeviceTypeService cmccDeviceTypeService;

    @Autowired
    private ISignalMapService signalMapService;



    @PostConstruct
    private void registerHandler(){
        lcmHandlerObserver.registerHandler(pluginId,this);
    }

    @Override
    public Boolean handleConfigChange(GatewayConfigChangeDto gatewayConfigChangeDto) {
        /**
         * 1.站点类型
         * 2.站点逻辑
         * 3.机房逻辑
         * 4.FSU CRUD
         * 5.设备类型
         * 6.设备CRUD
         * 7.信号CRUD
         */
        //局站
        Map<String, CmccSiteMap> cmccSiteMapMap = new HashMap<>();
        handleSiteInfo(gatewayConfigChangeDto,cmccSiteMapMap);
        //fsu
        Integer monitorUnitId = handleFSUInfo(gatewayConfigChangeDto, cmccSiteMapMap);
        //设备模板
        handleDeviceInfo(gatewayConfigChangeDto,cmccSiteMapMap,monitorUnitId);
        return null;
    }

    private Integer handleFSUInfo(GatewayConfigChangeDto gatewayConfigChangeDto, Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteInfoDTO siteInfoDTO = cmccConfigParser.parseSiteInfo(gatewayConfigChangeDto);
        switch (gatewayConfigChangeDto.getLifeCycleEvent()){
            case CREATE :
                CmccSiteMap cmccSiteMap = cmccSiteMapMap.get(siteInfoDTO.getSiteId());
                CreateMonitorUnitDTO createMonitorUnitDTO = cmccConfigParser.parseFSUInfo(gatewayConfigChangeDto);
                MonitorUnitDTO monitorUnit = createMonitorUnitDTO.toMonitorUnit(cmccSiteMap.getStationId());
                sitewebPersistentService.getConfigAPI().createV3ForMonitorUnit(monitorUnit);
                //落库
                GatewayMap gatewayMap = new GatewayMap();
                gatewayMap.setGatewayId(gatewayConfigChangeDto.getId());
                gatewayMap.setNorthMonitorUnitId(monitorUnit.getMonitorUnitId());
                gatewayMapService.saveOrUpdate(gatewayMap);
                return monitorUnit.getMonitorUnitId();
            break;
            case DELETE:
                break;
            case UPDATE:
                break;
            default:
                break;
        }
        return null;
    }

    private void handleDeviceInfo(GatewayConfigChangeDto gatewayConfigChangeDto,Map<String, CmccSiteMap> cmccSiteMapMap, Integer monitorUnitId) {
        Map<String, CmccRoomMap> cmccRoomMapMap = new HashMap<>();
        //站点
        List<DeviceConfigChangeDto> configChangeDtoList = gatewayConfigChangeDto.getDevices();
        Equipment equipment = null;
        for (DeviceConfigChangeDto deviceConfigChangeDto : configChangeDtoList) {
            switch (deviceConfigChangeDto.getLifeCycleEvent()){
                case CREATE:
                    //局站
                    CmccSiteInfoDTO cmccSiteInfoDTO = cmccConfigParser.parseSiteInfo(deviceConfigChangeDto);
                    handleSiteInfo(cmccSiteInfoDTO,cmccSiteMapMap);
                    CmccSiteMap cmccSiteMap = cmccSiteMapMap.get(cmccSiteInfoDTO.getSiteId());
                    //机房
                    CmccRoomInfoDTO cmccRoomInfoDTO = cmccConfigParser.parseRoomInfo(deviceConfigChangeDto);
                    handleRoomInfo(cmccRoomInfoDTO,cmccRoomMapMap,cmccSiteMap);
                    CmccRoomMap cmccRoomMap = cmccRoomMapMap.get(cmccRoomInfoDTO.getRoomId());
                    //端口
                    Port port = createPortInfo(deviceConfigChangeDto, monitorUnitId);
                    //采集单元
                    SamplerUnit samplerUnit = createSamplerUnit(deviceConfigChangeDto,monitorUnitId,port);
                    //设备类型
                    Integer equipmentCategoryId = handleDeviceType(deviceConfigChangeDto);
                    //设备 模板
                    equipment = createEquipment(deviceConfigChangeDto,cmccSiteMap,cmccRoomMap,monitorUnitId,port,samplerUnit,equipmentCategoryId);
                    //存映射表
                    DeviceMap deviceMap = new DeviceMap();
                    deviceMap.setDeviceId(deviceConfigChangeDto.getId());
                    deviceMap.setGatewayId(gatewayConfigChangeDto.getId());
                    deviceMap.setNorthMonitorUnitId(monitorUnitId);
                    deviceMap.setNorthEquipmentTemplateId(equipment.getEquipmentTemplateId());
                    deviceMap.setNorthEquipmentId(equipment.getEquipmentId());
                    deviceMapService.save(deviceMap);
                    //todo 还有ext表
                    break;
                case DELETE:
                    break;
                case UPDATE:
                    break;
                default:
                    break;
            }
            //信号
            handleSignal(deviceConfigChangeDto,equipment);
            //告警
            handleAlarm(deviceConfigChangeDto,equipment);
            //控制
            handleControl(deviceConfigChangeDto,equipment);
        }
    }

    private void handleSignal(DeviceConfigChangeDto deviceConfigChangeDto, Equipment equipment) {
        List<SignalConfigChangeDto> signals = deviceConfigChangeDto.getSignals();
        for (SignalConfigChangeDto signal : signals) {
            switch (signal.getLifeCycleEvent()){
                case CREATE:
                    SignalConfigItem signalConfigItem = cmccConfigParser.parseSignalConfigItem(signal,equipment);
                    Signal sitewebSignal = sitewebPersistentService.getConfigAPI().createForSignal(signalConfigItem);
                    signalMapService.save()
                    break;
                case DELETE:
                    break;
                case UPDATE:
                    break;
                default:
                    break;
            }
        }
    }

    private void handleAlarm(DeviceConfigChangeDto deviceConfigChangeDto, Equipment equipment) {
        List<AlarmConfigChangeDto> alarms = deviceConfigChangeDto.getAlarms();
        for (AlarmConfigChangeDto alarm : alarms) {
            switch (alarm.getLifeCycleEvent()){
                case CREATE:
                    break;
                case DELETE:
                    break;
                case UPDATE:
                    break;
                default:
                    break;
            }
        }
    }

    private void handleControl(DeviceConfigChangeDto deviceConfigChangeDto, Equipment equipment) {
        List<ControlConfigChangeDto> controls = deviceConfigChangeDto.getControls();
        for (ControlConfigChangeDto control : controls) {
            switch (control.getLifeCycleEvent()){
                case CREATE:
                    break;
                case DELETE:
                    break;
                case UPDATE:
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 创建端口，只有在创建设备需要
     * @param deviceConfigChangeDto
     * @param monitorUnitId
     */
    private Port createPortInfo(DeviceConfigChangeDto deviceConfigChangeDto, Integer monitorUnitId) {
        Integer maxPortByMonitorUnitId = sitewebPersistentService.getConfigAPI().getMaxPortByMonitorUnitId(monitorUnitId);
        Integer portNo = maxPortByMonitorUnitId + 1;
        String portName = String.format(portNameTemplate, portNo);
        Port port = new Port();
        port.setMonitorUnitId(monitorUnitId);
        port.setPortName(portName);
        port.setPortNo(portNo);
        port.setDescription("TCS create");
        port.setLinkSamplerUnitId(0);
        //虚拟端口
        port.setPortType(34);
        port.setSetting("comm_host_dev.so");
        sitewebPersistentService.getConfigAPI().createForPort(port);
        return port;
    }


    /**
     * 创建采集单元
     * @param deviceConfigChangeDto
     * @param monitorUnitId
     * @param port
     * @return
     */
    private SamplerUnit createSamplerUnit(DeviceConfigChangeDto deviceConfigChangeDto, Integer monitorUnitId, Port port) {
        SamplerUnit samplerUnit = new SamplerUnit();
        samplerUnit.setMonitorUnitId(monitorUnitId);
        samplerUnit.setPortId(port.getPortId());
        samplerUnit.setParentSamplerUnitId(0);
        samplerUnit.setSamplerType(Short.valueOf("18"));
        samplerUnit.setAddress(1);
        samplerUnit.setSpUnitInterval(2.0);
        samplerUnit.setDllPath("IDUHOST.so");
        samplerUnit.setConnectState(0);
        samplerUnit.setDescription("TCS create");
        samplerUnit.setSamplerUnitName(deviceConfigChangeDto.getSouthDeviceName());
        Integer samplerId = siteWebDefaultProvider.getDefaultSamplerIdByProtocolCode();
        samplerUnit.setSamplerId(samplerId);
        return sitewebPersistentService.getConfigAPI().createForSamplerUnit(samplerUnit);
    }

    /**
     * 处理设备类型
     * @param deviceConfigChangeDto
     * @return
     */
    private Integer handleDeviceType(DeviceConfigChangeDto deviceConfigChangeDto) {
        JsonNode metadata = deviceConfigChangeDto.getMetadata();
        String deviceType = metadata.get("deviceType").asText().replaceFirst("^0+(?!$)", "");;
        String deviceSubType = metadata.get("deviceSubType").asText().replaceFirst("^0+(?!$)", "");;
        String deviceSubTypeName = metadata.get("deviceSubTypeName").asText();
        CmccDeviceTypeMap cmccDeviceTypeMap = cmccDeviceTypeMapService.getByDeviceTypeAndSubType(deviceType, deviceSubType);
        if(ObjectUtil.isNotEmpty(cmccDeviceTypeMap)) return cmccDeviceTypeMap.getEquipmentCategoryId();
        cmccDeviceTypeService.deleteByDeviceTypeAndDeviceSubType(deviceType,deviceSubType);
        Integer sitewebEquipmentCategoryId = cmccStandardMappingService.createSitewebEquipmentCategory(deviceSubTypeName);
        cmccDeviceTypeMapService.saveMapping(deviceType, deviceSubType, sitewebEquipmentCategoryId);
        return sitewebEquipmentCategoryId;
    }

    /**
     * 创建设备
     * @param deviceConfigChangeDto
     * @param cmccSiteMap
     * @param cmccRoomMap
     * @param monitorUnitId
     * @param port
     * @param samplerUnit
     * @param equipmentCategoryId
     * @return
     */
    private Equipment createEquipment(DeviceConfigChangeDto deviceConfigChangeDto, CmccSiteMap cmccSiteMap, CmccRoomMap cmccRoomMap, Integer monitorUnitId, Port port, SamplerUnit samplerUnit, Integer equipmentCategoryId) {
        CreateEquipmentDto createEquipmentDto = cmccConfigParser.parseDeviceInfo(deviceConfigChangeDto);
        createEquipmentDto.setStationId(cmccSiteMap.getStationId());
        createEquipmentDto.setHouseId(cmccRoomMap.getHouseId());
        createEquipmentDto.setInstantiated(true);
        createEquipmentDto.setMonitorUnitId(monitorUnitId);
        createEquipmentDto.setSamplerUnitId(samplerUnit.getSamplerUnitId());
        return sitewebPersistentService.getConfigAPI().createForEquipment(createEquipmentDto);
    }


    /**
     * 处理机房
     * @param cmccRoomInfoDTO 机房信息
     * @param cmccRoomMapMap 机房映射表
     * @param cmccSiteMap 站点信息，机房挂到哪个站点下
     */
    private void handleRoomInfo(CmccRoomInfoDTO cmccRoomInfoDTO, Map<String, CmccRoomMap> cmccRoomMapMap,CmccSiteMap cmccSiteMap) {
        // 已存在，直接返回
        String cmccSiteId = cmccSiteMap.getCmccSiteId();
        if (cmccRoomMapMap.containsKey(cmccRoomInfoDTO.getUniqueKey())) {
            return;
        }
        // 从数据库查询
        CmccRoomMap cmccRoomMap = cmccRoomMapService.getByCmccSiteIdAndRoomId(cmccSiteId, cmccRoomInfoDTO.getRoomId());
        // 判断是更新还是新建
        boolean needUpdate = ObjectUtil.isNotEmpty(cmccRoomMapMap) &&
                (!StringUtils.equals(cmccRoomMap.getCmccRoomName(), cmccRoomInfoDTO.getRoomName())
                        || !NumberUtil.equals(cmccRoomMap.getCmccRoomType(), cmccRoomInfoDTO.getRoomType()));
        House house = buildRoomDTO(cmccRoomMap, cmccRoomInfoDTO, needUpdate);
        cmccRoomMapMap.put(cmccRoomInfoDTO.getUniqueKey(),cmccRoomMap);
        // TODO: 根据业务需要保存/更新 stationDTO，调用siteweb持久化层
        if(needUpdate){
            sitewebPersistentService.getConfigAPI().updateForHouse(house);
        }else {
            cmccRoomMap = cmccRoomInfoDTO.toCmccRoomMap();
            cmccRoomMap.setStationId(cmccSiteMap.getStationId());
            sitewebPersistentService.getConfigAPI().createForHouse(house);
            cmccRoomMap.setHouseId(house.getHouseId());
            cmccRoomMapService.save(cmccRoomMap);
        }
    }

    private House buildRoomDTO(CmccRoomMap cmccRoomMap, CmccRoomInfoDTO cmccRoomInfoDTO, boolean needUpdate) {
        House house = new House();
        if (needUpdate) {
            house.setStationId(cmccRoomMap.getStationId());
            house.setHouseId(cmccRoomMap.getHouseId());
        }
        house.setHouseName(cmccRoomInfoDTO.getRoomName());
        return house;
    }

    /**
     * 处理局站
     * @param gatewayConfigChangeDto
     * @param cmccSiteMapMap
     */
    private void handleSiteInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                                Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteInfoDTO siteInfoDTO = cmccConfigParser.parseSiteInfo(gatewayConfigChangeDto);
        handleSiteInfo(siteInfoDTO,cmccSiteMapMap);
    }

    /**
     * 处理局站
     * @param siteInfoDTO
     * @param cmccSiteMapMap
     */
    private void handleSiteInfo(CmccSiteInfoDTO siteInfoDTO,
                                Map<String, CmccSiteMap> cmccSiteMapMap) {
        // 已存在，直接返回
        if (cmccSiteMapMap.containsKey(siteInfoDTO.getSiteId())) {
            return;
        }
        // 从数据库查询
        CmccSiteMap cmccSiteMap = cmccSiteMapService.getBySiteId(siteInfoDTO.getSiteId());
        // 判断是更新还是新建
        boolean needUpdate = ObjectUtil.isNotEmpty(cmccSiteMap) &&
                (!StringUtils.equals(cmccSiteMap.getCmccSiteName(), siteInfoDTO.getSiteName())
                        || !NumberUtil.equals(cmccSiteMap.getCmccSiteType(), siteInfoDTO.getSiteType()));
        StationDTO stationDTO = buildStationDTO(cmccSiteMap, siteInfoDTO, needUpdate);
        cmccSiteMapMap.put(cmccSiteMap.getCmccSiteId(),cmccSiteMap);
        // TODO: 根据业务需要保存/更新 stationDTO，调用siteweb持久化层
        if(needUpdate){
            sitewebPersistentService.getConfigAPI().updateStationDTO(stationDTO);
        }else {
            cmccSiteMap = new CmccSiteMap();
            StationStructure defaultStationStructure = siteWebDefaultProvider.getDefaultStationStructure();
            stationDTO.setStationStructureId(defaultStationStructure.getStructureId());
            sitewebPersistentService.getConfigAPI().createStation(stationDTO);
            cmccSiteMap.setCmccSiteId(siteInfoDTO.getSiteId());
            cmccSiteMap.setCmccSiteName(siteInfoDTO.getSiteName());
            cmccSiteMap.setCmccSiteType(siteInfoDTO.getSiteType());
            cmccSiteMap.setStationId(stationDTO.getStationId());
            cmccSiteMap.setStationCategoryId(stationDTO.getStationCategory());
            cmccSiteMapService.save(cmccSiteMap);
        }
    }

    /**
     * 构建 StationDTO（新建或更新）
     */
    private StationDTO buildStationDTO(CmccSiteMap cmccSiteMap, CmccSiteInfoDTO siteInfoDTO, boolean update) {
        StationDTO stationDTO = new StationDTO();
        if (update) {
            stationDTO.setStationId(cmccSiteMap.getStationId());
        }
        stationDTO.setStationName(siteInfoDTO.getSiteName());
        stationDTO.setStationCategory(resolveStationCategory(siteInfoDTO));
        return stationDTO;
    }

    /**
     * 处理局站类型映射逻辑
     */
    private Integer resolveStationCategory(CmccSiteInfoDTO siteInfoDTO) {
        CmccStationTypeMap stationTypeMap = cmccStationTypeMapService.getByStationTypeId(siteInfoDTO.getSiteType());
        if (ObjectUtil.isNotEmpty(stationTypeMap)) {
            return stationTypeMap.getStationCategoryId();
        }
        // 删除并创建 cmcc 局站类型
        cmccStationTypeService.clearAndAdd(siteInfoDTO);
        // 创建 siteweb 局站类型
        Integer siteWebCategoryId = cmccStandardMappingService.createSitewebStationCategory(siteInfoDTO.getSiteTypeName());
        // 落库 cmcc 与 siteweb 的局站类型映射
        cmccStationTypeMapService.saveStationTypeMap(siteInfoDTO.getSiteType(), siteWebCategoryId);
        return siteWebCategoryId;
    }
}
