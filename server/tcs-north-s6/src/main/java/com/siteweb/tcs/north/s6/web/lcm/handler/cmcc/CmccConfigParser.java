package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.hub.dal.dto.*;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccRoomInfoDTO;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccSiteInfoDTO;
import com.siteweb.tcs.siteweb.dto.*;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: tcs2
 * @description: 移动配置解析器
 * @author: xsx
 * @create: 2025-08-20 10:08
 **/
@Component
public class CmccConfigParser {

    public CmccSiteInfoDTO parseSiteInfo(GatewayConfigChangeDto configChangeDto) {
        CmccSiteInfoDTO siteInfo = getSiteInfo(configChangeDto.getMetadata());
        return siteInfo;
    }

    public CmccSiteInfoDTO parseSiteInfo(DeviceConfigChangeDto configChangeDto) {
        CmccSiteInfoDTO siteInfo = getSiteInfo(configChangeDto.getMetadata());
        return siteInfo;
    }

    public CmccRoomInfoDTO parseRoomInfo(DeviceConfigChangeDto configChangeDto){
        JsonNode metadata = configChangeDto.getMetadata();
        String roomId = metadata.get("roomId").asText();
        String roomName = metadata.get("roomName").asText();
        int roomType = metadata.get("roomType").asInt();
        String siteId = metadata.get("SiteId").asText();
        CmccRoomInfoDTO roomInfo = new CmccRoomInfoDTO();
        roomInfo.setRoomId(roomId);
        roomInfo.setRoomName(roomName);
        roomInfo.setRoomType(roomType);
        roomInfo.setSiteId(siteId);
        return roomInfo;
    }

    public CreateMonitorUnitDTO parseFSUInfo(GatewayConfigChangeDto gatewayConfigChangeDto){
        CreateMonitorUnitDTO createMonitorUnitDTO = new CreateMonitorUnitDTO();
        createMonitorUnitDTO.setFsu(true);
        createMonitorUnitDTO.setMonitorUnitCategory(10);
        createMonitorUnitDTO.setIpAddress(gatewayConfigChangeDto.getSouthAddress());
        createMonitorUnitDTO.setMonitorUnitName(gatewayConfigChangeDto.getSouthGatewayName());
        return createMonitorUnitDTO;
    }

    public CreateEquipmentDto parseDeviceInfo(DeviceConfigChangeDto configChangeDto){
        CreateEquipmentDto createEquipmentDto = new CreateEquipmentDto();
        createEquipmentDto.setEquipmentName(createEquipmentDto.getEquipmentName());
        return createEquipmentDto;
    }
//
//    public CmccDeviceType parseDeviceType(DeviceConfigChangeDto configChangeDto){
//
//    }
//
//    public CmccDeviceExt parseDeviceExt(DeviceConfigChangeDto configChangeDto){
//
//    }
//
    public SignalConfigItem parseSignalConfigItem(SignalConfigChangeDto signalConfigChangeDto, Equipment equipment){
        SignalConfigItem signalConfigItem = new SignalConfigItem();
        signalConfigItem.setSignalName(signalConfigChangeDto.getSouthSignalName());
        signalConfigItem.setUnit(signalConfigChangeDto.getSouthSignalUnit());
        signalConfigItem.setEquipmentTemplateId(equipment.getEquipmentTemplateId());
        List<SignalMeanings> signalMeaningsList = new ArrayList<>();
        signalConfigChangeDto.getSouthSignalMeanings().forEach((k,v) ->{
            SignalMeanings signalMeanings = new SignalMeanings();
            signalMeanings.setStateValue(k);
            signalMeanings.setMeanings(v);
            signalMeanings.setEquipmentTemplateId(equipment.getEquipmentTemplateId());
            signalMeaningsList.add(signalMeanings);
        });
        signalConfigItem.setSignalMeaningsList(signalMeaningsList);
        signalConfigItem.setSignalType(signalConfigChangeDto.getSignalType());
        signalConfigItem.setEnable(true);
        signalConfigItem.setVisible(true);
        return signalConfigItem;
    }
//
//    public EventConfigItem parseEventConfigItem(AlarmConfigChangeDto alarmConfigChangeDto){
//
//    }
//
//    public ControlConfigItem parseControlConfigItem(ControlConfigChangeDto controlConfigChangeDto){
//
//    }

    private CmccSiteInfoDTO getSiteInfo(JsonNode metadata) {
        String siteId = metadata.get("SiteId").asText();
        String siteName = metadata.get("SiteName").asText();
        Integer siteType = metadata.get("SiteType").asInt();
        String siteTypeName = metadata.get("siteTypeName").asText();
        CmccSiteInfoDTO siteInfo = new CmccSiteInfoDTO();
        siteInfo.setSiteId(siteId);
        siteInfo.setSiteName(siteName);
        siteInfo.setSiteType(siteType);
        siteInfo.setSiteTypeName(siteTypeName);
        return siteInfo;
    }
}
