package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccRoomInfoDTO;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccSiteInfoDTO;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceTypeMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccRoomMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import com.siteweb.tcs.north.s6.web.service.IDeviceMapService;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccDeviceTypeMapService;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccDeviceTypeService;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccStandardMappingService;
import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CMCC设备处理器
 * 负责处理设备相关的创建、更新、删除逻辑
 */
@Component
public class CmccDeviceHandler extends CmccBaseHandler {

    @Autowired
    private IDeviceMapService deviceMapService;

    @Autowired
    private ICmccDeviceTypeMapService cmccDeviceTypeMapService;

    @Autowired
    private ICmccDeviceTypeService cmccDeviceTypeService;

    @Autowired
    private ICmccStandardMappingService cmccStandardMappingService;

    @Autowired
    private CmccSiteHandler cmccSiteHandler;

    @Autowired
    private CmccRoomHandler cmccRoomHandler;

    /**
     * 处理设备信息
     */
    public void handleDeviceInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                                 Map<String, CmccSiteMap> cmccSiteMapMap,
                                 Integer monitorUnitId) {
        Map<String, CmccRoomMap> cmccRoomMapMap = new HashMap<>();
        List<DeviceConfigChangeDto> configChangeDtoList = gatewayConfigChangeDto.getDevices();
        Equipment equipment = null;

        for (DeviceConfigChangeDto deviceConfigChangeDto : configChangeDtoList) {
            switch (deviceConfigChangeDto.getLifeCycleEvent()) {
                case CREATE:
                    equipment = createDevice(gatewayConfigChangeDto, deviceConfigChangeDto, 
                                           cmccSiteMapMap, cmccRoomMapMap, monitorUnitId);
                    break;
                case DELETE:
                    deleteDevice(deviceConfigChangeDto);
                    break;
                case UPDATE:
                    equipment = updateDevice(deviceConfigChangeDto, cmccSiteMapMap, cmccRoomMapMap, monitorUnitId);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 创建设备
     */
    private Equipment createDevice(GatewayConfigChangeDto gatewayConfigChangeDto,
                                   DeviceConfigChangeDto deviceConfigChangeDto,
                                   Map<String, CmccSiteMap> cmccSiteMapMap,
                                   Map<String, CmccRoomMap> cmccRoomMapMap,
                                   Integer monitorUnitId) {
        // 处理站点
        CmccSiteInfoDTO cmccSiteInfoDTO = cmccConfigParser.parseSiteInfo(deviceConfigChangeDto);
        cmccSiteHandler.handleSiteInfo(cmccSiteInfoDTO, cmccSiteMapMap);
        CmccSiteMap cmccSiteMap = getSiteMap(cmccSiteInfoDTO.getSiteId(), cmccSiteMapMap);

        // 处理机房
        CmccRoomInfoDTO cmccRoomInfoDTO = cmccConfigParser.parseRoomInfo(deviceConfigChangeDto);
        cmccRoomHandler.handleRoomInfo(cmccRoomInfoDTO, cmccRoomMapMap, cmccSiteMap);
        CmccRoomMap cmccRoomMap = getRoomMap(cmccRoomInfoDTO.getUniqueKey(), cmccRoomMapMap);

        // 创建端口
        Port port = createPortInfo(deviceConfigChangeDto, monitorUnitId);

        // 创建采集单元
        SamplerUnit samplerUnit = createSamplerUnit(deviceConfigChangeDto, monitorUnitId, port);

        // 处理设备类型
        Integer equipmentCategoryId = handleDeviceType(deviceConfigChangeDto);

        // 创建设备
        Equipment equipment = createEquipment(deviceConfigChangeDto, cmccSiteMap, cmccRoomMap, 
                                            monitorUnitId, port, samplerUnit, equipmentCategoryId);

        // 保存设备映射
        saveDeviceMap(gatewayConfigChangeDto, deviceConfigChangeDto, monitorUnitId, equipment);

        return equipment;
    }

    /**
     * 删除设备
     */
    private void deleteDevice(DeviceConfigChangeDto deviceConfigChangeDto) {
        // TODO: 实现删除逻辑
    }

    /**
     * 更新设备
     */
    private Equipment updateDevice(DeviceConfigChangeDto deviceConfigChangeDto,
                                   Map<String, CmccSiteMap> cmccSiteMapMap,
                                   Map<String, CmccRoomMap> cmccRoomMapMap,
                                   Integer monitorUnitId) {
        // TODO: 实现更新逻辑
        return null;
    }

    /**
     * 创建端口信息
     */
    private Port createPortInfo(DeviceConfigChangeDto deviceConfigChangeDto, Integer monitorUnitId) {
        Integer maxPortByMonitorUnitId = sitewebPersistentService.getConfigAPI().getMaxPortByMonitorUnitId(monitorUnitId);
        Integer portNo = maxPortByMonitorUnitId + 1;
        String portName = String.format(PORT_NAME_TEMPLATE, portNo);
        
        Port port = new Port();
        port.setMonitorUnitId(monitorUnitId);
        port.setPortName(portName);
        port.setPortNo(portNo);
        port.setDescription("TCS create");
        port.setLinkSamplerUnitId(0);
        port.setPortType(34); // 虚拟端口
        port.setSetting("comm_host_dev.so");
        
        sitewebPersistentService.getConfigAPI().createForPort(port);
        return port;
    }

    /**
     * 创建采集单元
     */
    private SamplerUnit createSamplerUnit(DeviceConfigChangeDto deviceConfigChangeDto, 
                                          Integer monitorUnitId, Port port) {
        SamplerUnit samplerUnit = new SamplerUnit();
        samplerUnit.setMonitorUnitId(monitorUnitId);
        samplerUnit.setPortId(port.getPortId());
        samplerUnit.setParentSamplerUnitId(0);
        samplerUnit.setSamplerType(Short.valueOf("18"));
        samplerUnit.setAddress(1);
        samplerUnit.setSpUnitInterval(2.0);
        samplerUnit.setDllPath("IDUHOST.so");
        samplerUnit.setConnectState(0);
        samplerUnit.setDescription("TCS create");
        samplerUnit.setSamplerUnitName(deviceConfigChangeDto.getSouthDeviceName());
        
        Integer samplerId = siteWebDefaultProvider.getDefaultSamplerIdByProtocolCode();
        samplerUnit.setSamplerId(samplerId);
        
        return sitewebPersistentService.getConfigAPI().createForSamplerUnit(samplerUnit);
    }

    /**
     * 处理设备类型
     */
    private Integer handleDeviceType(DeviceConfigChangeDto deviceConfigChangeDto) {
        JsonNode metadata = deviceConfigChangeDto.getMetadata();
        String deviceType = metadata.get("deviceType").asText().replaceFirst("^0+(?!$)", "");
        String deviceSubType = metadata.get("deviceSubType").asText().replaceFirst("^0+(?!$)", "");
        String deviceSubTypeName = metadata.get("deviceSubTypeName").asText();
        
        CmccDeviceTypeMap cmccDeviceTypeMap = cmccDeviceTypeMapService.getByDeviceTypeAndSubType(deviceType, deviceSubType);
        if (ObjectUtil.isNotEmpty(cmccDeviceTypeMap)) {
            return cmccDeviceTypeMap.getEquipmentCategoryId();
        }
        
        cmccDeviceTypeService.deleteByDeviceTypeAndDeviceSubType(deviceType, deviceSubType);
        Integer sitewebEquipmentCategoryId = cmccStandardMappingService.createSitewebEquipmentCategory(deviceSubTypeName);
        cmccDeviceTypeMapService.saveMapping(deviceType, deviceSubType, sitewebEquipmentCategoryId);
        
        return sitewebEquipmentCategoryId;
    }

    /**
     * 创建设备
     */
    private Equipment createEquipment(DeviceConfigChangeDto deviceConfigChangeDto,
                                      CmccSiteMap cmccSiteMap,
                                      CmccRoomMap cmccRoomMap,
                                      Integer monitorUnitId,
                                      Port port,
                                      SamplerUnit samplerUnit,
                                      Integer equipmentCategoryId) {
        CreateEquipmentDto createEquipmentDto = cmccConfigParser.parseDeviceInfo(deviceConfigChangeDto);
        createEquipmentDto.setStationId(cmccSiteMap.getStationId());
        createEquipmentDto.setHouseId(cmccRoomMap.getHouseId());
        createEquipmentDto.setInstantiated(true);
        createEquipmentDto.setMonitorUnitId(monitorUnitId);
        createEquipmentDto.setSamplerUnitId(samplerUnit.getSamplerUnitId());
        
        return sitewebPersistentService.getConfigAPI().createForEquipment(createEquipmentDto);
    }

    /**
     * 保存设备映射
     */
    private void saveDeviceMap(GatewayConfigChangeDto gatewayConfigChangeDto,
                               DeviceConfigChangeDto deviceConfigChangeDto,
                               Integer monitorUnitId,
                               Equipment equipment) {
        DeviceMap deviceMap = new DeviceMap();
        deviceMap.setDeviceId(deviceConfigChangeDto.getId());
        deviceMap.setGatewayId(gatewayConfigChangeDto.getId());
        deviceMap.setNorthMonitorUnitId(monitorUnitId);
        deviceMap.setNorthEquipmentTemplateId(equipment.getEquipmentTemplateId());
        deviceMap.setNorthEquipmentId(equipment.getEquipmentId());
        
        deviceMapService.save(deviceMap);
    }
}
