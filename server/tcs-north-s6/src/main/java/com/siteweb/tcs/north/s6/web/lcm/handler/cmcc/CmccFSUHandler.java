package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.north.s6.dal.dto.cmcc.CmccSiteInfoDTO;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import com.siteweb.tcs.north.s6.web.service.IGatewayMapService;
import com.siteweb.tcs.siteweb.dto.CreateMonitorUnitDTO;
import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * CMCC FSU处理器
 * 负责处理FSU相关的创建、更新、删除逻辑
 */
@Component
public class CmccFSUHandler extends CmccBaseHandler {

    @Autowired
    private IGatewayMapService gatewayMapService;

    /**
     * 处理FSU信息
     */
    public Integer handleFSUInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                                 Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteInfoDTO siteInfoDTO = cmccConfigParser.parseSiteInfo(gatewayConfigChangeDto);
        
        switch (gatewayConfigChangeDto.getLifeCycleEvent()) {
            case CREATE:
                return createFSU(gatewayConfigChangeDto, siteInfoDTO, cmccSiteMapMap);
            case DELETE:
                return deleteFSU(gatewayConfigChangeDto);
            case UPDATE:
                return updateFSU(gatewayConfigChangeDto, siteInfoDTO, cmccSiteMapMap);
            default:
                return null;
        }
    }

    /**
     * 创建FSU
     */
    private Integer createFSU(GatewayConfigChangeDto gatewayConfigChangeDto,
                              CmccSiteInfoDTO siteInfoDTO,
                              Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteMap cmccSiteMap = getSiteMap(siteInfoDTO.getSiteId(), cmccSiteMapMap);
        if (cmccSiteMap == null) {
            throw new IllegalStateException("站点映射不存在: " + siteInfoDTO.getSiteId());
        }

        CreateMonitorUnitDTO createMonitorUnitDTO = cmccConfigParser.parseFSUInfo(gatewayConfigChangeDto);
        MonitorUnitDTO monitorUnit = createMonitorUnitDTO.toMonitorUnit(cmccSiteMap.getStationId());
        
        sitewebPersistentService.getConfigAPI().createV3ForMonitorUnit(monitorUnit);

        // 保存网关映射
        saveGatewayMap(gatewayConfigChangeDto.getId(), monitorUnit.getMonitorUnitId());

        return monitorUnit.getMonitorUnitId();
    }

    /**
     * 删除FSU
     */
    private Integer deleteFSU(GatewayConfigChangeDto gatewayConfigChangeDto) {
        // TODO: 实现删除逻辑
        return null;
    }

    /**
     * 更新FSU
     */
    private Integer updateFSU(GatewayConfigChangeDto gatewayConfigChangeDto,
                              CmccSiteInfoDTO siteInfoDTO,
                              Map<String, CmccSiteMap> cmccSiteMapMap) {
        // TODO: 实现更新逻辑
        return null;
    }

    /**
     * 保存网关映射
     */
    private void saveGatewayMap(Long gatewayId, Integer monitorUnitId) {
        GatewayMap gatewayMap = new GatewayMap();
        gatewayMap.setGatewayId(gatewayId);
        gatewayMap.setNorthMonitorUnitId(monitorUnitId);
        gatewayMapService.saveOrUpdate(gatewayMap);
    }
}
