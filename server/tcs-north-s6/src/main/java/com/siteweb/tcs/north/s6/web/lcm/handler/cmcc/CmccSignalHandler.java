package com.siteweb.tcs.north.s6.web.lcm.handler.cmcc;

import com.siteweb.tcs.hub.dal.dto.DeviceConfigChangeDto;
import com.siteweb.tcs.hub.dal.dto.SignalConfigChangeDto;
import com.siteweb.tcs.north.s6.dal.entity.DeviceMap;
import com.siteweb.tcs.north.s6.dal.entity.SignalMap;
import com.siteweb.tcs.north.s6.web.service.ISignalMapService;
import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.Signal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * CMCC信号处理器
 * 负责处理信号相关的创建、更新、删除逻辑
 */
@Component
public class CmccSignalHandler extends CmccBaseHandler {

    @Autowired
    private ISignalMapService signalMapService;

    /**
     * 处理信号信息
     */
    public void handleSignal(DeviceConfigChangeDto deviceConfigChangeDto, DeviceMap deviceMap) {
        List<SignalConfigChangeDto> signals = deviceConfigChangeDto.getSignals();
        
        for (SignalConfigChangeDto signal : signals) {
            switch (signal.getLifeCycleEvent()) {
                case CREATE:
                    createSignal(signal, deviceMap);
                    break;
                case DELETE:
                    deleteSignal(signal, deviceMap);
                    break;
                case UPDATE:
                    updateSignal(signal, deviceMap);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 创建信号
     */
    private void createSignal(SignalConfigChangeDto signal, DeviceMap deviceMap) {
        SignalConfigItem signalConfigItem = cmccConfigParser.parseSignalConfigItem(signal, deviceMap);
        Signal sitewebSignal = sitewebPersistentService.getConfigAPI().createForSignal(signalConfigItem);
        SignalMap signalMap = new SignalMap();
        signalMap.setSignalId(signal.getId());
        signalMap.setDeviceId(signal.getDeviceId());
        signalMap.setNorthEquipmentId(deviceMap.getNorthEquipmentId());
        signalMap.setNorthSignalId(sitewebSignal.getSignalId());
         signalMapService.save(signalMap);
    }

    /**
     * 删除信号
     */
    private void deleteSignal(SignalConfigChangeDto signal, DeviceMap deviceMap) {
        // TODO: 实现删除逻辑
    }

    /**
     * 更新信号
     */
    private void updateSignal(SignalConfigChangeDto signal, DeviceMap deviceMap) {
        // TODO: 实现更新逻辑
    }
}
