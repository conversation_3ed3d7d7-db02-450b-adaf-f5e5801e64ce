package com.siteweb.tcs.north.s6.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.CmccDeviceTypeMap;

import java.util.List;

/**
 * 移动设备类型映射表 Service 接口
 */
public interface ICmccDeviceTypeMapService extends IService<CmccDeviceTypeMap> {

    /**
     * 根据设备类型ID和子类型ID查询映射
     * @param deviceTypeId 设备大类ID
     * @param deviceSubTypeId 设备子类ID
     * @return 映射记录
     */
    CmccDeviceTypeMap getByDeviceTypeAndSubType(String deviceTypeId, String deviceSubTypeId);

    /**
     * 批量更新映射关系
     * @param mappingList 映射关系列表
     * @return 是否成功
     */
    boolean batchUpdateMapping(List<CmccDeviceTypeMap> mappingList);
}

