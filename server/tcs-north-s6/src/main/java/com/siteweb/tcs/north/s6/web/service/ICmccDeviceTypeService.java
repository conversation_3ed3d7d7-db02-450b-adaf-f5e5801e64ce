package com.siteweb.tcs.north.s6.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.CmccDeviceType;

import java.util.List;

/**
 * 移动设备种类表 Service 接口
 */
public interface ICmccDeviceTypeService {

    /**
     * 查询所有设备大类（去重）
     * @return 设备大类列表
     */
    List<CmccDeviceType> listDistinctDeviceTypes();

    /**
     * 根据设备大类ID查询设备子类
     * @param deviceTypeId 设备大类ID
     * @return 设备子类列表
     */
    List<CmccDeviceType> listSubTypesByDeviceTypeId(Integer deviceTypeId);

    /**
     * 清空表数据并批量插入新数据
     * @param deviceTypeList 设备类型列表
     * @return 是否成功
     */
    boolean clearAndBatchInsert(List<CmccDeviceType> deviceTypeList);
}

