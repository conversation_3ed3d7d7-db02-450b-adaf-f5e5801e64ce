package com.siteweb.tcs.north.s6.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.CmccStationType;

import java.util.List;

/**
 * 移动标准化局站类型表 Service 接口
 */
public interface ICmccStationTypeService extends IService<CmccStationType> {

    /**
     * 清空表数据并批量插入新数据
     * @param stationTypeList 局站类型列表
     * @return 是否成功
     */
    boolean clearAndBatchInsert(List<CmccStationType> stationTypeList);
}

