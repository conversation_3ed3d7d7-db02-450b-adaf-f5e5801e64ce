package com.siteweb.tcs.north.s6.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.GatewayMap;

import java.util.List;

/**
 * 网关映射服务接口
 */
public interface IGatewayMapService extends IService<GatewayMap> {

    /**
     * 根据网关ID查询网关映射
     * @param gatewayId 网关ID
     * @return 网关映射列表
     */
    List<GatewayMap> getByGatewayId(Long gatewayId);

    /**
     * 根据北向监控单元ID查询网关映射
     * @param northMonitorUnitId 北向监控单元ID
     * @return 网关映射列表
     */
    List<GatewayMap> getByNorthMonitorUnitId(Integer northMonitorUnitId);

    /**
     * 批量保存网关映射
     * @param gatewayMaps 网关映射列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<GatewayMap> gatewayMaps);

    /**
     * 根据复合主键删除网关映射
     * @param gatewayId 网关ID
     * @param northMonitorUnitId 北向监控单元ID
     * @return 是否删除成功
     */
    boolean deleteByCompositeKey(Long gatewayId, Integer northMonitorUnitId);

    /**
     * 查询所有活跃的网关映射
     * @return 网关映射列表
     */
    List<GatewayMap> getAllActive();

    /**
     * 创建或更新网关映射
     * @param gatewayMap 网关映射
     * @return 是否操作成功
     */
    boolean saveOrUpdate(GatewayMap gatewayMap);
} 