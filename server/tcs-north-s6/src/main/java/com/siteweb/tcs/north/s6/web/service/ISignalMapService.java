package com.siteweb.tcs.north.s6.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.SignalMap;

import java.util.List;

/**
 * 信号映射服务接口
 */
public interface ISignalMapService extends IService<SignalMap> {
    
    /**
     * 根据设备ID查询
     * @param deviceId 设备ID
     * @return 列表
     */
    List<SignalMap> getByDeviceId(Long deviceId);
    
    /**
     * 批量保存
     * @param signalMaps 列表
     * @return boolean
     */
    boolean saveBatch(List<SignalMap> signalMaps);
    
    /**
     * 根据复合主键删除
     * @param deviceId 设备ID
     * @param signalId 信号ID
     * @param northSignalId 北向信号ID
     * @return boolean
     */
    boolean deleteByCompositeKey(Long deviceId, Long signalId, Integer northSignalId);
} 