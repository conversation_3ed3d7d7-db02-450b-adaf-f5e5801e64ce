package com.siteweb.tcs.north.s6.web.service.cmcc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceExt;

import java.util.List;

/**
 * CMCC设备拓展表 服务接口
 */
public interface ICmccDeviceExtService extends IService<CmccDeviceExt> {

    /**
     * 根据设备GUID查询设备拓展信息
     * @param deviceGuid 设备GUID
     * @return 设备拓展信息
     */
    CmccDeviceExt getByDeviceGuid(Long deviceGuid);

    /**
     * 根据设备类型和子类型查询设备拓展信息
     * @param deviceTypeId 设备大类ID
     * @param deviceSubTypeId 设备子类ID
     * @return 设备拓展信息列表
     */
    List<CmccDeviceExt> getByDeviceTypeAndSubType(Integer deviceTypeId, Integer deviceSubTypeId);

    /**
     * 批量保存设备拓展信息
     * @param deviceExtList 设备拓展信息列表
     * @return 是否成功
     */
    boolean batchSave(List<CmccDeviceExt> deviceExtList);

    /**
     * 清空并批量插入设备拓展信息
     * @param deviceExtList 设备拓展信息列表
     * @return 是否成功
     */
    boolean clearAndBatchInsert(List<CmccDeviceExt> deviceExtList);
}
