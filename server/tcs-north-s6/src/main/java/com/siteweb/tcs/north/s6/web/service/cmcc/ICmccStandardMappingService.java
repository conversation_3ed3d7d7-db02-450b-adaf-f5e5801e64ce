package com.siteweb.tcs.north.s6.web.service.cmcc;

/**
 * CMCC标准化映射服务接口
 */
public interface ICmccStandardMappingService {

    /**
     * 自动创建SiteWeb设备类型并建立映射
     * @param deviceTypeId 设备大类ID
     * @param deviceSubTypeId 设备子类ID
     * @param deviceTypeName 设备大类名称
     * @param deviceSubTypeName 设备子类名称
     * @return 是否成功
     */
    boolean autoCreateAndMapDeviceType(Integer deviceTypeId, Integer deviceSubTypeId, 
                                     String deviceTypeName, String deviceSubTypeName);

    /**
     * 批量自动创建未映射的设备类型
     * @return 成功处理的设备类型数量
     */
    int batchAutoCreateUnmappedDevices();

    /**
     * 创建SiteWeb设备类型
     * @param categoryName 设备类型名称
     * @return 创建的设备类型ID，失败返回null
     */
    Integer createSitewebEquipmentCategory(String categoryName);

    /**
     * 删除SiteWeb设备类型
     * @param categoryId 设备类型ID
     * @return 是否成功
     */
    boolean deleteSitewebEquipmentCategory(Integer categoryId);

    /**
     * 更新设备模板表中的设备类型字段
     * @param newCategoryId 新的设备类型ID
     * @param oldCategoryId 旧的设备类型ID
     */
    void updateDeviceTemplateCategory(Integer newCategoryId, Integer oldCategoryId);
}
