package com.siteweb.tcs.north.s6.web.service.cmcc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationTypeMap;

import java.util.List;

/**
 * 移动标准化局站类型与Siteweb局站类型映射表 Service 接口
 */
public interface ICmccStationTypeMapService extends IService<CmccStationTypeMap> {

    /**
     * 根据局站类型ID查询映射
     * @param stationTypeId 局站类型ID
     * @return 映射记录
     */
    CmccStationTypeMap getByStationTypeId(Integer stationTypeId);

    /**
     * 批量更新映射关系
     * @param mappingList 映射关系列表
     * @return 是否成功
     */
    boolean batchUpdateMapping(List<CmccStationTypeMap> mappingList);
}

