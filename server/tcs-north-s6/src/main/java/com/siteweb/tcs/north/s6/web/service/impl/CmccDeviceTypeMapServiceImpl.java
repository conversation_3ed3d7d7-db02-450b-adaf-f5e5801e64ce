package com.siteweb.tcs.north.s6.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.CmccDeviceTypeMap;
import com.siteweb.tcs.north.s6.dal.mapper.CmccDeviceTypeMapMapper;
import com.siteweb.tcs.north.s6.web.service.ICmccDeviceTypeMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动设备类型映射表 Service 实现类
 */
@Service
public class CmccDeviceTypeMapServiceImpl extends ServiceImpl<CmccDeviceTypeMapMapper, CmccDeviceTypeMap> implements ICmccDeviceTypeMapService {

    @Resource
    private CmccDeviceTypeMapMapper cmccDeviceTypeMapMapper;

    @Override
    public CmccDeviceTypeMap getByDeviceTypeAndSubType(String deviceTypeId, String deviceSubTypeId) {
        return cmccDeviceTypeMapMapper.selectByDeviceTypeAndSubType(deviceTypeId, deviceSubTypeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateMapping(List<CmccDeviceTypeMap> mappingList) {
        if (mappingList == null || mappingList.isEmpty()) {
            return true;
        }
        
        for (CmccDeviceTypeMap mapping : mappingList) {
            if (mapping.getId() != null) {
                // 更新现有记录
                this.updateById(mapping);
            } else {
                // 插入新记录
                this.save(mapping);
            }
        }
        return true;
    }
}

