package com.siteweb.tcs.north.s6.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.CmccStationType;
import com.siteweb.tcs.north.s6.dal.mapper.CmccStationTypeMapper;
import com.siteweb.tcs.north.s6.web.service.ICmccStationTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动标准化局站类型表 Service 实现类
 */
@Service
public class CmccStationTypeServiceImpl extends ServiceImpl<CmccStationTypeMapper, CmccStationType> implements ICmccStationTypeService {

    @Resource
    private CmccStationTypeMapper cmccStationTypeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAndBatchInsert(List<CmccStationType> stationTypeList) {
        // 清空表数据
        this.remove(null);
        
        // 批量插入新数据
        if (stationTypeList != null && !stationTypeList.isEmpty()) {
            return this.saveBatch(stationTypeList);
        }
        return true;
    }
}

