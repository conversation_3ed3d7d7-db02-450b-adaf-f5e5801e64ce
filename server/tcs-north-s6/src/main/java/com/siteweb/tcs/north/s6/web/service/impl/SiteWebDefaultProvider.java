package com.siteweb.tcs.north.s6.web.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import com.siteweb.tcs.siteweb.service.ISamplerService;
import com.siteweb.tcs.siteweb.service.IStationStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: tcs2
 * @description: siteweb默认参数提供类
 * @author: xsx
 * @create: 2025-08-21 09:34
 **/
@Service
public class SiteWebDefaultProvider {
    private static final String DEFAULT_STATION_STRUCTURE_NAME = "TCS默认层级";

    private static final String DEFAULT_STATION_STRUCTURE_FLAG = "TCS Create";

    private StationStructure tcsStationStructure;

    private static Integer bInterfaceSamplerId;

    private static final String bInterfaceSamplerProtocolCode = "BInterface-HOST设备6-00";

    @Autowired
    private IStationStructureService stationStructureService;

    @Autowired
    private ISamplerService samplerService;



    public StationStructure getDefaultStationStructure() {
        // 如果已有缓存，直接返回
        if (ObjectUtil.isNotEmpty(tcsStationStructure)) {
            return tcsStationStructure;
        }

        // 从服务中获取默认结构
        StationStructure defaultStructure = stationStructureService.getTCSStationStructure();
        if (ObjectUtil.isNotEmpty(defaultStructure)) {
            return tcsStationStructure = defaultStructure;
        }

        // 获取根结构
        StationStructure rootStructure = stationStructureService.getRootStationStructure();
        if (ObjectUtil.isEmpty(rootStructure)) {
            return null;
        }

        // 创建新结构
        StationStructure newStructure = new StationStructure();
        newStructure.setParentStructureId(rootStructure.getStructureId());
        newStructure.setStructureName(DEFAULT_STATION_STRUCTURE_NAME);
        newStructure.setDescription(DEFAULT_STATION_STRUCTURE_FLAG);
        newStructure.setEnable(true);

        stationStructureService.create(newStructure);

        return tcsStationStructure = newStructure;
    }

    public Integer getDefaultSamplerIdByProtocolCode(){
        if(ObjectUtil.isEmpty(bInterfaceSamplerId)){
            Sampler tslSampler = samplerService.getSamplerByProtocolCode(bInterfaceSamplerProtocolCode);
            bInterfaceSamplerId = tslSampler.getSamplerId();
            return bInterfaceSamplerId;
        }else {
            return bInterfaceSamplerId;
        }
    }

}
