package com.siteweb.tcs.north.s6.web.service.impl.cmcc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceExt;
import com.siteweb.tcs.north.s6.dal.mapper.cmcc.CmccDeviceExtMapper;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccDeviceExtService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * CMCC设备拓展表 服务实现类
 */
@Service
public class CmccDeviceExtServiceImpl extends ServiceImpl<CmccDeviceExtMapper, CmccDeviceExt> implements ICmccDeviceExtService {

    @Override
    public CmccDeviceExt getByDeviceGuid(Long deviceGuid) {
        return this.getById(deviceGuid);
    }

    @Override
    public List<CmccDeviceExt> getByDeviceTypeAndSubType(Integer deviceTypeId, Integer deviceSubTypeId) {
        QueryWrapper<CmccDeviceExt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DeviceTypeID", deviceTypeId)
                   .eq("DeviceSubTypeID", deviceSubTypeId);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<CmccDeviceExt> deviceExtList) {
        return this.saveBatch(deviceExtList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAndBatchInsert(List<CmccDeviceExt> deviceExtList) {
        // 清空表
        this.remove(null);
        // 批量插入
        return this.saveBatch(deviceExtList);
    }
}
