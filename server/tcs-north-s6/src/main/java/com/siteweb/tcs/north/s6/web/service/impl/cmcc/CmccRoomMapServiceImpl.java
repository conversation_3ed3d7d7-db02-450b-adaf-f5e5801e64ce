package com.siteweb.tcs.north.s6.web.service.impl.cmcc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccRoomMap;
import com.siteweb.tcs.north.s6.dal.mapper.cmcc.CmccRoomMapMapper;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccRoomMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * CMCC机房映射表 服务实现类
 */
@Service
public class CmccRoomMapServiceImpl extends ServiceImpl<CmccRoomMapMapper, CmccRoomMap> implements ICmccRoomMapService {

    @Override
    public CmccRoomMap getByCmccRoomId(String cmccRoomId) {
        return this.getById(cmccRoomId);
    }

    @Override
    public List<CmccRoomMap> getByCmccSiteId(String cmccSiteId) {
        QueryWrapper<CmccRoomMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("CmccSiteID", cmccSiteId);
        return this.list(queryWrapper);
    }

    @Override
    public List<CmccRoomMap> getByStationId(Integer stationId) {
        QueryWrapper<CmccRoomMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("StationID", stationId);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<CmccRoomMap> roomMapList) {
        return this.saveBatch(roomMapList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAndBatchInsert(List<CmccRoomMap> roomMapList) {
        // 清空表
        this.remove(null);
        // 批量插入
        return this.saveBatch(roomMapList);
    }
}
