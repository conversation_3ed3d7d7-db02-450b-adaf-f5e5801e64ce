package com.siteweb.tcs.north.s6.web.service.impl.cmcc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccSiteMap;
import com.siteweb.tcs.north.s6.dal.mapper.cmcc.CmccSiteMapMapper;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccSiteMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * CMCC站点映射表 服务实现类
 */
@Service
public class CmccSiteMapServiceImpl extends ServiceImpl<CmccSiteMapMapper, CmccSiteMap> implements ICmccSiteMapService {

    @Override
    public CmccSiteMap getByCmccSiteId(String cmccSiteId) {
        return this.getById(cmccSiteId);
    }

    @Override
    public List<CmccSiteMap> getByStationId(Integer stationId) {
        QueryWrapper<CmccSiteMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("StationID", stationId);
        return this.list(queryWrapper);
    }

    @Override
    public List<CmccSiteMap> getByCmccSiteType(Integer cmccSiteType) {
        QueryWrapper<CmccSiteMap> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("CmccSiteType", cmccSiteType);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<CmccSiteMap> siteMapList) {
        return this.saveBatch(siteMapList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearAndBatchInsert(List<CmccSiteMap> siteMapList) {
        // 清空表
        this.remove(null);
        // 批量插入
        return this.saveBatch(siteMapList);
    }
}
