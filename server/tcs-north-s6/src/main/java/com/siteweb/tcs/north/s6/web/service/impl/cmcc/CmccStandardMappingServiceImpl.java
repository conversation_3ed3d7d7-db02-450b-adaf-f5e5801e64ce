package com.siteweb.tcs.north.s6.web.service.impl.cmcc;

import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceType;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceTypeMap;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccDeviceTypeMapService;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccDeviceTypeService;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccStandardMappingService;
import com.siteweb.tcs.siteweb.dto.DataItemCreateDTO;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * CMCC标准化映射服务实现类
 */
@Slf4j
@Service
public class CmccStandardMappingServiceImpl implements ICmccStandardMappingService {

    @Autowired
    private ICmccDeviceTypeService cmccDeviceTypeService;

    @Autowired
    private ICmccDeviceTypeMapService cmccDeviceTypeMapService;

    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    private SitewebPersistentService sitewebPersistentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoCreateAndMapDeviceType(Integer deviceTypeId, Integer deviceSubTypeId, 
                                            String deviceTypeName, String deviceSubTypeName) {
        try {
            // 生成SiteWeb设备类型名称
            String sitewebCategoryName = deviceTypeName + "-" + deviceSubTypeName + "(CMCC自动)";
            
            // 创建SiteWeb设备类型
            Integer newCategoryId = createSitewebEquipmentCategory(sitewebCategoryName);
            if (newCategoryId == null) {
                log.error("创建SiteWeb设备类型失败: {}", sitewebCategoryName);
                return false;
            }
            
            // 创建映射关系
            CmccDeviceTypeMap newMap = new CmccDeviceTypeMap();
            newMap.setDeviceTypeId(String.valueOf(deviceTypeId));
            newMap.setDeviceSubTypeId(String.valueOf(deviceSubTypeId));
            newMap.setEquipmentCategoryId(newCategoryId);
            
            boolean success = cmccDeviceTypeMapService.save(newMap);
            if (success) {
                log.info("自动创建并映射成功: {} - {} -> {}", deviceTypeName, deviceSubTypeName, sitewebCategoryName);
                return true;
            } else {
                log.error("创建映射关系失败: {} - {}", deviceTypeName, deviceSubTypeName);
                return false;
            }
        } catch (Exception e) {
            log.error("自动创建并映射设备类型失败: {} - {}", deviceTypeName, deviceSubTypeName, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAutoCreateUnmappedDevices() {
        try {
            // 获取所有未映射的CMCC设备类型
            List<CmccDeviceType> unmappedDevices = getUnmappedDeviceTypes();
            
            int successCount = 0;
            for (CmccDeviceType device : unmappedDevices) {
                try {
                    boolean success = autoCreateAndMapDeviceType(
                        device.getDeviceTypeId(),
                        device.getDeviceSubTypeId(),
                        device.getDeviceTypeName(),
                        device.getDeviceSubTypeName()
                    );
                    if (success) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.warn("批量创建设备类型失败: {} - {}", 
                        device.getDeviceTypeName(), device.getDeviceSubTypeName(), e);
                }
            }
            
            log.info("批量自动创建完成，共处理 {} 个设备类型，成功 {} 个", unmappedDevices.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量自动创建未映射设备类型失败", e);
            return 0;
        }
    }

    @Override
    public Integer createSitewebEquipmentCategory(String categoryName) {
        try {
            log.info("创建SiteWeb设备类型: {}", categoryName);
            DataItemCreateDTO createDTO = new DataItemCreateDTO();
            createDTO.setEntryId(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());
            int itemId = sitewebPersistentService.getConfigAPI().getNextDataItemId(DataEntryEnum.EQUIPMENT_CATEGORY);
            createDTO.setItemId(itemId);
            createDTO.setItemValue(categoryName);
            createDTO.setIsSystem(false);
            sitewebPersistentService.getConfigAPI().createDataItemForDataItem(createDTO);
            log.info("SiteWeb设备类型创建成功: {} -> {}", categoryName, itemId);
            return itemId;
        } catch (Exception e) {
            log.error("创建SiteWeb设备类型失败: {}", categoryName, e);
            return null;
        }
    }

    @Override
    public boolean deleteSitewebEquipmentCategory(Integer categoryId) {
        try {
            log.info("删除SiteWeb设备类型: {}", categoryId);
            sitewebPersistentService.getConfigAPI().deleteByEntryIdAndItemId(
                DataEntryEnum.EQUIPMENT_CATEGORY.getValue(), categoryId);
            log.info("SiteWeb设备类型删除成功: {}", categoryId);
            return true;
        } catch (Exception e) {
            log.error("删除SiteWeb设备类型失败: {}", categoryId, e);
            return false;
        }
    }

    @Override
    public void updateDeviceTemplateCategory(Integer newCategoryId, Integer oldCategoryId) {
        try {
            // 这里需要更新设备模板表中的设备类型字段
            // TODO: 实现实际的设备模板表更新逻辑
            log.info("需要更新设备模板表: 从 {} 到 {}", oldCategoryId, newCategoryId);
        } catch (Exception e) {
            log.error("更新设备模板表失败", e);
        }
    }

    /**
     * 获取未映射的设备类型
     */
    private List<CmccDeviceType> getUnmappedDeviceTypes() {
        List<CmccDeviceType> allDeviceTypes = cmccDeviceTypeService.list();
        List<CmccDeviceTypeMap> allMappings = cmccDeviceTypeMapService.list();
        
        // 将已映射的设备类型ID收集到Set中
        List<String> mappedDeviceKeys = allMappings.stream()
            .map(map -> map.getDeviceTypeId() + "_" + map.getDeviceSubTypeId())
            .collect(Collectors.toList());
        
        // 过滤出未映射的设备类型
        return allDeviceTypes.stream()
            .filter(device -> {
                String deviceKey = device.getDeviceTypeId() + "_" + device.getDeviceSubTypeId();
                return !mappedDeviceKeys.contains(deviceKey);
            })
            .collect(Collectors.toList());
    }
}
