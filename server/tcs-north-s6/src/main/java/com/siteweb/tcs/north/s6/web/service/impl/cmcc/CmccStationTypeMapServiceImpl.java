package com.siteweb.tcs.north.s6.web.service.impl.cmcc;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationTypeMap;
import com.siteweb.tcs.north.s6.dal.mapper.cmcc.CmccStationTypeMapMapper;
import com.siteweb.tcs.north.s6.web.service.cmcc.ICmccStationTypeMapService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动标准化局站类型与Siteweb局站类型映射表 Service 实现类
 */
@Service
public class CmccStationTypeMapServiceImpl extends ServiceImpl<CmccStationTypeMapMapper, CmccStationTypeMap> implements ICmccStationTypeMapService {

    @Resource
    private CmccStationTypeMapMapper cmccStationTypeMapMapper;

    @Override
    public CmccStationTypeMap getByStationTypeId(Integer stationTypeId) {
        return cmccStationTypeMapMapper.selectByStationTypeId(stationTypeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateMapping(List<CmccStationTypeMap> mappingList) {
        if (mappingList == null || mappingList.isEmpty()) {
            return true;
        }
        
        for (CmccStationTypeMap mapping : mappingList) {
            if (mapping.getId() != null) {
                // 更新现有记录
                this.updateById(mapping);
            } else {
                // 插入新记录
                this.save(mapping);
            }
        }
        return true;
    }
}

