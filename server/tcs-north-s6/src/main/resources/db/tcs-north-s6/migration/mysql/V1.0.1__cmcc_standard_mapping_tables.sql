-- 移动设备种类表
CREATE TABLE `cmcc_device_type` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DeviceTypeID` int DEFAULT NULL,
  `DeviceTypeName` varchar(128) DEFAULT NULL,
  `DeviceSubTypeID` int DEFAULT NULL,
  `DeviceSubTypeName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='移动设备种类表';

-- 移动设备类型映射表
CREATE TABLE `cmcc_device_type_map` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DeviceTypeID` varchar(45) DEFAULT NULL COMMENT '设备大类id',
  `DeviceSubTypeID` varchar(45) DEFAULT NULL COMMENT '设备子类ID',
  `EquipmentCategoryID` varchar(45) DEFAULT NULL COMMENT 'SiteWeb设备种类id',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 移动标准化局站类型表
CREATE TABLE `cmcc_station_type` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `StationTypeID` int DEFAULT NULL COMMENT '局站类型',
  `StationTypeName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='移动标准化局站类型';

-- 移动标准化局站类型与Siteweb局站类型映射表
CREATE TABLE `cmcc_station_type_map` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `StationTypeID` int DEFAULT NULL,
  `StationCategoryID` int DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='移动标准化局站类型与Siteweb局站类型映射';
