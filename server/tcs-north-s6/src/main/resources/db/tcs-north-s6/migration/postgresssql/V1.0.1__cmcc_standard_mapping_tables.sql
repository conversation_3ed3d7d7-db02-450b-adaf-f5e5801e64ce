-- 移动设备种类表
CREATE TABLE cmcc_device_type (
  ID SERIAL PRIMARY KEY,
  DeviceTypeID INT DEFAULT NULL,
  DeviceTypeName VARCHAR(128) DEFAULT NULL,
  DeviceSubTypeID INT DEFAULT NULL,
  DeviceSubTypeName VARCHAR(128) DEFAULT NULL
);
COMMENT ON TABLE cmcc_device_type IS '移动设备种类表';

-- 移动设备类型映射表
CREATE TABLE cmcc_device_type_map (
  ID SERIAL PRIMARY KEY,
  DeviceTypeID VARCHAR(45) DEFAULT NULL,
  DeviceSubTypeID VARCHAR(45) DEFAULT NULL,
  EquipmentCategoryID VARCHAR(45) DEFAULT NULL
);
COMMENT ON TABLE cmcc_device_type_map IS '移动设备类型映射表';
COMMENT ON COLUMN cmcc_device_type_map.DeviceTypeID IS '设备大类id';
COMMENT ON COLUMN cmcc_device_type_map.DeviceSubTypeID IS '设备子类ID';
COMMENT ON COLUMN cmcc_device_type_map.EquipmentCategoryID IS 'SiteWeb设备种类id';

-- 移动标准化局站类型表
CREATE TABLE cmcc_station_type (
  ID SERIAL PRIMARY KEY,
  StationTypeID INT DEFAULT NULL,
  StationTypeName VARCHAR(128) DEFAULT NULL
);
COMMENT ON TABLE cmcc_station_type IS '移动标准化局站类型';
COMMENT ON COLUMN cmcc_station_type.StationTypeID IS '局站类型';

-- 移动标准化局站类型与Siteweb局站类型映射表
CREATE TABLE cmcc_station_type_map (
  ID SERIAL PRIMARY KEY,
  StationTypeID INT DEFAULT NULL,
  StationCategoryID INT DEFAULT NULL
);
COMMENT ON TABLE cmcc_station_type_map IS '移动标准化局站类型与Siteweb局站类型映射';
