# siteweb6 North Interface Plugin Internationalization - English
plugin.name=siteweb6 North Interface
plugin.description=North interface plugin for third-party system integration

# Common
common.success=Operation Successful
common.error=Operation Failed
common.save=Save
common.cancel=Cancel
common.confirm=Confirm
common.delete=Delete
common.edit=Edit
common.view=View
common.search=Search
common.reset=Reset
common.refresh=Refresh

# API Related
api.test.hello=Hello World - siteweb6 North Interface
api.test.success=Connection Test Successful
api.test.failed=Connection Test Failed
api.log.title=API Logs
api.log.list=API Call Logs
api.log.detail=Log Details
api.log.request.params=Request Parameters
api.log.response.data=Response Data
api.log.execution.time=Execution Time
api.log.api.path=API Path
api.log.request.method=Request Method
api.log.response.status=Response Status
api.log.create.time=Create Time

# Status
status.running=Running
status.stopped=Stopped
status.success=Success
status.failed=Failed

# Statistics
stats.total.requests=Total Requests
stats.success.requests=Success Requests
stats.error.requests=Error Requests
stats.avg.response.time=Average Response Time

# Error Messages
error.plugin.load.failed=Plugin Load Failed
error.database.connection.failed=Database Connection Failed
error.api.call.failed=API Call Failed
error.data.not.found=Data Not Found
error.parameter.invalid=Invalid Parameter 