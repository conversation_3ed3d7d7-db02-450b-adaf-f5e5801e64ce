<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.AlarmMapMapper">
    
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.north.s6.dal.entity.AlarmMap">
        <result column="DeviceId" property="deviceId" />
        <result column="NorthEquipmentId" property="northEquipmentId" />
        <result column="AlarmId" property="alarmId" />
        <result column="NorthEventId" property="northEventId" />
        <result column="AlarmMeaningId" property="alarmMeaningId" />
        <result column="NorthEventConditionId" property="northEventConditionId" />
        <result column="Deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        DeviceId, NorthEquipmentId, AlarmId, NorthEventId, AlarmMeaningId, NorthEventConditionId, Deleted
    </sql>

    <!-- 根据设备ID查询告警映射 -->
    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE DeviceId = #{deviceId}
        AND Deleted = FALSE
    </select>

    <!-- 根据北向设备ID查询告警映射 -->
    <select id="selectByNorthEquipmentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE NorthEquipmentId = #{northEquipmentId}
        AND Deleted = FALSE
    </select>

    <!-- 根据告警ID查询告警映射 -->
    <select id="selectByAlarmId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE AlarmId = #{alarmId}
        AND Deleted = FALSE
    </select>

    <!-- 根据北向事件ID查询告警映射 -->
    <select id="selectByNorthEventId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE NorthEventId = #{northEventId}
        AND Deleted = FALSE
    </select>

    <!-- 批量插入告警映射 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tcs_alarm_map (
            DeviceId, NorthEquipmentId, AlarmId, NorthEventId, 
            AlarmMeaningId, NorthEventConditionId, Deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceId}, #{item.northEquipmentId}, #{item.alarmId}, #{item.northEventId}, 
             #{item.alarmMeaningId}, #{item.northEventConditionId}, #{item.deleted})
        </foreach>
    </insert>

    <!-- 根据复合主键删除告警映射 -->
    <update id="deleteByCompositeKey">
        UPDATE tcs_alarm_map 
        SET Deleted = TRUE
        WHERE DeviceId = #{deviceId}
        AND NorthEquipmentId = #{northEquipmentId}
        AND AlarmId = #{alarmId}
        AND NorthEventId = #{northEventId}
        AND NorthEventConditionId = #{northEventConditionId}
    </update>

    <!-- 插入单个告警映射 -->
    <insert id="insert" parameterType="com.siteweb.tcs.north.s6.dal.entity.AlarmMap">
        INSERT INTO tcs_alarm_map (
            DeviceId, NorthEquipmentId, AlarmId, NorthEventId, 
            AlarmMeaningId, NorthEventConditionId, Deleted
        ) VALUES (
            #{deviceId}, #{northEquipmentId}, #{alarmId}, #{northEventId}, 
            #{alarmMeaningId}, #{northEventConditionId}, #{deleted}
        )
    </insert>

    <!-- 更新告警映射 -->
    <update id="updateById" parameterType="com.siteweb.tcs.north.s6.dal.entity.AlarmMap">
        UPDATE tcs_alarm_map
        <set>
            <if test="alarmMeaningId != null">AlarmMeaningId = #{alarmMeaningId},</if>
            <if test="deleted != null">Deleted = #{deleted}</if>
        </set>
        WHERE DeviceId = #{deviceId}
        AND NorthEquipmentId = #{northEquipmentId}
        AND AlarmId = #{alarmId}
        AND NorthEventId = #{northEventId}
        AND NorthEventConditionId = #{northEventConditionId}
    </update>

    <!-- 查询所有活跃的告警映射 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_alarm_map
        WHERE Deleted = FALSE
        ORDER BY DeviceId, NorthEquipmentId, AlarmId
    </select>

</mapper> 