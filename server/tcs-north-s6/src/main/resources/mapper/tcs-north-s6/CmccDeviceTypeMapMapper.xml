<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.CmccDeviceTypeMapMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.north.s6.dal.entity.CmccDeviceTypeMap">
        <id column="ID" property="id" />
        <result column="DeviceTypeID" property="deviceTypeId" />
        <result column="DeviceSubTypeID" property="deviceSubTypeId" />
        <result column="EquipmentCategoryID" property="equipmentCategoryId" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, DeviceTypeID, DeviceSubTypeID, EquipmentCategoryID
    </sql>

    <!-- 根据设备类型ID和子类型ID查询映射 -->
    <select id="selectByDeviceTypeAndSubType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_device_type_map
        WHERE DeviceTypeID = #{deviceTypeId}
        AND DeviceSubTypeID = #{deviceSubTypeId}
        LIMIT 1
    </select>

</mapper>

