<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.cmcc.CmccDeviceTypeMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccDeviceType">
        <id column="ID" property="id" />
        <result column="DeviceTypeID" property="deviceTypeId" />
        <result column="DeviceTypeName" property="deviceTypeName" />
        <result column="DeviceSubTypeID" property="deviceSubTypeId" />
        <result column="DeviceSubTypeName" property="deviceSubTypeName" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, DeviceTypeID, DeviceTypeName, DeviceSubTypeID, DeviceSubTypeName
    </sql>

    <!-- 查询所有设备大类（去重） -->
    <select id="selectDistinctDeviceTypes" resultMap="BaseResultMap">
        SELECT DISTINCT DeviceTypeID, DeviceTypeName, NULL as ID, NULL as DeviceSubTypeID, NULL as DeviceSubTypeName
        FROM cmcc_device_type
        WHERE DeviceTypeID IS NOT NULL
        ORDER BY DeviceTypeID
    </select>

    <!-- 根据设备大类ID查询设备子类 -->
    <select id="selectSubTypesByDeviceTypeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_device_type
        WHERE DeviceTypeID = #{deviceTypeId}
        ORDER BY DeviceSubTypeID
    </select>

    <!-- 查询所有记录 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_device_type
        ORDER BY DeviceTypeID, DeviceSubTypeID
    </select>

</mapper>

