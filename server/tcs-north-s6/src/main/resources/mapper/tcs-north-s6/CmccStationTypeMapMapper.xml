<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.CmccStationTypeMapMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.north.s6.dal.entity.CmccStationTypeMap">
        <id column="ID" property="id" />
        <result column="StationTypeID" property="stationTypeId" />
        <result column="StationCategoryID" property="stationCategoryId" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, StationTypeID, StationCategoryID
    </sql>

    <!-- 根据局站类型ID查询映射 -->
    <select id="selectByStationTypeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_station_type_map
        WHERE StationTypeID = #{stationTypeId}
        LIMIT 1
    </select>

</mapper>

