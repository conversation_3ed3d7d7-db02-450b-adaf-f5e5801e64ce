<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.cmcc.CmccStationTypeMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.north.s6.dal.entity.cmcc.CmccStationType">
        <id column="ID" property="id" />
        <result column="StationTypeID" property="stationTypeId" />
        <result column="StationTypeName" property="stationTypeName" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, StationTypeID, StationTypeName
    </sql>

    <!-- 查询所有记录 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_station_type
        ORDER BY StationTypeID
    </select>

</mapper>

