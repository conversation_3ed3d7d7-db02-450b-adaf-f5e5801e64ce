<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.GatewayMapMapper">
    
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.north.s6.dal.entity.GatewayMap">
        <result column="GatewayId" property="gatewayId" />
        <result column="NorthMonitorUnitId" property="northMonitorUnitId" />
        <result column="Deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        GatewayId, NorthMonitorUnitId, Deleted
    </sql>

    <!-- 根据网关ID查询网关映射 -->
    <select id="selectByGatewayId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        WHERE GatewayId = #{gatewayId}
        AND Deleted = FALSE
    </select>

    <!-- 根据北向监控单元ID查询网关映射 -->
    <select id="selectByNorthMonitorUnitId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        WHERE NorthMonitorUnitId = #{northMonitorUnitId}
        AND Deleted = FALSE
    </select>

    <!-- 查询所有活跃的网关映射 -->
    <select id="selectAllActive" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        WHERE Deleted = FALSE
        ORDER BY GatewayId, NorthMonitorUnitId
    </select>

    <!-- 批量插入网关映射 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tcs_gateway_map (GatewayId, NorthMonitorUnitId, Deleted) 
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.gatewayId}, #{item.northMonitorUnitId}, #{item.deleted})
        </foreach>
    </insert>

    <!-- 根据复合主键删除网关映射 -->
    <update id="deleteByCompositeKey">
        UPDATE tcs_gateway_map 
        SET Deleted = TRUE
        WHERE GatewayId = #{gatewayId}
        AND NorthMonitorUnitId = #{northMonitorUnitId}
    </update>

    <!-- 插入单个网关映射 -->
    <insert id="insert" parameterType="com.siteweb.tcs.north.s6.dal.entity.GatewayMap">
        INSERT INTO tcs_gateway_map (GatewayId, NorthMonitorUnitId, Deleted)
        VALUES (#{gatewayId}, #{northMonitorUnitId}, #{deleted})
    </insert>

    <!-- 更新网关映射 -->
    <update id="updateById" parameterType="com.siteweb.tcs.north.s6.dal.entity.GatewayMap">
        UPDATE tcs_gateway_map
        <set>
            <if test="deleted != null">Deleted = #{deleted}</if>
        </set>
        WHERE GatewayId = #{gatewayId}
        AND NorthMonitorUnitId = #{northMonitorUnitId}
    </update>

    <!-- 查询所有网关映射（包括已删除的） -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_gateway_map
        ORDER BY GatewayId, NorthMonitorUnitId
    </select>

</mapper> 