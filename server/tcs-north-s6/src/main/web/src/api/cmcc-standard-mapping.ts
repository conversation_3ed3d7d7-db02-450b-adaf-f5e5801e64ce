import { http } from "@/utils/http";

export interface DeviceTypeMapping {
  deviceTypeId: number;
  deviceTypeName: string;
  deviceSubTypeId: number;
  deviceSubTypeName: string;
  equipmentCategoryId?: string;
  equipmentCategoryName?: string;
  mappingId?: number;
}

export interface StationTypeMapping {
  stationTypeId: number;
  stationTypeName: string;
  stationCategoryId?: number;
  stationCategoryName?: string;
  mappingId?: number;
}

export interface SiteWebEquipmentCategory {
  itemId: string,
  itemValue: string
}

export interface SiteWebStationCategory {
  itemId: string,
  itemValue: string
}

export interface DeviceStandardData {
  deviceTypeMappings: DeviceTypeMapping[];
  siteWebEquipmentCategories: SiteWebEquipmentCategory[];
}

export interface StationStandardData {
  stationTypeMappings: StationTypeMapping[];
  siteWebStationCategories: SiteWebStationCategory[];
}

export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

/**
 * 获取设备类型标准化数据
 */
export const getDeviceStandardData = () => {
  return http.request<ApiResponse<DeviceTypeMapping[]>>(
    "get",
    "/api/thing/south-cmcc-plugin/standard/standard-device-type"
  );
};

/**
 * 获取局站类型标准化数据
 */
export const getStationStandardData = () => {
  return http.request<ApiResponse<StationTypeMapping[]>>(
    "get",
    "/api/thing/south-cmcc-plugin/standard/standard-station-type"
  );
};

/**
 * 获取移动设备类型
 */
export const fetchCmccDeviceTypes = () => {
  return http.request<ApiResponse<string>>(
    "get",
    "/api/thing/south-cmcc-plugin/standard/standard-device-type"
  );
};

/**
 * 获取移动局站类型
 */
export const fetchCmccStationTypes = () => {
  return http.request<ApiResponse<string>>(
    "post",
    "/cmcc-standard-mapping/fetch-cmcc-station-types"
  );
};

/**
 * 保存设备类型映射
 */
export const saveDeviceMapping = (mappings: DeviceTypeMapping[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/cmcc-standard-mapping/save-device-mapping",
    { data: mappings }
  );
};

/**
 * 保存局站类型映射
 */
export const saveStationMapping = (mappings: StationTypeMapping[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/cmcc-standard-mapping/save-station-mapping",
    { data: mappings }
  );
};

/**
 * 获取SiteWeb设备类型
 */
export const getSiteWebEquipmentCategories = () => {
  return http.request<ApiResponse<SiteWebEquipmentCategory[]>>(
    "get",
    "/api/thing/tcs-north-s6/siteweb-standard/equipment-category"
  );
};

/**
 * 获取SiteWeb局站类型
 */
export const getSiteWebStationCategories = () => {
  return http.request<ApiResponse<SiteWebStationCategory[]>>(
    "get",
    "/api/thing/tcs-north-s6/siteweb-standard/station-category"
  );
};

/**
 * 持久化移动设备类型数据
 */
export const insertCmccDeviceTypes = (deviceTypes: any[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/insert-cmcc-device-types",
    { data: deviceTypes }
  );
};

/**
 * 持久化移动局站类型数据
 */
export const insertCmccStationTypes = (stationTypes: any[]) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-s6/cmcc-standard-mapping/insert-cmcc-station-types",
    { data: stationTypes }
  );
};

