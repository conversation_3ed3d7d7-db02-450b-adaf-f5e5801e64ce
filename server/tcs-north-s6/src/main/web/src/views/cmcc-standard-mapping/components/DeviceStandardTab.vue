<template>
  <div class="device-standard-tab">
    <!-- 操作按钮区域 -->
    <div class="operation-bar mb-4">
      <el-button 
        type="primary" 
        @click="fetchCmccDeviceTypesHandler"
        :loading="fetchLoading"
      >
        获取移动设备类型
      </el-button>
      <el-button 
        type="success" 
        @click="saveMapping"
        :loading="saveLoading"
      >
        保存映射
      </el-button>
    </div>

    <!-- 主内容区域 -->
    <div class="content-area">
      <el-row :gutter="20">
        <!-- 左侧：移动设备类型 -->
        <el-col :span="12">
          <el-card class="device-card">
            <template #header>
              <span class="font-bold">移动设备类型</span>
            </template>
            
            <el-tree
              ref="deviceTreeRef"
              :data="deviceTreeData"
              node-key="id"
              :props="treeProps"
              :expand-on-click-node="false"
              @node-click="handleDeviceNodeClick"
              class="device-tree"
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.isParent" class="mr-1">
                    <Folder />
                  </el-icon>
                  <el-icon v-else class="mr-1">
                    <Document />
                  </el-icon>
                  {{ node.label }}
                </span>
              </template>
            </el-tree>
          </el-card>
        </el-col>

        <!-- 右侧：SiteWeb设备类型和映射 -->
        <el-col :span="12">
          <el-card class="mapping-card">
            <template #header>
              <span class="font-bold">SiteWeb设备类型映射</span>
            </template>
            
            <div v-if="selectedDevice" class="mapping-content">
              <div class="selected-device mb-4">
                <el-tag type="info" size="large">
                  {{ selectedDevice.deviceTypeName }} - {{ selectedDevice.deviceSubTypeName }}
                </el-tag>
              </div>
              
              <div class="mapping-selector">
                <el-form-item label="映射到SiteWeb设备类型 (多选):">
                  <el-select
                    v-model="selectedDeviceMappings"
                    placeholder="请选择SiteWeb设备类型"
                    class="w-full"
                    multiple
                    @change="handleMappingChange"
                  >
                    <el-option
                      v-for="category in siteWebCategories"
                      :key="category.itemId"
                      :label="category.itemValue"
                      :value="category.itemId"
                    />
                  </el-select>
                </el-form-item>
              </div>
              
              <div class="current-mappings">
                <h4>当前映射关系:</h4>
                <el-tag 
                  v-for="mappingId in selectedDeviceMappings" 
                  :key="mappingId"
                  type="success" 
                  class="mr-2 mb-2"
                  closable
                  @close="removeMappingById(mappingId)"
                >
                  {{ getSiteWebCategoryName(mappingId) }}
                </el-tag>
              </div>
            </div>
            
            <div v-else class="no-selection">
              <el-empty description="请选择左侧的设备子类型进行映射配置" />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 映射列表 -->
      <el-card class="mapping-list-card mt-4">
        <template #header>
          <span class="font-bold">当前映射关系</span>
        </template>
        
        <el-table 
          :data="mappingTableData" 
          style="width: 100%"
          :loading="tableLoading"
        >
          <el-table-column prop="deviceTypeName" label="设备大类" width="150" />
          <el-table-column prop="deviceSubTypeName" label="设备子类" width="150" />
          <el-table-column label="映射关系" min-width="300">
            <template #default="scope">
              <div v-if="scope.row.mappingCount > 0" class="mapping-tags">
                <el-tag 
                  v-for="(name, index) in scope.row.equipmentCategoryNames" 
                  :key="index"
                  type="success" 
                  class="mr-1 mb-1"
                  size="small"
                >
                  {{ name }}
                </el-tag>
                <el-badge :value="scope.row.mappingCount" class="ml-2" type="primary" />
              </div>
              <el-tag v-else type="warning" size="small">未映射</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                size="small" 
                @click="editMapping(scope.row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Folder, Document } from "@element-plus/icons-vue";
import { 
  getDeviceStandardData, 
  fetchCmccDeviceTypes, 
  saveDeviceMapping,
  getSiteWebEquipmentCategories,
  insertCmccDeviceTypes,
  type DeviceTypeMapping,
  type SiteWebEquipmentCategory,
  type DeviceStandardData
} from "@/api/cmcc-standard-mapping";

const emit = defineEmits(["loading-change"]);

// 响应式数据
const fetchLoading = ref(false);
const saveLoading = ref(false);
const tableLoading = ref(false);
const deviceTreeRef = ref();

const deviceMappings = ref<DeviceTypeMapping[]>([]);
const siteWebCategories = ref<SiteWebEquipmentCategory[]>([]);
const selectedDevice = ref<DeviceTypeMapping | null>(null);
const selectedDeviceMappings = ref<string[]>([]); // 当前选中设备的多个映射
const allMappings = ref<Map<string, string[]>>(new Map()); // 存储所有设备的映射关系

// 树形数据
const treeProps = {
  children: 'children',
  label: 'label'
};

// 构建设备树数据
const deviceTreeData = computed(() => {
  const deviceTypes = new Map<number, any>();
  
  if (!deviceMappings.value || !Array.isArray(deviceMappings.value)) {
    return [];
  }
  
  deviceMappings.value.forEach(device => {
    if (!deviceTypes.has(device.deviceTypeId)) {
      deviceTypes.set(device.deviceTypeId, {
        id: `type_${device.deviceTypeId}`,
        label: device.deviceTypeName,
        isParent: true,
        deviceTypeId: device.deviceTypeId,
        children: []
      });
    }
    
    const parent = deviceTypes.get(device.deviceTypeId);
    parent.children.push({
      id: `subtype_${device.deviceTypeId}_${device.deviceSubTypeId}`,
      label: device.deviceSubTypeName,
      isParent: false,
      deviceData: device
    });
  });
  
  return Array.from(deviceTypes.values());
});

// 映射表格数据
const mappingTableData = computed(() => {
  if (!deviceMappings.value || !Array.isArray(deviceMappings.value)) {
    return [];
  }
  return deviceMappings.value.map(device => {
    const deviceKey = `${device.deviceTypeId}_${device.deviceSubTypeId}`;
    const mappingIds = allMappings.value.get(deviceKey) || [];
    const mappingNames = mappingIds.map(id => 
      siteWebCategories.value?.find(cat => cat.itemId === id)?.itemValue || id
    );
    
    return {
      ...device,
      equipmentCategoryNames: mappingNames,
      mappingCount: mappingNames.length
    };
  });
});

// 方法
const loadData = async () => {
  tableLoading.value = true;
  emit("loading-change", true);
  
  try {
    const response = await getDeviceStandardData();
    if (response.state) {
      deviceMappings.value = response.data;
    } else {
      ElMessage.error(response.err_msg || "获取数据失败");
    }
    
    // 单独获取 SiteWeb 设备类型
    await loadSiteWebCategories();
  } catch (error) {
    ElMessage.error("获取数据失败");
    console.error(error);
  } finally {
    tableLoading.value = false;
    emit("loading-change", false);
  }
};

const loadSiteWebCategories = async () => {
  try {
    const response = await getSiteWebEquipmentCategories();
    if (response.state) {
      siteWebCategories.value = response.data;
    } else {
      console.warn("获取SiteWeb设备类型失败:", response.err_msg);
    }
  } catch (error) {
    console.warn("获取SiteWeb设备类型失败:", error);
  }
};

const fetchCmccDeviceTypesHandler = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将清空现有移动设备类型数据并重新获取，是否继续？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    fetchLoading.value = true;
    emit("loading-change", true);
    
    // 1. 先调用StandardController获取移动设备类型
    const response = await fetchCmccDeviceTypes();
    if (response.state) {
      // 2. 调用成功后持久化数据
      const insertResponse = await insertCmccDeviceTypes(response.data);
      if (insertResponse.state) {
        ElMessage.success("获取并保存移动设备类型成功");
        await loadData(); // 重新加载数据
      } else {
        ElMessage.error(insertResponse.err_msg || "保存移动设备类型失败");
      }
    } else {
      ElMessage.error(response.err_msg || "获取移动设备类型失败");
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error("获取移动设备类型失败");
      console.error(error);
    }
  } finally {
    fetchLoading.value = false;
    emit("loading-change", false);
  }
};

const handleDeviceNodeClick = (data: any) => {
  if (!data.isParent && data.deviceData) {
    selectedDevice.value = data.deviceData;
    // 加载当前设备的映射关系
    const deviceKey = `${data.deviceData.deviceTypeId}_${data.deviceData.deviceSubTypeId}`;
    selectedDeviceMappings.value = allMappings.value.get(deviceKey) || [];
  }
};

const handleMappingChange = () => {
  if (selectedDevice.value) {
    // 更新映射关系
    const deviceKey = `${selectedDevice.value.deviceTypeId}_${selectedDevice.value.deviceSubTypeId}`;
    allMappings.value.set(deviceKey, [...selectedDeviceMappings.value]);
  }
};

const removeMappingById = (mappingId: string) => {
  const index = selectedDeviceMappings.value.indexOf(mappingId);
  if (index > -1) {
    selectedDeviceMappings.value.splice(index, 1);
    handleMappingChange();
  }
};

const getSiteWebCategoryName = (categoryId: string) => {
  const category = siteWebCategories.value.find(cat => cat.itemId === categoryId);
  return category ? category.itemValue : categoryId;
};

const editMapping = (mapping: DeviceTypeMapping) => {
  selectedDevice.value = { ...mapping };
  const deviceKey = `${mapping.deviceTypeId}_${mapping.deviceSubTypeId}`;
  selectedDeviceMappings.value = allMappings.value.get(deviceKey) || [];
};

const saveMapping = async () => {
  // 过滤出有映射关系的数据
  const mappingsToSave = deviceMappings.value.filter(
    device => device.equipmentCategoryId
  );
  
  if (mappingsToSave.length === 0) {
    ElMessage.warning("没有需要保存的映射关系");
    return;
  }
  
  try {
    saveLoading.value = true;
    emit("loading-change", true);
    
    const response = await saveDeviceMapping(mappingsToSave);
    if (response.state) {
      ElMessage.success("保存映射关系成功");
      await loadData(); // 重新加载数据
    } else {
      ElMessage.error(response.err_msg || "保存映射关系失败");
    }
  } catch (error) {
    ElMessage.error("保存映射关系失败");
    console.error(error);
  } finally {
    saveLoading.value = false;
    emit("loading-change", false);
  }
};

// 暴露方法给父组件
defineExpose({
  loadData
});

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.device-standard-tab {
  padding: 20px;
}

.operation-bar {
  display: flex;
  gap: 12px;
}

.device-card,
.mapping-card,
.mapping-list-card {
  height: auto;
  min-height: 400px;
}

.device-tree {
  max-height: 350px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
}

.mapping-content {
  padding: 10px 0;
}

.selected-device {
  padding: 10px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.mapping-list-card {
  margin-top: 20px;
}

.w-full {
  width: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.font-bold {
  font-weight: bold;
}

.mapping-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.current-mappings h4 {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
