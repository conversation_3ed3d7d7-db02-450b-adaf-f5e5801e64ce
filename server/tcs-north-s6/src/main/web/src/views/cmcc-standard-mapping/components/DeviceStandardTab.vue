<template>
  <div class="device-standard-tab">
    <!-- 顶部操作区 -->
    <div class="operation-bar mb-4">
      <el-button 
        type="primary" 
        @click="fetchCmccDeviceTypesHandler"
        :loading="fetchLoading"
      >
        获取移动设备类型
      </el-button>
      
      <el-button 
        type="warning" 
        @click="batchAutoCreateUnmapped"
        :loading="batchCreateLoading"
        :disabled="unmappedDevices.length === 0"
      >
        批量创建未映射类型
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 1. 映射配置区（核心表格） -->
      <el-card class="mapping-config-card mb-4">
        <template #header>
          <div class="flex-bc">
            <span class="font-bold">设备类型映射配置</span>
            <el-input 
              v-model="searchKeyword" 
              placeholder="搜索CMCC设备类型" 
              clearable 
              style="width: 300px" 
            />
          </div>
        </template>

        <el-table 
          :data="filteredMappingData"
          style="width: 100%"
          :loading="tableLoading"
          stripe
          height="400"
          max-height="400"
        >
          <el-table-column label="CMCC设备大类" min-width="200" prop="deviceTypeName" :filters="deviceTypeFilters" :filter-method="filterDeviceType" filter-placement="bottom-start">
            <template #default="scope">
              <div class="font-bold text-primary">{{ scope.row.deviceTypeName }}</div>
            </template>
          </el-table-column>
          
          <el-table-column label="CMCC设备子类" min-width="200" prop="deviceSubTypeName" :filters="deviceSubTypeFilters" :filter-method="filterDeviceSubType" filter-placement="bottom-start">
            <template #default="scope">
              <div class="text-secondary">{{ scope.row.deviceSubTypeName }}</div>
            </template>
          </el-table-column>
          
          <el-table-column label="当前映射的SiteWeb类型" min-width="300">
            <template #default="scope">
              <div v-if="scope.row.sitewebEquipmentCategoryName" class="mapping-display">
                <el-tag 
                  :type="scope.row.isAutoCreated ? 'warning' : 'success'"
                  size="default"
                >
                  {{ scope.row.sitewebEquipmentCategoryName }}
                  <span v-if="scope.row.isAutoCreated" class="ml-1">(自动创建)</span>
                </el-tag>
              </div>
              <div v-else class="text-secondary">
                <el-icon><Warning /></el-icon>
                未映射
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click="openMappingDialog(scope.row)"
              >
                {{ scope.row.sitewebEquipmentCategoryId ? '修改映射' : '设置映射' }}
              </el-button>
              
              <el-button 
                v-if="!scope.row.sitewebEquipmentCategoryId"
                type="success" 
                size="small" 
                @click="autoCreateAndMap(scope.row)"
                :loading="autoCreateLoadingMap.has(getDeviceKey(scope.row))"
              >
                自动创建
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 2. 未映射提示区 -->
      <el-card v-if="unmappedDevices.length > 0" class="unmapped-card mb-4">
        <template #header>
          <div class="flex-bc">
            <span class="font-bold text-orange-600">
              <el-icon><WarningFilled /></el-icon>
              未映射的CMCC设备类型 ({{ unmappedDevices.length }}个)
            </span>
            <el-button 
              type="warning" 
              size="small" 
              @click="batchAutoCreateUnmapped"
              :loading="batchCreateLoading"
            >
              一键创建全部映射
            </el-button>
          </div>
        </template>
        
        <div class="unmapped-list">
          <div 
            v-for="device in unmappedDevices" 
            :key="getDeviceKey(device)"
            class="unmapped-item"
          >
            <div class="device-info">
              <span class="device-name">{{ device.deviceTypeName }} - {{ device.deviceSubTypeName }}</span>
            </div>
            <el-button 
              type="success" 
              size="small" 
              @click="autoCreateAndMap(device)"
              :loading="autoCreateLoadingMap.has(getDeviceKey(device))"
            >
              创建并映射
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 3. SiteWeb类型管理区 -->
      <el-card v-if="orphanedSitewebTypes.length > 0" class="cleanup-card">
        <template #header>
          <div class="flex-bc">
            <span class="font-bold text-gray-600">
              <el-icon><Delete /></el-icon>
              无映射的自动创建类型 ({{ orphanedSitewebTypes.length }}个)
            </span>
            <el-button 
              type="danger" 
              size="small" 
              @click="batchCleanupOrphaned"
              :loading="cleanupLoading"
            >
              批量清理
            </el-button>
          </div>
        </template>
        
        <div class="orphaned-list">
          <div 
            v-for="type in orphanedSitewebTypes" 
            :key="type.itemId"
            class="orphaned-item"
          >
            <div class="type-info">
              <span class="type-name">{{ type.itemValue }}</span>
              <el-tag type="info" size="small">自动创建</el-tag>
            </div>
            <el-popconfirm 
              title="确定删除此SiteWeb设备类型？"
              @confirm="cleanupSingleOrphaned(type.itemId)"
            >
              <template #reference>
                <el-button 
                  type="danger" 
                  size="small"
                  :loading="singleCleanupLoadingMap.has(type.itemId)"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 映射选择对话框 -->
    <el-dialog 
      v-model="mappingDialogVisible" 
      title="设置设备类型映射" 
      width="500px"
      @close="closeMappingDialog"
    >
      <div v-if="currentEditDevice">
        <div class="mb-4">
          <h4>CMCC设备类型:</h4>
          <p class="text-primary font-bold">{{ currentEditDevice.deviceTypeName }}</p>
          <p class="text-secondary">{{ currentEditDevice.deviceSubTypeName }}</p>
        </div>
        
        <div class="mb-4">
          <h4>选择SiteWeb设备类型:</h4>
          <el-select 
            v-model="selectedSitewebCategoryId" 
            placeholder="请选择SiteWeb设备类型"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option 
              v-for="category in siteWebCategories" 
              :key="category.itemId"
              :label="category.itemValue + (category.isAutoCreated ? ' (自动创建)' : '')"
              :value="category.itemId"
            >
              <span>{{ category.itemValue }}</span>
              <el-tag 
                v-if="category.isAutoCreated" 
                type="warning" 
                size="small"
                class="ml-2"
              >
                自动创建
              </el-tag>
            </el-option>
          </el-select>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeMappingDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveMappingChange"
            :loading="saveMappingLoading"
          >
            保存映射
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Warning, WarningFilled, Delete } from "@element-plus/icons-vue";
import { 
  getDeviceStandardData, 
  fetchCmccDeviceTypes, 
  getSiteWebEquipmentCategories,
  insertCmccDeviceTypes,
  saveDeviceMappingOneToOne,
  autoCreateAndMapDeviceType,
  batchAutoCreateUnmappedDevices,
  cleanupOrphanedSitewebTypes,
  getUnmappedCmccDeviceTypes,
  getOrphanedSitewebTypes,
  type DeviceTypeMapping,
  type SiteWebEquipmentCategory,
} from "@/api/cmcc-standard-mapping";

const emit = defineEmits(["loading-change"]);

// 加载状态
const fetchLoading = ref(false);
const tableLoading = ref(false);
const batchCreateLoading = ref(false);
const cleanupLoading = ref(false);
const saveMappingLoading = ref(false);
const autoCreateLoadingMap = ref<Set<string>>(new Set());
const singleCleanupLoadingMap = ref<Set<string>>(new Set());

// 数据
const deviceMappings = ref<DeviceTypeMapping[]>([]);
const siteWebCategories = ref<SiteWebEquipmentCategory[]>([]);
const unmappedDevices = ref<DeviceTypeMapping[]>([]);
const orphanedSitewebTypes = ref<SiteWebEquipmentCategory[]>([]);

// 搜索关键词
const searchKeyword = ref("");

// 筛选数据
const deviceTypeFilters = ref<Array<{text: string, value: string}>>([]);
const deviceSubTypeFilters = ref<Array<{text: string, value: string}>>([]);

// 对话框状态
const mappingDialogVisible = ref(false);
const currentEditDevice = ref<DeviceTypeMapping | null>(null);
const selectedSitewebCategoryId = ref<string>("");

// 计算属性 - 过滤后的映射数据
const filteredMappingData = computed(() => {
  const keyword = searchKeyword.value.trim();
  if (!keyword) return deviceMappings.value;
  return deviceMappings.value.filter(d =>
    d.deviceTypeName.includes(keyword) ||
    d.deviceSubTypeName.includes(keyword) ||
    String(d.deviceTypeId).includes(keyword) ||
    String(d.deviceSubTypeId).includes(keyword)
  );
});

// 筛选方法
const filterDeviceType = (value: string, row: DeviceTypeMapping) => {
  return row.deviceTypeName === value;
};

const filterDeviceSubType = (value: string, row: DeviceTypeMapping) => {
  return row.deviceSubTypeName === value;
};

// 更新筛选选项
const updateFilters = () => {
  // 更新设备大类筛选选项
  const deviceTypes = [...new Set(deviceMappings.value.map(d => d.deviceTypeName))];
  deviceTypeFilters.value = deviceTypes.map(type => ({ text: type, value: type }));
  
  // 更新设备子类筛选选项
  const deviceSubTypes = [...new Set(deviceMappings.value.map(d => d.deviceSubTypeName))];
  deviceSubTypeFilters.value = deviceSubTypes.map(subType => ({ text: subType, value: subType }));
};

// 工具方法
const getDeviceKey = (device: DeviceTypeMapping) => `${device.deviceTypeId}_${device.deviceSubTypeId}`;

// 打开映射对话框
const openMappingDialog = (device: DeviceTypeMapping) => {
  currentEditDevice.value = device;
  selectedSitewebCategoryId.value = device.sitewebEquipmentCategoryId || "";
  mappingDialogVisible.value = true;
};

// 关闭映射对话框
const closeMappingDialog = () => {
  mappingDialogVisible.value = false;
  currentEditDevice.value = null;
  selectedSitewebCategoryId.value = "";
};

// 保存映射修改
const saveMappingChange = async () => {
  if (!currentEditDevice.value || !selectedSitewebCategoryId.value) {
    ElMessage.warning("请选择SiteWeb设备类型");
    return;
  }

  try {
    saveMappingLoading.value = true;
    const response = await saveDeviceMappingOneToOne({
      deviceTypeId: currentEditDevice.value.deviceTypeId,
      deviceSubTypeId: currentEditDevice.value.deviceSubTypeId,
      sitewebEquipmentCategoryId: selectedSitewebCategoryId.value
    });

    if (response.state) {
      ElMessage.success("映射保存成功");
      closeMappingDialog();
      await loadAllData(); // 重新加载数据
    } else {
      ElMessage.error(response.err_msg || "映射保存失败");
    }
  } catch (error) {
    ElMessage.error("映射保存失败");
    console.error(error);
  } finally {
    saveMappingLoading.value = false;
  }
};

// 自动创建并映射单个设备类型
const autoCreateAndMap = async (device: DeviceTypeMapping) => {
  const deviceKey = getDeviceKey(device);
  try {
    autoCreateLoadingMap.value.add(deviceKey);
    const response = await autoCreateAndMapDeviceType({
      deviceTypeId: device.deviceTypeId,
      deviceSubTypeId: device.deviceSubTypeId,
      deviceTypeName: device.deviceTypeName,
      deviceSubTypeName: device.deviceSubTypeName
    });

    if (response.state) {
      ElMessage.success(`成功创建并映射: ${device.deviceTypeName} - ${device.deviceSubTypeName}`);
      await loadAllData(); // 重新加载数据
    } else {
      ElMessage.error(response.err_msg || "自动创建映射失败");
    }
  } catch (error) {
    ElMessage.error("自动创建映射失败");
    console.error(error);
  } finally {
    autoCreateLoadingMap.value.delete(deviceKey);
  }
};

// 批量自动创建未映射的设备类型
const batchAutoCreateUnmapped = async () => {
  try {
    await ElMessageBox.confirm(
      `将为所有 ${unmappedDevices.value.length} 个未映射的CMCC设备类型自动创建对应的SiteWeb设备类型并建立映射关系，是否继续？`,
      '确认批量创建',
      { type: 'warning' }
    );

    batchCreateLoading.value = true;
    const response = await batchAutoCreateUnmappedDevices();

    if (response.state) {
      ElMessage.success("批量自动创建映射成功");
      await loadAllData(); // 重新加载数据
    } else {
      ElMessage.error(response.err_msg || "批量自动创建映射失败");
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error("批量自动创建映射失败");
      console.error(error);
    }
  } finally {
    batchCreateLoading.value = false;
  }
};

// 清理单个孤立的siteweb类型
const cleanupSingleOrphaned = async (sitewebCategoryId: string) => {
  try {
    singleCleanupLoadingMap.value.add(sitewebCategoryId);
    const response = await cleanupOrphanedSitewebTypes([sitewebCategoryId]);

    if (response.state) {
      ElMessage.success("清理成功");
      await loadAllData(); // 重新加载数据
    } else {
      ElMessage.error(response.err_msg || "清理失败");
    }
  } catch (error) {
    ElMessage.error("清理失败");
    console.error(error);
  } finally {
    singleCleanupLoadingMap.value.delete(sitewebCategoryId);
  }
};

// 批量清理孤立的siteweb类型
const batchCleanupOrphaned = async () => {
  try {
    await ElMessageBox.confirm(
      `将删除所有 ${orphanedSitewebTypes.value.length} 个无映射的自动创建SiteWeb设备类型，是否继续？`,
      '确认批量清理',
      { type: 'warning' }
    );

    cleanupLoading.value = true;
    const orphanedIds = orphanedSitewebTypes.value.map(type => type.itemId);
    const response = await cleanupOrphanedSitewebTypes(orphanedIds);

    if (response.state) {
      ElMessage.success("批量清理成功");
      await loadAllData(); // 重新加载数据
    } else {
      ElMessage.error(response.err_msg || "批量清理失败");
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error("批量清理失败");
      console.error(error);
    }
  } finally {
    cleanupLoading.value = false;
  }
};

// 数据加载方法
const loadAllData = async () => {
  tableLoading.value = true;
  emit("loading-change", true);
  
  try {
    // 并行加载所有数据
    const [mappingRes, categoriesRes, unmappedRes, orphanedRes] = await Promise.all([
      getDeviceStandardData(),
      getSiteWebEquipmentCategories(),
      getUnmappedCmccDeviceTypes(),
      getOrphanedSitewebTypes()
    ]);

    if (mappingRes.state) {
      deviceMappings.value = mappingRes.data ?? [];
    }

    if (categoriesRes.state) {
      siteWebCategories.value = categoriesRes.data ?? [];
    }

    if (unmappedRes.state) {
      unmappedDevices.value = unmappedRes.data ?? [];
    }

    if (orphanedRes.state) {
      orphanedSitewebTypes.value = orphanedRes.data ?? [];
    }

    // 更新筛选选项
    updateFilters();

  } catch (error) {
    ElMessage.error("获取数据失败");
    console.error(error);
  } finally {
    tableLoading.value = false;
    emit("loading-change", false);
  }
};

// 获取+持久化CMCC设备类型
const fetchCmccDeviceTypesHandler = async () => {
  try {
    // 显示确认弹框
    await ElMessageBox.confirm(
      '此操作将清空现有移动设备类型数据并重新获取，是否继续？',
      '确认操作',
      { 
        confirmButtonText: '确定', 
        cancelButtonText: '取消', 
        type: 'warning' 
      }
    );
    
    fetchLoading.value = true;
    emit("loading-change", true);
    
    // 调用API获取移动设备类型
    const response = await fetchCmccDeviceTypes();
    
    if (response.state) {
      // 获取成功后，持久化数据
      const insertResponse = await insertCmccDeviceTypes(response.data as any[]);
      
      if (insertResponse.state) {
        ElMessage.success("获取并保存移动设备类型成功");
        await loadAllData(); // 重新加载数据
      } else {
        ElMessage.error(insertResponse.err_msg || "保存移动设备类型失败");
      }
    } else {
      ElMessage.error(response.err_msg || "获取移动设备类型失败");
    }
  } catch (error: any) {
    if (error === 'cancel') {
      // 用户取消操作
      return;
    }
    
    ElMessage.error("获取移动设备类型失败: " + (error.message || error));
  } finally {
    fetchLoading.value = false;
    emit("loading-change", false);
  }
};

onMounted(() => {
  loadAllData();
});

// 暴露方法给父组件调用
defineExpose({
  loadData: loadAllData
});
</script>

<style scoped>
.device-standard-tab { 
  padding: 20px; 
}

.operation-bar { 
  display: flex; 
  gap: 12px; 
}

.mapping-config-card {
  min-height: 480px;
}

.unmapped-card {
  border-left: 4px solid var(--el-color-warning);
}

.cleanup-card {
  border-left: 4px solid var(--el-color-info);
}

.unmapped-list, .orphaned-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 250px;
  overflow-y: auto;
}

.unmapped-item, .orphaned-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  background-color: var(--el-bg-color-page);
}

.device-info, .type-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-name, .type-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.mapping-display {
  display: flex;
  align-items: center;
}

.text-primary {
  color: var(--el-color-primary);
}

.text-secondary {
  color: var(--el-text-color-secondary);
}

.text-sm {
  font-size: 12px;
}

.text-orange-600 {
  color: var(--el-color-warning);
}

.text-gray-600 {
  color: var(--el-text-color-secondary);
}

.flex-bc { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}

.font-bold { 
  font-weight: bold; 
}

.mb-4 { 
  margin-bottom: 16px; 
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.dialog-footer {
  display: flex;
  gap: 8px;
}
</style>
