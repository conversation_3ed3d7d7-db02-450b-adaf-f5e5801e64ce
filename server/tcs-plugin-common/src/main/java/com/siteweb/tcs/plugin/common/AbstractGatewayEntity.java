package com.siteweb.tcs.plugin.common;

import com.siteweb.stream.common.messages.ShapeRouteMessage;
import com.siteweb.stream.common.stream.StreamGraphOption;
import com.siteweb.stream.core.manager.StreamGraphInstanceManager;
import com.siteweb.stream.core.provider.StreamGraphProvider;
import com.siteweb.tcs.common.exception.code.StandardTechnicalErrorCode;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.common.sharding.GatewayFailoverComplete;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.domain.GatewayHub;
import com.siteweb.tcs.hub.domain.process.lifecycle.GatewayPipelineProxy;
import com.siteweb.tcs.plugin.common.exception.SouthGatewayErrorCode;
import com.siteweb.tcs.plugin.common.message.SouthControlCmdRequest;
import com.siteweb.tcs.plugin.common.message.SouthRawMessage;
import com.siteweb.tcs.plugin.common.sharding.IGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * 南向Gateway分盘代理基类
 * <p>
 * <p>
 * ===========================
 * == Actor 异常重启触发顺序==
 * ===========================
 * => 1. PreRestart
 * => 2. PostStop
 * => 3. Constructor
 * => 4. PostRestart
 * => 5. PreStart
 *
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
public abstract class AbstractGatewayEntity<TGatewayEntity extends IGateway, TRawMessage extends SouthRawMessage> extends ProbeActor {

    protected final String gatewayId;                           // 当前GatewayId
    protected final String shardingName;                        // 当前Entity 分片名
    protected final String cacheKey;                            // 缓存Key
    protected final MessageTracer messageTracer = new MessageTracer();
    protected final GatewayState<TGatewayEntity> state;         // Gateway 内存状态
    protected final String pluginId;                    // Gateway PluginId
    private final StreamGraphInstanceManager streamGraphInstanceManager = StreamGraphInstanceManager.getInstance();
    private final StreamGraphProvider streamGraphProvider;      // 流处理相关Provider
    private ActorRef graphActor;                                // 流处理的入口节点
    private final Class<TRawMessage> rawMessageClazz;           // 原始消息类，过滤消息用
    private final GatewayCacheManager<TGatewayEntity> gatewayCacheManager;
    private ActorRef pipeline;                                  // Gateway Pipeline Actor

    // PROBE NAME
    private static final String LCM_MESSAGE_COUNTER = "LCM_MESSAGE_COUNTER";    // 生命周期消息计数器
    private static final String USER_MESSAGE_COUNTER = "USER_MESSAGE_COUNTER";  // 用户命令消息计数器
    private static final String RAW_MESSAGE_COUNTER = "RAW_MESSAGE_COUNTER";    // 业务原始消息计数器
    private static final String ERROR_COUNTER = "ERROR_COUNTER";                // 处理异常次数计数器
    private static final String GRAPH_HANDLE_COUNTER = "GRAPH_HANDLE_COUNTER";  // 流计算  处理计数器
    private static final String EXCEPTION_COUNTER = "EXCEPTION_COUNTER";        // 异常重启次数计数器


    //    private final ActorRef pipeline;
    public AbstractGatewayEntity(String gatewayId, String shardingName, String pluginId, Class<TRawMessage> rawMessageClazz) {
        super();
        this.gatewayId = gatewayId;
        this.shardingName = shardingName;
        this.cacheKey = shardingName + ":" + gatewayId;
        this.state = new GatewayState<>(gatewayId, shardingName, pluginId);
        this.rawMessageClazz = rawMessageClazz;
        this.pluginId = pluginId;
        this.streamGraphProvider = PluginScope.getBean(StreamGraphProvider.class);
        this.gatewayCacheManager = createGatewayCacheManager();
        probe.addCounter(GRAPH_HANDLE_COUNTER);
        probe.addCounter(ERROR_COUNTER);
        probe.addCounter(LCM_MESSAGE_COUNTER);
        probe.addCounter(USER_MESSAGE_COUNTER);
        probe.addCounter(RAW_MESSAGE_COUNTER);
        probe.addCounter(EXCEPTION_COUNTER);

        this.registerToHub();
    }


    /**
     * 注册到Hub的单例Gateway缓存。
     */
    private void registerToHub() {
        var gatewayHub = (ActorRef) PluginScope.getBean("gateway-hub");
        var reg = new GatewayHub.GatewayRegister();
        reg.setGatewayId(state.gatewayId);
        reg.setShardingName(state.shardingName);
        reg.setPluginId(state.pluginId);
        reg.setActor(self());
        gatewayHub.tell(reg, self());
    }


    @Override
    public final AbstractActor.Receive createReceive() {
        var builder = receiveBuilder()
                .match(GatewayFailoverComplete.class, this::onFailoverComplete)                    // 节点被故障转移
                .match(SouthControlCmdRequest.class, this::onControlHandler)                      // 设备控制消息
                .match(SouthLifeCycleEvent.class, this::onLifeCycleHandler)                      // 生命周期消息
                .match(SouthUserCommand.class, this::onUserCommandHandler)                      // 用户命令消息
                .match(SouthRawMessage.class, this::onSouthRawMessageHandler)                  // 原始报文消息
                .matchAny((e) -> {
                    log.info("XXXXXXXX" + e.getClass().getName());
                })
                .build();
        return super.createReceive().orElse(builder);
    }


    /**
     * 节点异常重启</br>
     * 此步骤需从缓存加载数据并load处理
     *
     * @param reason
     * @throws Exception
     */
    @Override
    public final void postRestart(final Throwable reason) throws Exception {
        probe.incrementCounter(EXCEPTION_COUNTER);
        log.warn("Gateway [{}] Recovered From Anomaly: {}", gatewayId, reason.toString());
        var gatewayInfo = gatewayCacheManager.get(cacheKey);
        if (gatewayInfo != null) {
            initializeGatewayState(gatewayInfo);
        }
        super.postRestart(reason);
    }

    /**
     * 节点故障转移完成或自平衡完成。</br>
     * 此步骤需从缓存加载数据并load处理
     *
     * @param startEntity
     */
    private void onFailoverComplete(GatewayFailoverComplete startEntity) {
        log.info("Gateway [{}] Failover Complete.", cacheKey);
        var gatewayInfo = gatewayCacheManager.get(cacheKey);
        if (gatewayInfo != null) {
            initializeGatewayState(gatewayInfo);
        }
    }


    @Override
    public final void postStop() throws Exception {
        log.info("FSU {} PostStop", state.getGatewayId());
        // 1.停止前尝试禁用 触发OnDisabled事件 清理相关资源
        onLifeCycleHandler(new SouthLifeCycleEvent(SouthLifeCycleEventType.DISABLE));
        super.postStop();
    }

    @SuppressWarnings("unchecked")
    /**
     * todo
     * todo 生命周期事件(CRUD)使用com.siteweb.tcs.hub.domain.v2.letter.LifeCycleEvent
     * todo 操作事件(start、stop、restart)使用com.siteweb.tcs.hub.domain.v2.letter.letter.OperationEvent
     */
    private void onLifeCycleHandler(SouthLifeCycleEvent message) {
        probe.incrementCounter(LCM_MESSAGE_COUNTER);
        log.info("Process LifeCycle GatewayId:{} Event:{}", message.getGatewayId(), message.getEvent());
//        messageTracer.traceRequestFrame(UUID.randomUUID(),"LCM_" + message.getEvent().name(), message.getGatewayInfo());
        switch (message.getEvent()) {
            case LOAD:
                // 加载Gateway并根据enable属性是否启用
                state.tryLoad(() -> {
                    var gatewayInfo = (TGatewayEntity) message.getGatewayInfo();
                    gatewayCacheManager.set(cacheKey, gatewayInfo);
                    log.info("Gateway: {} is Launch Event, Path = {}", gatewayId, getSelf().path());
                    initializeGatewayState(gatewayInfo);
                });
                break;
            case ENABLE:
                // 启用Gateway 并开始流计算
                state.tryEnable(() -> {
                    var gatewayInfo = (TGatewayEntity) message.getGatewayInfo();
                    if (gatewayInfo != null) {
                        gatewayCacheManager.set(cacheKey, gatewayInfo);
                    }
                    log.info("Gateway: {} is Enabled, Path = {}", gatewayId, getSelf().path());
                    startGatewayWorker();
                    onGatewayEnabled();
                });
                break;

            case DISABLE:
                // 禁用Gateway 并停止流计算
                state.tryDisable(() -> {
                    var gatewayInfo = (TGatewayEntity) message.getGatewayInfo();
                    if (gatewayInfo != null) {
                        gatewayCacheManager.set(cacheKey, gatewayInfo);
                    }
                    log.info("Gateway: {} is Disable, Path = {}", gatewayId, getSelf().path());
                    stopGatewayWorker();
                    onGatewayDisabled();
                });
                break;

            case CREATE:
                // 收到南向创建FSU请求，此处加载配置（与LOAD一致）并转发到Pipeline
                if (state.isLoaded()) {
                    log.error("The Gateway {} already exists, but has received the creation event again.", gatewayId);
                }
                // 尝试加载FSU
                onLifeCycleHandler(new SouthLifeCycleEvent(SouthLifeCycleEventType.LOAD, message.getGatewayInfo()));
                break;

            case UPDATE:
                //
                var gatewayInfo = (TGatewayEntity) message.getGatewayInfo();
                // 更新redis缓存
                gatewayCacheManager.set(cacheKey, gatewayInfo);
                // 更新state缓存
                state.setGatewayInfo(gatewayInfo);
                // 触发回调
                onGatewayUpdate();
                break;

            case DELETE:
                // 1. 先尝试禁用
                onLifeCycleHandler(new SouthLifeCycleEvent(SouthLifeCycleEventType.DISABLE));
                // 2. 触发回调
                onGatewayDeleted();
                // 删除redis缓存
                gatewayCacheManager.delete(cacheKey);
                // 相关资源清理
                state.clean();
                break;

            default:
                log.error("未知的生命周期事件类型: {}", message.getEvent());
        }
    }


    /**
     * 写入消息至Pipeline
     */
    protected final void writePipeline(ConfigChangeResult result) {
        if (result.isSuccess()) {
            pipeline.tell(result, self());
        }
    }


    /**
     * 创建并同步执行SouthLifeCycleEventType生命周期事件</br>
     * 将gatewayInfo 写入state 并通知Hub的Pipeline
     *
     * @param gatewayInfo gatewayInfo
     */
    protected void executeUpdateEvent(IGateway gatewayInfo) {
        onLifeCycleHandler(new SouthLifeCycleEvent(SouthLifeCycleEventType.UPDATE, gatewayInfo));
    }

    /**
     * 初始化Gateway状态
     *
     * @param gatewayInfo 必须保证gatewayInfo
     */
    private void initializeGatewayState(TGatewayEntity gatewayInfo) {
        if (gatewayInfo == null) throw StandardTechnicalErrorCode.INVALID_PARAMETER.toException();
        state.setGatewayInfo(gatewayInfo);
        state.setStatus(GatewayStatus.OFFLINE);
        // 创建管道
        state.setGatewayEnable(gatewayInfo.isEnable());
        pipeline = this.context().actorOf(Props.create(GatewayPipelineProxy.class, gatewayId, pluginId));
        onGatewayLoaded();
        if (gatewayInfo.isEnable()) {
            startGatewayWorker();
            onGatewayEnabled();
            // 发送给自己 启用
            SouthLifeCycleEvent enableEvent = new SouthLifeCycleEvent();
            enableEvent.setGatewayId(gatewayId);
            enableEvent.setEvent(SouthLifeCycleEventType.ENABLE);
            self().tell(enableEvent, ActorRef.noSender());
        }
    }

    /**
     * 启动Gateway相关工作
     */
    private void startGatewayWorker() {
        var gatewayInfo = state.getGatewayInfo();
        // 启动流计算
        if (gatewayInfo != null && gatewayInfo.getGraphId() != null) {
            var streamGraph = streamGraphProvider.findGraph(state.getGatewayInfo().getGraphId());
            if (streamGraph != null) {
                var graphOption = streamGraph.getGraphOption();
                graphOption.setProperty("gatewayId", gatewayId);
                graphOption.setProperty("gatewayInfo", gatewayInfo);
                graphOption.setProperty("pipelineActorRef", pipeline);
                configureGraphOption(graphOption, gatewayInfo);
                var graphInstance = streamGraphInstanceManager.createGraph(context(), streamGraph, graphOption);
                streamGraphInstanceManager.startGraph(state.getGatewayInfo().getGraphId());
                graphActor = graphInstance.getGraphActorRef();
            }
        }
    }

    /**
     * 停止Gateway相关工作
     */
    private void stopGatewayWorker() {
        if (state.getGatewayInfo().getGatewayId() != null) {
            streamGraphInstanceManager.stopGraph(state.getGatewayInfo().getGraphId());
            streamGraphInstanceManager.destroyGraph(state.getGatewayInfo().getGraphId());
        }
    }


    private void onUserCommandHandler(SouthUserCommand command) {
        probe.incrementCounter(USER_MESSAGE_COUNTER);
        log.info("Process UserCommand GatewayId:{}", command.getGatewayId());
        if (!state.isGatewayEnable()) return; // 停止后不处理消息
        handleUserCommand(command);
    }

    /**
     * 此处处理第一条RawMessage
     */
    private void gatewayFirstRawMessageTrigger() {
        var status = state.getStatus();
        if (GatewayStatus.UNKNOWN.equals(status)) {
            var isPending = fetchPendingState(gatewayId);
            if (isPending) {
                state.setStatus(GatewayStatus.PENDING);
            }
        }
    }


    @SuppressWarnings("unchecked")
    private void onSouthRawMessageHandler(SouthRawMessage message) {
        TRawMessage rawMessage = null;
        log.info("Process SouthRawMessage GatewayId:{} Type:{}", message.getGatewayId(), message.getMsgType());
        probe.incrementCounter(RAW_MESSAGE_COUNTER);
        messageTracer.traceRequestFrame(message.getMsgId(), message.getMsgType(), message.getContent());
        if (rawMessageClazz.isInstance(message)) {
            rawMessage = (TRawMessage) message;
        } else {
            // 无效消息，及时返回防止httpserver一直占用
            message.response(SouthGatewayErrorCode.UNKNOWN_MESSAGE.toException());
            return;
        }

        try {
            // 接入检测
            if (state.handleOnce()) {
                gatewayFirstRawMessageTrigger();
            }
            var status = state.getStatus();
            if (GatewayStatus.UNKNOWN.equals(status)) {
                if (handleUnauthorizedMessage(rawMessage, false)) return;
            } else if (GatewayStatus.PENDING.equals(status)) {
                if (handleUnauthorizedMessage(rawMessage, true)) return;
            }
            if (!state.isGatewayEnable()) {
                return;
            }
            // 授权检测
            if (!state.isLoginAuthentication()) {
                if (handleAnonymousMessage(rawMessage)) return;
            }

            // 前置消息处理
            if (handleRawMessage(rawMessage)) return;
            // 转发至流计算图
            forwardGraph(rawMessage);
        } catch (Throwable throwable) {
            handleMessageProcessError(rawMessage, throwable);
        } finally {
            // 对于未回应的消息及时回应 防止HttpServer未返回一直占用
            if (rawMessage.getSender() != null) {
                if (handleMessageAck(rawMessage)) {
                    rawMessage.setSender(null);
                }
            }
        }
    }

    /**
     * 报文未回应确认需要处理<br/>
     * 如果协议文档规定消息必须返回结果，消息在离开GatewayEntity之前必须完成返回。<br/>
     * StreamGraph内不处理消息返回
     *
     * @param rawMessage 原始消息
     * @return 如果返回true 表示已回应消息。
     */
    protected abstract boolean handleMessageAck(TRawMessage rawMessage);


    protected void loginOk() {
        log.info("FSU {} login success.", gatewayId);
        state.setStatus(GatewayStatus.ONLINE);
    }


    /**
     * 创建Gateway缓存器，缓存始终保存最新的TGatewayEntity备份<br/>
     * 用于Actor故障转移启动时的数据拉取<br/>
     * 引出该方法至派生类的原因是在基类中获取不到插件的Bean
     *
     * @return
     */
    protected abstract GatewayCacheManager<TGatewayEntity> createGatewayCacheManager();


    /**
     * 拉取Gateway的授权状态/是否在待处理列表
     *
     * @param gatewayId
     * @return
     */
    protected abstract boolean fetchPendingState(String gatewayId);


    /**
     * 拉取Gateway配置信息
     *
     * @param fsuId
     * @return
     */
    protected abstract TGatewayEntity fetchGatewayEntity(String fsuId);


    /**
     * 配置流计算图的携带选项<br/>
     * 可在Shape中通过context获取Option的值
     *
     * @param option  Graph Options
     * @param gateway Gateway Info
     */
    protected void configureGraphOption(StreamGraphOption option, TGatewayEntity gateway) {

    }

    /**
     * 由Hub发送来的设备控制命令消息处理
     *
     * @param request
     */
    protected void onControlHandler(SouthControlCmdRequest request) {

    }

    /**
     * 生成Gateway的服务地址 如下所示<br/>
     * http://***********:8080/services/FSUService<br/>
     * https://***********:8080/services/FSUService<br/>
     * tcp://***********:8080<br/>
     * udp://***********:8080<br/>
     *
     * @return
     */

    protected String generateServiceAddress(TGatewayEntity gateway) {
        return "";
    }

    protected String generateServiceAddress() {
        return generateServiceAddress(state.getGatewayInfo());
    }


    /**
     * 转发消息至流计算图
     */
    protected final void forwardGraph(TRawMessage message) {
        if (graphActor != null) {
            //转发到 Streams 处理
            probe.incrementCounter(GRAPH_HANDLE_COUNTER);
            graphActor.tell(new ShapeRouteMessage(message), self());
        }
    }


    /**
     * 用户命令处理<br/>
     *
     * @param command 用户发送的命令
     */
    protected void handleUserCommand(SouthUserCommand command) {

    }

    /**
     * Gateway未被授权/未接入时的消息处理器<br/>
     * 派生类自己维护state内状态<br/>
     * 下一步：handleUnauthorizedMessage | handlePrecedingMessage<br/>
     * 方法内异常触发 handleMessageProcessError<br/>
     * 此方法默认拦截所有未授权的Gateway消息
     *
     * @param message   未授权的消息
     * @param isPending 是否为待接入状态
     * @return 为true时表示已处理消息，中止后续流程
     */
    protected boolean handleUnauthorizedMessage(TRawMessage message, boolean isPending) {
        state.setStatus(GatewayStatus.PENDING);
        return true;
    }

    /**
     * Gateway 未登录认证时的消息处理器<br/>
     * 派生类自己维护state内状态<br/>
     * 下一步：handlePrecedingMessage<br/>
     * 方法内异常触发 handleMessageProcessError<br/>
     * 此方法默认拦截所有未认证的Gateway消息
     *
     * @param message 未登录认证的消息
     * @return 为true时表示已处理消息，中止后续流程
     */
    protected boolean handleAnonymousMessage(TRawMessage message) {
        return true;
    }


    /**
     * Gateway 生命周期消息处理器，需要自己过滤<br/>
     * 下一步：转发至 Flow Graph<br/>
     * 方法内异常触发 handleMessageProcessError<br/>
     *
     * @param message 原始消息
     * @return 为true时表示已处理消息，中止后续流程
     */
    protected boolean handleRawMessage(TRawMessage message) {
        return false;
    }


    /**
     * 消息处理过程中异常捕获<br/>
     *
     * @param message   处理的哪条消息
     * @param throwable 异常是什么
     */
    protected void handleMessageProcessError(TRawMessage message, Throwable throwable) {
        probe.incrementCounter(ERROR_COUNTER);
    }


    /**
     * Gateway 加载回调<br/>
     * 此时Gateway配置已经加载到Entity<br/>
     * 处理时机：<br/>
     * 1. 系统启动后会将所有Gateway Load<br/>
     */
    protected void onGatewayLoaded() {

    }

    /**
     * Gateway 更新回调<br/>
     * 此时Gateway配置已更新至state
     * 处理时机：<br/>
     * 1. 用户更新了Gateway数据<br/>
     * 2. Gateway向上同步的配置变更了<br/>
     */
    protected void onGatewayUpdate() {

    }


    /**
     * Gateway 启用回调方法。<br/>
     * 当 Gateway 被启用时（即从非启用状态切换为启用状态），将触发该方法。
     * 此时 Gateway 将开始处理消息逻辑和流计算图。<br/><br/>
     * <p>
     * 触发时机包括：<br/>
     * 1. 用户手动启用 Gateway（例如调用 enable 操作）<br/>
     * 2. 系统启动期间，从持久化状态加载 Gateway 时，如果其状态为启用（Enabled）<br/><br/>
     * <p>
     * 注意：此方法属于用户态生命周期，可用于初始化与 Gateway 生命周期相关的资源，
     * 如注册定时器、启动任务等、创建连接。
     */
    protected void onGatewayEnabled() {

    }

    /**
     * Gateway 禁用回调方法。<br/>
     * 当 Gateway 被禁用时（即从启用状态切换为非启用状态），将触发该方法。
     * 禁用后，Gateway 将不再处理任何消息逻辑和流计算图。<br/><br/>
     * <p>
     * 触发时机包括：<br/>
     * 1. 用户手动禁用 Gateway（例如调用 disable 操作）<br/>
     * 2. 用户删除 Gateway 时（若当前为启用状态）<br/>
     * 3. Gateway 实例销毁前（若当前为启用状态）<br/><br/>
     * <p>
     * 注意：此方法可用于清理用户态资源，如释放定时器、关闭连接等。
     */
    protected void onGatewayDisabled() {

    }


    /**
     * Gateway 被删除，Entity即将被销毁
     * 处理时机：<br/>
     * 1. 用户删除Gateway时<br/>
     */
    protected void onGatewayDeleted() {

    }
}
