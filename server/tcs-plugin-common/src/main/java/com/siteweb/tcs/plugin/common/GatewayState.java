package com.siteweb.tcs.plugin.common;

import com.siteweb.tcs.plugin.common.sharding.IGateway;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> (2025-07-04)
 **/
@Data
public class GatewayState<TGatewayEntity extends IGateway> {

    /**
     * 空闲指定时间后判定为离线
     */
    private static final Long IDLE_TIMEOUT_MILLIS = 60 * 1000L;

    /**
     * 当前State的gatewayID
     */
    public final String gatewayId;

    /**
     * PluginID
     */
    public final String pluginId;

    /**
     * 分片ID
     */
    public final String shardingName;

    public GatewayState(String gatewayId, String shardingName, String pluginId) {
        this.gatewayId = gatewayId;
        this.pluginId = pluginId;
        this.shardingName = shardingName;
    }

    /**
     * 获取Gateway信息
     */
    private TGatewayEntity gatewayInfo;

    /**
     * Gateway 接入状态
     */
    private GatewayStatus status = GatewayStatus.UNKNOWN;


    /**
     * Gateway 启动状态
     */
    private boolean gatewayEnable = false;

    /**
     * 是否已经拉取了Gateway Pending状态
     */
    private boolean fetchPending;

    /**
     * 是否已经处理过一条RawMessage
     */
    private boolean firstRawMessage;

    /**
     * 最后访问时间
     */
    private Long lastAccessTime = 0L;

    /**
     * 上次心跳时间
     */
    private Timestamp lastHeartbeatTime = new Timestamp(0L);

    /**
     * 上次时间同步时间
     */
    private Timestamp lastTimeCheckTime = new Timestamp(0L);

    /**
     * 判断是否登录认证过
     */
    public boolean isLoginAuthentication() {
        if (!status.equals(GatewayStatus.ONLINE)) return false;
        // 验证lastAccessDate 是否超时
        var now = System.currentTimeMillis();
        if ((now - lastAccessTime) > IDLE_TIMEOUT_MILLIS) {
            status = GatewayStatus.OFFLINE;
        }
        // lastAccessDate
        lastAccessTime = System.currentTimeMillis();
        return true;
    }

    /**
     * FSU配置是否已加载
     *
     * @return
     */
    public boolean isLoaded() {
        return gatewayInfo != null;
    }


    /**
     * 清理所有状态
     */
    public void clean() {
        this.gatewayInfo = null;
        this.status = GatewayStatus.UNKNOWN;
        this.gatewayEnable = false;
        this.lastAccessTime = 0L;
        this.fetchPending = false;
        this.firstRawMessage = false;
    }

    /**
     * 仅处理一次
     *
     * @return
     */
    public boolean handleOnce() {
        if (firstRawMessage) return false;
        firstRawMessage = true;
        return true;
    }

    /**
     * 尝试加载fsu配置，如果已加载则不执行
     *
     * @param nameSupplier
     */
    public void tryLoad(Runnable nameSupplier) {
        if (gatewayInfo == null) {
            nameSupplier.run();
        }
    }

    /**
     * 尝试禁用FSU 如果已禁用则不执行
     *
     * @param nameSupplier
     */
    public void tryDisable(Runnable nameSupplier) {
        if (gatewayEnable) {
            nameSupplier.run();
            gatewayEnable = false;
        }
    }

    /**
     * 尝试启用FSU 如果已启用则不执行
     *
     * @param nameSupplier
     */
    public void tryEnable(Runnable nameSupplier) {
        if (!gatewayEnable) {
            nameSupplier.run();
            gatewayEnable = true;
        }
    }
}
