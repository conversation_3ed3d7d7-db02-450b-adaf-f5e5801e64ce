package com.siteweb.tcs.plugin.common;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.siteweb.tcs.common.ISerializableMessage;
import com.siteweb.tcs.plugin.common.tracer.TraceMessage;
import com.siteweb.tcs.plugin.common.tracer.TraceRequestFrame;
import com.siteweb.tcs.siteweb.util.JsonHelper;
import lombok.Data;

import java.util.*;

/**
 * <AUTHOR> (2025-07-29)
 **/
@Data
public class MessageTracer {

    private Boolean enabled = false;

    private final HashMap<String, TraceMessage> messages = new HashMap<>();

    public void enable() {
        enabled = true;
    }

    public void disable() {
        enabled = false;
        messages.clear();
    }

    /**
     * 创建跟踪请求帧
     *
     * @param msgId   消息ID 必须，表示请求的唯一性
     * @param type    消息的类型，用于消息分类。
     * @param message 消息内容
     * @return 用于更新响应
     */
    public TraceRequestFrame traceRequestFrame(UUID msgId, String type, String message) {
        if (!enabled) return null;
        if (msgId == null) throw new RuntimeException("参数msgId不能为空");
        var cached = messages.computeIfAbsent(type, TraceMessage::new);
        return cached.createRequestFrame(msgId, message);
    }

    /**
     * 创建跟踪请求帧
     *
     * @param msgId   消息ID 必须，表示请求的唯一性
     * @param type    消息的类型，用于消息分类。
     * @param message 消息内容
     * @return 用于更新响应
     */
    public TraceRequestFrame traceRequestFrame(UUID msgId, String type, Object message) {
        if (!enabled) return null;
        if (message instanceof String strMessage) {
            return traceRequestFrame(msgId, type, strMessage);
        }
        if (msgId == null) throw new RuntimeException("参数msgId不能为空");
        var cached = messages.computeIfAbsent(type, TraceMessage::new);
        return cached.createRequestFrame(msgId, JsonHelper.toSafeJson(message));
    }

    /**
     * 为请求的消息跟踪帧写入响应数据
     *
     * @param reqMsgId 请求的消息ID
     * @param type     消息的类型，用于消息分类。
     * @param message  响应的消息内容
     */
    public void traceResponseFrame(UUID reqMsgId, String type, String message) {
        if (!enabled) return;
        if (reqMsgId == null) throw new RuntimeException("参数msgId不能为空");
        var cached = messages.computeIfAbsent(type, TraceMessage::new);
        cached.writeToResponse(reqMsgId, message);
    }

    /**
     * 为请求的消息跟踪帧写入异常数据
     *
     * @param reqMsgId  请求的消息ID
     * @param type      消息的类型，用于消息分类。
     * @param throwable 异常数据
     */
    public void traceResponseFrame(UUID reqMsgId, String type, Throwable throwable) {
        if (!enabled) return;
        if (reqMsgId == null) throw new RuntimeException("参数msgId不能为空");
        var cached = messages.computeIfAbsent(type, TraceMessage::new);
        cached.writeToResponse(reqMsgId, throwable);
    }


    public TraceJson toJson() {
        TraceJson result = new TraceJson();
        messages.forEach((k, v) -> {
            var list = Arrays.stream(v.getQueue()).filter(Objects::nonNull).toList();
            result.getStream().put(k, list);
        });
        return result;
    }


    public void clear() {
        messages.clear();
    }

    @Data
    public static class TraceJson implements ISerializableMessage {
        private Map<String, List<TraceRequestFrame>> stream = new HashMap<>();
    }


}

