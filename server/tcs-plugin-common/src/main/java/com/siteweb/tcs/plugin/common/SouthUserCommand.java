package com.siteweb.tcs.plugin.common;

import com.siteweb.tcs.common.sharding.IGatewayShardingMessage;
import lombok.Data;
import org.apache.pekko.actor.ActorRef;

/**
 * 来自用户的非生命周期类指令请求基类
 *
 * <AUTHOR> (2025-05-15)
 **/
@Data
public class SouthUserCommand implements IGatewayShardingMessage {
    private String gatewayId;

    /**
     * 发送人 可空
     */
    private ActorRef sender;


    /**
     * 提供方法直接返回数据
     * @param response
     */
    public void tell(UserCommandResponse response){
        if (sender != null) sender.tell(response, ActorRef.noSender());
    }





}
