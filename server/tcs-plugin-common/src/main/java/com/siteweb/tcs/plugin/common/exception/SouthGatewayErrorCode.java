package com.siteweb.tcs.plugin.common.exception;

import com.siteweb.tcs.common.exception.code.BusinessErrorCode;
import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;

/**
 * <AUTHOR> (2025-07-09)
 **/
public enum SouthGatewayErrorCode implements BusinessErrorCode {


    UNKNOWN_MESSAGE("UNKNOWN_MESSAGE", "未知的消息类型。"),
    GATEWAY_STOPED("GATEWAY_STOPED", "Gateway服务已停止。"),

    ;



    private final String code;
    private final String message;

    SouthGatewayErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
