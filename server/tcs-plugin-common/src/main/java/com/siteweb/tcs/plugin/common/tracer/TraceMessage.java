package com.siteweb.tcs.plugin.common.tracer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.UUID;

/**
 * <AUTHOR> (2025-07-29)
 **/
@Data

public class TraceMessage {

    /**
     * 报文类型
     */
    @JsonIgnore
    private final String type;

    @JsonIgnore
    private final int CACHE_SIZE = 5;
    private final TraceRequestFrame[] queue = new TraceRequestFrame[CACHE_SIZE];

    @JsonIgnore
    private final Object lockObject = new Object();


    public TraceMessage(String type) {
        this.type = type;
    }



    public TraceRequestFrame createRequestFrame(UUID msgId, String message) {
        var requestFrame = new TraceRequestFrame(msgId, message);
        appendFrame(requestFrame);
        return requestFrame;
    }


    public void writeToResponse(UUID msgId, String message) {
        var msg = getFrame(msgId);
        if (msg != null) {
            msg.writeResponse(message);
        }
    }

    public void writeToResponse(UUID msgId, Throwable exception) {
        var msg = getFrame(msgId);
        if (msg != null) {
            msg.writeException(exception);
        }
    }

    private TraceRequestFrame getFrame(UUID msgId) {
        var _q = queue;
        for (int i = 0; i < CACHE_SIZE; i++) {
            var msg = _q[i];
            if (msg != null && msgId.equals(msg.getMsgId())) {
                return msg;
            }
        }
        return null;
    }

    private void appendFrame(TraceRequestFrame requestFrame) {
        var _q = queue;
        synchronized (lockObject) {
            for (int i = 0; i < CACHE_SIZE - 1; i++) {
                _q[i] = _q[i + 1];
            }
            _q[CACHE_SIZE - 1] = requestFrame;
        }
    }

}
