package com.siteweb.tcs.plugin.common.util;

import lombok.Getter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> (2025-07-25)
 **/
public class DatetimeHelper {


    public enum DateFormat {
        DEFAULT("yyyyMMddHHmmss"),
        MS("yyMMddHHmmssSSS"),
        MINUTE("yyMMddHHmm"),
        HOUR("yyMMddHH"),
        DAY("yyMMdd"),
        YEAR("yy"),

        ;
        DateFormat(String format) {
            this.format = format;
        }

        @Getter
        private final String format;
    }


    /**
     * @param filenameFormat  如 "device_backup_{}.json"
     * @param dateFormat yyMMddHHmmss
     * @return
     */
    public static String generateFileName(String filenameFormat, DateFormat dateFormat) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat.getFormat());
        String timestamp = now.format(formatter);
        return filenameFormat.replaceFirst("\\{}", timestamp);
    }


}
