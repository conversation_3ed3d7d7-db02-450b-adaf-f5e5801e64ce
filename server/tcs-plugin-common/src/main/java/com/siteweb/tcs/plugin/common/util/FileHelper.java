package com.siteweb.tcs.plugin.common.util;

import com.siteweb.tcs.siteweb.util.JsonHelper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

/**
 * <AUTHOR> (2025-07-25)
 **/
public class FileHelper {


    public static void writeToFile(String filePath, String content) throws IOException {
        Path path = Paths.get(filePath);
        // 自动创建父目录
        Path parentDir = path.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }
        // 写入内容（覆盖写入）
        Files.write(path, content.getBytes(StandardCharsets.UTF_8), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
    }


    public static void writeToFile(String filePath, byte[] content) throws IOException {
        Path path = Paths.get(filePath);
        // 自动创建父目录
        Path parentDir = path.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }
        // 写入内容（覆盖写入）
        Files.write(path, content, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

    }

    /**
     * 从文件读取Json对象
     *
     * @param filePath 文件路径
     * @param clazz    类型
     * @param <T>
     * @return 返回null 表示文件不存在
     * @throws IOException
     */
    public static <T> T readJsonFromFile(String filePath, Class<T> clazz) throws IOException {
        Path path = Paths.get(filePath);
        ensureDirectoryExists(path.getParent());
        if (Files.exists(path)) {
            var content = Files.readString(path, StandardCharsets.UTF_8);
            return JsonHelper.readValue(content, clazz);
        }
        return null;
    }


    public static <T> void writeJsonFromFile(String filePath, T jsonObject) throws IOException {
        Path path = Paths.get(filePath);
        ensureDirectoryExists(path.getParent());
        var content = JsonHelper.toJson(jsonObject);
        Files.writeString(path, content, StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
    }


    public static void ensureDirectoryExists(Path path) throws IOException {
        if (path != null && !Files.exists(path)) {
            Files.createDirectories(path);
        }
    }


}
