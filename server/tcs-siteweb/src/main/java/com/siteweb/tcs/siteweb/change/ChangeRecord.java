package com.siteweb.tcs.siteweb.change;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import com.siteweb.tcs.siteweb.util.JsonHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 变更记录类
 * 
 * <AUTHOR> (2024-02-22)
 **/
@Data
@Slf4j
public class ChangeRecord {
    
    // 变更的信道
    private String channel;
    // 变更由哪个产品发布的
    private String product;
    // 此次变更的哪个数据
    private String dataSource;
    // 此次变更是什么操作
    private String operator;
    // 变更对象的主键
    private String primaryKey;
    // 变更消息对象
    private MqttMessage message;

    // Getter和Setter方法
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }

    public MqttMessage getMessage() {
        return message;
    }

    public void setMessage(MqttMessage message) {
        this.message = message;
    }

    /**
     * 读取变更消息对象
     *
     * <AUTHOR> (2024/3/9)
     */
    public ChangeMessage readChangeMessage() {
        try {
            return JsonHelper.readValue(new String(message.getPayload()), ChangeMessage.class);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * 读取变更消息的内容对象
     *
     * @param tyleClass 内容对象类型
     * <AUTHOR> (2024/3/14)
     */
    public <T> T readMessageBody(Class<T> tyleClass) {
        return readChangeMessage().readBody(tyleClass);
    }

    /**
     * 判断变更消息是否属于某个实体对象
     *
     * @param typeClass 对象类型 如 TBL_Equipment.class
     * <AUTHOR> (2024/3/15)
     */
    public <T> boolean isDataSource(Class<T> typeClass) {
        ChangeSource source = typeClass.getAnnotation(ChangeSource.class);
        if (source == null) {
            log.error("错误：{} 未实现 @ChangeSource注解", typeClass);
            throw new RuntimeException(typeClass.getName() + "未实现 @ChangeSource注解");
        }
        return source.source().equals(this.dataSource);
    }
} 