package com.siteweb.tcs.siteweb.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 配置下发任务请求DTO
 */
@Data
@Schema(description = "配置下发任务请求")
public class ConfigDistributionTaskRequest {

    @Schema(description = "监控单元ID列表", required = true, example = "[1, 2, 3]")
    private List<Integer> monitorUnitIds;

    @Schema(description = "FTP用户名", required = true, example = "root")
    private String username;

    @Schema(description = "FTP密码", required = true, example = "password")
    private String password;

    @Schema(description = "FTP端口", required = true, example = "21")
    private Integer port;

    @Schema(description = "协议类型", required = true, example = "ftp", allowableValues = {"ftp", "sftp"})
    private String protocol;

    @Schema(description = "用户ID", example = "admin")
    private String userId;
}
