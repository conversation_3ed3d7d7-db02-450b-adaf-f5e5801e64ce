package com.siteweb.tcs.siteweb.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备模板更改详情DTO
 * 从tcs-config迁移
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class EquipTemplateChangeDTO {
    /**
     * 设备名称
     */
    @ExcelProperty("设备名称")
    private String equipmentName;
    
    /**
     * 局站名称
     */
    @ExcelProperty("局站名称")
    private String stationName;
    
    @ExcelIgnore
    private Integer objectId;
    
    @ExcelProperty("影响配置名称")
    private String objectName;
    
    @ExcelProperty("影响配置类型")
    private String objectType;
    
    @ExcelProperty("影响配置描述")
    private String description;
    
    @ExcelIgnore
    private Integer objectChangeType;
}
