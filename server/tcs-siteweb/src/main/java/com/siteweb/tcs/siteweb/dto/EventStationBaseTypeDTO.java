package com.siteweb.tcs.siteweb.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventStationBaseTypeDTO {
    /**
     * 标准字典ID
     */
    private Integer standardDicId;
    /**
     * 设备逻辑类
     */
    private String equipmentLogicClass;
    /**
     * 事件逻辑类
     */
    private String eventLogicClass;
    /**
     * 事件标准名称
     */
    private String eventStandardName;
    /**
     * 告警解释
     */
    private String meanings;
    /**
     * 告警门限
     */
    private String compareValue;

    /**
     * 标准化局站类型id TBL_StationBaseType id
     */
    private Integer id;
    /**
     * 标准化局站类型
     */
    private String type;

    /**
     * 信号基础类别id 多个拼接
     */
    private String baseTypeId;
    /**
     * 基类信号名 多个拼接
     */
    private String baseTypeName;

    private Integer isDirty;

} 