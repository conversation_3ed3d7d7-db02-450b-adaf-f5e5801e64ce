package com.siteweb.tcs.siteweb.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.siteweb.tcs.siteweb.entity.MonitorUnit;
import com.siteweb.tcs.siteweb.entity.MonitorUnitProjectInfo;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 监控单元DTO
 */
@Data
public class MonitorUnitDTO extends MonitorUnit{

    private String projectName;
    private String contractNo;
    private LocalDateTime installTime;
    @TableField(exist = false)
    private Integer state;
    @TableField(exist = false)
    private String portNos;
    @TableField(exist = false)
    private String workStationName;

    @TableField(exist = false)
    private String stationName;

    public MonitorUnit toEntity(MonitorUnitDTO monitorUnitDTO){
        MonitorUnit MonitorUnit = new MonitorUnit();
        MonitorUnit.setMonitorUnitName(monitorUnitDTO.getMonitorUnitName());
        MonitorUnit.setRunMode(monitorUnitDTO.getRunMode()== null ? 1 : monitorUnitDTO.getRunMode());
        MonitorUnit.setSoftwareVersion(monitorUnitDTO.getSoftwareVersion());
        MonitorUnit.setDescription(monitorUnitDTO.getDescription());
        MonitorUnit.setEnable(monitorUnitDTO.getEnable());
        return MonitorUnit;
    }

    public MonitorUnitProjectInfo toProjectInfo(MonitorUnitDTO monitorUnitDTO){
        MonitorUnitProjectInfo projectInfo = new MonitorUnitProjectInfo();
        projectInfo.setMonitorUnitId(monitorUnitDTO.getMonitorUnitId());
        projectInfo.setProjectName(monitorUnitDTO.getProjectName());
        projectInfo.setContractNo(monitorUnitDTO.getContractNo());
        projectInfo.setStationId(monitorUnitDTO.getStationId());
        projectInfo.setInstallTime(monitorUnitDTO.getInstallTime());
        return projectInfo;
    }
}
