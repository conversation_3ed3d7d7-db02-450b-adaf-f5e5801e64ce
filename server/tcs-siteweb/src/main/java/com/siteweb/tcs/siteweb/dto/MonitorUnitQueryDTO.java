package com.siteweb.tcs.siteweb.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 监控单元查询筛选DTO
 */
@Data
public class MonitorUnitQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 监控单元名称（模糊查询）
     */
    private String monitorUnitName;

    /**
     * 监控单元类别
     */
    private Integer monitorUnitCategory;

    /**
     * IP地址（模糊查询）
     */
    private String ipAddress;

    /**
     * 连接状态
     */
    private Integer connectState;

    /**
     * 配置是否正确
     */
    private Boolean isConfigOK;

    /**
     * 是否同步
     */
    private Boolean isSync;

    /**
     * 运行模式
     */
    private Integer runMode;

    /**
     * 工作站ID（可选筛选条件）
     */
    private Integer workStationId;

    /**
     * 站点ID（可选筛选条件）
     */
    private Integer stationId;

    /**
     * 是否启用（可选筛选条件）
     */
    private Boolean enable;
}
