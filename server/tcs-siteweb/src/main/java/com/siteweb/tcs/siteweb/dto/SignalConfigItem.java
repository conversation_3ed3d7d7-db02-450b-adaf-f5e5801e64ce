package com.siteweb.tcs.siteweb.dto;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import com.siteweb.tcs.siteweb.entity.SignalProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Signal Configuration Item DTO
 */
@Data
public class SignalConfigItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    private Integer id;

    /**
     * Equipment template ID
     */
    @NotNull(message = "Equipment template ID cannot be null")
    private Integer equipmentTemplateId;

    /**
     * Signal ID
     */
    private Integer signalId;

    /**
     * Enable status
     */
    private Boolean enable;

    /**
     * Visibility
     */
    private Boolean visible;

    /**
     * Description
     */
    private String description;

    /**
     * Signal name
     */
    private String signalName;

    /**
     * Signal category
     */
    private Integer signalCategory;

    /**
     * Signal type
     */
    private Integer signalType;

    /**
     * Channel number
     */
    private Integer channelNo;

    /**
     * Channel type
     */
    private Integer channelType;

    /**
     * Expression
     */
    private String expression;

    /**
     * Data type
     */
    private Integer dataType;

    /**
     * Show precision
     */
    private String showPrecision;

    /**
     * Unit
     */
    private String unit;

    /**
     * Store interval
     */
    private Double storeInterval;

    /**
     * Absolute value threshold
     */
    private Double absValueThreshold;

    /**
     * Percent threshold
     */
    private Double percentThreshold;

    /**
     * Statistics period
     */
    private Integer staticsPeriod;

    /**
     * Base type ID
     */
    private Long baseTypeId;

    /**
     * Charge store interval
     */
    private Double chargeStoreInterVal;

    /**
     * Charge absolute value
     */
    private Double chargeAbsValue;

    /**
     * Display index
     */
    private Integer displayIndex;

    /**
     * MDB Signal ID
     */
    private Integer mdbSignalId;

    /**
     * Module number
     */
    private Integer moduleNo;
    
    /**
     * Signal meanings list
     */
    private List<SignalMeanings> signalMeaningsList;
    
    /**
     * Signal property list
     */
    private List<SignalProperty> signalPropertyList;
    
    /**
     * Flag indicating if this is an across signal
     */
    private Boolean acrossSignal;

    @JsonIgnore
    public String getSignalPropertyXmlFormat() {
        if (CollUtil.isEmpty(this.signalPropertyList)) {
            return "";
        }
        return this.signalPropertyList.stream()
                .map(e -> String.valueOf(e.getSignalPropertyId()))
                .collect(Collectors.joining(","));
    }

    @JsonIgnore
    public String getSignalMeaningsXmlFormat() {
        if (CollUtil.isEmpty(this.signalMeaningsList)) {
            return "";
        }
        return this.signalMeaningsList.stream()
                .filter(e -> Objects.nonNull(e.getStateValue()) && Objects.nonNull(e.getMeanings()))
                .map(e -> e.getStateValue() + ":" + e.getMeanings())
                .collect(Collectors.joining(";"));
    }

    @JsonIgnore
    public List<SignalMeanings> getNotEmptySignalMeaningsList() {
        if (CollUtil.isEmpty(this.signalMeaningsList)) {
            return List.of(new SignalMeanings());
        }
        return signalMeaningsList;
    }
} 