package com.siteweb.tcs.siteweb.dto.excel;

/**
 * @Description:
 */

import lombok.Getter;

import java.util.Collection;
import java.util.Collections;
import java.util.Objects;

@Getter
public class SheetDataWrapper {
    private final Class<?> headClass;
    private final Collection<?> data;

    public SheetDataWrapper(Class<?> headClass, Collection<?> data) {
        Objects.requireNonNull(headClass, "Header class cannot be null");
        this.headClass = headClass;
        // 如果 data 为 null，视作空集合处理
        this.data = (data == null) ? Collections.emptyList() : data;
    }

    public static SheetDataWrapper of(Class<?> headClass, Collection<?> data){
        return new SheetDataWrapper(headClass, data);
    }
}

