package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.io.Serializable;

/**
 * 基础设备分类映射实体类
 */
@Data
@TableName("tbl_baseequipmentcategorymap")
@AllArgsConstructor
public class BaseEquipmentCategoryMap implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基础设备ID (复合主键)
     */
    @TableField(value = "BaseEquipmentID")
    private Integer baseEquipmentId;

    /**
     * 设备分类 (复合主键)
     */
    @TableField(value = "EquipmentCategory")
    private Integer equipmentCategory;
}