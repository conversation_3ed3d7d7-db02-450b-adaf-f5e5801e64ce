package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Complex index entity
 */
@Data
@TableName("complexindex")
public class ComplexIndex implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "complexindexid", type = IdType.AUTO)
    private Integer complexIndexId;

    @TableField("complexindexname")
    private String complexIndexName;

    @TableField("complexindexdefinitionid")
    private Integer complexIndexDefinitionId;

    @TableField("objectid")
    private Integer objectId;

    @TableField("calccron")
    private String calcCron;

    @TableField("calctype")
    private Integer calcType;

    @TableField("aftercalc")
    private String afterCalc;

    @TableField("savecron")
    private String saveCron;

    @TableField("expression")
    private String expression;

    @TableField("unit")
    private String unit;

    @TableField("accuracy")
    private String accuracy;

    @TableField("objecttypeid")
    private Integer objectTypeId;

    @TableField("remark")
    private String remark;

    @TableField("label")
    private String label;

    @TableField("businesstypeid")
    private Integer businessTypeId;

    @TableField("checkexpression")
    private String checkExpression;
}
