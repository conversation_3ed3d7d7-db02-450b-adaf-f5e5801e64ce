package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Config change macro log entity
 */
@Data
@TableName("tbl_configchangemacrolog")
public class ConfigChangeMacroLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ObjectId")
    private String objectId;

    @TableField("ConfigId")
    private Integer configId;

    @TableField("EditType")
    private Integer editType;

    @TableField("UpdateTime")
    private LocalDateTime updateTime;
}
