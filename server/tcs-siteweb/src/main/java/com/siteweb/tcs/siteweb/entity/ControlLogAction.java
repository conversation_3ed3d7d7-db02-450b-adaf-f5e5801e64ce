package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Control log action entity
 */
@Data
@TableName("tbl_controllogaction")
public class ControlLogAction implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "LogActionId")
    private Integer logActionId;

    @TableField("ActionId")
    private Integer actionId;

    @TableField("ActionName")
    private String actionName;

    @TableField("EquipmentId")
    private Integer equipmentId;

    @TableField("ControlId")
    private Integer controlId;

    @TableField("ActionValue")
    private String actionValue;
}
