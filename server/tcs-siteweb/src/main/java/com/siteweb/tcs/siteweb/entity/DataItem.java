package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.dto.CenterDTO;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Data item entity
 */
@Data
@TableName("tbl_dataitem")
@NoArgsConstructor
public class DataItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "EntryItemId")
    private Integer entryItemId;

    @TableField("ParentEntryId")
    private Integer parentEntryId;

    @TableField("ParentItemId")
    private Integer parentItemId;

    @TableField("EntryId")
    private Integer entryId;

    @TableField("ItemId")
    private Integer itemId;

    @TableField("ItemValue")
    private String itemValue;

    @TableField("ItemAlias")
    private String itemAlias;

    @TableField("Enable")
    private Boolean enable;

    @TableField("IsSystem")
    private Boolean isSystem;

    @TableField("IsDefault")
    private Boolean isDefault;

    @TableField("Description")
    private String description;

    @TableField("ExtendField1")
    private String extendField1;

    @TableField("ExtendField2")
    private String extendField2;

    @TableField("ExtendField3")
    private String extendField3;

    @TableField("ExtendField4")
    private String extendField4;

    @TableField("ExtendField5")
    private String extendField5;

    public DataItem(Integer entryId, Integer itemId, Integer parentEntryId, Integer parentItemId, Boolean isSystem,
                       String itemValue, String description, String itemAlias, String extendField1, String extendField2,
                       String extendField3) {
        this.entryId = entryId;
        this.itemId = itemId;
        this.parentEntryId = parentEntryId;
        this.parentItemId = parentItemId;
        this.isSystem = isSystem;
        this.itemValue = itemValue;
        this.description = description;
        this.itemAlias = itemAlias;
        this.extendField1 = extendField1;
        this.extendField2 = extendField2;
        this.extendField3 = extendField3;
    }
}
