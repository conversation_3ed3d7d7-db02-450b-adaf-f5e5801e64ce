package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;

/**
 * Door entity
 */
@Data
@TableName("tbl_door")
@ChangeSource(channel = "tcs", product = "siteweb", source = "door")
public class Door implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DoorId")
    private Integer doorId;

    @TableField("DoorNo")
    private Integer doorNo;

    @TableField("DoorName")
    private String doorName;

    @TableField("StationId")
    private Integer stationId;

    @TableField("EquipmentId")
    private Integer equipmentId;

    @TableField("SamplerUnitId")
    private Integer samplerUnitId;

    @TableField("Category")
    private Integer category;

    @TableField("Address")
    private String address;

    @TableField("WorkMode")
    private Integer workMode;

    @TableField("Infrared")
    private Integer infrared;

    @TableField("Password")
    private String password;

    @TableField("DoorControlId")
    private Integer doorControlId;

    @TableField("DoorInterval")
    private Integer doorInterval;

    @TableField("OpenDelay")
    private Integer openDelay;

    @TableField("Description")
    private String description;

    @TableField("OpenMode")
    private Integer openMode;
}
