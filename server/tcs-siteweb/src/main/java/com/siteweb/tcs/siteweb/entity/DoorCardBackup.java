package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Door Card Backup entity
 */
@Data
@TableName("tbl_door_card_backup")
public class DoorCardBackup implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    @TableField("card_id")
    private Integer cardId;

    @TableField("equipment_id")
    private Integer equipmentId;

    @TableField("equipment_name")
    private String equipmentName;

    @TableField("backup_time")
    private LocalDateTime backupTime;
} 