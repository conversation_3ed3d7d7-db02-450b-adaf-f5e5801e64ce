package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Door time group entity
 */
@Data
@TableName("tbl_doortimegroup")
public class DoorTimeGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DoorId")
    private Integer doorId;

    @TableField("TimeGroupId")
    private Integer timeGroupId;

    @TableField("TimeGroupType")
    private Integer timeGroupType;
}
