package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Equipment project info entity
 */
@Data
@TableName("tbl_equipmentprojectinfo")
public class EquipmentProjectInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StationId")
    private Integer stationId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("EquipmentId")
    private Integer equipmentId;

    @TableField("ProjectName")
    private String projectName;

    @TableField("ContractNo")
    private String contractNo;

    @TableField("InstallTime")
    private LocalDateTime installTime;

    @TableField("EquipmentSN")
    private String equipmentSN;

    @TableField("SO")
    private String so;
}
