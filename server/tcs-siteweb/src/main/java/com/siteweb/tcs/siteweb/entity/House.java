package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * House entity
 */
@Data
@TableName("tbl_house")
@ChangeSource(channel = "tcs", product = "siteweb", source = "house")
public class House implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "HouseId")
    private Integer houseId;

    @TableField("StationId")
    private Integer stationId;

    @TableField("HouseName")
    private String houseName;

    @TableField("Description")
    private String description;

    @TableField("LastUpdateDate")
    private LocalDateTime lastUpdateDate;
}
