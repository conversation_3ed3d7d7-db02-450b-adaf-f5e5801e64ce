package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Log inform list entity
 */
@Data
@TableName("tbl_loginformlist")
public class LogInformList implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("LogActionId")
    private Integer logActionId;

    @TableField("InformerId")
    private Integer informerId;

    @TableField("UserId")
    private Integer userId;

    @TableField("InfoType")
    private Integer infoType;

    @TableField("Description")
    private String description;
}
