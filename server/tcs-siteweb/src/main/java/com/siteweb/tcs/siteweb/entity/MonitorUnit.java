package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Monitor unit entity
 */
@Data
@TableName("tsl_monitorunit")
@ChangeSource(channel = "tcs", product = "siteweb", source = "monitorunit")
public class MonitorUnit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("MonitorUnitName")
    private String monitorUnitName;

    @TableField("MonitorUnitCategory")
    private Integer monitorUnitCategory;

    @TableField("MonitorUnitCode")
    private String monitorUnitCode;

    @TableField("WorkStationId")
    private Integer workStationId;

    @TableField("StationId")
    private Integer stationId;

    @TableField("IpAddress")
    private String ipAddress;

    @TableField("RunMode")
    private Integer runMode;

    @TableField("ConfigFileCode")
    private String configFileCode;

    @TableField("ConfigUpdateTime")
    private LocalDateTime configUpdateTime;

    @TableField("SampleConfigCode")
    private String sampleConfigCode;

    @TableField("SoftwareVersion")
    private String softwareVersion;

    @TableField("Description")
    private String description;

    @TableField("StartTime")
    private LocalDateTime startTime;

    @TableField("HeartbeatTime")
    private LocalDateTime heartbeatTime;

    @TableField("ConnectState")
    private Integer connectState;

    @TableField("UpdateTime")
    private LocalDateTime updateTime;

    @TableField("IsSync")
    private Boolean isSync;

    @TableField("SyncTime")
    private LocalDateTime syncTime;

    @TableField("IsConfigOK")
    private Boolean isConfigOK;

    @TableField("ConfigFileCode_Old")
    private String configFileCode_Old;

    @TableField("SampleConfigCode_Old")
    private String sampleConfigCode_Old;

    @TableField("AppCongfigId")
    private Integer appConfigId;

    @TableField("CanDistribute")
    private Boolean canDistribute;

    @TableField("Enable")
    private Boolean enable;

    /**
     * RDS服务器
     */
    @TableField("projectName")
    private String rdsServer;

    /**
     * 数据服务器
     */
    @TableField("contractNo")
    private String dataServer;

    @TableField("InstallTime")
    private LocalDateTime installTime;

    @TableField("FSU")
    private Boolean fsu;
}
