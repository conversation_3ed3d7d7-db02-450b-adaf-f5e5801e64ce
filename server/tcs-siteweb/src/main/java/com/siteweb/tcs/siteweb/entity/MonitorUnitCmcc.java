package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 监控单元实体类 (CMCC)
 */
@Data
@TableName("tsl_monitorunitcmcc")
public class MonitorUnitCmcc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID (复合主键)
     */
    @TableField("StationId")
    private Integer stationId;

    /**
     * 监控单元ID (复合主键)
     */
    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("FSUID")
    private String fsuid;

    @TableField("FSUName")
    private String fsuName;

    @TableField("SiteID")
    private String siteId;

    @TableField("SiteName")
    private String siteName;

    @TableField("RoomID")
    private String roomId;

    @TableField("RoomName")
    private String roomName;

    @TableField("UserName")
    private String userName;

    @TableField("PassWord")
    private String passWord;

    @TableField("FSUIP")
    private String fsuip;

    @TableField("FSUMAC")
    private String fsumac;

    @TableField("FSUVER")
    private String fsuver;

    @TableField("Result")
    private Integer result;

    @TableField("FailureCause")
    private String failureCause;

    @TableField("CPUUsage")
    private Double cpuUsage;

    @TableField("MEMUsage")
    private Double memUsage;

    @TableField("HardDiskUsage")
    private Double hardDiskUsage;

    @TableField("GetFSUInfoResult")
    private Integer getFsuInfoResult;

    @TableField("GetFSUFaliureCause")
    private String getFsuFaliureCause;

    @TableField("GetFSUTime")
    private LocalDateTime getFsuTime;

    @TableField("FTPUserName")
    private String ftpUserName;

    @TableField("FTPPassWord")
    private String ftpPassWord;

    @TableField("ExtendField1")
    private String extendField1;

    @TableField("ExtendField2")
    private String extendField2;

    @TableField("GetConfigFlag")
    private Integer getConfigFlag;
}