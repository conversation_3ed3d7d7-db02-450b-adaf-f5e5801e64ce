package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Monitor unit config entity
 */
@Data
@TableName("tsl_monitorunitconfig")
public class MonitorUnitConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("AppConfigId")
    private Integer appConfigId;

    @TableField("SiteWebTimeOut")
    private Integer siteWebTimeOut;

    @TableField("RetryTimes")
    private Integer retryTimes;

    @TableField("HeartBeat")
    private Integer heartBeat;

    @TableField("EquipmentTimeOut")
    private Integer equipmentTimeOut;

    @TableField("PortInterruptCount")
    private Integer portInterruptCount;

    @TableField("PortInitializeInternal")
    private Integer portInitializeInternal;

    @TableField("MaxPortInitializeTimes")
    private Integer maxPortInitializeTimes;

    @TableField("PortQueryTimeOut")
    private Integer portQueryTimeOut;

    @TableField("DataSaveTimes")
    private Integer dataSaveTimes;

    @TableField("HistorySignalSavedTimes")
    private Integer historySignalSavedTimes;

    @TableField("HistoryBatterySavedTimes")
    private Integer historyBatterySavedTimes;

    @TableField("HistoryEventSavedTimes")
    private Integer historyEventSavedTimes;

    @TableField("CardRecordSavedCount")
    private Integer cardRecordSavedCount;

    @TableField("ControlLog")
    private Boolean controlLog;

    @TableField("IpAddressDS")
    private String ipAddressDS;
}
