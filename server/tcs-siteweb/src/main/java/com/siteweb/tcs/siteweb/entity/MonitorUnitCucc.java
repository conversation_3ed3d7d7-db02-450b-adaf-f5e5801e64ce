package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 监控单元实体类 (cucc)
 */
@Data
@TableName("tsl_monitorunitcucc")
public class MonitorUnitCucc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID (复合主键)
     */
    @TableField("StationId")
    private Integer stationId;

    /**
     * 监控单元ID (复合主键)
     */
    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("SUID")
    private String suid;

    @TableField("SUName")
    private String suName;

    @TableField("SURID")
    private String surid;

    @TableField("UserName")
    private String userName;

    @TableField("PassWord")
    private String passWord;

    @TableField("SUIP")
    private String suip;

    @TableField("SUVER")
    private String suver;

    @TableField("SUPort")
    private String suPort;

    @TableField("SUVendor")
    private String suVendor;

    @TableField("SUModel")
    private String suModel;

    @TableField("SUHardVER")
    private String suHardVer;

    @TableField("Longitude")
    private Double longitude;

    @TableField("Latitude")
    private Double latitude;

    @TableField("Result")
    private Integer result;

    @TableField("FailureCause")
    private String failureCause;

    @TableField("CPUUsage")
    private Double cpuUsage;

    @TableField("MEMUsage")
    private Double memUsage;

    @TableField("GetSUInfoResult")
    private Integer getSuInfoResult;

    @TableField("GetSUTime")
    private LocalDateTime getSuTime;

    @TableField("FTPUserName")
    private String ftpUserName;

    @TableField("FTPPassWord")
    private String ftpPassWord;

    @TableField("ConfigState")
    private Integer configState;

    @TableField("RegisterTime")
    private LocalDateTime registerTime;

    @TableField("SUConfigTime")
    private String suConfigTime;

    @TableField("CenterConfigTime")
    private String centerConfigTime;

    @TableField("Devices")
    private String devices; // Assuming TEXT maps to String

    @TableField("ExtendField1")
    private String extendField1;

    @TableField("ExtendField2")
    private String extendField2;
}