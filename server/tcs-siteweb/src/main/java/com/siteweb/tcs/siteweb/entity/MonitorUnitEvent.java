package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Monitor unit event entity
 */
@Data
@TableName("tsl_monitorunitevent")
public class MonitorUnitEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StationId")
    private Integer stationId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableId(value = "EquipmentId")
    private Integer equipmentId;

    @TableField("EventId")
    private Integer eventId;

    @TableField("StartExpression")
    private String startExpression;

    @TableField("SuppressExpression")
    private String suppressExpression;
}
