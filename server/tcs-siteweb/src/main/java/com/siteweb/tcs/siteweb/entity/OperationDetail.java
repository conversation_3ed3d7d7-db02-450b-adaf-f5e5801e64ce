package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Operation detail entity
 */
@Data
@TableName("tbl_operationdetail")
public class OperationDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("UserId")
    private Integer userId;

    @TableField("UserName")
    private String userName;

    @TableField("ObjectId")
    private String objectId;

    @TableField("ObjectType")
    private Integer objectType;

    @TableField("PropertyName")
    private String propertyName;

    @TableField("OperationTime")
    private LocalDateTime operationTime;

    @TableField("OperationType")
    private String operationType;

    @TableField("OldValue")
    private String oldValue;

    @TableField("NewValue")
    private String newValue;
}
