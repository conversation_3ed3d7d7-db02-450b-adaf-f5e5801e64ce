package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Primary key identity entity
 */
@Data
@TableName("tbl_primarykeyidentity")
public class PrimaryKeyIdentity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "TableId")
    private Integer tableId;

    @TableField("TableName")
    private String tableName;

    @TableField("Description")
    private String description;
}
