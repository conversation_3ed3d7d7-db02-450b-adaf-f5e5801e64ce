package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Primary key value entity
 */
@Data
@TableName("tbl_primarykeyvalue")
public class PrimaryKeyValue implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "TableId")
    private Integer tableId;

    @TableField("PostalCode")
    private Integer postalCode;

    @TableField("MinValue")
    private Integer minValue;

    @TableField("CurrentValue")
    private Integer currentValue;
}
