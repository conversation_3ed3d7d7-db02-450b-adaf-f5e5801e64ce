package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 角色权限映射实体类
 * Role permission mapping table
 */
@Data
@TableName("rolepermissionmap")
public class RolePermissionMap implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 映射ID，主键，自增
     */
    @TableId(value = "rolepermissionmapid", type = IdType.AUTO)
    private Integer rolePermissionMapId;

    /**
     * 角色ID
     */
    @TableField("roleid")
    private Integer roleId;

    /**
     * 权限分类ID
     */
    @TableField("permissioncategoryid")
    private Integer permissionCategoryId;

    /**
     * 权限ID
     */
    @TableField("permissionid")
    private Integer permissionId;
}