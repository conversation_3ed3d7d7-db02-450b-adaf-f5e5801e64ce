package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Sampler entity
 */
@Data
@TableName("tsl_sampler")
@Builder
@ChangeSource(channel = "tcs", product = "siteweb", source = "sampler")
@AllArgsConstructor
@NoArgsConstructor
public class Sampler implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "SamplerId", type = IdType.AUTO)
    private Integer samplerId;

    @TableField("SamplerName")
    private String samplerName;

    @TableField("SamplerType")
    private Short samplerType;

    @TableField("ProtocolCode")
    private String protocolCode;

    @TableField("DLLCode")
    private String dllCode;

    @TableField("DLLVersion")
    private String dllVersion;

    @TableField("ProtocolFilePath")
    private String protocolFilePath;

    @TableField("DLLFilePath")
    private String dllFilePath;

    @TableField("DllPath")
    private String dllPath;

    @TableField("Setting")
    private String setting;

    @TableField("Description")
    private String description;

    @TableField("SoCode")
    private String soCode;

    @TableField("SoPath")
    private String soPath;

    @TableField("UploadProtocolFile")
    private Boolean uploadProtocolFile;
}
