package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Standard type entity
 */
@Data
@TableName("tbl_standardtype")
public class StandardType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StandardId")
    private Integer standardId;

    @TableField("StandardName")
    private String standardName;

    @TableField("StandardAlias")
    private String standardAlias;

    @TableField("Remark")
    private String remark;
}
