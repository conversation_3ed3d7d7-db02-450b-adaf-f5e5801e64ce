package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Station mask entity
 */
@Data
@TableName("tbl_stationmask")
public class StationMask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StationId")
    private Integer stationId;

    @TableField("TimeGroupId")
    private Integer timeGroupId;

    @TableField("Reason")
    private String reason;

    @TableField("StartTime")
    private LocalDateTime startTime;

    @TableField("EndTime")
    private LocalDateTime endTime;

    @TableField("UserId")
    private Integer userId;
}
