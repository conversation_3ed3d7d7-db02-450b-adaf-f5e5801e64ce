package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Station structure entity
 */
@Data
@TableName("tbl_stationstructure")
@ChangeSource(channel = "tcs", product = "siteweb", source = "stationstructure")
public class StationStructure implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StructureId")
    private Integer structureId;

    @TableField("StructureGroupId")
    private Integer structureGroupId;

    @TableField("ParentStructureId")
    private Integer parentStructureId;

    @TableField("StructureName")
    private String structureName;

    @TableField("IsUngroup")
    private Boolean isUngroup;

    @TableField("StructureType")
    private Integer structureType;

    @TableField("MapZoom")
    private Double mapZoom;

    @TableField("Longitude")
    private BigDecimal longitude;

    @TableField("Latitude")
    private BigDecimal latitude;

    @TableField("Description")
    private String description;

    @TableField("LevelPath")
    private String levelPath;

    @TableField("Enable")
    private Boolean enable;
}
