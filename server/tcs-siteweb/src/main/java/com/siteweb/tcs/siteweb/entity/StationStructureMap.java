package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Station structure map entity
 */
@Data
@TableName("tbl_stationstructuremap")
public class StationStructureMap implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StructureId")
    private Integer structureId;

    @TableId(value = "StationId")
    private Integer stationId;
}
