package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Swatch station entity
 */
@Data
@TableName("tbl_swatchstation")
public class SwatchStation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "SwatchStationId", type = IdType.AUTO)
    private Integer swatchStationId;

    @TableField("SwatchStationName")
    private String swatchStationName;

    @TableField("StationId")
    private Integer stationId;

    @TableField("CreateTime")
    private LocalDateTime createTime;

    @TableField("Description")
    private String description;
}
