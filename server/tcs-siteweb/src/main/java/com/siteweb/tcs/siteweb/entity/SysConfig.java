package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * System config entity
 */
@Data
@TableName("tbl_sysconfig")
public class SysConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ConfigKey")
    private String configKey;

    @TableField("ConfigValue")
    private String configValue;
}
