package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 监控单元信号实体类
 */
@Data
@TableName("tsl_monitorunitsignal")
public class TslMonitorUnitSignal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StationId")
    private Integer stationId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("EquipmentId")
    private Integer equipmentId;

    @TableField("SignalId")
    private Integer signalId;

    @TableField("ReferenceSamplerUnitId")
    private Integer referenceSamplerUnitId;

    @TableField("ReferenceChannelNo")
    private Integer referenceChannelNo;

    @TableField("Expression")
    private String expression;

    @TableField("InstanceType")
    private Integer instanceType;
}