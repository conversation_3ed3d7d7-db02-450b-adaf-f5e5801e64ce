package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Write back entry entity
 */
@Data
@TableName("tbl_writebackentry")
public class WriteBackEntry implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "EntryId")
    private Integer entryId;

    @TableField("EntryCategory")
    private Integer entryCategory;

    @TableField("EntryName")
    private String entryName;

    @TableField("EntryTitle")
    private String entryTitle;

    @TableField("EntryAlias")
    private String entryAlias;

    @TableField("Enable")
    private Boolean enable;

    @TableField("Description")
    private String description;
}
