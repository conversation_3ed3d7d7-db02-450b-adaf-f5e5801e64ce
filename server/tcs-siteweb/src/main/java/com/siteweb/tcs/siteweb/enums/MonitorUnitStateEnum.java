package com.siteweb.tcs.siteweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事件开始类型枚举
 * 对应数据库表 tbl_dataitem表EntryId = 25
 */
@Getter
@AllArgsConstructor
public enum MonitorUnitStateEnum {
    NO_NEED_TO_SEND(0, "无需下发"),
    PENDING(1, "待下发"),
    SENDING(2, "正在下发"),
    SUCCESS(3, "下发成功"),
    FAILURE(4, "下发失败");
    private final int value;
    private final String describe;
}
