package com.siteweb.tcs.siteweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 操作对象类型枚举
 */
@Getter
@AllArgsConstructor
public enum OperationObjectTypeEnum {
    MONITOR_CENTER(1, "监控中心", "MON<PERSON>ORCENTER"),
    STATION_STRUCTURE(2, "局站分组", "STATIONSTRUCTURE"),
    HOUSE(3, "局房", "HOUSE"),
    WORK_STATION(4, "工作站", "WORKSTATION"),
    STATION(5, "局站", "STATION"),
    MONITOR_UNIT(6, "监控单元", "<PERSON><PERSON><PERSON>ORUN<PERSON>"),
    PORT(7, "端口", "PORT"),
    SAMPLER_UNIT(8, "采集单元", "SAMPLERUNIT"),
    SAMPLER(9, "采集器", "SAMPLER"),
    EQUIPMENT_TEMPLATE(10, "设备模板", "EQUIPMENTTEMPLATE"),
    EQUIPMENT(11, "设备", "EQUIPMENT"),
    SIGNAL(12, "信号", "SIGNAL"),
    EVENT(15, "事件", "EVENT"),
    CONTROL(17, "控制", "CONTROL"),
    CONTROL_MEANINGS(18, "控制含义", "CONTROLMEANINGS"),
    // SwatchStation
    SWATCH_STATION(19, "样板站", "SWATCHSTATION"),
    // 从50开始，不和CS配置工具冲突
    RESOURCE_STRUCTURE(50, "层级", "RESOURCESTRUCTURE"),
    STANDARD(31,"标准化","STANDARD"),
    SIGNAL_STANDARD(51,"信号标准化","SIGNALSTANDARD"),
    EVENT_STANDARD(52,"告警标准化", "EVENTSTANDARD"),
    CONTROL_STANDARD(53,"控制标准化", "CONTROLSTANDARD");
    private static final ConcurrentHashMap<Integer, String> OPERATION_OBJECT_TYPE_MAP = new ConcurrentHashMap<>();
    static {
        for (OperationObjectTypeEnum type : OperationObjectTypeEnum.values()) {
            OPERATION_OBJECT_TYPE_MAP.put(type.getValue(), type.getDescribe());
        }
    }
    private final int value;
    private final String describe;
    private final String className;
    public static String getDescribeByKey(Integer key) {
        return OPERATION_OBJECT_TYPE_MAP.get(key);
    }
}
