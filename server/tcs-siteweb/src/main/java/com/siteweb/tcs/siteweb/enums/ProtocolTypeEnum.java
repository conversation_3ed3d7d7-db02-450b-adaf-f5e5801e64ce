package com.siteweb.tcs.siteweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 采集器架构类型枚举
 * 用于区分不同架构的采集器，处理动态库兼容性问题
 */
@Getter
@AllArgsConstructor
public enum ProtocolTypeEnum {

    /**
     * 新架构(335X)采集器
     * 历史原因 新架构的libPath直接放入到protocol目录下
     */
    ARCHITECTURE_335X(1, "335X", "新架构采集器", ""),

    /**
     * 老架构(9200)采集器
     * 旧架构的libPath直接放入到protocol/9200目录下
     */
    ARCHITECTURE_9200(2, "9200", "老架构采集器", "9200");

    private final int protocolType;

    /**
     * 架构代号
     */
    private final String code;

    /**
     * 架构描述
     */
    private final String description;

    /**
     * 动态库存储路径
     */
    private final String libPath;

    /**
     * 根据架构代号获取对应的枚举值
     *
     * @param protocolType 架构类型
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static ProtocolTypeEnum getByProtocolType(Integer protocolType) {
        for (ProtocolTypeEnum type : values()) {
            if (Objects.equals(type.getProtocolType(), protocolType)) {
                return type;
            }
        }
        return null;
    }
}

