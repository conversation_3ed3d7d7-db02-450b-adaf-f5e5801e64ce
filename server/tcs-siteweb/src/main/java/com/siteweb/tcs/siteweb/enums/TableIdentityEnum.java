package com.siteweb.tcs.siteweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 部分需要通过查表获取主键的枚举
 */
@Getter
@AllArgsConstructor
public enum TableIdentityEnum {
    TBL_EQUIPMENT_TEMPLATE("TBL_EquipmentTemplate", "设备模板表"),
    TBL_DATA_ITEM("TBL_DataItem", "数据项"),
    TSL_SAMPLER("TSL_Sampler", "采集器表"),
    TBL_SIGNAL("TBL_Signal", "信号表"),
    TBL_EVENT("TBL_Event", "告警表"),
    TBL_CONTROL("TBL_Control", "控制表"),
    TBL_EVENT_CONDITION("TBL_EventCondition", "告警条件表"),
    TBL_STATION_STRUCTURE("TBL_StationStructure", "局站结构"),
    TBL_WORKSTATION("TBL_WorkStation", "服务器"),
    TBL_STATION("TBL_Station", "局站"),
    RESOURCE_STRUCTURE("resourcestructure", "管理结构"),
    TBL_HOUSE("TBL_House", "局房"),
    TSL_MONITOR_UNIT("TSL_MonitorUnit", "监控单元"),
    TSL_PORT("TSL_Port", "端口"),
    TBL_EQUIPMENT("TBL_Equipment","设备"),
    TSL_SAMPLER_UNIT("TSL_SamplerUnit", "采集单元"),
    TBL_EVENT_LOG_ACTION("TBL_EventLogAction", "告警联动"),
    TBL_DOOR("TBL_Door", "门设备"),
    TBL_ACCOUNT("tbl_account","账号表"),
    TBL_SIGNAL_BASE_DIC("tbl_signalbasedic","信号基类字典"),
    TBL_EVENT_BASE_DIC("tbl_eventbasedic","事件基类字典"),
    TBL_COMMAND_BASE_DIC("tbl_commandbasedic","控制基类字典"),
    TBL_STATION_PROJECT_INFO("tbl_stationprojectinfo","局站工程信息表"),
    TBL_STATION_MASK("tbl_stationmask","局站屏蔽表"),
    TBL_MONITOR_UNIT_PROJECT_INFO("tbl_monitorunitprojectinfo","监控单元工程信息表"),
    TBL_EQUIPMENT_PROJECT_INFO("tbl_equipmentprojectinfo","设备工程信息表"),
    TBL_SYSCONFIG("tbl_sysconfig","系统配置表")
    ;
    private final String tableName;
    private final String description;
}
