package com.siteweb.tcs.siteweb.enums;

/**
 * 任务状态枚举
 */
public enum TaskStatusEnum {

    /**
     * 等待中
     */
    PENDING("PENDING", "等待中"),

    /**
     * 运行中
     */
    RUNNING("RUNNING", "运行中"),

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 失败
     */
    FAILED("FAILED", "失败"),

    /**
     * 已完成 - 全部成功
     */
    COMPLETED("COMPLETED", "已完成"),

    /**
     * 部分成功
     */
    COMPLETED_PARTIAL("COMPLETED_PARTIAL", "部分成功"),

    /**
     * 全部失败
     */
    COMPLETED_FAILED("COMPLETED_FAILED", "全部失败"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String description;

    TaskStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static TaskStatusEnum fromCode(String code) {
        for (TaskStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown task status code: " + code);
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinal() {
        return this == COMPLETED || this == COMPLETED_PARTIAL ||
                this == COMPLETED_FAILED || this == SUCCESS ||
                this == FAILED || this == CANCELLED;
    }

    /**
     * 根据执行结果确定任务状态
     * @param totalCount 总数量
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @return 任务状态
     */
    public static TaskStatusEnum determineByResult(int totalCount, int successCount, int failureCount) {
        if (totalCount == 0) {
            return FAILED;
        }

        if (failureCount == 0) {
            return COMPLETED;  // 全部成功
        } else if (successCount > 0) {
            return COMPLETED_PARTIAL;  // 部分成功
        } else {
            return COMPLETED_FAILED;   // 全部失败
        }
    }
}
