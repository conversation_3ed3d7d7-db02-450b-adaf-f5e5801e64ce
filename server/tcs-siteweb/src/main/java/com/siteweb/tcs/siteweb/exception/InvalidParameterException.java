package com.siteweb.tcs.siteweb.exception;

/**
 * Exception thrown when a parameter is invalid
 */
public class InvalidParameterException extends RuntimeException {
    
    /**
     * Constructor
     *
     * @param message Error message
     */
    public InvalidParameterException(String message) {
        super(message);
    }
    
    /**
     * Constructor
     *
     * @param message Error message
     * @param cause Root cause
     */
    public InvalidParameterException(String message, Throwable cause) {
        super(message, cause);
    }
} 