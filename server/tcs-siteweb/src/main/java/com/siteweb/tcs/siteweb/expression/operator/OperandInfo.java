package com.siteweb.tcs.siteweb.expression.operator;


import lombok.Data;

/**
 * 表示一个操作数所包含的基本信息
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
@Data
public class OperandInfo implements IOperatorOrOperand {

    private Double value;

    public OperandInfo(Double value) {
        this.value = value;
    }

    @Override
    public boolean isOperator() {
        return false;
    }

    @Override
    public boolean isOperand() {
        return true;
    }

    @Override
    public String toString() {
        return "操作数" + value;
    }
}
