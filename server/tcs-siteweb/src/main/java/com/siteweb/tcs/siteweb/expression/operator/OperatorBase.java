package com.siteweb.tcs.siteweb.expression.operator;

import com.siteweb.tcs.siteweb.expression.enums.OperatingDirectionEnum;

/**
 * 表示一个运算符所包含的信息
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public abstract class OperatorBase implements IOperatorOrOperand {
    //protected OperatorBase(Double value) {
    //    this.value = value;
    //}

    @Override
    public boolean isOperator() {
        return true;
    }

    @Override
    public boolean isOperand() {
        return false;
    }

    private Double value;

    /**
     * 运算符符号
     *
     * @return {@link String}
     */
    public abstract String  operatorSymbol();

    /**
     * 运算符名称
     *
     * @return {@link String}
     */
    public abstract String operatorName();

    /**
     * 优先级
     *
     * @return int
     */
    public abstract int priority();

    /**
     * 结合性方向
     *
     * @return {@link OperatingDirectionEnum}
     */
    public abstract OperatingDirectionEnum direction();

    /**
     * 需要的操作数个数
     *
     * @return int
     */
    public abstract int operandCount();

    /**
     * 计算结果（参数已检查）
     *
     * @param operands 需要的操作数（已检查）
     * @return double 返回计算结果
     */
    public abstract double onCalculate(double[] operands);

    @Override
    public String toString() {
        return operatorName() + "---------" + operatorSymbol();
    }
}
