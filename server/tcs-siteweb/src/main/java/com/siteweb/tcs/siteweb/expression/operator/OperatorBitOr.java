package com.siteweb.tcs.siteweb.expression.operator;

import cn.hutool.core.util.NumberUtil;
import com.siteweb.tcs.siteweb.expression.enums.OperatingDirectionEnum;


/**
 * 表示位或运算符
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorBitOr extends OperatorBase{
    @Override
    public String operatorSymbol() {
        return "OR";
    }

    @Override
    public String operatorName() {
        return "按位或";
    }

    @Override
    public int priority() {
        return 6;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.LEFT_TO_RIGHT;
    }

    @Override
    public int operandCount() {
        return 2;
    }

    @Override
    public double onCalculate(double[] operands) {
        if (NumberUtil.isInteger(String.valueOf(operands[0])) && NumberUtil.isInteger(String.valueOf(operands[1]))) {
            return (int)operands[0] | (int)operands[1];
        }
        throw new RuntimeException("OR 运算符必须用于两个整数。");
    }
}
