package com.siteweb.tcs.siteweb.expression.operator;

import com.siteweb.tcs.siteweb.expression.enums.OperatingDirectionEnum;

/**
 * 表示闭括号运算符
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorCloseBracket extends OperatorBase {

    @Override
    public String operatorSymbol() {
        return ")";
    }

    @Override
    public String operatorName() {
        return "闭括号";
    }

    @Override
    public int priority() {
        return 0;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.NONE;
    }

    @Override
    public int operandCount() {
        return 0;
    }

    @Override
    public double onCalculate(double[] operands) {
        throw new RuntimeException("闭括号不应该参与计算");
    }
}
