package com.siteweb.tcs.siteweb.expression.operator;

import com.siteweb.tcs.siteweb.expression.enums.OperatingDirectionEnum;

/**
 * OperatorPositive
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorPositive extends OperatorBase{
    @Override
    public String operatorSymbol() {
        return "+";
    }

    @Override
    public String operatorName() {
        return "取正号";
    }

    @Override
    public int priority() {
        return 15;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.RIGHT_TO_LEFT;
    }

    @Override
    public int operandCount() {
        return 1;
    }

    @Override
    public double onCalculate(double[] operands) {
        return operands[0];
    }
}
