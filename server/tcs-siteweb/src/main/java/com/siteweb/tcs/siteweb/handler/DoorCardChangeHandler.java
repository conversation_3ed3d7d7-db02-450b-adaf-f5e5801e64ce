package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.DoorCard;
import com.siteweb.tcs.siteweb.service.IDoorCardService;
import com.siteweb.tcs.siteweb.service.IDoorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 门禁卡数据变更处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class DoorCardChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IDoorCardService doorCardService;
    
    @Autowired
    private IDoorService doorService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(DoorCard.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        DoorCard doorCard = changeRecord.readMessageBody(DoorCard.class);
        log.info("门禁卡创建成功,cardId:{},doorId:{}", doorCard.getCardId(), doorCard.getDoorId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        DoorCard doorCard = changeRecord.readMessageBody(DoorCard.class);
        log.info("门禁卡删除成功,cardId:{},doorId:{}", doorCard.getCardId(), doorCard.getDoorId());
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        DoorCard doorCard = changeRecord.readMessageBody(DoorCard.class);
        log.trace("门禁卡修改成功,cardId:{},doorId:{}", doorCard.getCardId(), doorCard.getDoorId());
    }
}
