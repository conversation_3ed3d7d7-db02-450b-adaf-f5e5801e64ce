package com.siteweb.tcs.siteweb.handler;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.service.IDoorService;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IMonitorUnitService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 设备数据变更处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class EquipmentChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IMonitorUnitService monitorUnitService;
    
    @Autowired
    private IDoorService doorService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IEquipmentService equipmentService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(Equipment.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        Equipment equipment = changeRecord.readMessageBody(Equipment.class);
        // 增加日志
        log.info("设备创建成功,equipmentId:{},equipmentName:{},resourceStructureId:{}, stationId:{}, houseId:{}", 
                equipment.getEquipmentId(), equipment.getEquipmentName(), equipment.getResourceStructureId(), 
                equipment.getStationId(), equipment.getHouseId());
        
        // 更新监控单元状态
        updateMonitorUnit(equipment.getMonitorUnitId());
        
        // 创建门
        doorService.createDoor(equipment.getEquipmentId());
        
        // 同步设备关联层级
        equipmentService.syncEquipmentStructure(equipment);
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        Equipment equipment = changeRecord.readMessageBody(Equipment.class);
        log.info("设备删除成功,equipmentId:{},equipmentName:{},resourceStructureId:{}, stationId:{}, houseId:{}", 
                equipment.getEquipmentId(), equipment.getEquipmentName(), equipment.getResourceStructureId(), 
                equipment.getStationId(), equipment.getHouseId());
        
        // 更新监控单元状态
        updateMonitorUnit(equipment.getMonitorUnitId());
        
        // 删除门
        doorService.deleteByEquipmentId(equipment.getEquipmentId(), equipment.getEquipmentName());
        
        // 如果存在采集单元下存在其他设备，则不删除采集单元，如果不存在则需要删除采集单元
        List<Equipment> equipmentList = equipmentService.findBySamplerUnitId(equipment.getSamplerUnitId());
        if (CollUtil.isEmpty(equipmentList)) {
            samplerUnitService.deleteSamplerUnit(equipment.getSamplerUnitId());
        }
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        Equipment equipment = changeRecord.readMessageBody(Equipment.class);
        log.trace("设备修改成功,equipmentId:{},equipmentName:{},resourceStructureId:{}, stationId:{}, houseId:{}", 
                equipment.getEquipmentId(), equipment.getEquipmentName(), equipment.getResourceStructureId(), 
                equipment.getStationId(), equipment.getHouseId());
        
        // 更新监控单元状态
        updateMonitorUnit(equipment.getMonitorUnitId());

        // 样板站同步过来的的设备，重新进行层级映射
        if (equipment.getChangeType() != null && equipment.getChangeType() == 0) {
            equipmentService.syncSwatchEquipment(equipment);
        }
    }
    
    /**
     * 更新监控单元状态
     * 注意：在实际实现中需要调用监控单元状态服务
     * @param monitorUnitId 监控单元ID
     */
    private void updateMonitorUnit(Integer monitorUnitId) {
        // 在实际实现中需要使用MonitorUnitStateService
        log.info("更新监控单元{}.", monitorUnitId);
        // monitorUnitStateService.updateMonitorUnit(monitorUnitId);
    }
}
