package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.MonitorUnit;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IMonitorUnitService;
import com.siteweb.tcs.siteweb.service.IMonitorUnitStateService;
import com.siteweb.tcs.siteweb.service.IPortService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 监控单元数据变更处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class MonitorUnitChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IMonitorUnitStateService monitorUnitStateService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IPortService portService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IMonitorUnitService monitorUnitService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(MonitorUnit.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        MonitorUnit monitorUnit = changeRecord.readMessageBody(MonitorUnit.class);
        log.info("监控单元创建成功,monitorUnitId:{},monitorUnitName:{},stationId:{}",
                monitorUnit.getMonitorUnitId(), monitorUnit.getMonitorUnitName(), monitorUnit.getStationId());
        monitorUnitStateService.updateMonitorUnit(monitorUnit.getMonitorUnitId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        MonitorUnit monitorUnit = changeRecord.readMessageBody(MonitorUnit.class);
        log.info("监控单元删除成功,monitorUnitId:{},monitorUnitName:{},stationId:{}",
                monitorUnit.getMonitorUnitId(), monitorUnit.getMonitorUnitName(), monitorUnit.getStationId());

        monitorUnitStateService.updateMonitorUnit(monitorUnit.getMonitorUnitId());
        // 删除端口
        portService.deleteByMonitorUnitId(monitorUnit.getMonitorUnitId());

        // 删除监控单元下的所有设备
        equipmentService.deleteByMonitorUnitId(monitorUnit.getMonitorUnitId());

        // 删除监控单元下的所有采集单元
        List<SamplerUnit> samplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnit.getMonitorUnitId());
        for (SamplerUnit samplerUnit : samplerUnits) {
            samplerUnitService.deleteSamplerUnit(samplerUnit.getSamplerUnitId());
        }
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        MonitorUnit monitorUnit = changeRecord.readMessageBody(MonitorUnit.class);
        log.trace("监控单元修改成功,monitorUnitId:{},monitorUnitName:{},stationId:{}",
                monitorUnit.getMonitorUnitId(), monitorUnit.getMonitorUnitName(), monitorUnit.getStationId());

        monitorUnitStateService.updateMonitorUnit(monitorUnit.getMonitorUnitId());

        // 如果监控单元连接状态发生变化，可能需要更新相关设备的状态
        updateEquipmentConnectState(monitorUnit);
    }

    /**
     * 更新设备连接状态
     * 当监控单元连接状态变化时，可能需要更新其下属设备的连接状态
     *
     * @param monitorUnit 监控单元
     */
    private void updateEquipmentConnectState(MonitorUnit monitorUnit) {
        // 在实际实现中，可能需要根据监控单元的连接状态来更新设备的连接状态
        // 例如，如果监控单元断开连接，则其下属设备也应该标记为断开连接
        if (monitorUnit.getConnectState() != null && monitorUnit.getConnectState() == 0) {
            log.info("监控单元{}断开连接，可能需要更新其下属设备的连接状态", monitorUnit.getMonitorUnitId());
            // 在实际实现中，可能需要调用设备服务来更新设备状态
            // equipmentService.updateConnectStateByMonitorUnitId(monitorUnit.getMonitorUnitId(), 0);
        }
    }
}
