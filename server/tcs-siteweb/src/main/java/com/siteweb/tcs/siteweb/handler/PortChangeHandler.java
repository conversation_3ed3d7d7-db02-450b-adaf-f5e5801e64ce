package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.manager.SamplerTreeManager;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IMonitorUnitStateService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (2024-04-12)
 **/
@Slf4j
@Component
public class PortChangeHandler extends ObjectChangeHandlerAdapter {


    @Autowired
    private SamplerTreeManager samplerTreeManager;

    @Autowired
    private IMonitorUnitStateService monitorUnitStateService;

    @Autowired
    private ISamplerUnitService samplerUnitService;
    @Autowired
    private IEquipmentService equipmentService;


    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(Port.class);
    }


    @Override
    public void onCreate(ChangeRecord changeRecord) {
        Port port = changeRecord.readMessageBody(Port.class);
//        samplerTreeManager.updatePort(port, ChangeOperatorEnum.CREATE);
        monitorUnitStateService.updateMonitorUnit(port.getMonitorUnitId());
    }


    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        Port port = changeRecord.readMessageBody(Port.class);
//        samplerTreeManager.updatePort(port, ChangeOperatorEnum.UPDATE);
        monitorUnitStateService.updateMonitorUnit(port.getMonitorUnitId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        Port port = changeRecord.readMessageBody(Port.class);
//        samplerTreeManager.updatePort(port, ChangeOperatorEnum.DELETE);
        monitorUnitStateService.updateMonitorUnit(port.getMonitorUnitId());
        // 删除采集单元
        samplerUnitService.deleteByPortId(port.getPortId());
    }


}
