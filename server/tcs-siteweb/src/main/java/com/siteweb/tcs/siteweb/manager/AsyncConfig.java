package com.siteweb.tcs.siteweb.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步配置类
 * 配置异步任务执行器，统一管理所有异步任务
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig implements AsyncConfigurer {

    @Value("${tcs.siteweb.async.core-pool-size:8}")
    private int corePoolSize;

    @Value("${tcs.siteweb.async.max-pool-size:16}")
    private int maxPoolSize;

    @Value("${tcs.siteweb.async.queue-capacity:100}")
    private int queueCapacity;

    @Value("${tcs.siteweb.async.thread-name-prefix:siteweb-async-}")
    private String threadNamePrefix;

    /**
     * 默认异步任务执行器
     */
    @Bean
    @Override
    public TaskExecutor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

    /**
     * 监控单元任务专用执行器
     * 用于监控单元配置生成、下发、备份等任务
     */
    @Lazy
    @Bean(name = "monitorUnitTaskExecutor")
    public TaskExecutor monitorUnitTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 配置核心线程数为可用处理器数量
        int processors = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(processors);
        // 配置最大线程数
        executor.setMaxPoolSize(processors * 2);
        // 配置队列大小
        executor.setQueueCapacity(50);
        // 配置线程池中线程的名称前缀
        executor.setThreadNamePrefix("MonitorUnit-");
        // 配置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

    /**
     * 远程操作任务专用执行器
     * 用于远程配置下载、文件传输等任务
     */
    @Lazy
    @Bean(name = "remoteTaskExecutor")
    public TaskExecutor remoteTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 远程操作通常IO密集，可以设置更多线程
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(20);
        executor.setThreadNamePrefix("Remote-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

    /**
     * 异步任务异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            log.error("异步任务执行异常 - 方法: {}, 参数: {}", method.getName(), params, ex);
            // 这里可以添加更多的异常处理逻辑，比如发送告警、记录到数据库等
        };
    }
}
