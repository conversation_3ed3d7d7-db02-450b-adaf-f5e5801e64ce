package com.siteweb.tcs.siteweb.manager;

/**
 * @Description:
 */

import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;
import com.siteweb.tcs.siteweb.enums.MonitorUnitCategoryEnum;
import com.siteweb.tcs.siteweb.enums.MonitorUnitStateEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.MonitorUnitMapper;
import com.siteweb.tcs.siteweb.service.IMonitorUnitService;
import com.siteweb.tcs.siteweb.util.TableExistenceChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 下发状态描述
 * 待下发情况：
 *
 * 生成XML文件。
 * 修改监控单元、设备/设备模板、采集单元、端口。
 * 如果issync为0，即使之前失败了，重启后仍需要重新执行下发操作。
 * 无需下发：
 *
 * 对于RMU下MU，采用手动下发方式，无需自动下发。
 * 正在下发：
 *
 * 外部发起下发请求，正在执行下发过程。
 * 下发成功：
 *
 * 下发采集器成功并完成采集操作。
 * 如果issync为1，表示下发成功且记录了最近一次的下发时间。
 * 下发失败：
 *
 * 在下发采集器过程中发生异常，可能包括：
 * 密码错误。
 * 连接失败。
 * 代码异常。
 * 删除文件失败。
 * 上传文件失败。
 * 执行重启失败。
 * 重启采集器后的下发状态
 * 待下发：
 * 如果issync为0，表示未同步过，即便之前下发失败，重启后仍需重新下发。
 * 下发成功：
 * 如果issync为1，表示之前下发成功，并且有最近一次的下发时间记录。
 * 无需下发：
 * 对于RMU下MU，保持手动下发方式，无需自动下发。
 **/
@Slf4j
@Component
public class MonitorUnitStateManager {

    @Autowired
    private MonitorUnitMapper monitorUnitMapper;

    @Autowired
    private TableExistenceChecker tableExistenceChecker;

    @Autowired
    @Lazy
    private IMonitorUnitService monitorUnitService;

    // key: monitorUnitId, value: 状态码
    private final ConcurrentHashMap<Integer, Integer> monitorUnitStatusMap = new ConcurrentHashMap<>();

    // key: monitorUnitId, value: 监控单元 DTO（只有 SENDING 状态时保存）
    private final ConcurrentHashMap<Integer, MonitorUnitDTO> activeMonitorUnitDTOMap = new ConcurrentHashMap<>();

    /**
     * 获取监控单元状态（懒加载）
     */
    public Integer getMonitorUnitStatus(Integer monitorUnitId) {
        boolean isOMC = isStationProjectInfoTableExist();
        return monitorUnitStatusMap.computeIfAbsent(monitorUnitId, id -> {
            MonitorUnitDTO monitorUnit = selectByMonitorUnitId(id);

            if (monitorUnit == null) {
                log.warn("监控单元 {} 不存在", id);
                return MonitorUnitStateEnum.NO_NEED_TO_SEND.getValue();
            }

            Integer state;
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.MU_OF_RMU.getValue()
                    || monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()) {
                state = MonitorUnitStateEnum.NO_NEED_TO_SEND.getValue();
            } else if (Boolean.TRUE.equals(monitorUnit.getIsSync())) {
                state = MonitorUnitStateEnum.SUCCESS.getValue();
            } else {
                state = MonitorUnitStateEnum.PENDING.getValue();
            }

            log.debug("懒加载设备 [{}] 的状态为 {}", id, state);
            return state;
        });
    }

    /**
     * 更新监控单元状态
     */
    public void updateMonitorUnitStatus(Integer monitorUnitId, MonitorUnitStateEnum status) {
        if (status == MonitorUnitStateEnum.SENDING) {
            MonitorUnitDTO dto = selectByMonitorUnitId(monitorUnitId);
            if (dto != null) {
                activeMonitorUnitDTOMap.put(monitorUnitId, dto);
            }
            // 开始下发时，设置同步状态为false
            updateDatabaseSyncStatus(monitorUnitId, false);
        } else if (status == MonitorUnitStateEnum.SUCCESS) {
            activeMonitorUnitDTOMap.remove(monitorUnitId);
            // 下发成功时，设置同步状态为true
            updateDatabaseSyncStatus(monitorUnitId, true);
        } else if (status == MonitorUnitStateEnum.FAILURE) {
            activeMonitorUnitDTOMap.remove(monitorUnitId);
            // 下发失败时，设置同步状态为false
            updateDatabaseSyncStatus(monitorUnitId, false);
        }

        monitorUnitStatusMap.put(monitorUnitId, status.getValue());
        log.debug("更新监控单元[{}]状态为: {}", monitorUnitId, status.name());
    }

    /**
     * 更新数据库中的同步状态
     */
    private void updateDatabaseSyncStatus(Integer monitorUnitId, Boolean isSync) {
        try {
            if (monitorUnitService != null) {
                monitorUnitService.updateMonitorUnitSyncStatus(monitorUnitId, isSync);
                log.debug("同步更新监控单元[{}]数据库同步状态: {}", monitorUnitId, isSync);
            } else {
                log.warn("MonitorUnitService未注入，无法更新监控单元[{}]数据库同步状态", monitorUnitId);
            }
        } catch (Exception e) {
            log.error("更新监控单元[{}]数据库同步状态失败", monitorUnitId, e);
        }
    }

    public MonitorUnitDTO getActiveMonitorUnitDTO(Integer monitorUnitId) {
        return activeMonitorUnitDTOMap.get(monitorUnitId);
    }

    public List<MonitorUnitDTO> getMonitorUnitDTOs(List<Integer> monitorUnitIds) {
        return monitorUnitIds.stream()
                .map(this::getActiveMonitorUnitDTO)
                .collect(Collectors.toList());
    }
    MonitorUnitDTO selectByMonitorUnitId(Integer monitorUnitId){
        if (isStationProjectInfoTableExist()){
            return monitorUnitMapper.selectByMonitorUnitId(monitorUnitId);
        }
        else {
            return monitorUnitMapper.selectByMonitorUnitIdWithoutStation(monitorUnitId);
        }
    }
    boolean isStationProjectInfoTableExist(){
        return tableExistenceChecker.areAllTablesExist(TableIdentityEnum.TBL_MONITOR_UNIT_PROJECT_INFO.getTableName(), TableIdentityEnum.TBL_STATION.getTableName());
    }
}
