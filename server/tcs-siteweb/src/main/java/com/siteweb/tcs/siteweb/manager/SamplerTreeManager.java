package com.siteweb.tcs.siteweb.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IPortService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import com.siteweb.tcs.siteweb.vo.EquipmentTreeVO;
import com.siteweb.tcs.siteweb.vo.PortTreeVO;
import com.siteweb.tcs.siteweb.vo.SamplerUnitTreeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采样器树管理器
 * 负责构建监控单元下的端口-采集单元-设备的树形结构
 */
@Slf4j
@Component
public class SamplerTreeManager {

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IPortService portService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    /**
     * 获取监控单元的采样器树结构
     *
     * @param monitorUnitId 监控单元ID
     * @return 端口树列表
     */
    public List<PortTreeVO> getSamplerTree(Integer monitorUnitId) {
        List<PortTreeVO> portTreeVOS = new ArrayList<>();
        
        // 查询端口列表
        List<Port> ports = portService.list(new LambdaQueryWrapper<Port>()
                .eq(Port::getMonitorUnitId, monitorUnitId));
        ports.sort(Comparator.comparing(Port::getPortNo));
        
        // 查询采集单元列表
        List<SamplerUnit> samplerUnits = samplerUnitService.list(new LambdaQueryWrapper<SamplerUnit>()
                .eq(SamplerUnit::getMonitorUnitId, monitorUnitId));
        
        // 查询设备列表
        List<Equipment> equipments = equipmentService.findByMonitorUnitId(monitorUnitId);
        
        // 按端口ID分组采集单元
        Map<Integer, List<SamplerUnitTreeVO>> samplerUnitMap = samplerUnits.stream()
                .collect(Collectors.groupingBy(
                        SamplerUnit::getPortId, // 键为 portID
                        Collectors.mapping(
                                e -> new SamplerUnitTreeVO().copy(e), // 将每个元素转换为 SamplerUnitTreeVO
                                Collectors.toList() // 将结果收集到 List 中
                        )
                ));
        
        // 按采集单元ID分组设备
        Map<Integer, List<EquipmentTreeVO>> equipmentMap = equipments.stream()
                .collect(Collectors.groupingBy(
                        Equipment::getSamplerUnitId, // 键为 SamplerUnitID
                        Collectors.mapping(
                                e -> new EquipmentTreeVO().copy(e), // 将每个元素转换为 EquipmentTreeVO
                                Collectors.toList() // 将结果收集到 List 中
                        )
                ));
        
        // 构建树形结构
        ports.forEach(port -> {
            PortTreeVO portTreeVO = new PortTreeVO().copy(port);
            List<SamplerUnitTreeVO> samplerUnitTreeVOS = samplerUnitMap.getOrDefault(port.getPortId(), new ArrayList<>());
            
            if (samplerUnitTreeVOS != null) {
                samplerUnitTreeVOS.forEach(samplerUnit -> {
                    List<EquipmentTreeVO> equipmentTreeVOS = equipmentMap.getOrDefault(samplerUnit.getSamplerUnitId(), new ArrayList<>());
                    samplerUnit.setEquipments(equipmentTreeVOS);
                });
            }
            
            portTreeVO.setSamplerUnits(samplerUnitTreeVOS);
            portTreeVOS.add(portTreeVO);
        });

        return portTreeVOS;
    }

    /**
     * 获取指定端口的树形结构
     *
     * @param portId 端口ID
     * @return 端口树VO
     */
    public PortTreeVO getPortVO(Integer portId) {
        // 根据端口ID查询端口
        Port port = portService.getOne(new LambdaQueryWrapper<Port>()
                .eq(Port::getPortId, portId));
        
        if (port == null) {
            log.warn("Port with ID {} not found.", portId);
            return null;
        }

        // 创建端口树VO
        PortTreeVO portTreeVO = new PortTreeVO().copy(port);

        // 查询该端口关联的采集单元
        List<SamplerUnit> samplerUnits = samplerUnitService.list(new LambdaQueryWrapper<SamplerUnit>()
                .eq(SamplerUnit::getPortId, portId));

        // 查询监控单元下的所有设备
        List<Equipment> equipments = equipmentService.findByMonitorUnitId(portTreeVO.getMonitorUnitId());

        // 按采集单元ID分组设备
        Map<Integer, List<EquipmentTreeVO>> equipmentMap = equipments.stream()
                .collect(Collectors.groupingBy(
                        Equipment::getSamplerUnitId,
                        Collectors.mapping(e -> new EquipmentTreeVO().copy(e), Collectors.toList())
                ));

        // 创建采集单元树VO列表并分配设备
        List<SamplerUnitTreeVO> samplerUnitTreeVOS = samplerUnits.stream()
                .map(samplerUnit -> {
                    SamplerUnitTreeVO samplerUnitTreeVO = new SamplerUnitTreeVO().copy(samplerUnit);
                    List<EquipmentTreeVO> equipmentTreeVOS = equipmentMap.getOrDefault(samplerUnit.getSamplerUnitId(), new ArrayList<>());
                    samplerUnitTreeVO.setEquipments(equipmentTreeVOS);
                    return samplerUnitTreeVO;
                })
                .collect(Collectors.toList());

        // 设置采集单元到端口树VO
        portTreeVO.setSamplerUnits(samplerUnitTreeVOS);

        return portTreeVO;
    }
}
