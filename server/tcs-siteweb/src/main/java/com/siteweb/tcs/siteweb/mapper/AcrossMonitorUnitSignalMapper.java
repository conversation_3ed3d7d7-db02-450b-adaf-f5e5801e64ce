package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.AcrossMonitorUnitSignal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 跨站监控单元信号 Mapper
 */
@Mapper
@Repository
public interface AcrossMonitorUnitSignalMapper extends BaseMapper<AcrossMonitorUnitSignal> {

    /**
     * 根据条件查询跨站信号
     *
     * @param condition 查询条件
     * @return 跨站信号列表
     */
    List<AcrossMonitorUnitSignal> findByCondition(@Param("condition") Map<String, Object> condition);

    /**
     * 根据设备ID和信号ID查询跨站信号
     *
     * @param equipmentId 设备ID
     * @param signalId 信号ID
     * @return 跨站信号列表
     */
    List<AcrossMonitorUnitSignal> findByEquipmentIdAndSignalId(@Param("equipmentId") Integer equipmentId,
                                                              @Param("signalId") Integer signalId);

    /**
     * 创建跨站信号
     *
     * @param signal 跨站信号
     * @return 影响行数
     */
    int createAcrossMonitorUnitSignal(AcrossMonitorUnitSignal signal);
}
