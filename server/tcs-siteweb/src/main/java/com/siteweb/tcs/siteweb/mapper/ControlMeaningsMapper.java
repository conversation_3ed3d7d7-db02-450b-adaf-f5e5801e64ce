package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Control Meanings Mapper
 */
@Mapper
public interface ControlMeaningsMapper extends BaseMapper<ControlMeanings> {
    
    void deleteByEquipmentTemplateIdAndControlId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("controlId") Integer controlId);
    
    // MybatisPlus BatchBaseMapper already provides batchInsert
    // void batchInsert(@Param("controlMeaningsList") List<ControlMeanings> controlMeaningsList);

    List<ControlMeanings> findByEquipmentTemplateIdAndControlId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("controlId") Integer controlId);

}
