package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.Door;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Door Mapper
 */
@Mapper
@Repository
public interface DoorMapper extends BaseMapper<Door> {
    /**
     * 查找门控制器ID
     * @param equipmentId 设备ID
     * @return 门控制器ID
     */
    Integer findDoorControlId(@Param("equipmentId") Integer equipmentId);

    /**
     * 查找门号，门控制器类型为4
     * @param equipmentId 设备ID
     * @return 门号列表
     */
    List<Integer> findDoorNoByEquipmentIdAndFour(@Param("equipmentId") Integer equipmentId);

    /**
     * 查找门号，门控制器类型为12
     * @param equipmentId 设备ID
     * @return 门号列表
     */
    List<Integer> findDoorNoByEquipmentIdAndTwelve(@Param("equipmentId") Integer equipmentId);

    /**
     * 查找门号，门控制器类型为20
     * @param equipmentId 设备ID
     * @return 门号列表
     */
    List<Integer> findDoorNoByEquipmentIdAndTwenty(@Param("equipmentId") Integer equipmentId);

    /**
     * 查找门号，门控制器类型为其他
     * @param equipmentId 设备ID
     * @return 门号列表
     */
    List<Integer> findDoorNoByEquipmentIdAndOther(@Param("equipmentId") Integer equipmentId);
}
