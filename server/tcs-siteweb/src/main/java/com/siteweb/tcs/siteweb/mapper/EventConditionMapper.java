package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.EventCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Event Condition Mapper
 */
@Mapper
public interface EventConditionMapper extends BaseMapper<EventCondition> {
    
    /**
     * 根据设备模板ID和事件ID批量删除事件条件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 删除结果
     */
    int deleteByEvent(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);
    
    /**
     * 批量更新事件条件
     * @param conditions 事件条件列表
     */
    void batchUpdate(@Param("conditions") List<EventCondition> conditions);
    
    /**
     * 根据设备模板ID和事件ID查询事件条件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 事件条件列表
     */
    List<EventCondition> findByEventId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);

    EventCondition findMaxEventByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    Integer findMaxEventConditionByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);
}
