package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.SignalBaseDic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 基础信号字典 Mapper 接口
 */
@Mapper
public interface SignalBaseDicMapper extends BaseMapper<SignalBaseDic> {

    /**
     * 生成信号基类字典
     * @param baseTypeId 目标基类ID
     * @param sourceId 源基类ID
     */
    void generateSignalBaseDic(@Param("baseTypeId") Long baseTypeId, @Param("sourceId") Long sourceId);
}