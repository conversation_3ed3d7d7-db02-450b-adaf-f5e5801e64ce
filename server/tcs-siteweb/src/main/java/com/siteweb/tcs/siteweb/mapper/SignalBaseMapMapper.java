package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.dto.SignalBaseMapDTO;
import com.siteweb.tcs.siteweb.entity.SignalBaseMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Signal Base Map Mapper
 */
@Mapper
@Repository
public interface SignalBaseMapMapper extends BaseMapper<SignalBaseMap> {

    /**
     * 获取信号基础映射
     *
     * @param standardId 标准ID
     * @return 信号基础映射列表
     */
    List<SignalBaseMapDTO> getSignalBaseMap(@Param("standardId") Integer standardId);
}
