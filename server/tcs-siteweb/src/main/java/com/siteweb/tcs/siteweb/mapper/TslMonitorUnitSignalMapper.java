package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitSignal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 监控单元信号 Mapper
 */
@Mapper
@Repository
public interface TslMonitorUnitSignalMapper extends BaseMapper<TslMonitorUnitSignal> {

    /**
     * 根据设备ID和信号ID查询监控单元信号
     *
     * @param equipmentId 设备ID
     * @param signalId 信号ID
     * @return 监控单元信号列表
     */
    List<TslMonitorUnitSignal> findByEquipmentIdAndSignalId(@Param("equipmentId") Integer equipmentId,
                                                           @Param("signalId") Integer signalId);

    /**
     * 根据条件查询监控单元信号
     *
     * @param condition 查询条件
     * @return 监控单元信号列表
     */
    List<TslMonitorUnitSignal> findByCondition(@Param("condition") Map<String, Object> condition);

    /**
     * 创建或更新监控单元信号
     *
     * @param signal 监控单元信号
     * @return 影响行数
     */
    int createOrUpdate(TslMonitorUnitSignal signal);

    /**
     * 删除监控单元信号
     *
     * @param equipmentId 设备ID
     * @param signalId 信号ID
     * @return 影响行数
     */
    int deleteByEquipmentIdAndSignalId(@Param("equipmentId") Integer equipmentId,
                                      @Param("signalId") Integer signalId);
}
