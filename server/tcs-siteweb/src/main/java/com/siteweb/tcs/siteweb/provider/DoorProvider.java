package com.siteweb.tcs.siteweb.provider;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.siteweb.entity.Door;
import com.siteweb.tcs.siteweb.service.IDoorService;
import com.siteweb.tcs.siteweb.util.I18n;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 门禁设备业务逻辑类
 */
@Slf4j
@Service
public class DoorProvider {

    @Autowired
    private I18n i18n;

    @Autowired
    private IDoorService doorService;

    /**
     * 新增门禁设备
     */
    public Door addDoor(Door door) {
        if (ObjectUtil.isEmpty(door)) {
            log.error("request body cannot be null");
        }
        if (ObjectUtil.isEmpty(door.getEquipmentId())) {
            log.error(i18n.T("parameter.cannot.be.null", "equipmentId"));
        }
        if (ObjectUtil.isEmpty(door.getSamplerUnitId())) {
            log.error(i18n.T("parameter.cannot.be.null", "samplerUnitId"));
        }
        if (ObjectUtil.isEmpty(door.getDoorControlId())) {
            log.error(i18n.T("parameter.cannot.be.null", "doorControlId"));
        }
        if (ObjectUtil.isEmpty(door.getDoorNo())) {
            log.error(i18n.T("parameter.cannot.be.null", "doorNo"));
        }
        doorService.createDoor(door);
        return door;
    }

    /**
     * 删除门禁设备
     */
    public boolean deleteDoor(Integer equipmentId, Integer doorNo) {
        if (ObjectUtil.isEmpty(equipmentId)) {
            log.error(i18n.T("parameter.cannot.be.null", "equipmentId"));
        }
        if (ObjectUtil.isEmpty(doorNo)) {
            log.error(i18n.T("parameter.cannot.be.null", "doorNo"));
        }
        return doorService.deleteDoor(equipmentId, doorNo);
    }

    /**
     * 更新门禁设备
     */
    public Door updateDoor(Door door) {
        if (ObjectUtil.isEmpty(door.getEquipmentId())) {
            log.error(i18n.T("parameter.cannot.be.null", "equipmentId"));
        }
        if (ObjectUtil.isEmpty(door.getDoorNo())) {
            log.error(i18n.T("parameter.cannot.be.null", "doorNo"));
        }
        return doorService.updateDoor(door);
    }

    /**
     * 查询门禁设备
     */
    public List<Door> getDoor(Integer equipmentId) {
        if (ObjectUtil.isEmpty(equipmentId)) {
            log.error(i18n.T("parameter.cannot.be.null", "equipmentId"));
        }
        return doorService.getDoorByEquipmentId(equipmentId);
    }
} 