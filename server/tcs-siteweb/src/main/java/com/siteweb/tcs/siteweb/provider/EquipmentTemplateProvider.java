package com.siteweb.tcs.siteweb.provider;

import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IEquipmentTemplateService;
import com.siteweb.tcs.siteweb.util.I18n;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Equipment Template Provider (业务逻辑类)
 */
@Service
public class EquipmentTemplateProvider {

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private I18n i18n;

    /**
     * 获取B接口设备根模板ID
     * @return B接口设备根模板ID
     */
    public Integer getBInterfaceDeviceTemplateRootId() {
        return equipmentTemplateService.getBInterfaceDeviceTemplateRootId();
    }

    /**
     * 根据设备模板ID删除模板
     * @param equipmentTemplateId 设备模板ID
     * @return 是否删除成功
     */
    public boolean deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.getById(equipmentTemplateId);
        if (equipmentTemplate == null) {
            return false;
        }
        if (equipmentTemplateService.existsChildTemplate(equipmentTemplateId)) {
            return false;
        }
        if (equipmentService.existsByEquipmentTemplateId(equipmentTemplateId)) {
            return false;
        }
        return equipmentTemplateService.deleteTemplateById(equipmentTemplateId);
    }

    /**
     * 创建设备模板
     * @param equipmentTemplateVo 设备模板VO
     * @return 创建后的设备模板VO
     */
    public EquipmentTemplateVO createTemplate(EquipmentTemplateVO equipmentTemplateVo) {
        equipmentTemplateVo.setEquipmentTemplateId(null); // Ensure ID is null for creation
        EquipmentTemplate createdTemplate = equipmentTemplateService.createEquipmentTemplate(equipmentTemplateVo.toEntity());
        if (createdTemplate != null) {
            return EquipmentTemplateVO.toVo(createdTemplate);
        } else {
            return null;
        }
    }

    /**
     * 根据条件查询设备模板
     * @param equipmentTemplateVO 查询条件VO
     * @return 设备模板VO列表
     */
    public List<EquipmentTemplateVO> queryTemplate(EquipmentTemplateVO equipmentTemplateVO) {
        List<EquipmentTemplate> equipmentTemplateList = equipmentTemplateService.queryTemplate(equipmentTemplateVO);
        return equipmentTemplateList.stream().map(EquipmentTemplateVO::toVo).toList();
    }
} 