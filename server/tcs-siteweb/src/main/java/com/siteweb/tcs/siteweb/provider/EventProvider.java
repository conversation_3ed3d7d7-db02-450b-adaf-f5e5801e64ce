package com.siteweb.tcs.siteweb.provider;

import com.siteweb.tcs.siteweb.dto.BatchEventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IEventService;
import com.siteweb.tcs.siteweb.util.I18n;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Event Provider (业务逻辑类)
 */
@Slf4j
@Service
public class EventProvider {
    @Autowired
    private IEventService eventService;

    @Autowired
    private I18n i18n;

    @Autowired
    private IEquipmentService equipmentService;

    public boolean createEvent(EventConfigItem eventConfigItem) {
        eventService.createEventByEventItem(eventConfigItem);
        return true;
    }

    public boolean updateEvent(EventConfigItem eventConfigItem) {
        eventService.updateByEventItem(eventConfigItem);
        return true;
    }

    public boolean batchUpdateEvent(BatchEventConfigItem batchEventConfigItem) {
        eventService.batchUpdateByEventItem(batchEventConfigItem);
        return true;
    }

    public boolean deleteEvent(int equipmentTemplateId, int eventId) {
        return eventService.deleteEvent(equipmentTemplateId, eventId)>0;
    }

    public boolean batchDeleteEvent(int equipmentTemplateId, List<Integer> eventIds) {
        if (equipmentTemplateId == 0 || eventIds == null || eventIds.isEmpty()) {
            return false;
        }
        Boolean result = eventService.batchDeleteEvent(equipmentTemplateId, eventIds);
        return Boolean.TRUE.equals(result);
    }

    public List<?> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventService.findEventItemByEquipmentTemplateId(equipmentTemplateId);
    }

    public List<?> findByEquipmentId(Integer equipmentId) {
        Equipment equipment = equipmentService.getById(equipmentId);
        if (Objects.isNull(equipment)) {
            return null;
        }
        return eventService.findEventItemByEquipmentTemplateId(equipment.getEquipmentTemplateId());
    }

    public Object getEventInfo(Integer equipmentTemplateId, Integer eventId) {
        return eventService.getEventInfo(equipmentTemplateId, eventId);
    }

    public Object linkEvent(Integer equipmentTemplateId, Integer signalId) {
        return eventService.linkEvent(equipmentTemplateId, signalId);
    }
} 