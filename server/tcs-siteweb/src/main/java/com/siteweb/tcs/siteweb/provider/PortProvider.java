package com.siteweb.tcs.siteweb.provider;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.service.IPortService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Optional;

/**
 * Port Provider (业务逻辑类)
 */
@Slf4j
@Service
public class PortProvider {

    @Autowired
    private IPortService portService;

    /**
     * 获取端口配置
     *
     * @param portId 端口ID
     * @return 端口信息
     */
    public Port getConfig(Integer portId) {
        return portService.getOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Port>()
                .eq("PortId", portId));
    }

    /**
     * 创建端口
     *
     * @param port 端口信息
     * @return 创建后的端口
     */
    public Port createConfig(Port port) {
        portService.verification(port);
        return portService.createPort(port);
    }

    /**
     * 更新端口
     *
     * @param port 端口信息
     * @return 是否更新成功
     */
    public boolean updateConfig(Port port) {
        portService.verification(port);
        return portService.updateById(port);
    }

    /**
     * 删除端口
     *
     * @param portId 端口ID
     * @return 是否删除成功
     */
    public boolean deleteConfig(Integer portId) {
        return portService.remove(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Port>()
                .eq("PortId", portId));
    }

    public Integer getMaxPortNoByMonitorUnitId(Integer monitorUnitId){
        Optional<Port> maxPort = portService
                .list(new LambdaQueryWrapper<Port>().eq(Port::getMonitorUnitId, monitorUnitId))
                .stream()
                .max(Comparator.comparingInt(Port::getPortId));
        if (maxPort.isPresent()) {
            return maxPort.get().getPortId();
        }
        else {
            return 0;
        }
    }
} 