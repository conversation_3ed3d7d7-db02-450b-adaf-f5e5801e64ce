package com.siteweb.tcs.siteweb.provider;

import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Sampler Unit Provider (业务逻辑类)
 */
@Slf4j
@Service
public class SamplerUnitProvider {

    @Autowired
    private ISamplerUnitService samplerUnitService;

    /**
     * 获取采样单元详情
     *
     * @param samplerUnitId 采样单元ID
     * @return 采样单元详情
     */
    public SamplerUnit getConfig(Integer samplerUnitId) {
        return samplerUnitService.findBySamplerUnitId(samplerUnitId);
    }

    /**
     * 创建采样单元
     *
     * @param samplerUnit 采样单元
     * @return 创建后的采样单元
     */
    public SamplerUnit createConfig(SamplerUnit samplerUnit) {
        try {
            samplerUnitService.createSamplerUnit(samplerUnit);
            return samplerUnit;
        } catch (Exception e) {
            log.error("Failed to create sampler unit", e);
            return null;
        }
    }

    /**
     * 更新采样单元
     *
     * @param samplerUnit 采样单元
     * @return 是否更新成功
     */
    public boolean putConfig(SamplerUnit samplerUnit) {
        return samplerUnitService.updateSamplerUnit(samplerUnit);
    }

    /**
     * 删除采样单元
     *
     * @param samplerUnitId 采样单元ID
     * @return 是否删除成功
     */
    public boolean deleteConfig(Integer samplerUnitId) {
        return samplerUnitService.deleteSamplerUnit(samplerUnitId);
    }

    /**
     * 获取指定监控单元下带端口信息的采样单元列表
     *
     * @param monitorUnitId 监控单元ID
     * @return 采样单元列表
     */
    public List<SamplerUnit> selectSamplerUnitWithPort(Integer monitorUnitId) {
        return samplerUnitService.selectSamplerUnitWithPort(monitorUnitId);
    }

    /**
     * 根据监控单元ID和端口ID获取采样单元列表
     *
     * @param monitorUnitId 监控单元ID
     * @param portId 端口ID
     * @return 采样单元列表
     */
    public List<SamplerUnit> getSamplerUnit(Integer monitorUnitId, Integer portId) {
        return samplerUnitService.findByMonitorUnitIdAndPortId(monitorUnitId, portId);
    }
} 