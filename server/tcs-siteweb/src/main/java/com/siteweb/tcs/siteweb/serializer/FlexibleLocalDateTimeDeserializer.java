package com.siteweb.tcs.siteweb.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 灵活的LocalDateTime反序列化器
 * 支持多种日期时间格式：
 * - yyyy-MM-dd HH:mm:ss
 * - yyyy-MM-dd HH:mm
 * - yyyy-MM-dd (自动补充时间为00:00:00)
 */
public class FlexibleLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    private static final DateTimeFormatter FORMATTER_FULL = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FORMATTER_SHORT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final DateTimeFormatter FORMATTER_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final List<DateTimeFormatter> DATETIME_FORMATTERS = Arrays.asList(
            ISO_FORMATTER, FORMATTER_FULL, FORMATTER_SHORT
    );

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getText();
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        value = value.trim();

        // 尝试解析为LocalDateTime
        // 先尝试解析为 LocalDateTime
        for (DateTimeFormatter formatter : DATETIME_FORMATTERS) {
            try {
                return LocalDateTime.parse(value, formatter);
            } catch (DateTimeParseException ignored) {
                // 尝试下一个
            }
        }

        // 再尝试 LocalDate
        try {
            LocalDate date = LocalDate.parse(value, FORMATTER_DATE);
            return date.atStartOfDay();
        } catch (DateTimeParseException e) {
            throw new IOException("Unable to parse date: " + value +
                    ". Supported formats: yyyy-MM-dd'T'HH:mm:ss, yyyy-MM-dd HH:mm:ss, yyyy-MM-dd HH:mm, yyyy-MM-dd", e);
        }
    }
}
