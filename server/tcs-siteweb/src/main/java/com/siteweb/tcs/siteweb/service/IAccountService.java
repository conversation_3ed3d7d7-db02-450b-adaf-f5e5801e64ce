package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.AccountDTO;
import com.siteweb.tcs.siteweb.entity.Account;

import java.util.List;

/**
 * 账户服务接口
 */
public interface IAccountService extends IService<Account> {

    /**
     * 根据登录ID查找账户
     *
     * @param logonId 登录ID
     * @return 账户列表
     */
    List<AccountDTO> findByLogonId(String logonId);

    /**
     * 根据手机号查找账户
     *
     * @param mobile 手机号
     * @return 账户列表
     */
    List<AccountDTO> findByMobile(String mobile);

    /**
     * 根据用户ID查找账户
     *
     * @param userId 用户ID
     * @return 账户DTO
     */
    AccountDTO findByUserId(Integer userId);

    /**
     * 查找所有账户
     *
     * @return 所有账户列表
     */
    List<AccountDTO> findAll();

    /**
     * 为负用户ID更新中心ID
     *
     * @param centerId 中心ID
     */
    void updateCenterIdForNegativeUserId(int centerId);
} 