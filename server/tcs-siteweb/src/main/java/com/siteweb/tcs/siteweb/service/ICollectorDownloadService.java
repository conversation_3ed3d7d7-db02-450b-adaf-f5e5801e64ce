package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;

import java.io.File;
import java.util.List;

/**
 * 采集器下载服务接口
 * 负责从远程采集器下载文件和获取信息
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface ICollectorDownloadService {

    /**
     * 从采集器下载日志文件
     * 
     * @param monitorUnit 监控单元信息
     * @param logPath 日志文件路径
     * @param protocol 协议类型 (ftp/sftp)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 下载的文件
     */
    File downloadLogFile(MonitorUnitDTO monitorUnit, String logPath, String protocol, 
                        Integer port, String username, String password, String uniqueId);

    /**
     * 从采集器下载配置文件
     * 
     * @param monitorUnit 监控单元信息
     * @param configPath 配置文件路径
     * @param protocol 协议类型 (ftp/sftp)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 下载的文件
     */
    File downloadConfigFile(MonitorUnitDTO monitorUnit, String configPath, String protocol, 
                           Integer port, String username, String password, String uniqueId);

    /**
     * 批量下载文件并打包
     *
     * @param monitorUnit 监控单元信息
     * @param filePaths 文件路径列表
     * @param protocol 协议类型 (ftp/sftp/ssh/scp)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 打包后的文件
     */
    File downloadAndPackageFiles(MonitorUnitDTO monitorUnit, List<String> filePaths,
                                String protocol, Integer port, String username, String password,
                                String uniqueId);

    /**
     * 下载采集器配置文件包
     * 从/home/<USER>/xmlCfg目录下载所有XML文件，从/home/<USER>/cmbcfg目录下载cmb_dictionary.xml文件
     *
     * @param monitorUnit 监控单元信息
     * @param protocol 协议类型 (ftp/sftp/ssh/scp)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 打包后的配置文件压缩包
     */
    File downloadCollectorConfigPackage(MonitorUnitDTO monitorUnit, String protocol,
                                       Integer port, String username, String password, String uniqueId);

    /**
     * 获取监控单元的最新远程配置文件
     *
     * @param monitorUnitId 监控单元ID
     * @return 最新的配置文件，如果不存在返回null
     */
    File getLatestRemoteConfigFile(Integer monitorUnitId);

    /**
     * 获取采集器状态信息
     * 
     * @param monitorUnit 监控单元信息
     * @param protocol 协议类型 (telnet/ssh)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 状态信息
     */
    String getCollectorStatus(MonitorUnitDTO monitorUnit, String protocol, Integer port, 
                             String username, String password, String uniqueId);

    /**
     * 检查文件是否存在
     * 
     * @param monitorUnit 监控单元信息
     * @param filePath 文件路径
     * @param protocol 协议类型 (ftp/sftp/ssh/scp)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 文件是否存在
     */
    boolean checkFileExists(MonitorUnitDTO monitorUnit, String filePath, String protocol,
                           Integer port, String username, String password, String uniqueId);

}
