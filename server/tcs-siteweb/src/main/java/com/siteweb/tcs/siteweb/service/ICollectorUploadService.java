package com.siteweb.tcs.siteweb.service;

import com.siteweb.tcs.siteweb.dto.MonitorUnitDTO;

import java.io.File;
import java.util.List;

/**
 * 采集器上传服务接口
 * 负责向远程采集器上传文件和执行命令
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface ICollectorUploadService {

    /**
     * 上传监控单元所有文件到采集器（一站式上传服务）
     *
     * 此方法会在单个连接中完成以下所有上传任务：
     * 1. SO库文件上传
     * 2. CMB字典文件上传 (cmb_dictionary.xml)
     * 3. 监控单元配置文件上传
     *
     * 支持的协议：
     * - FTP: 使用FTP协议上传，支持中文文件名编码转换
     * - SFTP: 使用SFTP协议上传，支持UTF-8编码
     * - SSH/SCP: 使用SSH+SCP协议上传，支持MD5验证和UTF-8编码
     *
     * @param monitorUnit 监控单元信息
     * @param configFile 配置文件
     * @param protocol 协议类型 (ftp/sftp/ssh/scp)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 上传结果（所有文件都成功上传才返回true）
     */
    boolean uploadMonitorUnitConfig(MonitorUnitDTO monitorUnit, File configFile,
                                   String protocol, Integer port, String username, String password,
                                   String uniqueId);


    /**
     * 执行采集器重启命令
     * 
     * @param monitorUnit 监控单元信息
     * @param protocol 协议类型 (telnet/ssh)
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 执行结果
     */
    boolean executeRestartCommand(MonitorUnitDTO monitorUnit, String protocol, Integer port, 
                                 String username, String password, String uniqueId);

    /**
     * 批量上传监控单元配置
     *
     * @param monitorUnits 监控单元列表
     * @param protocol 协议类型
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param uniqueId 任务唯一ID
     * @return 上传结果
     */
    boolean batchUploadConfigs(List<MonitorUnitDTO> monitorUnits, String protocol, Integer port,
                              String username, String password, String uniqueId);


    boolean uploadMonitorUnitConfigViaScp(MonitorUnitDTO monitorUnit, File configFile,
                                          Integer port, String username, String password, String uniqueId);
}
