package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.CommandBaseDic;

/**
 * Command Base Dictionary Service Interface
 */
public interface ICommandBaseDicService extends IService<CommandBaseDic>, IBaseDicService {

    /**
     * 根据基类ID查找控制基类字典
     * @param baseTypeId 基类ID
     * @return 控制基类字典
     */
    CommandBaseDic findByBaseTypeId(Long baseTypeId);

}
