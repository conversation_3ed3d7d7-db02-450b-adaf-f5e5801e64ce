package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.TypeItemDTO;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;

import java.util.List;
import java.util.Map;

/**
 * Data Item Service Interface
 */
public interface IDataItemService extends IService<DataItem> {

    /**
     * 根据字典项类型查找字典项列表
     *
     * @param dataEntryEnum 数据字典项类型
     * @return 字典项列表
     */
    List<DataItem> findByEntryId(DataEntryEnum dataEntryEnum);

    /**
     * 根据字典项类型和项ID查找字典项
     *
     * @param dataEntryEnum 数据字典项类型
     * @param itemId 项ID
     * @return 字典项
     */
    DataItem findByEntryIdAndItemId(DataEntryEnum dataEntryEnum, Integer itemId);

    /**
     * 创建数据字典项
     *
     * @param dataItem 数据字典项
     * @return 创建结果
     */
    int createDataItem(DataItem dataItem);

    /**
     * 初始化数据字典项
     * 对应存储过程：PCT_InitDataEntryItem
     *
     * @param dataItem 数据字典项
     */
    void initDataEntryItem(DataItem dataItem);

    /**
     * 根据字典项类型删除字典项
     *
     * @param dataEntryEnum 数据字典项类型
     */
    void deleteByEntryId(DataEntryEnum dataEntryEnum);

    /**
     * 保存字典项
     * 对应存储过程：PIL_SaveDictionaryItemByEntry
     *
     * @param dataItem 数据字典项
     * @return 保存结果
     */
    int saveDictionaryItemByEntry(DataItem dataItem);

    /**
     * 根据字典项类型获取字典项映射
     *
     * @param dataEntryEnum 数据字典项类型
     * @return 字典项映射
     */
    Map<Integer, String> findMapByEntryId(DataEntryEnum dataEntryEnum);

    /**
     * 根据字典项ID和项ID删除字典项
     *
     * @param entryId 字典项ID
     * @param itemId 项ID
     * @return 删除结果
     */
    int deleteByEntryIdAndItemId(Integer entryId, Integer itemId);

    /**
     * 更新端口扩展字段2
     *
     * @return 更新结果
     */
    int updatePortExtendField2();

    /**
     * 获取递增的最大ItemId
     *
     * @param entryId 字典项类型ID
     * @return 最大ItemId + 1
     */
    int getIncrementMaxItemId(Integer entryId);

    /**
     * 获取递增的最大EntryItemId
     *
     * @return 最大EntryItemId + 1
     */
    int getIncrementMaxEntryItemId();

    /**
     * 根据EntryItemId列表批量删除数据字典项
     *
     * @param entryItemIds EntryItemId列表
     * @return 删除的记录数
     */
    int deleteByEntryItemIds(List<Integer> entryItemIds);

    int update(DataItem dataItem);;

    Map<Integer, String> findMapByEntryIdAndItemId(DataEntryEnum dataEntryEnum, Integer originCategoryKey);

    List<TypeItemDTO> findTypes(DataEntryEnum dataEntryEnum);

    List<TypeItemDTO> findPortTypesByMonitorUnitCategory(Integer monitorUnitCategory);

    List<DataItem> findEquipmentCategory();
}
