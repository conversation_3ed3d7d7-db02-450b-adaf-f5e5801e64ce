package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.DoorCard;

import java.util.List;

/**
 * Door Card Service Interface
 */
public interface IDoorCardService extends IService<DoorCard> {
    
    /**
     * 根据设备ID删除门禁卡
     * @param equipmentId 设备ID
     */
    void deleteByEquipmentId(Integer equipmentId);
    
    /**
     * 根据设备ID查找门禁卡ID列表
     * @param equipmentId 设备ID
     * @return 门禁卡ID列表
     */
    List<Integer> findCardIdByEquipmentId(Integer equipmentId);
}
