package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.dto.SwitchTemplateDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.vo.EquipmentReferenceVO;
import com.siteweb.tcs.siteweb.vo.EquipmentTreeVO;
// Potentially import EquipmentVO if the method returns List<EquipmentVO>
// import com.siteweb.tcs.siteweb.vo.EquipmentVO;

import java.util.List;

/**
 * Equipment Service Interface
 */
public interface IEquipmentService extends IService<Equipment> {
    /**
     * Check if equipment name already exists for the given monitor unit
     *
     * @param equipmentId Equipment ID (null for new equipment)
     * @param monitorUnitId Monitor unit ID
     * @param equipmentName Equipment name
     * @return true if equipment name exists, false otherwise
     */
    boolean existsByMonitorUnitIdAndEquipmentName(Integer equipmentId, Integer monitorUnitId, String equipmentName);

    /**
     * Create a new equipment
     *
     * @param equipment Equipment entity
     * @return Created equipment
     */
    Equipment createEquipment(Equipment equipment);

    /**
     * Create equipment instance from template
     *
     * @param equipmentId Equipment ID
     * @return Template ID
     */
    Integer equipmentInstance(Integer equipmentId);

    /**
     * Get all equipment
     *
     * @return List of all equipment
     */
    List<Equipment> allEquipment();

    /**
     * Delete equipment by ID
     *
     * @param equipmentId Equipment ID
     * @return true if deleted successfully, false otherwise
     */
    boolean deleteEquipment(Integer equipmentId);

    /**
     * Update equipment
     *
     * @param equipmentDetailDTO Equipment detail DTO
     * @return Updated equipment
     */
    Equipment updateEquipment(EquipmentDetailDTO equipmentDetailDTO);

    /**
     * Find equipment by ID
     *
     * @param equipmentId Equipment ID
     * @return Equipment entity
     */
    Equipment findEquipmentById(Integer equipmentId);

    /**
     * Find equipment by monitor unit ID
     *
     * @param monitorUnitId Monitor unit ID
     * @return List of equipment entities
     */
    List<Equipment> findByMonitorUnitId(Integer monitorUnitId);

    /**
     * Delete all equipment associated with a monitor unit
     *
     * @param monitorUnitId Monitor unit ID
     */
    void deleteByMonitorUnitId(Integer monitorUnitId);

    /**
     * Get equipment list by resource structure ID.
     * This is needed by the StructureTreeManager to populate equipment in the tree nodes.
     * @param resourceStructureId The ID of the resource structure.
     * @return A list of Equipment entities (or EquipmentVOs if preferred for DTOs).
     */
    List<Equipment> getEquipmentsByResourceStructureId(Integer resourceStructureId);

    boolean existsByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * Find equipment by sampler unit ID
     *
     * @param samplerUnitId Sampler unit ID
     * @return List of equipment entities
     */
    List<Equipment> findBySamplerUnitId(Integer samplerUnitId);

    /**
     * Synchronize equipment structure
     *
     * @param equipment Equipment entity
     */
    void syncEquipmentStructure(Equipment equipment);

    /**
     * Synchronize swatch equipment
     *
     * @param equipment Equipment entity
     */
    void syncSwatchEquipment(Equipment equipment);

    /**
     * Delete all equipment associated with a station
     *
     * @param stationId Station ID
     */
    boolean deleteByStationId(Integer stationId);

    /**
     * Delete all equipment associated with a house
     *
     * @param houseId House ID
     */
    void deleteByHouseId(Integer houseId);

    /**
     * Delete all equipment associated with a sampler unit
     *
     * @param samplerUnitId Sampler unit ID
     */
    void deleteBySamplerUnitId(Integer samplerUnitId);

    /**
     * Clear equipment resource structure ID
     *
     * @param resourceStructureId Resource structure ID
     * @return True if cleared successfully
     */
    boolean clearEquipmentResourceStructureId(Integer resourceStructureId);
    // If returning EquipmentVO is preferred for DTOs:
    // List<EquipmentVO> getEquipmentsByResourceStructureId(Integer resourceStructureId);

    /**
     * 根据分类映射更新设备分类
     *
     * @param businessId 业务ID
     * @param categoryTypeId 分类类型ID
     * @return 是否更新成功
     */
    boolean updateEquipmentCategoryByCategoryIdMap(Integer businessId, Integer categoryTypeId);

    /**
     * 切换设备模板
     *
     * @param equipmentId 设备ID
     * @param newEquipmentTemplateId 新设备模板ID
     * @return 是否切换成功
     */
    boolean switchEquipmentTemplate(Integer equipmentId, Integer newEquipmentTemplateId);

    // ==================== 设备管理相关方法 ====================

    /**
     * 获取设备树结构
     *
     * @return 设备树VO列表
     */
    List<EquipmentTreeVO> getEquipmentTree();



    /**
     * 获取简化设备列表（上一级设备）
     *
     * @return 简化设备列表
     */
    List<Equipment> findSimplifyEquipments();


    /**
     * 根据设备模板ID查询设备引用信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 设备引用信息列表
     */
    List<EquipmentReferenceVO> findReferenceByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 导出设备引用信息
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 导出的字节数组
     */
    byte[] exportReferenceByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 切换设备模板
     *
     * @param switchTemplateDTO 切换模板DTO
     * @return 切换结果
     */
    boolean switchEquipmentTemplate(SwitchTemplateDTO switchTemplateDTO);

    Equipment findSimplifyEquipment(Integer equipmentId);

    /**
     * 批量更新设备
     *
     * @param equipments 设备列表
     * @return 更新数量
     */
    int batchUpdateEquipment(List<Equipment> equipments);

    /**
     * 获取设备信息详情
     *
     * @param equipmentId 设备ID
     * @return {@link EquipmentDetailDTO}
     * <AUTHOR> (2024/4/11)
     */
    EquipmentDetailDTO findEquipmentDetail(Integer equipmentId);

    void checkEquipmentSyncField(Integer equipmentTemplateId);
}
