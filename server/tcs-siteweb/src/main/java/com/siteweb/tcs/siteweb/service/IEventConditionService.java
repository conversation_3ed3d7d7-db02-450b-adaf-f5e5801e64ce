package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.EventCondition;

import java.util.List;

/**
 * Event Condition Service Interface
 */
public interface IEventConditionService extends IService<EventCondition> {

    /**
     * 批量创建事件条件
     * @param conditions 事件条件列表
     */
    void batchCreate(List<EventCondition> conditions);

    /**
     * 批量更新事件条件
     * @param conditions 事件条件列表
     */
    void batchUpdate(List<EventCondition> conditions);

    /**
     * 根据事件删除条件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     */
    void deleteByEvent(Integer equipmentTemplateId, Integer eventId);

    /**
     * 更新事件条件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @param conditions 事件条件列表
     */
    void updateEventCondition(Integer equipmentTemplateId, Integer eventId, List<EventCondition> conditions);

    /**
     * 查找设备模板ID对应的最大事件条件
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 事件条件
     */
    EventCondition findMaxEventConditionByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 批量保存联通事件条件
     *
     * @return 是否保存成功
     */
    boolean batchsaveLianTongEventConditions();

    void createEventCondition(EventCondition eventCondition);

    void copyEventCondition(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    /**
     * 根据设备模板ID和事件ID查找事件条件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 事件条件列表
     */
    List<EventCondition> findByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId);

    /**
     * 根据设备模板ID和事件ID删除事件条件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     */
    void deleteByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId);

    /**
     * 批量创建事件条件
     * @param eventConditions 事件条件列表
     */
    void batchCreateEventCondition(List<EventCondition> eventConditions);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);
}
