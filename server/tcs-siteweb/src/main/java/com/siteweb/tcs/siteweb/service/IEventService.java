package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.BatchEventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventConfigItem;
import com.siteweb.tcs.siteweb.dto.EventFieldCopyDTO;
import com.siteweb.tcs.siteweb.dto.excel.EventExcel;
import com.siteweb.tcs.siteweb.entity.Event;

import java.util.List;

/**
 * Event Service Interface
 */
public interface IEventService extends IService<Event> {

    /**
     * 根据事件配置项创建事件
     * @param eventConfigItem 事件配置项
     */
    void createEventByEventItem(EventConfigItem eventConfigItem);

    /**
     * 根据事件配置项更新事件
     * @param eventConfigItem 事件配置项
     */
    void updateByEventItem(EventConfigItem eventConfigItem);

    /**
     * 批量更新事件
     * @param batchEventConfigItem 批量事件配置项
     */
    void batchUpdateByEventItem(BatchEventConfigItem batchEventConfigItem);

    /**
     * 删除事件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 删除结果
     */
    int deleteEvent(Integer equipmentTemplateId, Integer eventId);

    /**
     * 批量删除事件
     * @param equipmentTemplateId 设备模板ID
     * @param eventIds 事件ID列表
     * @return 删除结果
     */
    Boolean batchDeleteEvent(int equipmentTemplateId, List<Integer> eventIds);

    /**
     * 根据设备模板ID查询事件
     * @param equipmentTemplateId 设备模板ID
     * @return 事件配置项列表
     */
    List<EventConfigItem> findEventItemByEquipmentTemplateId(Integer equipmentTemplateId);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 获取事件信息
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 事件配置项
     */
    EventConfigItem getEventInfo(Integer equipmentTemplateId, Integer eventId);

    /**
     * 根据设备模板ID和事件ID查询事件
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 事件配置项
     */
    EventConfigItem findByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId);

    /**
     * 关联事件
     * @param equipmentTemplateId 设备模板ID
     * @param signalId 信号ID
     * @return 关联结果
     */
    boolean linkEvent(Integer equipmentTemplateId, Integer signalId);

    /**
     * 批量保存联通事件
     *
     * @return 是否保存成功
     */
    boolean batchsaveLianTongEvents();

    /**
     * 更新工作站事件名称
     *
     * @param workStationName 工作站名称
     * @param equipmentTemplateId 设备模板ID
     */
    void updateWorkStationEventName(String workStationName, Integer equipmentTemplateId);

    /**
     * 更新数据库工作站事件名称
     *
     * @param workStationName 工作站名称
     * @param equipmentTemplateId 设备模板ID
     * @param workStationId 工作站ID
     */
    void updateDBWorkStationEventName(String workStationName, Integer equipmentTemplateId, Integer workStationId);

    /**
     * 更新自诊断事件
     *
     * @param equipmentTemplateId 设备模板ID
     * @param centerId 中心ID
     */
    void updateSelfDiagnosisEvent(Integer equipmentTemplateId, Integer centerId);

    /**
     * 查找设备模板ID对应的最大事件
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 事件配置项
     */
    EventConfigItem findMaxEventByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * Find cmcc type IDs not in event cmcc dictionary for equipment template
     *
     * @param equipmentTemplateId Equipment template ID
     * @return List of cmcc type IDs
     */
    List<Long> findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 创建通信状态事件
     *
     * @param equipmentTemplateId 设备模板ID
     */
    void createCommunicationStateEvent(Integer equipmentTemplateId);

    void createEvent(Event event);

    /**
     * 批量插入事件
     *
     * @param batchEventList 事件列表
     */
    void batchInsertEvent(List<Event> batchEventList);

    /**
     * 应用事件标准化
     *
     * @param standardId 标准ID
     * @param equipmentTemplateIds 设备模板ID列表
     * @return 应用的数量
     */
    Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds);

    // ==================== 设备管理相关方法 ====================

    /**
     * 创建事件并返回创建的事件
     *
     * @param eventConfigItem 事件配置项
     * @return 创建的事件
     */
    Event createEvent(EventConfigItem eventConfigItem);

    /**
     * 更新事件并返回更新的事件
     *
     * @param eventConfigItem 事件配置项
     * @return 更新的事件
     */
    Event updateEvent(EventConfigItem eventConfigItem);

    /**
     * 批量更新事件
     *
     * @param batchEventConfigItem 批量事件配置项
     * @return 是否更新成功
     */
    boolean batchUpdateEvent(BatchEventConfigItem batchEventConfigItem);

    List<EventExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId);

    void copyEvent(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    void updateEventDescriptions(Integer childTemplateId, List<Event> parentEvents);

    /**
     * 事件字段复制
     *
     * @param eventFieldCopyList 事件字段复制列表
     * @return 是否复制成功
     */
    boolean fieldCopyEvent(List<EventFieldCopyDTO> eventFieldCopyList);

}
