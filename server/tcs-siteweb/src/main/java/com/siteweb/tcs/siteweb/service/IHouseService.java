package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.House;

import java.util.List;

/**
 * House Service Interface
 */
public interface IHouseService extends IService<House> {

    /**
     * Find house by ID
     *
     * @param houseId House ID
     * @return House
     */
    House findByHouseId(Integer houseId);

    /**
     * Find default house of a station
     *
     * @param stationId Station ID
     * @return Default house
     */
    House findStationDefaultHouse(Integer stationId);

    /**
     * Find houses by station ID
     *
     * @param stationId Station ID
     * @return List of houses
     */
    List<House> findHouseByStationId(Integer stationId);

    /**
     * Create house
     *
     * @param house House to create
     * @return True if created successfully
     */
    boolean createHouse(House house);

    Boolean updateHouse(House house);
}
