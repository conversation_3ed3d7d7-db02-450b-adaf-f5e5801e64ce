package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.MonitorUnitConfig;

import java.util.List;

/**
 * Monitor Unit Config Service Interface
 */
public interface IMonitorUnitConfigService extends IService<MonitorUnitConfig> {

    /**
     * 更新所有监控单元配置的数据服务器IP地址
     *
     * @param ipAddressDS 数据服务器IP地址
     */
    void updateAllIpAddressDS(String ipAddressDS);

    /**
     * 根据监控单元ID查找配置
     *
     * @param monitorUnitId 监控单元ID
     * @return 监控单元配置列表
     */
    List<MonitorUnitConfig> findByMonitorUnitId(Integer monitorUnitId);
}
