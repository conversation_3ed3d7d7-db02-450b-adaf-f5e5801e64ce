package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.IdValueDTO;
import com.siteweb.tcs.siteweb.dto.OperationDetailDTO;
import com.siteweb.tcs.siteweb.entity.OperationDetail;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.vo.OperationDetailVO;

import java.util.List;
import java.util.Map;

/**
 * Operation Detail Service Interface
 */
public interface IOperationDetailService extends IService<OperationDetail> {

    /**
     * 记录操作日志（自动获取当前用户信息）
     *
     * @param objectId                对象id
     * @param operationObjectTypeEnum 对象类型
     * @param propertyName            操作属性
     * @param operationType           操作
     * @param oldValue                旧值
     * @param newValue                新值
     */
    void recordOperationLog(String objectId, OperationObjectTypeEnum operationObjectTypeEnum, String propertyName, String operationType, String oldValue, String newValue);

    /**
     * 比较实体并记录操作日志（自动获取当前用户信息）
     * 注意【只能用于更新操作】
     *
     * @param oldEntity  旧实体
     * @param newEntity  新实体
     */
    <T> void compareEntitiesRecordLog(T oldEntity, T newEntity);

    /**
     * 获取所有操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findPage(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO);

    /**
     * 根据对象类型和对象ID查询操作日志
     *
     * @param page 分页参数
     * @param objectType 对象类型
     * @param objectId 对象ID
     * @return 分页结果
     */
    Page<OperationDetail> findPageByObjectTypeAndObjectId(Page<OperationDetail> page, String objectType, String objectId);

    /**
     * 查询设备模板相关的操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentTemplateLogPage(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO);

    /**
     * 查询设备相关的操作日志
     *
     * @param page 分页参数
     * @param operationDetailDTO 查询条件
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentLogPage(Page<OperationDetail> page, OperationDetailDTO operationDetailDTO);

    // ==================== 设备管理相关方法 ====================

    /**
     * 获取操作类型列表
     *
     * @return 操作类型列表
     */
    List<IdValueDTO<Integer, String>> findOperationTypes();

    /**
     * 清理指定天数之前的操作日志
     *
     * @param daysToKeep 保留天数
     * @return 清理的记录数
     */
    int cleanupOldOperationLogs(int daysToKeep);

    /**
     * 获取设备模板日志列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentTemplateLogPage(Map<String, Object> params);

    /**
     * 获取设备日志列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    Page<OperationDetailVO> findEquipmentLogPage(Map<String, Object> params);

    /**
     * 获取全部日志列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    Page<OperationDetailVO> findPage(Map<String, Object> params);
}
