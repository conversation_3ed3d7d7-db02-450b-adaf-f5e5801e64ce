package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.entity.Station;

import java.util.List;
import java.util.Map;

/**
 * Resource Structure Service Interface
 */
public interface IResourceStructureService extends IService<ResourceStructure> {

    /**
     * Get structure by ID
     *
     * @param structureId Structure ID
     * @return Resource structure
     */
    ResourceStructure getStructureByID(Integer structureId);

    /**
     * Get the root resource structure.
     * The root is typically identified by a null ParentResourceStructureId or a specific predefined ID.
     * @return The root ResourceStructure, or null if not found.
     */
    ResourceStructure getRootStructure();

    /**
     * Get all resource structures.
     * @return A list of all ResourceStructure entities.
     */
    List<ResourceStructure> getAllStructures();

    /**
     * Get child resource structures by parent ID.
     * @param parentId The ID of the parent resource structure.
     * @return A list of child ResourceStructure entities.
     */
    List<ResourceStructure> getChildrenByParentId(Integer parentId);

    /**
     * Get tree root ID
     * @return Root ID
     */
    Integer getTreeRootId();

    /**
     * Find resource structure by origin ID and structure type
     *
     * @param originId Origin ID
     * @param structureType Structure type
     * @return Resource structure
     */
    ResourceStructure findByOriginIdAndStructureType(Integer originId, Integer structureType);

    /**
     * Delete resource structure by ID
     *
     * @param resourceStructureId Resource structure ID
     * @return True if deleted successfully
     */
    boolean deleteByID(Integer resourceStructureId);

    /**
     * Update resource structure
     *
     * @param resourceStructure Resource structure to update
     * @return True if updated successfully
     */
    boolean update(ResourceStructure resourceStructure);

    /**
     * Find resource structures by parent resource structure ID
     *
     * @param parentResourceStructureId Parent resource structure ID
     * @return List of resource structures
     */
    List<ResourceStructure> findByParentResourceStructureId(Integer parentResourceStructureId);

    /**
     * Create resource structure
     *
     * @param resourceStructure Resource structure to create
     * @return True if created successfully
     */
    Boolean create(ResourceStructure resourceStructure);

    ResourceStructure findResourceStructureByOriginIdAndParentIdAndStructureTypeId(Integer houseId, Integer stationId, Integer value);

    /**
     * Create default station for resource structure
     *
     * @param resourceStructureDTO Resource structure DTO
     * @return Created station
     */
    Station createDefaultStation(com.siteweb.tcs.siteweb.dto.ResourceStructureDTO resourceStructureDTO);

    /**
     * Create station template room for resource structure
     *
     * @param resourceStructure Resource structure
     * @return True if created successfully
     */
    boolean createStationTemplate(ResourceStructure resourceStructure);

    List<ResourceStructure> createStationResourceStructure(Integer stationStructureId);

    List<ResourceStructure> findResourceStructures();
}
