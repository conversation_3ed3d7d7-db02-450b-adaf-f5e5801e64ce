package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.enums.ProtocolTypeEnum;
import com.siteweb.tcs.siteweb.vo.SamplerVO;
import org.dom4j.Element;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * Sampler Service Interface
 */
public interface ISamplerService extends IService<Sampler> {

    /**
     * Find a sampler by protocol code
     *
     * @param protocolCode Protocol code
     * @return Sampler entity or null if not found
     */
    Sampler getSamplerByProtocolCode(String protocolCode);

    /**
     * 批量保存联通采样器
     *
     * @return 是否保存成功
     */
    boolean batchInsertLianTongSampler();

    Sampler findBySamplerType(int samplerType);

    /**
     * 根据采样器名称和DLL路径查找采样器
     *
     * @param samplerName 采样器名称
     * @param dllPath DLL路径
     * @return 采样器实体
     */
    Sampler findByNameAndDllPath(String samplerName, String dllPath);

    void createSamplersFromXml(EquipmentTemplate equipmentTemplate, Element samplers);

    void create(Sampler sampler);

    List<SamplerVO> findAllVo();

    List<Sampler> findByIds(List<Integer> samplerIdList);


    Set<String> findReferenceEquipmentNameByProtocolCodes(List<String> protocolCodes);

    Set<String> findReferenceSamplerNameBySamplerIdList(List<Integer> samplerIdList);

    @Transactional(rollbackFor = Exception.class)
    void uploadProtocolFile(Sampler sampler, ProtocolTypeEnum protocolTypeEnum, byte[] bytes, String fileName);

    void deleteProtocolFile(String protocolCode, ProtocolTypeEnum protocolTypeEnum );

    Sampler findByProtocolCode(String protocolCode);

    Sampler findById(Integer samplerId);

    @Transactional(rollbackFor = Exception.class)
    boolean update(Sampler sampler);

    /**
     * 删除单个采集器
     *
     * @param samplerId 采集器ID
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    boolean deleteById(Integer samplerId);

    /**
     * 批量删除采集器
     *
     * @param samplerIdList 采集器ID列表
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    boolean deleteByIds(List<Integer> samplerIdList);

    /**
     * 根据设备模板ID查找采集器
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 采集器实体
     */
    Sampler findByEquipmentTemplateId(Integer equipmentTemplateId);
}
