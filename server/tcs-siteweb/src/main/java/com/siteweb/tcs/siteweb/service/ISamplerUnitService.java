package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.SamplerUnit;
import com.siteweb.tcs.siteweb.vo.SamplerUnitWithPortVO;

import java.util.List;

/**
 * Sampler Unit Service Interface
 */
public interface ISamplerUnitService extends IService<SamplerUnit> {
    
    /**
     * Find a sampler unit by its ID
     *
     * @param samplerUnitId Sampler unit ID
     * @return Sampler unit
     */
    SamplerUnit findBySamplerUnitId(Integer samplerUnitId);
    
    /**
     * Create a new sampler unit
     *
     * @param samplerUnit Sampler unit entity
     * @return Created sampler unit
     */
    SamplerUnit createSamplerUnit(SamplerUnit samplerUnit);
    
    /**
     * Update a sampler unit
     *
     * @param samplerUnit Sampler unit entity
     * @return true if updated successfully, false otherwise
     */
    boolean updateSamplerUnit(SamplerUnit samplerUnit);
    
    /**
     * Delete a sampler unit by its ID
     *
     * @param samplerUnitId Sampler unit ID
     * @return true if deleted successfully, false otherwise
     */
    boolean deleteSamplerUnit(Integer samplerUnitId);

    void deleteByPortId(Integer portId);

    /**
     * Select sampler units with port information for a specific monitor unit
     *
     * @param monitorUnitId Monitor unit ID
     * @return List of sampler units
     */
    List<SamplerUnit> selectSamplerUnitWithPort(Integer monitorUnitId);
    
    /**
     * Find sampler units by monitor unit ID
     *
     * @param monitorUnitId Monitor unit ID
     * @return List of sampler units
     */
    List<SamplerUnit> findByMonitorUnitId(Integer monitorUnitId);
    
    /**
     * Find sampler units by parent sampler unit ID and sampler unit name
     *
     * @param parentSamplerUnitId Parent sampler unit ID
     * @param samplerUnitName Sampler unit name
     * @return List of sampler units
     */
    List<SamplerUnit> findByParentSamplerUnitIdAndSamplerUnitName(Integer parentSamplerUnitId, String samplerUnitName);
    
    /**
     * Find a sampler unit by monitor unit ID, sampler unit name, and port name
     *
     * @param monitorUnitId Monitor unit ID
     * @param samplerUnitName Sampler unit name
     * @param portName Port name
     * @return Sampler unit
     */
    SamplerUnit findSamplerUnit(Integer monitorUnitId, String samplerUnitName, String portName);
    
    /**
     * Find sampler units by monitor unit ID and port ID
     *
     * @param monitorUnitId Monitor unit ID
     * @param portId Port ID
     * @return List of sampler units
     */
    List<SamplerUnit> findByMonitorUnitIdAndPortId(Integer monitorUnitId, Integer portId);

    // ==================== 设备管理相关方法 ====================

    /**
     * 获取采样单元列表（带端口信息）
     *
     * @return 采样单元带端口信息VO列表
     */
    List<SamplerUnitWithPortVO> getSamplerUnitWithPort();
}
