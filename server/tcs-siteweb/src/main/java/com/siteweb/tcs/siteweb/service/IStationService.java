package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.StationDTO;
import com.siteweb.tcs.siteweb.entity.Station;

/**
 * Station Service Interface
 */
public interface IStationService extends IService<Station> {

    /**
     * 创建局站
     *
     * @param station 局站
     * @return 创建结果
     */
    boolean create(Station station);

    /**
     * 根据站点ID查找站点
     *
     * @param stationId 站点ID
     * @return 站点信息
     */
    Station findByStationId(int stationId);


    Boolean createDTO(StationDTO station);

    Station findStationByName(String stationName);

    Boolean updateDTO(StationDTO stationDTO);

    Boolean update(Station station);
}
