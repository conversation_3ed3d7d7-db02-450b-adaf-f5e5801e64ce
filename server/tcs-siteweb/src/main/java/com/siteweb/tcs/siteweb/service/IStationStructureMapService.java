package com.siteweb.tcs.siteweb.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.StationStructureMap;

/**
 * Station Structure Map Service Interface
 */
public interface IStationStructureMapService extends IService<StationStructureMap> {
    
    /**
     * 根据站点ID删除站点结构映射
     *
     * @param stationId 站点ID
     * @return 是否删除成功
     */
    boolean deleteByStationId(Integer stationId);
    
    /**
     * 根据站点ID查询站点结构映射
     *
     * @param stationId 站点ID
     * @return 站点结构映射
     */
    StationStructureMap findStationStructureMapByStationId(Integer stationId);
    
    /**
     * 根据结构ID查询站点结构映射列表
     *
     * @param structureId 结构ID
     * @return 站点结构映射列表
     */
    List<StationStructureMap> findStationStructureMapByStructureId(Integer structureId);

    /**
     * 创建站点结构映射
     *
     * @param stationStructureMap 站点结构映射
     * @return 创建结果
     */
    boolean create(StationStructureMap stationStructureMap);
}
