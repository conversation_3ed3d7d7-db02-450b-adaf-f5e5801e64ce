package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.siteweb.entity.TaskStatus;
import com.siteweb.tcs.siteweb.enums.TaskStatusEnum;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.vo.TaskStatusVO;

import java.util.List;

/**
 * 任务状态服务接口
 * 用于替代WebSocket状态管理
 */
public interface ITaskStatusService {

    /**
     * 创建新任务
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @param monitorUnitId 监控单元ID（可选）
     * @return 任务状态
     */
    TaskStatus createTask(String taskId, TaskTypeEnum taskType, String monitorUnitId);


    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 状态
     * @param message 消息
     * @param isFinal 是否是最终状态
     */
    void updateTaskStatus(String taskId, TaskStatusEnum status, String message, Boolean isFinal);

    /**
     * 更新任务进度
     * @param taskId 任务ID
     * @param progress 进度（0-100）
     * @param currentStep 当前步骤
     * @param message 消息
     */
    void updateTaskProgress(String taskId, Integer progress, String currentStep, String message);

    /**
     * 设置任务错误
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     */
    void setTaskError(String taskId, String errorMessage);

    /**
     * 完成任务
     * @param taskId 任务ID
     * @param message 完成消息
     */
    void completeTask(String taskId, String message);

    /**
     * 获取任务状态
     * @param taskId 任务ID
     * @return 任务状态VO
     */
    TaskStatusVO getTaskStatus(String taskId);

    /**
     * 获取任务的所有状态历史
     * @param taskId 任务ID
     * @return 状态历史列表
     */
    List<TaskStatusVO> getTaskHistory(String taskId);

    /**
     * 根据类型获取活跃任务
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<TaskStatusVO> getActiveTasksByType(TaskTypeEnum taskType);

    /**
     * 根据监控单元ID获取相关任务
     * @param monitorUnitId 监控单元ID
     * @return 任务列表
     */
    List<TaskStatusVO> getTasksByMonitorUnitId(Integer monitorUnitId);

    /**
     * 删除任务
     * @param taskId 任务ID
     */
    void deleteTask(String taskId);

    /**
     * 清理过期任务
     */
    void cleanupExpiredTasks();

    /**
     * 发送状态消息（替代WebSocket发送）
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @param message 消息
     * @param isFinal 是否是最终状态
     */
    void sendMessage(String taskId, TaskTypeEnum taskType, String message, Boolean isFinal);

    /**
     * 发送带进度的状态消息
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @param message 消息
     * @param progress 进度
     * @param isFinal 是否是最终状态
     */
    void sendMessageWithProgress(String taskId, TaskTypeEnum taskType, String message, Integer progress, Boolean isFinal);

    /**
     * 分页查询任务历史
     * @param page 分页对象
     * @param taskType 任务类型（可选）
     * @return 分页结果
     */
    Page<TaskStatusVO> pageTaskHistory(Page<TaskStatus> page, String taskType);

    // ==================== 新增的简化增强方法 ====================

    /**
     * 设置任务错误（简化版本，避免错误信息过长）
     * @param taskId 任务ID
     * @param errorMessage 错误消息（会被简化）
     */
    void setTaskErrorSimplified(String taskId, String errorMessage);

    /**
     * 完成任务（根据成功失败情况自动判断状态）
     * @param taskId 任务ID
     * @param totalCount 总数量
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param message 完成消息
     */
    void completeTaskWithCounts(String taskId, int totalCount, int successCount, int failureCount, String message);

    /**
     * 从数据库获取任务状态（内部使用）
     * @param taskId 任务ID
     * @return 任务状态
     */
    TaskStatus getTaskFromDatabase(String taskId);

    /**
     * 更新任务到数据库（内部使用）
     * @param taskStatus 任务状态
     */
    void updateTaskInDatabase(TaskStatus taskStatus);
}
