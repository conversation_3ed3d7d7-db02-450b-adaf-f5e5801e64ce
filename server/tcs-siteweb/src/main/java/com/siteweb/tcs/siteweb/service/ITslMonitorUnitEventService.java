package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitEvent;

import java.util.List;

/**
 * 监控单元事件服务接口
 */
public interface ITslMonitorUnitEventService extends IService<TslMonitorUnitEvent> {
    
    /**
     * 根据站点ID删除监控单元事件
     *
     * @param stationId 站点ID
     * @return 是否删除成功
     */
    boolean deleteByStationId(Integer stationId);

    // ==================== 设备管理相关方法 ====================

    /**
     * 根据设备ID和事件ID查询监控单元事件
     *
     * @param equipmentId 设备ID
     * @param eventId 事件ID
     * @return 监控单元事件列表
     */
    List<TslMonitorUnitEvent> findByEquipmentIdAndEventId(Integer equipmentId, Integer eventId);

    /**
     * 创建或更新监控单元事件
     *
     * @param event 监控单元事件
     * @return 是否成功
     */
    boolean createOrUpdate(TslMonitorUnitEvent event);

    /**
     * 删除监控单元事件
     *
     * @param equipmentId 设备ID
     * @param eventId 事件ID
     * @return 是否成功
     */
    boolean deleteByEquipmentIdAndEventId(Integer equipmentId, Integer eventId);
}