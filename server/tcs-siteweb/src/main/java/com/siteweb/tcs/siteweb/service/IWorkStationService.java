package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.dto.CenterDTO;
import com.siteweb.tcs.siteweb.entity.WorkStation;
import com.siteweb.tcs.siteweb.enums.WorkStationTypeEnum;

import java.util.List;

/**
 * Work Station Service Interface
 */
public interface IWorkStationService extends IService<WorkStation> {

    /**
     * 创建默认工作站
     *
     * @param centerDTO 中心DTO
     */
    void createDefaultWorkStation(CenterDTO centerDTO);

    /**
     * 批量插入工作站
     *
     * @param workStations 工作站列表
     */
    void batchInsertWorkStation(List<WorkStation> workStations);

    /**
     * 根据工作站ID查找工作站
     *
     * @param workStationId 工作站ID
     * @return 工作站
     */
    WorkStation findByWorkStationId(int workStationId);

    /**
     * 根据工作站类型查找工作站
     *
     * @param workStationTypeEnum 工作站类型枚举
     * @return 工作站列表
     */
    List<WorkStation> findByWorkStationType(WorkStationTypeEnum workStationTypeEnum);

    /**
     * 查找数据服务器工作站
     *
     * @return 数据服务器工作站列表
     */
    List<WorkStation> findDsWorkStations();

    /**
     * 查找实时数据服务器工作站
     *
     * @return 实时数据服务器工作站列表
     */
    List<WorkStation> findRDsWorkStations();
}
