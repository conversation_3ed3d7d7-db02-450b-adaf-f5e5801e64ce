package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.AcrossMonitorUnitSignal;
import com.siteweb.tcs.siteweb.mapper.AcrossMonitorUnitSignalMapper;
import com.siteweb.tcs.siteweb.service.IAcrossMonitorUnitSignalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 跨站监控单元信号 Service Implementation
 */
@Slf4j
@Service
public class AcrossMonitorUnitSignalServiceImpl extends ServiceImpl<AcrossMonitorUnitSignalMapper, AcrossMonitorUnitSignal> implements IAcrossMonitorUnitSignalService {

    @Autowired
    private AcrossMonitorUnitSignalMapper acrossMonitorUnitSignalMapper;

    @Override
    public List<AcrossMonitorUnitSignal> findByCondition(Map<String, Object> condition) {
        try {
            return acrossMonitorUnitSignalMapper.findByCondition(condition);
        } catch (Exception e) {
            log.error("Failed to find across monitor unit signals by condition", e);
            return List.of();
        }
    }

    @Override
    public List<AcrossMonitorUnitSignal> findByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId) {
        try {
            return acrossMonitorUnitSignalMapper.findByEquipmentIdAndSignalId(equipmentId, signalId);
        } catch (Exception e) {
            log.error("Failed to find across monitor unit signals by equipment ID {} and signal ID {}", equipmentId, signalId, e);
            return List.of();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAcrossMonitorUnitSignal(AcrossMonitorUnitSignal signal) {
        try {
            if (signal == null) {
                log.warn("Cannot create across monitor unit signal: signal is null");
                return false;
            }

            int result = acrossMonitorUnitSignalMapper.createAcrossMonitorUnitSignal(signal);
            if (result > 0) {
                log.info("Successfully created across monitor unit signal for equipment {} and signal {}",
                        signal.getEquipmentId(), signal.getSignalId());
                return true;
            } else {
                log.warn("Failed to create across monitor unit signal for equipment {} and signal {}",
                        signal.getEquipmentId(), signal.getSignalId());
                return false;
            }
        } catch (Exception e) {
            log.error("Error creating across monitor unit signal", e);
            throw e;
        }
    }
}
