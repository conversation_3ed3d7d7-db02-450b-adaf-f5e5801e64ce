package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.dto.CommandBaseMapDTO;
import com.siteweb.tcs.siteweb.entity.CommandBaseMap;
import com.siteweb.tcs.siteweb.mapper.CommandBaseMapMapper;
import com.siteweb.tcs.siteweb.service.ICommandBaseMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Command Base Map Service Implementation
 */
@Service
public class CommandBaseMapServiceImpl extends ServiceImpl<CommandBaseMapMapper, CommandBaseMap> implements ICommandBaseMapService {

    @Autowired
    private CommandBaseMapMapper commandBaseMapMapper;

    @Override
    public List<CommandBaseMapDTO> getControlBaseMap(Integer standardId) {
        return commandBaseMapMapper.getControlBaseMap(standardId);
    }
}
