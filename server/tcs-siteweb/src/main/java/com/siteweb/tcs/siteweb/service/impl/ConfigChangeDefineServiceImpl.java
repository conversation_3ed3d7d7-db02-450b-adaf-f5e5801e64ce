package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.ConfigChangeDefine;
import com.siteweb.tcs.siteweb.mapper.ConfigChangeDefineMapper;
import com.siteweb.tcs.siteweb.service.IConfigChangeDefineService;
import org.springframework.stereotype.Service;

/**
 * Config Change Define Service Implementation
 */
@Service
public class ConfigChangeDefineServiceImpl extends ServiceImpl<ConfigChangeDefineMapper, ConfigChangeDefine> implements IConfigChangeDefineService {
}
