package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.common.util.FileUtil;

import com.siteweb.tcs.siteweb.dto.ConfigDiffResult;
import com.siteweb.tcs.siteweb.dto.ConfigImportReport;
import com.siteweb.tcs.siteweb.dto.ConfigImportResult;
import com.siteweb.tcs.siteweb.dto.ConfigImportTaskRequest;
import com.siteweb.tcs.siteweb.dto.ConfigUploadAndDiffResult;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.ProtocolTypeEnum;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.TaskStatusEnum;
import com.siteweb.tcs.siteweb.enums.TaskTypeEnum;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.PathUtil;
import com.siteweb.tcs.siteweb.util.XMLUtil;
import com.siteweb.tcs.siteweb.handler.FilePathProvider;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import cn.hutool.crypto.digest.DigestUtil;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipInputStream;

/**
 * 配置导入服务实现
 */
@Slf4j
@Service
public class ConfigImportServiceImpl implements IConfigImportService {

    // 存储上传文件信息的临时缓存
    private final Map<String, UploadFileInfo> uploadFileCache = new ConcurrentHashMap<>();

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private IMonitorUnitService monitorUnitService;

    @Autowired
    private ITaskStatusService taskStatusService;

    @Autowired
    private ISignalService signalService;

    @Autowired
    private IEventService eventService;

    @Autowired
    private IControlService controlService;

    @Autowired
    private IEventConditionService eventConditionService;

    @Autowired
    private IControlMeaningsService controlMeaningsService;

    @Autowired
    private ISignalPropertyService signalPropertyService;

    @Autowired
    private ISignalMeaningsService signalMeaningsService;

    @Autowired
    private IPortService portService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired(required = false)
    private IOperationDetailService operationDetailService;

    @Autowired
    private ISamplerService samplerService;

    @Autowired
    private FilePathProvider filePathProvider;

    @Autowired
    private FileUtil fileUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConfigImportReport executeImportFromFile(String taskId, String zipFilePath, ConfigImportTaskRequest request) {
        ConfigImportReport report = new ConfigImportReport();
        report.setTaskId(taskId);
        report.setImportTime(LocalDateTime.now());
        report.setImportMode(request.getImportMode());

        File tempDir = null;
        try {
            // 更新任务状态
            taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.RUNNING, "开始解压配置文件...", false);
            File zipFile = new File(zipFilePath);
            // 1. 解压zip文件
            tempDir = extractZipFileFromFile(zipFile);
            log.info("配置文件解压完成: {}", tempDir.getAbsolutePath());

            // 2. 查找XML配置文件
            taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.RUNNING, "查找配置文件...", false);
            File[] xmlFiles = findXmlConfigFiles(tempDir);
            if (xmlFiles == null || xmlFiles.length == 0) {
                throw new RuntimeException("未找到有效的XML配置文件");
            }

            log.info("找到 {} 个XML配置文件", xmlFiles.length);

            // 3. 解析并导入配置文件
            taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.RUNNING, "解析并导入配置文件...", false);

            for (File xmlFile : xmlFiles) {
                try {
                    importSingleXmlFile(xmlFile, request, report);
                } catch (Exception e) {
                    log.error("导入XML文件失败: {}, 错误: {}", xmlFile.getName(), e.getMessage(), e);
                    // 继续处理其他文件，不中断整个导入过程
                }
            }

            // 4. 处理 SO 文件 TODO 暂时注释
//            taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.RUNNING, "处理 SO 文件...", false);
//            try {
//                // 查找监控单元 XML 文件
//                File monitorUnitXmlFile = findMonitorUnitXmlFile(xmlFiles);
//                if (monitorUnitXmlFile != null) {
//                    processSoFiles(zipFilePath, monitorUnitXmlFile.getAbsolutePath(), report);
//                } else {
//                    log.warn("未找到监控单元 XML 文件，跳过 SO 文件处理");
//                    addSoProcessingMessage(report, "未找到监控单元 XML 文件，跳过 SO 文件处理");
//                }
//            } catch (Exception e) {
//                log.error("处理 SO 文件时发生错误", e);
//                addSoProcessingMessage(report, "SO 文件处理失败: " + e.getMessage());
//                // SO 文件处理失败不影响整个导入流程
//            }

            // 5. 完成任务
            String successMsg = "配置导入完成";
            taskStatusService.completeTask(taskId, successMsg);
            log.info("配置导入任务完成: {}", taskId);

            // 记录操作日志
            if (operationDetailService != null && report.getMonitorUnitId() != null) {
                try {
                    operationDetailService.recordOperationLog(
                            report.getMonitorUnitId().toString(),
                            OperationObjectTypeEnum.MONITOR_UNIT,
                            "配置导入",
                            "导入",
                            "",
                            "配置导入完成"
                    );
                } catch (Exception e) {
                    log.warn("记录配置导入操作日志失败", e);
                }
            }

            report.setSuccess(true);

        } catch (Exception e) {
            report.setSuccess(false);
            report.setErrorMessage(e.getMessage());

            String errorMsg = "配置导入失败: " + e.getMessage();
            taskStatusService.setTaskError(taskId, errorMsg);
            log.error("配置导入任务失败: {}", taskId, e);

            throw new RuntimeException(errorMsg, e);
        } finally {
            // 5. 清理临时文件
            if (tempDir != null) {
                cleanupTempFiles(tempDir);
            }
        }

        return report;
    }

    /**
     * 从文件解压zip文件
     */
    private File extractZipFileFromFile(File zipFile) {
        try {
            // 使用指定的临时目录
            String tempDirName = "config_import_" + UUID.randomUUID().toString().replace("-", "");
            String tempRelativePath = "temp/" + tempDirName;
            String tempFullPath = filePathProvider.getFullPath(filePathProvider.getWorkspacePath(), tempRelativePath);

            // 创建临时目录
            fileUtil.createDirectory(tempFullPath);

            // 解压文件
            String extractRelativePath = tempRelativePath + "/extracted";
            String extractFullPath = filePathProvider.getFullPath(filePathProvider.getWorkspacePath(), extractRelativePath);
            fileUtil.createDirectory(extractFullPath);

            unzipFile(zipFile, new File(extractFullPath));

            return new File(extractFullPath);

        } catch (IOException e) {
            log.error("解压zip文件失败", e);
            throw new RuntimeException("解压zip文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导入单个XML文件
     */
    private void importSingleXmlFile(File xmlFile, ConfigImportTaskRequest request, ConfigImportReport report) {
        try {
            // 解析XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(xmlFile);
            Element root = document.getRootElement();

            // 查找EquipmentTemplates节点
            Element equipmentTemplatesElement = root.element("EquipmentTemplates");
            if (equipmentTemplatesElement == null) {
                log.warn("XML文件中未找到EquipmentTemplates节点: {}", xmlFile.getName());
                return;
            }

            // 查找EquipmentTemplates节点
            Element appConfigurationElement = root.element("AppConfiguration");
            if (appConfigurationElement == null) {
                log.warn("XML文件中未找到AppConfiguration节点: {}", xmlFile.getName());
                return;
            }

            // 获取所有设备模板
            @SuppressWarnings("unchecked")
            List<Element> templateElements = equipmentTemplatesElement.elements("EquipmentTemplate");

            for (Element templateElement : templateElements) {
                try {
                    importSingleEquipmentTemplate(templateElement, equipmentTemplatesElement, request, report);
                } catch (Exception e) {
                    log.error("导入设备模板失败", e);
                    // 继续处理其他模板
                }
            }

            // 查找MonitorUnit节点
            Element monitorUnitElement = root.element("MonitorUnit");
            if (monitorUnitElement != null) {
                try {
                    importMonitorUnit(monitorUnitElement,appConfigurationElement, request, report);
                } catch (Exception e) {
                    log.error("导入监控单元失败", e);
                    // 继续处理，不中断整个导入过程
                }
            }

        } catch (Exception e) {
            log.error("解析XML文件失败: {}", xmlFile.getName(), e);
            throw new RuntimeException("解析XML文件失败: " + xmlFile.getName(), e);
        }
    }

    /**
     * 导入单个设备模板（保留原始ID）
     */
    private void importSingleEquipmentTemplate(Element templateElement, Element equipmentTemplatesElement,
                                             ConfigImportTaskRequest request, ConfigImportReport report) {
        try {
            // 获取设备模板ID
            String templateIdStr = templateElement.attributeValue("EquipmentTemplateId");
            String templateName = templateElement.attributeValue("EquipmentTemplateName");

            if (templateIdStr == null) {
                log.warn("设备模板缺少ID，跳过导入: {}", templateName);
                return;
            }

            Integer templateId = Integer.parseInt(templateIdStr);
            List<Equipment> equipmentsToRebind = new ArrayList<>();

            // 检查是否需要删除现有模板
            if (request.getOverwrite()) {
                EquipmentTemplate existingTemplate = equipmentTemplateService.getById(templateId);
                if (existingTemplate != null) {
                    log.info("智能删除现有设备模板: {} (ID: {})", existingTemplate.getEquipmentTemplateName(), templateId);

                    // 使用智能删除方法，保存其他监控单元的设备信息
                    equipmentsToRebind = equipmentTemplateService.deleteTemplateForConfigImport(
                            templateId, request.getCurrentMonitorUnitId());
                }
            }

            EquipmentTemplate equipmentTemplate = parseEquipmentTemplateFromXml(templateElement);
            if (equipmentTemplate == null) {
                log.warn("解析设备模板失败: {}", templateName);
                return;
            }

            boolean saved = equipmentTemplateService.save(equipmentTemplate);
            if (!saved) {
                log.error("保存设备模板失败: {}", templateName);
                return;
            }

            log.info("成功导入设备模板: {} (ID: {})", equipmentTemplate.getEquipmentTemplateName(), equipmentTemplate.getEquipmentTemplateId());

            // 导入信号
            Element signalsElement = templateElement.element("Signals");
            if (signalsElement != null) {
                importSignalsFromXml(templateId, signalsElement, report);
            }

            // 导入事件
            Element eventsElement = templateElement.element("Events");
            if (eventsElement != null) {
                importEventsFromXml(templateId, eventsElement, report);
            }

            // 导入控制
            Element controlsElement = templateElement.element("Controls");
            if (controlsElement != null) {
                importControlsFromXml(templateId, controlsElement, report);
            }

            // 重新绑定其他监控单元的设备到新模板
            if (!equipmentsToRebind.isEmpty()) {
                try {
                    equipmentTemplateService.rebindEquipmentToNewTemplate(equipmentsToRebind, templateId);
                    log.info("成功重新绑定{}个设备到新模板[{}]", equipmentsToRebind.size(), templateId);
                } catch (Exception e) {
                    log.error("重新绑定设备失败，但设备模板导入成功", e);
                    // 不抛出异常，因为设备模板已经成功导入
                }
            }

        } catch (Exception e) {
            log.error("导入设备模板失败", e);
            throw new RuntimeException("导入设备模板失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导入监控单元及其关联数据
     */
    private void importMonitorUnit(Element monitorUnitElement,Element appConfigurationElement, ConfigImportTaskRequest request, ConfigImportReport report) {
        try {
            // 获取监控单元ID
            String monitorUnitIdStr = monitorUnitElement.attributeValue("MonitorUnitId");
            if (monitorUnitIdStr == null) {
                log.warn("监控单元缺少ID，跳过导入");
                return;
            }

            Integer monitorUnitId = Integer.parseInt(monitorUnitIdStr);

            // 解析并创建监控单元
            MonitorUnit monitorUnit = parseMonitorUnitFromXml(monitorUnitElement);
            if (monitorUnit == null) {
                log.warn("解析监控单元失败");
                return;
            }
//            if (!Objects.equals(monitorUnit.getMonitorUnitId(),request.getCurrentMonitorUnitId())) {
//                log.warn("监控单元ID与请求中的监控单元ID不一致，跳过导入");
//                return;
//            }

            // 检查是否需要删除现有监控单元
            if (request.getOverwrite()) {
                MonitorUnit existingMonitorUnit = monitorUnitService.getById(monitorUnitId);
                if (existingMonitorUnit != null) {
                    log.info("删除现有监控单元: {} (ID: {})", existingMonitorUnit.getMonitorUnitName(), monitorUnitId);
                    // 保留原有结构位置
                    monitorUnit.setStationId(existingMonitorUnit.getStationId());
                    monitorUnitService.deleteMonitorUnit(monitorUnitId);
                }
            }


            monitorUnit.setUpdateTime(LocalDateTime.now());

            if (appConfigurationElement != null) {
                monitorUnit.setAppConfigId(XMLUtil.getAttributeAsInteger(appConfigurationElement, "AppConfigId"));
            }

            // 保存监控单元
            boolean saved = monitorUnitService.save(monitorUnit);
            if (!saved) {
                log.error("保存监控单元失败: {}", monitorUnit.getMonitorUnitName());
                return;
            }

            log.info("成功导入监控单元: {} (ID: {})", monitorUnit.getMonitorUnitName(), monitorUnit.getMonitorUnitId());

            // 导入端口
            Element portsElement = monitorUnitElement.element("Ports");
            if (portsElement != null) {
                importPorts(monitorUnitId, portsElement, report);
            }

            // 导入采集单元
            samplerUnitService.remove(new LambdaQueryWrapper<SamplerUnit>().eq(SamplerUnit::getMonitorUnitId, monitorUnitId));
            Element samplerUnitsElement = monitorUnitElement.element("SamplerUnits");
            if (samplerUnitsElement != null) {
                importSamplerUnits(monitorUnitId, samplerUnitsElement, report);
            }

            // 导入设备
            Element equipmentsElement = monitorUnitElement.element("Equipments");
            if (equipmentsElement != null) {
                importEquipments(monitorUnitId, equipmentsElement, report);
            }

        } catch (Exception e) {
            log.error("导入监控单元失败", e);
            throw new RuntimeException("导入监控单元失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从XML解析设备模板（保留原始ID）
     */
    private EquipmentTemplate parseEquipmentTemplateFromXml(Element templateElement) {
        try {
            EquipmentTemplate template = new EquipmentTemplate();

            // 保留原始ID
            template.setEquipmentTemplateId(XMLUtil.getAttributeAsInteger(templateElement, "EquipmentTemplateId"));
            template.setEquipmentTemplateName(templateElement.attributeValue("EquipmentTemplateName"));
            template.setProtocolCode(templateElement.attributeValue("ProtocolCode"));
            template.setEquipmentCategory(XMLUtil.getAttributeAsInteger(templateElement, "EquipmentCategory"));
            template.setEquipmentType(XMLUtil.getAttributeAsInteger(templateElement, "EquipmentType"));
            template.setMemo(templateElement.attributeValue("Memo"));
            template.setProperty(templateElement.attributeValue("Property"));
            template.setDescription(templateElement.attributeValue("Decription")); // 注意XML中是Decription
            template.setEquipmentBaseType(XMLUtil.getAttributeAsInteger(templateElement, "EquipmentBaseType"));

            // 设置默认值
            template.setParentTemplateId(0);
            template.setEquipmentStyle("");
            template.setUnit("");
            template.setVendor("");
            template.setStationCategory(0);

            return template;

        } catch (Exception e) {
            log.error("解析设备模板XML失败", e);
            return null;
        }
    }

    /**
     * 从XML解析监控单元（保留原始ID）
     */
    private MonitorUnit parseMonitorUnitFromXml(Element monitorUnitElement) {
        try {
            MonitorUnit monitorUnit = new MonitorUnit();

            // 保留原始ID
            monitorUnit.setMonitorUnitId(XMLUtil.getAttributeAsInteger(monitorUnitElement, "MonitorUnitId"));
            monitorUnit.setMonitorUnitName(monitorUnitElement.attributeValue("MonitorUnitName"));
            monitorUnit.setMonitorUnitCategory(XMLUtil.getAttributeAsInteger(monitorUnitElement, "MonitorUnitCategory"));
            monitorUnit.setMonitorUnitCode(monitorUnitElement.attributeValue("MonitorUnitCode"));
            monitorUnit.setStationId(XMLUtil.getAttributeAsInteger(monitorUnitElement, "StationId"));
            monitorUnit.setIpAddress(monitorUnitElement.attributeValue("IpAddress"));
            monitorUnit.setRunMode(XMLUtil.getAttributeAsInteger(monitorUnitElement, "RunMode"));
            monitorUnit.setDescription(monitorUnitElement.attributeValue("Description"));

            // 设置默认值
            monitorUnit.setWorkStationId(0);
            monitorUnit.setConfigFileCode(monitorUnitElement.attributeValue("ConfigFileCode"));
            monitorUnit.setSampleConfigCode(monitorUnitElement.attributeValue("SampleConfigCode"));
            monitorUnit.setSoftwareVersion(monitorUnitElement.attributeValue("SoftwareVersion"));
            monitorUnit.setUpdateTime(LocalDateTime.now());
            monitorUnit.setCanDistribute(true);
            monitorUnit.setEnable(Boolean.TRUE);

            return monitorUnit;

        } catch (Exception e) {
            log.error("解析监控单元XML失败", e);
            return null;
        }
    }

    /**
     * 导入端口
     */
    private void importPorts(Integer monitorUnitId, Element portsElement, ConfigImportReport report) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> portElementList = portsElement.elements("Port");

            List<Port> batchPortList = new ArrayList<>(portElementList.size());

            for (Element portElement : portElementList) {
                Port port = parsePortFromXml(monitorUnitId, portElement);
                if (port != null) {
                    batchPortList.add(port);
                }
            }

            // 批量保存
            if (!batchPortList.isEmpty()) {
                portService.saveBatch(batchPortList);
                log.info("导入端口 {} 个", batchPortList.size());
            }

        } catch (Exception e) {
            log.error("导入端口失败", e);
            throw new RuntimeException("导入端口失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析端口（保留原始ID）
     */
    private Port parsePortFromXml(Integer monitorUnitId, Element element) {
        try {
            Port port = new Port();
            port.setMonitorUnitId(monitorUnitId);
            port.setPortId(XMLUtil.getAttributeAsInteger(element, "PortId"));
            port.setPortNo(XMLUtil.getAttributeAsInteger(element, "PortNo"));
            port.setPortName(element.attributeValue("PortName"));
            port.setPortType(XMLUtil.getAttributeAsInteger(element, "PortType"));
            port.setSetting(element.attributeValue("Setting"));
            port.setPhoneNumber(element.attributeValue("PhoneNumber"));
            port.setLinkSamplerUnitId(XMLUtil.getAttributeAsInteger(element, "LinkSamplerUnitId"));
            port.setDescription(element.attributeValue("Description"));

            return port;
        } catch (Exception e) {
            log.error("解析端口XML失败", e);
            return null;
        }
    }

    /**
     * 导入信号
     */
    private void importSignalsFromXml(Integer equipmentTemplateId, Element signalsElement, ConfigImportReport report) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> signalElementList = signalsElement.elements("Signal");

            List<Signal> batchSignalList = new ArrayList<>(signalElementList.size());
            List<SignalProperty> batchSignalPropertyList = new ArrayList<>();
            List<SignalMeanings> batchSignalMeaningsList = new ArrayList<>();

            for (Element signalElement : signalElementList) {
                // 解析信号（保留原始ID）
                Signal signal = parseSignalFromXml(equipmentTemplateId, signalElement);
                if (signal != null) {
                    batchSignalList.add(signal);

                    // 解析信号属性
                    List<SignalProperty> signalPropertyList = parseSignalPropertyFromXml(equipmentTemplateId, signalElement, signal.getSignalId());
                    batchSignalPropertyList.addAll(signalPropertyList);

                    // 解析信号含义
                    List<SignalMeanings> signalMeaningsList = parseSignalMeaningsFromXml(equipmentTemplateId, signalElement, signal.getSignalId());
                    batchSignalMeaningsList.addAll(signalMeaningsList);
                }
            }

            // 批量保存
            if (!batchSignalList.isEmpty()) {
                signalService.batchInsertSignal(batchSignalList);
                log.info("导入信号 {} 个", batchSignalList.size());
            }

            if (!batchSignalPropertyList.isEmpty()) {
                signalPropertyService.saveBatch(batchSignalPropertyList);
            }

            if (!batchSignalMeaningsList.isEmpty()) {
                signalMeaningsService.saveBatch(batchSignalMeaningsList);
            }

        } catch (Exception e) {
            log.error("导入信号失败", e);
            throw new RuntimeException("导入信号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析信号（保留原始ID）
     */
    private Signal parseSignalFromXml(Integer equipmentTemplateId, Element element) {
        try {
            Signal signal = new Signal();
            signal.setId(null);
            signal.setEquipmentTemplateId(equipmentTemplateId);
            signal.setSignalId(XMLUtil.getAttributeAsInteger(element, "SignalId"));
            signal.setSignalName(element.attributeValue("SignalName"));
            signal.setSignalCategory(XMLUtil.getAttributeAsInteger(element, "SignalCategory"));
            signal.setSignalType(XMLUtil.getAttributeAsInteger(element, "SignalType"));
            signal.setChannelNo(XMLUtil.getAttributeAsInteger(element, "ChannelNo"));
            signal.setChannelType(XMLUtil.getAttributeAsInteger(element, "ChannelType"));
            signal.setExpression(element.attributeValue("Expression"));
            signal.setDataType(XMLUtil.getAttributeAsInteger(element, "DataType"));
            signal.setShowPrecision(element.attributeValue("ShowPrecision"));
            signal.setUnit(element.attributeValue("Unit"));
            signal.setStoreInterval(XMLUtil.getAttributeAsDouble(element, "StoreInterval"));
            signal.setAbsValueThreshold(XMLUtil.getAttributeAsDouble(element, "AbsValueThreshold"));
            signal.setPercentThreshold(XMLUtil.getAttributeAsDouble(element, "PercentThreshold"));
            signal.setStaticsPeriod(XMLUtil.getAttributeAsInteger(element, "StaticsPeriod"));
            signal.setEnable(XMLUtil.getAttributeAsBoolean(element, "Enable"));
            signal.setVisible(XMLUtil.getAttributeAsBoolean(element, "Visible"));
            signal.setDescription(element.attributeValue("Discription")); // 注意XML中是Discription
            signal.setBaseTypeId(XMLUtil.getAttributeAsLong(element, "BaseTypeId"));
            signal.setChargeStoreInterVal(XMLUtil.getAttributeAsDouble(element, "ChargeStoreInterVal"));
            signal.setChargeAbsValue(XMLUtil.getAttributeAsDouble(element, "ChargeAbsValue"));
            signal.setDisplayIndex(XMLUtil.getAttributeAsInteger(element, "DisplayIndex"));
            signal.setModuleNo(XMLUtil.getAttributeAsInteger(element, "ModuleNo"));

            return signal;
        } catch (Exception e) {
            log.error("解析信号XML失败", e);
            return null;
        }
    }

    /**
     * 解析信号属性
     */
    private List<SignalProperty> parseSignalPropertyFromXml(Integer equipmentTemplateId, Element signalElement, Integer signalId) {
        List<SignalProperty> signalPropertyList = new ArrayList<>();

        try {
            String signalPropertyStr = signalElement.attributeValue("SignalProperty");
            if (signalPropertyStr != null && !signalPropertyStr.trim().isEmpty()) {
                String[] propertyArr = signalPropertyStr.split(",");
                for (String propertyStr : propertyArr) {
                    if (propertyStr.trim().isEmpty()) {
                        continue;
                    }
                    try {
                        Integer signalPropertyId = Integer.parseInt(propertyStr.trim());
                        SignalProperty property = new SignalProperty();
                        property.setEquipmentTemplateId(equipmentTemplateId);
                        property.setSignalId(signalId);
                        property.setSignalPropertyId(signalPropertyId);
                        signalPropertyList.add(property);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析信号属性ID: {}", propertyStr);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析信号属性失败", e);
        }

        return signalPropertyList;
    }

    /**
     * 解析信号含义
     */
    private List<SignalMeanings> parseSignalMeaningsFromXml(Integer equipmentTemplateId, Element signalElement, Integer signalId) {
        List<SignalMeanings> signalMeaningsList = new ArrayList<>();

        try {
            String signalMeanings = signalElement.attributeValue("SignalMeanings");
            if (signalMeanings != null && !signalMeanings.trim().isEmpty()) {
                String[] meaningsArr = signalMeanings.split(";");
                for (String meaningStr : meaningsArr) {
                    if (meaningStr.trim().isEmpty()) {
                        continue;
                    }
                    String[] parts = meaningStr.split(":");
                    if (parts.length >= 2) {
                        SignalMeanings meaning = new SignalMeanings();
                        meaning.setEquipmentTemplateId(equipmentTemplateId);
                        meaning.setSignalId(signalId);
                        try {
                            meaning.setStateValue(Integer.parseInt(parts[0]));
                        } catch (NumberFormatException e) {
                            meaning.setStateValue(0);
                        }
                        meaning.setMeanings(parts[1]);
                        signalMeaningsList.add(meaning);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析信号含义失败", e);
        }

        return signalMeaningsList;
    }

    /**
     * 导入事件
     */
    private void importEventsFromXml(Integer equipmentTemplateId, Element eventsElement, ConfigImportReport report) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> eventElementList = eventsElement.elements("Event");

            List<Event> batchEventList = new ArrayList<>(eventElementList.size());
            List<EventCondition> batchEventConditionList = new ArrayList<>();

            for (Element eventElement : eventElementList) {
                // 解析事件（保留原始ID）
                Event event = parseEventFromXml(equipmentTemplateId, eventElement);
                if (event != null) {
                    // insertBatchSomeColumn不允许字段为null，如果ModuleNo为null，默认设置为0
                    if (event.getModuleNo() == null) {
                        event.setModuleNo(0);
                    }
                    batchEventList.add(event);

                    // 解析事件条件
                    Element conditionsElement = eventElement.element("Conditions");
                    if (conditionsElement != null) {
                        List<EventCondition> eventConditions = parseEventConditionsFromXml(
                                equipmentTemplateId, event.getEventId(), conditionsElement);
                        batchEventConditionList.addAll(eventConditions);
                    }
                }
            }

            // 批量保存
            if (!batchEventList.isEmpty()) {
                eventService.saveBatch(batchEventList);
                log.info("导入事件 {} 个", batchEventList.size());
            }

            if (!batchEventConditionList.isEmpty()) {
                eventConditionService.saveBatch(batchEventConditionList);
            }

        } catch (Exception e) {
            log.error("导入事件失败", e);
            throw new RuntimeException("导入事件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析事件（保留原始ID）
     */
    private Event parseEventFromXml(Integer equipmentTemplateId, Element element) {
        try {
            Event event = new Event();
            event.setId(null);
            event.setEquipmentTemplateId(equipmentTemplateId);
            event.setEventId(XMLUtil.getAttributeAsInteger(element, "EventId"));
            event.setEventName(element.attributeValue("EventName"));
            event.setSignalId(XMLUtil.getAttributeAsInteger(element, "SignalId"));
            event.setStartExpression(element.attributeValue("StartExpression"));
            event.setEventCategory(XMLUtil.getAttributeAsInteger(element, "EventCategory"));
            event.setStartType(XMLUtil.getAttributeAsInteger(element, "StartType"));
            event.setEndType(XMLUtil.getAttributeAsInteger(element, "EndType"));
            event.setSuppressExpression(element.attributeValue("SuppressExpression"));
            event.setEnable(XMLUtil.getAttributeAsBoolean(element, "Enable"));
            event.setVisible(XMLUtil.getAttributeAsBoolean(element, "Visible"));
            event.setDescription(element.attributeValue("Description"));
            event.setDisplayIndex(XMLUtil.getAttributeAsInteger(element, "DisplayIndex"));
            event.setModuleNo(XMLUtil.getAttributeAsInteger(element, "ModuleNo"));

            return event;
        } catch (Exception e) {
            log.error("解析事件XML失败", e);
            return null;
        }
    }

    /**
     * 解析事件条件
     */
    private List<EventCondition> parseEventConditionsFromXml(Integer equipmentTemplateId, Integer eventId, Element conditionsElement) {
        List<EventCondition> eventConditionList = new ArrayList<>();

        try {
            @SuppressWarnings("unchecked")
            List<Element> conditionElements = conditionsElement.elements("EventCondition");

            for (Element conditionElement : conditionElements) {
                EventCondition condition = new EventCondition();
                condition.setEquipmentTemplateId(equipmentTemplateId);
                condition.setEventId(eventId);
                condition.setEventConditionId(XMLUtil.getAttributeAsInteger(conditionElement, "EventConditionId"));
                condition.setEventSeverity(XMLUtil.getAttributeAsInteger(conditionElement, "EventSeverity"));
                condition.setStartOperation(conditionElement.attributeValue("StartOperation"));
                condition.setStartCompareValue(XMLUtil.getAttributeAsDouble(conditionElement,"StartCompareValue"));
                condition.setStartDelay(XMLUtil.getAttributeAsInteger(conditionElement, "StartDelay"));
                condition.setEndOperation(conditionElement.attributeValue("EndOperation"));
                condition.setEndCompareValue(XMLUtil.getAttributeAsDouble(conditionElement,"EndCompareValue"));
                condition.setEndDelay(XMLUtil.getAttributeAsInteger(conditionElement, "EndDelay"));
                condition.setFrequency(XMLUtil.getAttributeAsInteger(conditionElement, "Frequency"));
                condition.setFrequencyThreshold(XMLUtil.getAttributeAsInteger(conditionElement, "FrequencyThreshold"));
                condition.setMeanings(conditionElement.attributeValue("Meanings"));
                condition.setEquipmentState(XMLUtil.getAttributeAsInteger(conditionElement, "EquipmentState"));
                condition.setBaseTypeId(XMLUtil.getAttributeAsLong(conditionElement, "BaseTypeId"));
                condition.setStandardName(XMLUtil.getAttributeAsInteger(conditionElement,"StandardName"));

                eventConditionList.add(condition);
            }
        } catch (Exception e) {
            log.error("解析事件条件失败", e);
        }

        return eventConditionList;
    }

    /**
     * 导入控制
     */
    private void importControlsFromXml(Integer equipmentTemplateId, Element controlsElement, ConfigImportReport report) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> controlElementList = controlsElement.elements("Control");

            List<Control> batchControlList = new ArrayList<>(controlElementList.size());
            List<ControlMeanings> batchControlMeaningsList = new ArrayList<>();

            for (Element controlElement : controlElementList) {
                // 解析控制（保留原始ID）
                Control control = parseControlFromXml(equipmentTemplateId, controlElement);
                if (control != null) {
                    batchControlList.add(control);

                    // 解析控制含义
                    String controlMeaningsStr = controlElement.attributeValue("ControlMeanings");
                    if (controlMeaningsStr != null && !controlMeaningsStr.trim().isEmpty()) {
                        List<ControlMeanings> controlMeaningsList = parseControlMeaningsFromXml(
                                equipmentTemplateId, control.getControlId(), controlMeaningsStr);
                        batchControlMeaningsList.addAll(controlMeaningsList);
                    }
                }
            }

            // 批量保存
            if (!batchControlList.isEmpty()) {
                controlService.saveBatch(batchControlList);
                log.info("导入控制 {} 个", batchControlList.size());
            }

            if (!batchControlMeaningsList.isEmpty()) {
                controlMeaningsService.saveBatch(batchControlMeaningsList);
            }

        } catch (Exception e) {
            log.error("导入控制失败", e);
            throw new RuntimeException("导入控制失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析控制（保留原始ID）
     */
    private Control parseControlFromXml(Integer equipmentTemplateId, Element element) {
        try {
            Control control = new Control();
            control.setId(null);
            control.setEquipmentTemplateId(equipmentTemplateId);
            control.setControlId(XMLUtil.getAttributeAsInteger(element, "ControlId"));
            control.setControlName(element.attributeValue("ControlName"));
            control.setControlCategory(XMLUtil.getAttributeAsInteger(element, "ControlCategory"));
            control.setCmdToken(element.attributeValue("CmdToken"));

            Long baseTypeId = XMLUtil.getAttributeAsLong(element, "BaseTypeId");
            control.setBaseTypeId((baseTypeId != null && baseTypeId == 0L) ? null : baseTypeId);

            control.setControlSeverity(XMLUtil.getAttributeAsInteger(element, "ControlSeverity"));
            control.setSignalId(XMLUtil.getAttributeAsInteger(element, "SignalId"));
            control.setTimeOut(XMLUtil.getAttributeAsDouble(element, "TimeOut"));
            control.setRetry(XMLUtil.getAttributeAsInteger(element, "Retry"));

            String description = element.attributeValue("Description");
            if (description != null && description.contains("bytedancebid=")) {
                description = description.replace("bytedancebid=", "");
            }
            control.setDescription(description != null ? description : "");

            control.setEnable(XMLUtil.getAttributeAsBoolean(element, "Enable"));
            control.setVisible(XMLUtil.getAttributeAsBoolean(element, "Visible"));
            control.setDisplayIndex(XMLUtil.getAttributeAsInteger(element, "DisplayIndex"));
            control.setCommandType(XMLUtil.getAttributeAsInteger(element, "CommandType"));
            control.setControlType(XMLUtil.getAttributeAsShort(element, "ControlType"));
            control.setDataType(XMLUtil.getAttributeAsShort(element, "DataType"));
            control.setMaxValue(XMLUtil.getAttributeAsDouble(element, "MaxValue"));
            control.setMinValue(XMLUtil.getAttributeAsDouble(element, "MinValue"));
            control.setDefaultValue(XMLUtil.getAttributeAsDouble(element, "DefaultValue"));
            control.setModuleNo(XMLUtil.getAttributeAsInteger(element, "ModuleNo"));

            return control;
        } catch (Exception e) {
            log.error("解析控制XML失败", e);
            return null;
        }
    }

    /**
     * 解析控制含义
     */
    private List<ControlMeanings> parseControlMeaningsFromXml(Integer equipmentTemplateId, Integer controlId, String controlMeaningsStr) {
        List<ControlMeanings> controlMeaningsList = new ArrayList<>();

        try {
            String[] meaningsArr = controlMeaningsStr.split(";");
            for (String meaningStr : meaningsArr) {
                if (meaningStr.trim().isEmpty()) {
                    continue;
                }
                String[] parts = meaningStr.split(":");
                if (parts.length >= 2) {
                    ControlMeanings meaning = new ControlMeanings();
                    meaning.setEquipmentTemplateId(equipmentTemplateId);
                    meaning.setControlId(controlId);
                    try {
                        meaning.setParameterValue(Integer.parseInt(parts[0]));
                    } catch (NumberFormatException e) {
                        meaning.setParameterValue(0);
                    }
                    meaning.setMeanings(parts[1]);
                    controlMeaningsList.add(meaning);
                }
            }
        } catch (Exception e) {
            log.error("解析控制含义失败", e);
        }

        return controlMeaningsList;
    }

    /**
     * 导入采集单元
     */
    private void importSamplerUnits(Integer monitorUnitId, Element samplerUnitsElement, ConfigImportReport report) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> samplerUnitElementList = samplerUnitsElement.elements("SamplerUnit");

            List<SamplerUnit> batchSamplerUnitList = new ArrayList<>(samplerUnitElementList.size());

            for (Element samplerUnitElement : samplerUnitElementList) {
                SamplerUnit samplerUnit = parseSamplerUnitFromXml(monitorUnitId, samplerUnitElement);
                if (samplerUnit != null) {
                    batchSamplerUnitList.add(samplerUnit);
                }
            }

            // 批量保存
            if (!batchSamplerUnitList.isEmpty()) {
                samplerUnitService.saveBatch(batchSamplerUnitList);
                log.info("导入采集单元 {} 个", batchSamplerUnitList.size());
            }

        } catch (Exception e) {
            log.error("导入采集单元失败", e);
            throw new RuntimeException("导入采集单元失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析采集单元（保留原始ID）
     */
    private SamplerUnit parseSamplerUnitFromXml(Integer monitorUnitId, Element element) {
        try {
            SamplerUnit samplerUnit = new SamplerUnit();
            samplerUnit.setMonitorUnitId(monitorUnitId);
            samplerUnit.setSamplerUnitId(XMLUtil.getAttributeAsInteger(element, "SamplerUnitId"));
            samplerUnit.setPortId(XMLUtil.getAttributeAsInteger(element, "PortId"));
            samplerUnit.setParentSamplerUnitId(XMLUtil.getAttributeAsInteger(element, "ParentSamplerUnitId"));
            samplerUnit.setSamplerUnitName(element.attributeValue("SamplerUnitName"));
            samplerUnit.setSamplerType(XMLUtil.getAttributeAsShort(element, "SamplerType"));
            samplerUnit.setAddress(XMLUtil.getAttributeAsInteger(element, "Address"));
            samplerUnit.setSpUnitInterval(XMLUtil.getAttributeAsDouble(element, "SpUnitInterval"));
            samplerUnit.setConnectState(XMLUtil.getAttributeAsInteger(element, "ConnectState"));
            samplerUnit.setPhoneNumber(element.attributeValue("PhoneNumber"));
            samplerUnit.setDescription(element.attributeValue("Description"));
            samplerUnit.setDllPath(element.attributeValue("DllPath"));
            samplerUnit.setSamplerId(XMLUtil.getAttributeAsInteger(element, "SamplerId"));
            samplerUnit.setConnectState(0);
            samplerUnit.setUpdateTime(LocalDateTime.now());
            return samplerUnit;
        } catch (Exception e) {
            log.error("解析采集单元XML失败", e);
            return null;
        }
    }

    /**
     * 导入设备
     */
    private void importEquipments(Integer monitorUnitId, Element equipmentsElement, ConfigImportReport report) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> equipmentElementList = equipmentsElement.elements("Equipment");

            List<Equipment> batchEquipmentList = new ArrayList<>(equipmentElementList.size());

            for (Element equipmentElement : equipmentElementList) {
                Equipment equipment = parseEquipmentFromXml(monitorUnitId, equipmentElement);
                equipment.setEquipmentNo("");
                equipment.setInstalledModule("");
                if (equipment != null) {
                    batchEquipmentList.add(equipment);
                }
            }

            // 批量保存
            if (!batchEquipmentList.isEmpty()) {
                equipmentService.saveBatch(batchEquipmentList);
                log.info("导入设备 {} 个", batchEquipmentList.size());
            }

        } catch (Exception e) {
            log.error("导入设备失败", e);
            throw new RuntimeException("导入设备失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析设备（保留原始ID）
     */
    private Equipment parseEquipmentFromXml(Integer monitorUnitId, Element element) {
        try {
            Equipment equipment = new Equipment();
            equipment.setMonitorUnitId(monitorUnitId);
            equipment.setEquipmentId(XMLUtil.getAttributeAsInteger(element, "EquipmentId"));
            equipment.setEquipmentName(element.attributeValue("EquipmentName"));
            equipment.setEquipmentCategory(XMLUtil.getAttributeAsInteger(element, "EquipmentCategory"));
            equipment.setEquipmentTemplateId(XMLUtil.getAttributeAsInteger(element, "EquipmentTemplateId"));
            equipment.setSamplerUnitId(XMLUtil.getAttributeAsInteger(element, "SamplerUnitId"));
            equipment.setParentEquipmentId(element.attributeValue("ParentEquipmentId"));
            equipment.setConnectState(XMLUtil.getAttributeAsInteger(element, "ConnectState"));
            equipment.setDescription(element.attributeValue("Description"));
            equipment.setEquipmentType(XMLUtil.getAttributeAsInteger(element, "EquipmentType"));
            equipment.setEquipmentClass(XMLUtil.getAttributeAsInteger(element, "EquipmentClass"));
            equipment.setEquipmentState(XMLUtil.getAttributeAsInteger(element, "EquipmentState"));

            // 设置默认值
            equipment.setHouseId(0);
            equipment.setResourceStructureId(0);
            equipment.setWorkStationId(0);
            equipment.setStationId(0);
            equipment.setDisplayIndex(0);
            equipment.setUpdateTime(LocalDateTime.now());
            equipment.setConnectState(0);

            return equipment;
        } catch (Exception e) {
            log.error("解析设备XML失败", e);
            return null;
        }
    }

    public File extractZipFile(MultipartFile zipFile) {
        try {
            // 使用指定的临时目录
            String tempDirName = "config_import_" + UUID.randomUUID().toString().replace("-", "");
            String tempRelativePath = "temp/" + tempDirName;
            String tempFullPath = filePathProvider.getFullPath(filePathProvider.getWorkspacePath(), tempRelativePath);

            // 创建临时目录
            fileUtil.createDirectory(tempFullPath);

            // 保存上传的文件到临时目录
            String zipFileName = "config.zip";
            fileUtil.writeFile(tempFullPath, zipFileName, zipFile.getBytes());

            // 解压文件
            String extractRelativePath = tempRelativePath + "/extracted";
            String extractFullPath = filePathProvider.getFullPath(filePathProvider.getWorkspacePath(), extractRelativePath);
            fileUtil.createDirectory(extractFullPath);

            // 读取zip文件内容并解压
            byte[] zipContent = fileUtil.readFile(tempFullPath, zipFileName);
            unzipFileFromBytes(zipContent, new File(extractFullPath));

            // 删除zip文件，只保留解压后的内容
            fileUtil.deleteFile(tempFullPath, zipFileName);

            return new File(extractFullPath);

        } catch (IOException e) {
            log.error("解压zip文件失败", e);
            throw new RuntimeException("解压zip文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从字节数组解压zip文件
     */
    private void unzipFileFromBytes(byte[] zipContent, File destDir) throws IOException {
        // 先尝试 UTF-8，如果失败再尝试 GBK（WinRAR、360 压缩等常用编码）
        List<Charset> charsetsToTry = Arrays.asList(StandardCharsets.UTF_8, Charset.forName("GBK"));
        boolean success = false;

        for (Charset charset : charsetsToTry) {
            try (ZipInputStream zis = new ZipInputStream(new ByteArrayInputStream(zipContent), charset)) {
                ZipEntry entry;
                while ((entry = zis.getNextEntry()) != null) {
                    // 防止路径穿越攻击
                    File targetFile = new File(destDir, entry.getName());
                    if (!targetFile.getCanonicalPath().startsWith(destDir.getCanonicalPath())) {
                        throw new IOException("Entry is outside of the target dir: " + entry.getName());
                    }

                    if (entry.isDirectory()) {
                        targetFile.mkdirs();
                    } else {
                        File parentDir = targetFile.getParentFile();
                        if (parentDir != null && !parentDir.exists()) {
                            parentDir.mkdirs();
                        }

                        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = zis.read(buffer)) > 0) {
                                fos.write(buffer, 0, len);
                            }
                        }
                    }
                    zis.closeEntry();
                }
                success = true;
                break; // 成功解压，跳出循环
            } catch (Exception e) {
                log.warn("使用编码 {} 解压失败: {}", charset.name(), e.getMessage());
                if (charset == charsetsToTry.get(charsetsToTry.size() - 1)) {
                    // 最后一个编码也失败了，抛出异常
                    throw new IOException("无法解压文件，尝试了所有编码都失败", e);
                }
            }
        }

        if (!success) {
            throw new IOException("解压文件失败");
        }
    }

    /**
     * 解压zip文件（使用Java原生方式）
     */
    private void unzipFile(File zipFile, File destDir) throws IOException {
        // 先尝试 UTF-8，如果失败再尝试 GBK（WinRAR、360 压缩等常用编码）
        List<Charset> charsetsToTry = Arrays.asList(StandardCharsets.UTF_8, Charset.forName("GBK"));
        boolean success = false;

        for (Charset charset : charsetsToTry) {
            try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile), charset)) {
                ZipEntry entry;
                while ((entry = zis.getNextEntry()) != null) {
                    // 防止路径穿越攻击
                    File targetFile = new File(destDir, entry.getName());
                    String canonicalPath = targetFile.getCanonicalPath();
                    if (!canonicalPath.startsWith(destDir.getCanonicalPath() + File.separator)) {
                        throw new IOException("ZIP entry is outside of the target dir: " + entry.getName());
                    }

                    if (entry.isDirectory()) {
                        targetFile.mkdirs();
                    } else {
                        File parentDir = targetFile.getParentFile();
                        if (parentDir != null && !parentDir.exists()) {
                            parentDir.mkdirs();
                        }

                        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                            byte[] buffer = new byte[4096];
                            int len;
                            while ((len = zis.read(buffer)) > 0) {
                                fos.write(buffer, 0, len);
                            }
                        }
                    }
                    zis.closeEntry();
                }
                success = true;
                break; // 解压成功就退出循环
            } catch (IllegalArgumentException | ZipException e) {
                // 当前编码失败，尝试下一个编码
                System.err.println("尝试使用编码 " + charset.name() + " 解压失败，尝试下一个编码");
            }
        }

        if (!success) {
            throw new IOException("无法使用 UTF-8 或 GBK 正确解压 zip 文件，文件可能损坏或包含非法文件名。");
        }
    }


    public File[] findXmlConfigFiles(File extractDir) {
        // 查找XmlCfg目录
        File xmlCfgDir = new File(extractDir, "XmlCfg");
        if (!xmlCfgDir.exists() || !xmlCfgDir.isDirectory()) {
            // 如果没有XmlCfg目录，直接在根目录查找XML文件
            return extractDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".xml"));
        }

        // 在XmlCfg目录中查找XML文件
        return xmlCfgDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".xml"));
    }

    // ==================== 辅助方法 ====================
    // 注意：原有的 XML 属性获取方法已迁移到 XMLUtil 工具类中

    @Override
    public void cleanupTempFiles(File tempDir) {
        try {
            if (tempDir != null && tempDir.exists()) {
                fileUtil.deleteFile(tempDir);
                log.info("清理临时文件: {}", tempDir.getAbsolutePath());
            }
        } catch (Exception e) {
            log.warn("清理临时文件失败: {}", tempDir.getAbsolutePath(), e);
        }
    }

    @Override
    @Deprecated
    public ConfigDiffResult compareConfigWithDatabase(MultipartFile file, Integer monitorUnitId) {
        File tempDir = null;
        try {
            // 1. 解压zip文件
            tempDir = extractZipFile(file);
            log.info("配置文件解压完成: {}", tempDir.getAbsolutePath());

            // 2. 查找XML配置文件
            File[] xmlFiles = findXmlConfigFiles(tempDir);
            if (xmlFiles == null || xmlFiles.length == 0) {
                throw new RuntimeException("未找到有效的XML配置文件");
            }

            // 3. 解析XML文件并与数据库对比
            ConfigDiffResult diffResult = new ConfigDiffResult();
            for (File xmlFile : xmlFiles) {
                try {
                    compareXmlWithDatabase(xmlFile, monitorUnitId, diffResult);
                } catch (Exception e) {
                    log.error("对比XML文件失败: {}, 错误: {}", xmlFile.getName(), e.getMessage(), e);
                    // 继续处理其他文件
                }
            }

            return diffResult;

        } catch (Exception e) {
            log.error("配置差异对比失败", e);
            throw new RuntimeException("配置差异对比失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            if (tempDir != null) {
                cleanupTempFiles(tempDir);
            }
        }
    }

    @Override
    @Deprecated
    public ConfigDiffResult compareConfigWithDatabaseFromFile(String zipFilePath, Integer monitorUnitId) {
        File tempDir = null;
        try {
            // 1. 解压zip文件
            File zipFile = new File(zipFilePath);
            tempDir = extractZipFileFromFile(zipFile);
            log.info("配置文件解压完成: {}", tempDir.getAbsolutePath());

            // 2. 查找XML配置文件
            File[] xmlFiles = findXmlConfigFiles(tempDir);
            if (xmlFiles == null || xmlFiles.length == 0) {
                throw new RuntimeException("未找到有效的XML配置文件");
            }

            // 3. 解析XML文件并与数据库对比
            ConfigDiffResult diffResult = new ConfigDiffResult();
            for (File xmlFile : xmlFiles) {
                try {
                    compareXmlWithDatabase(xmlFile, monitorUnitId, diffResult);
                } catch (Exception e) {
                    log.error("对比XML文件失败: {}, 错误: {}", xmlFile.getName(), e.getMessage(), e);
                    // 继续处理其他文件
                }
            }

            return diffResult;

        } catch (Exception e) {
            log.error("配置差异对比失败", e);
            throw new RuntimeException("配置差异对比失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            if (tempDir != null) {
                cleanupTempFiles(tempDir);
            }
        }
    }

    /**
     * 比较XML文件与数据库中的配置
     */
    private void compareXmlWithDatabase(File xmlFile, Integer monitorUnitId, ConfigDiffResult diffResult) {
        try {
            // 解析XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(xmlFile);
            Element root = document.getRootElement();

            // 比较监控单元
            Element monitorUnitElement = root.element("MonitorUnit");
            if (monitorUnitElement != null) {
                compareMonitorUnit(monitorUnitElement, monitorUnitId, diffResult);
            }

            // 比较设备模板
            Element equipmentTemplatesElement = root.element("EquipmentTemplates");
            if (equipmentTemplatesElement != null) {
                compareEquipmentTemplates(equipmentTemplatesElement, monitorUnitId, diffResult);
            }

        } catch (Exception e) {
            log.error("解析XML文件失败: {}", xmlFile.getName(), e);
            throw new RuntimeException("解析XML文件失败: " + xmlFile.getName(), e);
        }
    }

    /**
     * 比较监控单元
     */
    private void compareMonitorUnit(Element monitorUnitElement, Integer monitorUnitId, ConfigDiffResult diffResult) {
        try {
            // 从数据库获取现有监控单元
            MonitorUnit dbMonitorUnit = monitorUnitService.getById(monitorUnitId);
            if (dbMonitorUnit == null) {
                log.warn("数据库中未找到监控单元: {}", monitorUnitId);
                return;
            }

            // 解析XML中的监控单元
            MonitorUnit xmlMonitorUnit = parseMonitorUnitFromXml(monitorUnitElement);
            if (xmlMonitorUnit == null) {
                log.warn("解析XML监控单元失败");
                return;
            }

            // 比较字段差异
            ConfigDiffResult.MonitorUnitDiff monitorUnitDiff = new ConfigDiffResult.MonitorUnitDiff();
            monitorUnitDiff.setMonitorUnitId(monitorUnitId);
            monitorUnitDiff.setMonitorUnitName(dbMonitorUnit.getMonitorUnitName());

            // 比较各个字段
            compareField(monitorUnitDiff.getFieldDiffs(), "监控单元名称", "monitorUnitName",
                        dbMonitorUnit.getMonitorUnitName(), xmlMonitorUnit.getMonitorUnitName());
            compareField(monitorUnitDiff.getFieldDiffs(), "监控单元类别", "monitorUnitCategory",
                        dbMonitorUnit.getMonitorUnitCategory(), xmlMonitorUnit.getMonitorUnitCategory());
            compareField(monitorUnitDiff.getFieldDiffs(), "监控单元编码", "monitorUnitCode",
                        dbMonitorUnit.getMonitorUnitCode(), xmlMonitorUnit.getMonitorUnitCode());
            compareField(monitorUnitDiff.getFieldDiffs(), "站点ID", "stationId",
                        dbMonitorUnit.getStationId(), xmlMonitorUnit.getStationId());
            compareField(monitorUnitDiff.getFieldDiffs(), "IP地址", "ipAddress",
                        dbMonitorUnit.getIpAddress(), xmlMonitorUnit.getIpAddress());
            compareField(monitorUnitDiff.getFieldDiffs(), "运行模式", "runMode",
                        dbMonitorUnit.getRunMode(), xmlMonitorUnit.getRunMode());
            compareField(monitorUnitDiff.getFieldDiffs(), "描述", "description",
                        dbMonitorUnit.getDescription(), xmlMonitorUnit.getDescription());

            if (monitorUnitDiff.hasDifferences()) {
                diffResult.setMonitorUnitDiff(monitorUnitDiff);
            }

            // 比较端口
            Element portsElement = monitorUnitElement.element("Ports");
            if (portsElement != null) {
                comparePorts(portsElement, monitorUnitId, diffResult);
            }

            // 比较采集单元
            Element samplerUnitsElement = monitorUnitElement.element("SamplerUnits");
            if (samplerUnitsElement != null) {
                compareSamplerUnits(samplerUnitsElement, monitorUnitId, diffResult);
            }

            // 比较设备
            Element equipmentsElement = monitorUnitElement.element("Equipments");
            if (equipmentsElement != null) {
                compareEquipments(equipmentsElement, monitorUnitId, diffResult);
            }

        } catch (Exception e) {
            log.error("比较监控单元失败", e);
        }
    }

    /**
     * 比较字段值
     */
    private void compareField(List<ConfigDiffResult.FieldDiff> fieldDiffs, String displayName, String fieldName,
                             Object oldValue, Object newValue) {
        // 如果类型是字符串，且 null 和 "" 可视为相等
        if (oldValue == null && newValue == ""){
            return;
        }
        // 处理null值比较
        if (oldValue == null && newValue == null) {
            return;
        }
        if (oldValue == null || newValue == null || !oldValue.equals(newValue)) {
            fieldDiffs.add(new ConfigDiffResult.FieldDiff(fieldName, displayName, oldValue, newValue));
        }
    }

    /**
     * 比较设备模板
     */
    private void compareEquipmentTemplates(Element equipmentTemplatesElement, Integer monitorUnitId, ConfigDiffResult diffResult) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> templateElements = equipmentTemplatesElement.elements("EquipmentTemplate");

            for (Element templateElement : templateElements) {
                String templateIdStr = templateElement.attributeValue("EquipmentTemplateId");
                if (templateIdStr == null) {
                    continue;
                }

                Integer templateId = Integer.parseInt(templateIdStr);

                // 从数据库获取现有设备模板
                EquipmentTemplate dbTemplate = equipmentTemplateService.getById(templateId);

                // 解析XML中的设备模板
                EquipmentTemplate xmlTemplate = parseEquipmentTemplateFromXml(templateElement);
                if (xmlTemplate == null) {
                    continue;
                }

                ConfigDiffResult.EquipmentTemplateDiff templateDiff = new ConfigDiffResult.EquipmentTemplateDiff();
                templateDiff.setEquipmentTemplateId(templateId);
                templateDiff.setEquipmentTemplateName(xmlTemplate.getEquipmentTemplateName());

                if (dbTemplate == null) {
                    // 新增的设备模板
                    templateDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                } else {
                    // 比较设备模板字段
                    compareField(templateDiff.getFieldDiffs(), "设备模板名称", "equipmentTemplateName",
                                dbTemplate.getEquipmentTemplateName(), xmlTemplate.getEquipmentTemplateName());
                    compareField(templateDiff.getFieldDiffs(), "协议代码", "protocolCode",
                                dbTemplate.getProtocolCode(), xmlTemplate.getProtocolCode());
                    compareField(templateDiff.getFieldDiffs(), "设备类别", "equipmentCategory",
                                dbTemplate.getEquipmentCategory(), xmlTemplate.getEquipmentCategory());
                    compareField(templateDiff.getFieldDiffs(), "设备类型", "equipmentType",
                                dbTemplate.getEquipmentType(), xmlTemplate.getEquipmentType());
                    compareField(templateDiff.getFieldDiffs(), "备注", "memo",
                                dbTemplate.getMemo(), xmlTemplate.getMemo());
                    compareField(templateDiff.getFieldDiffs(), "属性", "property",
                                dbTemplate.getProperty(), xmlTemplate.getProperty());
                    compareField(templateDiff.getFieldDiffs(), "描述", "description",
                                dbTemplate.getDescription(), xmlTemplate.getDescription());
                    compareField(templateDiff.getFieldDiffs(), "设备基础类型", "equipmentBaseType",
                                dbTemplate.getEquipmentBaseType(), xmlTemplate.getEquipmentBaseType());

                    if (!templateDiff.getFieldDiffs().isEmpty()) {
                        templateDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }
                }

                // 比较信号
                Element signalsElement = templateElement.element("Signals");
                if (signalsElement != null) {
                    compareSignals(signalsElement, templateId, templateDiff);
                }

                // 比较事件
                Element eventsElement = templateElement.element("Events");
                if (eventsElement != null) {
                    compareEvents(eventsElement, templateId, templateDiff);
                }

                // 比较控制
                Element controlsElement = templateElement.element("Controls");
                if (controlsElement != null) {
                    compareControls(controlsElement, templateId, templateDiff);
                }

                // 如果有差异，添加到结果中
                if (templateDiff.getDiffType() != null ||
                    !templateDiff.getSignalDiffs().isEmpty() ||
                    !templateDiff.getEventDiffs().isEmpty() ||
                    !templateDiff.getControlDiffs().isEmpty()) {
                    diffResult.getEquipmentTemplateDiffs().add(templateDiff);
                }
            }

        } catch (Exception e) {
            log.error("比较设备模板失败", e);
        }
    }

    /**
     * 比较信号
     */
    private void compareSignals(Element signalsElement, Integer templateId, ConfigDiffResult.EquipmentTemplateDiff templateDiff) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> signalElements = signalsElement.elements("Signal");

            // 获取数据库中的信号
            List<Signal> dbSignals = signalService.findByEquipmentTemplateId(templateId);
            Map<Integer, Signal> dbSignalMap = dbSignals.stream()
                    .collect(Collectors.toMap(Signal::getSignalId, Function.identity()));

            Set<Integer> processedSignalIds = new HashSet<>();

            for (Element signalElement : signalElements) {
                String signalIdStr = signalElement.attributeValue("SignalId");
                if (signalIdStr == null) {
                    continue;
                }

                Integer signalId = Integer.parseInt(signalIdStr);
                processedSignalIds.add(signalId);

                Signal dbSignal = dbSignalMap.get(signalId);
                Signal xmlSignal = parseSignalFromXml(templateId, signalElement);
                if (xmlSignal == null) {
                    continue;
                }

                ConfigDiffResult.SignalDiff signalDiff = new ConfigDiffResult.SignalDiff();
                signalDiff.setSignalId(signalId);
                signalDiff.setSignalName(xmlSignal.getSignalName());

                if (dbSignal == null) {
                    // 新增的信号
                    signalDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                } else {
                    // 比较信号字段
                    compareField(signalDiff.getFieldDiffs(), "信号名称", "signalName",
                                dbSignal.getSignalName(), xmlSignal.getSignalName());
                    compareField(signalDiff.getFieldDiffs(), "信号类别", "signalCategory",
                                dbSignal.getSignalCategory(), xmlSignal.getSignalCategory());
                    compareField(signalDiff.getFieldDiffs(), "信号类型", "signalType",
                                dbSignal.getSignalType(), xmlSignal.getSignalType());
                    compareField(signalDiff.getFieldDiffs(), "通道号", "channelNo",
                                dbSignal.getChannelNo(), xmlSignal.getChannelNo());
                    compareField(signalDiff.getFieldDiffs(), "通道类型", "channelType",
                                dbSignal.getChannelType(), xmlSignal.getChannelType());
                    compareField(signalDiff.getFieldDiffs(), "表达式", "expression",
                                dbSignal.getExpression(), xmlSignal.getExpression());
                    compareField(signalDiff.getFieldDiffs(), "数据类型", "dataType",
                                dbSignal.getDataType(), xmlSignal.getDataType());
                    compareField(signalDiff.getFieldDiffs(), "显示精度", "showPrecision",
                                dbSignal.getShowPrecision(), xmlSignal.getShowPrecision());
                    compareField(signalDiff.getFieldDiffs(), "单位", "unit",
                                dbSignal.getUnit(), xmlSignal.getUnit());
                    compareField(signalDiff.getFieldDiffs(), "存储间隔", "storeInterval",
                                dbSignal.getStoreInterval(), xmlSignal.getStoreInterval());
                    compareField(signalDiff.getFieldDiffs(), "绝对值阈值", "absValueThreshold",
                                dbSignal.getAbsValueThreshold(), xmlSignal.getAbsValueThreshold());
                    compareField(signalDiff.getFieldDiffs(), "百分比阈值", "percentThreshold",
                                dbSignal.getPercentThreshold(), xmlSignal.getPercentThreshold());
                    compareField(signalDiff.getFieldDiffs(), "统计周期", "staticsPeriod",
                                dbSignal.getStaticsPeriod(), xmlSignal.getStaticsPeriod());
                    compareField(signalDiff.getFieldDiffs(), "启用", "enable",
                                dbSignal.getEnable(), xmlSignal.getEnable());
                    compareField(signalDiff.getFieldDiffs(), "可见", "visible",
                                dbSignal.getVisible(), xmlSignal.getVisible());
                    compareField(signalDiff.getFieldDiffs(), "描述", "description",
                                dbSignal.getDescription(), xmlSignal.getDescription());
                    compareField(signalDiff.getFieldDiffs(), "显示索引", "displayIndex",
                                dbSignal.getDisplayIndex(), xmlSignal.getDisplayIndex());
                    compareField(signalDiff.getFieldDiffs(), "模块号", "moduleNo",
                                dbSignal.getModuleNo(), xmlSignal.getModuleNo());

                    if (!signalDiff.getFieldDiffs().isEmpty()) {
                        signalDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }
                }

                if (signalDiff.getDiffType() != null) {
                    templateDiff.getSignalDiffs().add(signalDiff);
                }
            }

            // 检查删除的信号
            for (Signal dbSignal : dbSignals) {
                if (!processedSignalIds.contains(dbSignal.getSignalId())) {
                    ConfigDiffResult.SignalDiff signalDiff = new ConfigDiffResult.SignalDiff();
                    signalDiff.setSignalId(dbSignal.getSignalId());
                    signalDiff.setSignalName(dbSignal.getSignalName());
                    signalDiff.setDiffType(ConfigDiffResult.DiffType.DELETED);
                    templateDiff.getSignalDiffs().add(signalDiff);
                }
            }

        } catch (Exception e) {
            log.error("比较信号失败", e);
        }
    }

    /**
     * 比较事件
     */
    private void compareEvents(Element eventsElement, Integer templateId, ConfigDiffResult.EquipmentTemplateDiff templateDiff) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> eventElements = eventsElement.elements("Event");

            // 获取数据库中的事件
            List<Event> dbEvents = eventService.list(new LambdaQueryWrapper<Event>()
                    .eq(Event::getEquipmentTemplateId, templateId));
            Map<Integer, Event> dbEventMap = dbEvents.stream()
                    .collect(Collectors.toMap(Event::getEventId, Function.identity()));

            Set<Integer> processedEventIds = new HashSet<>();

            for (Element eventElement : eventElements) {
                String eventIdStr = eventElement.attributeValue("EventId");
                if (eventIdStr == null) {
                    continue;
                }

                Integer eventId = Integer.parseInt(eventIdStr);
                processedEventIds.add(eventId);

                Event dbEvent = dbEventMap.get(eventId);
                Event xmlEvent = parseEventFromXml(templateId, eventElement);
                if (xmlEvent == null) {
                    continue;
                }

                ConfigDiffResult.EventDiff eventDiff = new ConfigDiffResult.EventDiff();
                eventDiff.setEventId(eventId);
                eventDiff.setEventName(xmlEvent.getEventName());

                if (dbEvent == null) {
                    // 新增的事件
                    eventDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                } else {
                    // 比较事件字段
                    compareField(eventDiff.getFieldDiffs(), "事件名称", "eventName",
                                dbEvent.getEventName(), xmlEvent.getEventName());
                    compareField(eventDiff.getFieldDiffs(), "信号ID", "signalId",
                                dbEvent.getSignalId(), xmlEvent.getSignalId());
                    compareField(eventDiff.getFieldDiffs(), "开始表达式", "startExpression",
                                dbEvent.getStartExpression(), xmlEvent.getStartExpression());
                    compareField(eventDiff.getFieldDiffs(), "事件类别", "eventCategory",
                                dbEvent.getEventCategory(), xmlEvent.getEventCategory());
                    compareField(eventDiff.getFieldDiffs(), "开始类型", "startType",
                                dbEvent.getStartType(), xmlEvent.getStartType());
                    compareField(eventDiff.getFieldDiffs(), "结束类型", "endType",
                                dbEvent.getEndType(), xmlEvent.getEndType());
                    compareField(eventDiff.getFieldDiffs(), "抑制表达式", "suppressExpression",
                                dbEvent.getSuppressExpression(), xmlEvent.getSuppressExpression());
                    compareField(eventDiff.getFieldDiffs(), "启用", "enable",
                                dbEvent.getEnable(), xmlEvent.getEnable());
                    compareField(eventDiff.getFieldDiffs(), "可见", "visible",
                                dbEvent.getVisible(), xmlEvent.getVisible());
                    compareField(eventDiff.getFieldDiffs(), "描述", "description",
                                dbEvent.getDescription(), xmlEvent.getDescription());
                    compareField(eventDiff.getFieldDiffs(), "显示索引", "displayIndex",
                                dbEvent.getDisplayIndex(), xmlEvent.getDisplayIndex());
                    compareField(eventDiff.getFieldDiffs(), "模块号", "moduleNo",
                                dbEvent.getModuleNo(), xmlEvent.getModuleNo());

                    if (!eventDiff.getFieldDiffs().isEmpty()) {
                        eventDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }
                }

                if (eventDiff.getDiffType() != null) {
                    templateDiff.getEventDiffs().add(eventDiff);
                }
            }

            // 检查删除的事件
            for (Event dbEvent : dbEvents) {
                if (!processedEventIds.contains(dbEvent.getEventId())) {
                    ConfigDiffResult.EventDiff eventDiff = new ConfigDiffResult.EventDiff();
                    eventDiff.setEventId(dbEvent.getEventId());
                    eventDiff.setEventName(dbEvent.getEventName());
                    eventDiff.setDiffType(ConfigDiffResult.DiffType.DELETED);
                    templateDiff.getEventDiffs().add(eventDiff);
                }
            }

        } catch (Exception e) {
            log.error("比较事件失败", e);
        }
    }

    /**
     * 比较控制
     */
    private void compareControls(Element controlsElement, Integer templateId, ConfigDiffResult.EquipmentTemplateDiff templateDiff) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> controlElements = controlsElement.elements("Control");

            // 获取数据库中的控制
            List<Control> dbControls = controlService.findByEquipmentTemplateId(templateId);
            Map<Integer, Control> dbControlMap = dbControls.stream()
                    .collect(Collectors.toMap(Control::getControlId, Function.identity()));

            Set<Integer> processedControlIds = new HashSet<>();

            for (Element controlElement : controlElements) {
                String controlIdStr = controlElement.attributeValue("ControlId");
                if (controlIdStr == null) {
                    continue;
                }

                Integer controlId = Integer.parseInt(controlIdStr);
                processedControlIds.add(controlId);

                Control dbControl = dbControlMap.get(controlId);
                Control xmlControl = parseControlFromXml(templateId, controlElement);
                if (xmlControl == null) {
                    continue;
                }

                ConfigDiffResult.ControlDiff controlDiff = new ConfigDiffResult.ControlDiff();
                controlDiff.setControlId(controlId);
                controlDiff.setControlName(xmlControl.getControlName());

                if (dbControl == null) {
                    // 新增的控制
                    controlDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                } else {
                    // 比较控制字段
                    compareField(controlDiff.getFieldDiffs(), "控制名称", "controlName",
                                dbControl.getControlName(), xmlControl.getControlName());
                    compareField(controlDiff.getFieldDiffs(), "控制类别", "controlCategory",
                                dbControl.getControlCategory(), xmlControl.getControlCategory());
                    compareField(controlDiff.getFieldDiffs(), "命令令牌", "cmdToken",
                                dbControl.getCmdToken(), xmlControl.getCmdToken());
                    compareField(controlDiff.getFieldDiffs(), "基础类型ID", "baseTypeId",
                                dbControl.getBaseTypeId(), xmlControl.getBaseTypeId());
                    compareField(controlDiff.getFieldDiffs(), "控制严重性", "controlSeverity",
                                dbControl.getControlSeverity(), xmlControl.getControlSeverity());
                    compareField(controlDiff.getFieldDiffs(), "信号ID", "signalId",
                                dbControl.getSignalId(), xmlControl.getSignalId());
                    compareField(controlDiff.getFieldDiffs(), "超时时间", "timeOut",
                                dbControl.getTimeOut(), xmlControl.getTimeOut());
                    compareField(controlDiff.getFieldDiffs(), "重试次数", "retry",
                                dbControl.getRetry(), xmlControl.getRetry());
                    compareField(controlDiff.getFieldDiffs(), "描述", "description",
                                dbControl.getDescription(), xmlControl.getDescription());
                    compareField(controlDiff.getFieldDiffs(), "启用", "enable",
                                dbControl.getEnable(), xmlControl.getEnable());
                    compareField(controlDiff.getFieldDiffs(), "可见", "visible",
                                dbControl.getVisible(), xmlControl.getVisible());
                    compareField(controlDiff.getFieldDiffs(), "显示索引", "displayIndex",
                                dbControl.getDisplayIndex(), xmlControl.getDisplayIndex());
                    compareField(controlDiff.getFieldDiffs(), "命令类型", "commandType",
                                dbControl.getCommandType(), xmlControl.getCommandType());
                    compareField(controlDiff.getFieldDiffs(), "控制类型", "controlType",
                                dbControl.getControlType(), xmlControl.getControlType());
                    compareField(controlDiff.getFieldDiffs(), "数据类型", "dataType",
                                dbControl.getDataType(), xmlControl.getDataType());
                    compareField(controlDiff.getFieldDiffs(), "最大值", "maxValue",
                                dbControl.getMaxValue(), xmlControl.getMaxValue());
                    compareField(controlDiff.getFieldDiffs(), "最小值", "minValue",
                                dbControl.getMinValue(), xmlControl.getMinValue());
                    compareField(controlDiff.getFieldDiffs(), "默认值", "defaultValue",
                                dbControl.getDefaultValue(), xmlControl.getDefaultValue());
                    compareField(controlDiff.getFieldDiffs(), "模块号", "moduleNo",
                                dbControl.getModuleNo(), xmlControl.getModuleNo());

                    if (!controlDiff.getFieldDiffs().isEmpty()) {
                        controlDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }
                }

                if (controlDiff.getDiffType() != null) {
                    templateDiff.getControlDiffs().add(controlDiff);
                }
            }

            // 检查删除的控制
            for (Control dbControl : dbControls) {
                if (!processedControlIds.contains(dbControl.getControlId())) {
                    ConfigDiffResult.ControlDiff controlDiff = new ConfigDiffResult.ControlDiff();
                    controlDiff.setControlId(dbControl.getControlId());
                    controlDiff.setControlName(dbControl.getControlName());
                    controlDiff.setDiffType(ConfigDiffResult.DiffType.DELETED);
                    templateDiff.getControlDiffs().add(controlDiff);
                }
            }

        } catch (Exception e) {
            log.error("比较控制失败", e);
        }
    }

    /**
     * 比较端口
     */
    private void comparePorts(Element portsElement, Integer monitorUnitId, ConfigDiffResult diffResult) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> portElements = portsElement.elements("Port");

            // 获取数据库中的端口
            List<Port> dbPorts = portService.findByMonitorUnitId(monitorUnitId);
            Map<Integer, Port> dbPortMap = dbPorts.stream()
                    .collect(Collectors.toMap(Port::getPortId, Function.identity()));

            Set<Integer> processedPortIds = new HashSet<>();

            for (Element portElement : portElements) {
                String portIdStr = portElement.attributeValue("PortId");
                if (portIdStr == null) {
                    continue;
                }

                Integer portId = Integer.parseInt(portIdStr);
                processedPortIds.add(portId);

                Port dbPort = dbPortMap.get(portId);
                Port xmlPort = parsePortFromXml(monitorUnitId, portElement);
                if (xmlPort == null) {
                    continue;
                }

                ConfigDiffResult.PortDiff portDiff = new ConfigDiffResult.PortDiff();
                portDiff.setPortId(portId);
                portDiff.setPortName(xmlPort.getPortName());

                if (dbPort == null) {
                    // 新增的端口
                    portDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                } else {
                    // 比较端口字段
                    compareField(portDiff.getFieldDiffs(), "端口号", "portNo",
                                dbPort.getPortNo(), xmlPort.getPortNo());
                    compareField(portDiff.getFieldDiffs(), "端口名称", "portName",
                                dbPort.getPortName(), xmlPort.getPortName());
                    compareField(portDiff.getFieldDiffs(), "端口类型", "portType",
                                dbPort.getPortType(), xmlPort.getPortType());
                    compareField(portDiff.getFieldDiffs(), "设置", "setting",
                                dbPort.getSetting(), xmlPort.getSetting());
                    compareField(portDiff.getFieldDiffs(), "电话号码", "phoneNumber",
                                dbPort.getPhoneNumber(), xmlPort.getPhoneNumber());
                    compareField(portDiff.getFieldDiffs(), "链接采集单元ID", "linkSamplerUnitId",
                                dbPort.getLinkSamplerUnitId(), xmlPort.getLinkSamplerUnitId());
                    compareField(portDiff.getFieldDiffs(), "描述", "description",
                                dbPort.getDescription(), xmlPort.getDescription());

                    if (!portDiff.getFieldDiffs().isEmpty()) {
                        portDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }
                }

                if (portDiff.getDiffType() != null) {
                    diffResult.getPortDiffs().add(portDiff);
                }
            }

            // 检查删除的端口
            for (Port dbPort : dbPorts) {
                if (!processedPortIds.contains(dbPort.getPortId())) {
                    ConfigDiffResult.PortDiff portDiff = new ConfigDiffResult.PortDiff();
                    portDiff.setPortId(dbPort.getPortId());
                    portDiff.setPortName(dbPort.getPortName());
                    portDiff.setDiffType(ConfigDiffResult.DiffType.DELETED);
                    diffResult.getPortDiffs().add(portDiff);
                }
            }

        } catch (Exception e) {
            log.error("比较端口失败", e);
        }
    }

    /**
     * 比较采集单元
     */
    private void compareSamplerUnits(Element samplerUnitsElement, Integer monitorUnitId, ConfigDiffResult diffResult) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> samplerUnitElements = samplerUnitsElement.elements("SamplerUnit");

            // 获取数据库中的采集单元
            List<SamplerUnit> dbSamplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnitId);
            Map<Integer, SamplerUnit> dbSamplerUnitMap = dbSamplerUnits.stream()
                    .collect(Collectors.toMap(SamplerUnit::getSamplerUnitId, Function.identity()));

            Set<Integer> processedSamplerUnitIds = new HashSet<>();

            for (Element samplerUnitElement : samplerUnitElements) {
                String samplerUnitIdStr = samplerUnitElement.attributeValue("SamplerUnitId");
                if (samplerUnitIdStr == null) {
                    continue;
                }

                Integer samplerUnitId = Integer.parseInt(samplerUnitIdStr);
                processedSamplerUnitIds.add(samplerUnitId);

                SamplerUnit dbSamplerUnit = dbSamplerUnitMap.get(samplerUnitId);
                SamplerUnit xmlSamplerUnit = parseSamplerUnitFromXml(monitorUnitId, samplerUnitElement);
                if (xmlSamplerUnit == null) {
                    continue;
                }

                ConfigDiffResult.SamplerUnitDiff samplerUnitDiff = new ConfigDiffResult.SamplerUnitDiff();
                samplerUnitDiff.setSamplerUnitId(samplerUnitId);
                samplerUnitDiff.setSamplerUnitName(xmlSamplerUnit.getSamplerUnitName());

                if (dbSamplerUnit == null) {
                    // 新增的采集单元
                    samplerUnitDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                } else {
                    // 比较采集单元字段
                    compareField(samplerUnitDiff.getFieldDiffs(), "端口ID", "portId",
                                dbSamplerUnit.getPortId(), xmlSamplerUnit.getPortId());
                    compareField(samplerUnitDiff.getFieldDiffs(), "父采集单元ID", "parentSamplerUnitId",
                                dbSamplerUnit.getParentSamplerUnitId(), xmlSamplerUnit.getParentSamplerUnitId());
                    compareField(samplerUnitDiff.getFieldDiffs(), "采集单元名称", "samplerUnitName",
                                dbSamplerUnit.getSamplerUnitName(), xmlSamplerUnit.getSamplerUnitName());
                    compareField(samplerUnitDiff.getFieldDiffs(), "采集器类型", "samplerType",
                                dbSamplerUnit.getSamplerType(), xmlSamplerUnit.getSamplerType());
                    compareField(samplerUnitDiff.getFieldDiffs(), "地址", "address",
                                dbSamplerUnit.getAddress(), xmlSamplerUnit.getAddress());
                    compareField(samplerUnitDiff.getFieldDiffs(), "采集间隔", "spUnitInterval",
                                dbSamplerUnit.getSpUnitInterval(), xmlSamplerUnit.getSpUnitInterval());
                    compareField(samplerUnitDiff.getFieldDiffs(), "电话号码", "phoneNumber",
                                dbSamplerUnit.getPhoneNumber(), xmlSamplerUnit.getPhoneNumber());
                    compareField(samplerUnitDiff.getFieldDiffs(), "描述", "description",
                                dbSamplerUnit.getDescription(), xmlSamplerUnit.getDescription());
                    compareField(samplerUnitDiff.getFieldDiffs(), "DLL路径", "dllPath",
                                dbSamplerUnit.getDllPath(), xmlSamplerUnit.getDllPath());
                    compareField(samplerUnitDiff.getFieldDiffs(), "采集器ID", "samplerId",
                                dbSamplerUnit.getSamplerId(), xmlSamplerUnit.getSamplerId());

                    if (!samplerUnitDiff.getFieldDiffs().isEmpty()) {
                        samplerUnitDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }
                }

                if (samplerUnitDiff.getDiffType() != null) {
                    diffResult.getSamplerUnitDiffs().add(samplerUnitDiff);
                }
            }

            // 检查删除的采集单元
            for (SamplerUnit dbSamplerUnit : dbSamplerUnits) {
                if (!processedSamplerUnitIds.contains(dbSamplerUnit.getSamplerUnitId())) {
                    ConfigDiffResult.SamplerUnitDiff samplerUnitDiff = new ConfigDiffResult.SamplerUnitDiff();
                    samplerUnitDiff.setSamplerUnitId(dbSamplerUnit.getSamplerUnitId());
                    samplerUnitDiff.setSamplerUnitName(dbSamplerUnit.getSamplerUnitName());
                    samplerUnitDiff.setDiffType(ConfigDiffResult.DiffType.DELETED);
                    diffResult.getSamplerUnitDiffs().add(samplerUnitDiff);
                }
            }

        } catch (Exception e) {
            log.error("比较采集单元失败", e);
        }
    }

    /**
     * 比较设备
     */
    private void compareEquipments(Element equipmentsElement, Integer monitorUnitId, ConfigDiffResult diffResult) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> equipmentElements = equipmentsElement.elements("Equipment");

            // 获取数据库中的设备
            List<Equipment> dbEquipments = equipmentService.findByMonitorUnitId(monitorUnitId);
            Map<Integer, Equipment> dbEquipmentMap = dbEquipments.stream()
                    .collect(Collectors.toMap(Equipment::getEquipmentId, Function.identity()));

            Set<Integer> processedEquipmentIds = new HashSet<>();

            for (Element equipmentElement : equipmentElements) {
                String equipmentIdStr = equipmentElement.attributeValue("EquipmentId");
                if (equipmentIdStr == null) {
                    continue;
                }

                Integer equipmentId = Integer.parseInt(equipmentIdStr);
                processedEquipmentIds.add(equipmentId);

                Equipment dbEquipment = dbEquipmentMap.get(equipmentId);
                Equipment xmlEquipment = parseEquipmentFromXml(monitorUnitId, equipmentElement);
                if (xmlEquipment == null) {
                    continue;
                }

                ConfigDiffResult.EquipmentDiff equipmentDiff = new ConfigDiffResult.EquipmentDiff();
                equipmentDiff.setEquipmentId(equipmentId);
                equipmentDiff.setEquipmentName(xmlEquipment.getEquipmentName());

                if (dbEquipment == null) {
                    // 新增的设备
                    equipmentDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                } else {
                    // 比较设备字段
                    compareField(equipmentDiff.getFieldDiffs(), "设备名称", "equipmentName",
                                dbEquipment.getEquipmentName(), xmlEquipment.getEquipmentName());
                    compareField(equipmentDiff.getFieldDiffs(), "设备类别", "equipmentCategory",
                                dbEquipment.getEquipmentCategory(), xmlEquipment.getEquipmentCategory());
                    compareField(equipmentDiff.getFieldDiffs(), "设备模板ID", "equipmentTemplateId",
                                dbEquipment.getEquipmentTemplateId(), xmlEquipment.getEquipmentTemplateId());
                    compareField(equipmentDiff.getFieldDiffs(), "采集单元ID", "samplerUnitId",
                                dbEquipment.getSamplerUnitId(), xmlEquipment.getSamplerUnitId());
                    compareField(equipmentDiff.getFieldDiffs(), "父设备ID", "parentEquipmentId",
                                dbEquipment.getParentEquipmentId(), xmlEquipment.getParentEquipmentId());
                    compareField(equipmentDiff.getFieldDiffs(), "描述", "description",
                                dbEquipment.getDescription(), xmlEquipment.getDescription());
                    compareField(equipmentDiff.getFieldDiffs(), "设备类型", "equipmentType",
                                dbEquipment.getEquipmentType(), xmlEquipment.getEquipmentType());
                    compareField(equipmentDiff.getFieldDiffs(), "设备类", "equipmentClass",
                                dbEquipment.getEquipmentClass(), xmlEquipment.getEquipmentClass());
                    compareField(equipmentDiff.getFieldDiffs(), "设备状态", "equipmentState",
                                dbEquipment.getEquipmentState(), xmlEquipment.getEquipmentState());

                    if (!equipmentDiff.getFieldDiffs().isEmpty()) {
                        equipmentDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }
                }

                if (equipmentDiff.getDiffType() != null) {
                    diffResult.getEquipmentDiffs().add(equipmentDiff);
                }
            }

            // 检查删除的设备
            for (Equipment dbEquipment : dbEquipments) {
                if (!processedEquipmentIds.contains(dbEquipment.getEquipmentId())) {
                    ConfigDiffResult.EquipmentDiff equipmentDiff = new ConfigDiffResult.EquipmentDiff();
                    equipmentDiff.setEquipmentId(dbEquipment.getEquipmentId());
                    equipmentDiff.setEquipmentName(dbEquipment.getEquipmentName());
                    equipmentDiff.setDiffType(ConfigDiffResult.DiffType.DELETED);
                    diffResult.getEquipmentDiffs().add(equipmentDiff);
                }
            }

        } catch (Exception e) {
            log.error("比较设备失败", e);
        }
    }

    @Override
    public ConfigUploadAndDiffResult uploadAndCompareConfig(MultipartFile file) {
        ConfigUploadAndDiffResult result = new ConfigUploadAndDiffResult();
        result.setUploadTime(LocalDateTime.now());
        result.setOriginalFileName(file.getOriginalFilename());
        result.setFileSize(file.getSize());

        File tempDir = null;
        String uploadTaskId = UUID.randomUUID().toString();
        result.setUploadTaskId(uploadTaskId);

        try {
            // 1. 保存上传的文件到临时目录
            String uploadRelativePath = "upload";
            String uploadFullPath = filePathProvider.getFullPath(filePathProvider.getWorkspacePath(), uploadRelativePath);
            fileUtil.createDirectory(uploadFullPath);

            String fileName = "config_upload_" + uploadTaskId + ".zip";
            fileUtil.writeFile(uploadFullPath, fileName, file.getBytes());
            result.setUploadedFilePath(uploadFullPath + "/" + fileName);

            // 2. 解压并解析配置文件
            tempDir = extractZipFileFromFile(new File(uploadFullPath, fileName));
            log.info("配置文件解压完成: {}", tempDir.getAbsolutePath());

            // 3. 查找XML配置文件
            File[] xmlFiles = findXmlConfigFiles(tempDir);
            if (xmlFiles == null || xmlFiles.length == 0) {
                throw new RuntimeException("未找到有效的XML配置文件");
            }

            // 4. 解析监控单元信息
            Integer detectedMonitorUnitId = null;
            String detectedMonitorUnitName = null;

            for (File xmlFile : xmlFiles) {
                try {
                    SAXReader reader = new SAXReader();
                    Document document = reader.read(xmlFile);
                    Element root = document.getRootElement();
                    Element monitorUnitElement = root.element("MonitorUnit");

                    if (monitorUnitElement != null) {
                        String monitorUnitIdStr = monitorUnitElement.attributeValue("MonitorUnitId");
                        if (monitorUnitIdStr != null) {
                            detectedMonitorUnitId = Integer.parseInt(monitorUnitIdStr);
                            detectedMonitorUnitName = monitorUnitElement.attributeValue("MonitorUnitName");
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析XML文件失败: {}", xmlFile.getName(), e);
                }
            }

            if (detectedMonitorUnitId == null) {
                throw new RuntimeException("无法从配置文件中解析出监控单元ID");
            }

            result.setDetectedMonitorUnitId(detectedMonitorUnitId);
            result.setDetectedMonitorUnitName(detectedMonitorUnitName);

            // 5. 检查是否为新建监控单元
            MonitorUnit existingMonitorUnit = monitorUnitService.getById(detectedMonitorUnitId);
            result.setNewMonitorUnit(existingMonitorUnit == null);

            // 6. 进行配置差异对比
            ConfigDiffResult diffResult = new ConfigDiffResult();
            if (!result.isNewMonitorUnit()) {
                // 监控单元已存在，进行完整的差异对比
                for (File xmlFile : xmlFiles) {
                    try {
                        compareXmlWithDatabase(xmlFile, detectedMonitorUnitId, diffResult);
                    } catch (Exception e) {
                        log.error("对比XML文件失败: {}, 错误: {}", xmlFile.getName(), e.getMessage(), e);
                    }
                }
            } else {
                // 新建监控单元，需要区分处理：
                // - 监控单元本身：新建
                // - 端口、采集单元、设备：新建（属于监控单元）
                // - 设备模板：需要检查是否已存在（可能被其他监控单元使用）
                for (File xmlFile : xmlFiles) {
                    try {
                        compareXmlForNewMonitorUnit(xmlFile, detectedMonitorUnitId, diffResult);
                    } catch (Exception e) {
                        log.error("对比新建监控单元XML文件失败: {}, 错误: {}", xmlFile.getName(), e.getMessage(), e);
                    }
                }
            }

            result.setDiffResult(diffResult);

            // 7. 生成差异统计摘要
            ConfigUploadAndDiffResult.DiffSummary summary = generateDiffSummary(diffResult);
            result.setDiffSummary(summary);

            // 8. 缓存上传文件信息
            UploadFileInfo uploadInfo = new UploadFileInfo();
            uploadInfo.setUploadTaskId(uploadTaskId);
            uploadInfo.setFilePath(PathUtil.pathJoin(uploadFullPath, fileName));
            uploadInfo.setUploadTime(LocalDateTime.now());
            uploadInfo.setMonitorUnitId(detectedMonitorUnitId);
            uploadFileCache.put(uploadTaskId, uploadInfo);

            result.setSuccess(true);
            log.info("配置上传和差异对比完成，任务ID: {}, 监控单元ID: {}, 是否新建: {}",
                    uploadTaskId, detectedMonitorUnitId, result.isNewMonitorUnit());

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            log.error("配置上传和差异对比失败", e);
        } finally {
            // 清理临时解压目录
            if (tempDir != null) {
                cleanupTempFiles(tempDir);
            }
        }

        return result;
    }

    @Override
    public ConfigImportReport confirmImportFromUpload(String uploadTaskId, ConfigImportTaskRequest request) {
        // 从缓存中获取上传文件信息
        UploadFileInfo uploadInfo = uploadFileCache.get(uploadTaskId);
        if (uploadInfo == null) {
            return ConfigImportReport.createFailureReport(uploadTaskId, "上传任务不存在或已过期");
        }
        long startTime = System.currentTimeMillis();

        try {
            // 设置当前监控单元ID
            request.setCurrentMonitorUnitId(uploadInfo.getMonitorUnitId());

            // 检查是否为新建监控单元
            MonitorUnit existingMonitorUnit = monitorUnitService.getById(uploadInfo.getMonitorUnitId());
            boolean isNewMonitorUnit = (existingMonitorUnit == null);
            String monitorUnitName = isNewMonitorUnit ? "新建监控单元" : existingMonitorUnit.getMonitorUnitName();

            // 执行导入逻辑
            ConfigImportReport report = executeImportFromFile(uploadTaskId, uploadInfo.getFilePath(), request);

            // 计算耗时
            long duration = System.currentTimeMillis() - startTime;

            // 更新报告信息
            report.setDurationMs(duration);
            report.setMonitorUnitId(uploadInfo.getMonitorUnitId());
            report.setMonitorUnitName(monitorUnitName);
            report.setNewMonitorUnit(isNewMonitorUnit);

            // 简化消息
            if (report.isSuccess()) {
                if (isNewMonitorUnit) {
                    report.setMessage("新建监控单元配置导入成功");
                } else {
                    report.setMessage("监控单元配置更新成功");
                }
            } else {
                report.setMessage("配置导入失败");
            }

            // 清理缓存和文件
            uploadFileCache.remove(uploadTaskId);
            File uploadedFile = new File(uploadInfo.getFilePath());
            if (uploadedFile.exists()) {
                uploadedFile.delete();
            }

            log.info("配置导入完成，任务ID: {}, 成功: {}, 耗时: {}ms", uploadTaskId, report.isSuccess(), duration);
            return report;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("确认导入失败，任务ID: {}, 耗时: {}ms", uploadTaskId, duration, e);

            ConfigImportReport report = ConfigImportReport.createFailureReport(uploadTaskId, e.getMessage());
            report.setDurationMs(duration);
            return report;
        }
    }

    @Override
    public boolean cancelUploadTask(String uploadTaskId) {
        try {
            // 从缓存中获取上传文件信息
            UploadFileInfo uploadInfo = uploadFileCache.get(uploadTaskId);
            if (uploadInfo == null) {
                log.warn("上传任务不存在或已过期: {}", uploadTaskId);
                return false;
            }

            // 删除上传的文件
            File uploadedFile = new File(uploadInfo.getFilePath());
            if (uploadedFile.exists()) {
                boolean deleted = uploadedFile.delete();
                if (deleted) {
                    log.info("已删除上传文件: {}", uploadInfo.getFilePath());
                } else {
                    log.warn("删除上传文件失败: {}", uploadInfo.getFilePath());
                }
            }

            // 从缓存中移除
            uploadFileCache.remove(uploadTaskId);

            log.info("上传任务已取消: {}", uploadTaskId);
            return true;

        } catch (Exception e) {
            log.error("取消上传任务失败: {}", uploadTaskId, e);
            return false;
        }
    }



    /**
     * 为新建监控单元进行差异对比
     * 新建监控单元时：
     * - 监控单元本身：新建
     * - 端口、采集单元、设备：新建（属于监控单元）
     * - 设备模板：需要检查是否已存在（可能被其他监控单元使用）
     */
    private void compareXmlForNewMonitorUnit(File xmlFile, Integer monitorUnitId, ConfigDiffResult diffResult) {
        try {
            SAXReader reader = new SAXReader();
            Document document = reader.read(xmlFile);
            Element root = document.getRootElement();

            // 处理监控单元 - 新建
            Element monitorUnitElement = root.element("MonitorUnit");
            if (monitorUnitElement != null) {
                MonitorUnit xmlMonitorUnit = parseMonitorUnitFromXml(monitorUnitElement);
                if (xmlMonitorUnit != null) {
                    ConfigDiffResult.MonitorUnitDiff monitorUnitDiff = new ConfigDiffResult.MonitorUnitDiff();
                    monitorUnitDiff.setMonitorUnitId(xmlMonitorUnit.getMonitorUnitId());
                    monitorUnitDiff.setMonitorUnitName(xmlMonitorUnit.getMonitorUnitName());
                    // 新建监控单元，不需要显示字段差异，只是标记为新建
                    diffResult.setMonitorUnitDiff(monitorUnitDiff);

                    // 处理端口 - 新建（属于监控单元）
                    Element portsElement = monitorUnitElement.element("Ports");
                    if (portsElement != null) {
                        markPortsAsNew(portsElement, diffResult);
                    }

                    // 处理采集单元 - 新建（属于监控单元）
                    Element samplerUnitsElement = monitorUnitElement.element("SamplerUnits");
                    if (samplerUnitsElement != null) {
                        markSamplerUnitsAsNew(samplerUnitsElement, diffResult);
                    }

                    // 处理设备 - 新建（属于监控单元）
                    Element equipmentsElement = monitorUnitElement.element("Equipments");
                    if (equipmentsElement != null) {
                        markEquipmentsAsNew(equipmentsElement, diffResult);
                    }
                }
            }

            // 处理设备模板 - 需要检查是否已存在
            Element equipmentTemplatesElement = root.element("EquipmentTemplates");
            if (equipmentTemplatesElement != null) {
                compareEquipmentTemplatesForNewMonitorUnit(equipmentTemplatesElement, diffResult);
            }

        } catch (Exception e) {
            log.error("新建监控单元差异对比失败", e);
        }
    }

    /**
     * 为新建监控单元比较设备模板
     * 设备模板可能已经存在于数据库中（被其他监控单元使用）
     */
    private void compareEquipmentTemplatesForNewMonitorUnit(Element equipmentTemplatesElement, ConfigDiffResult diffResult) {
        try {
            @SuppressWarnings("unchecked")
            List<Element> templateElements = equipmentTemplatesElement.elements("EquipmentTemplate");

            for (Element templateElement : templateElements) {
                String templateIdStr = templateElement.attributeValue("EquipmentTemplateId");
                if (templateIdStr == null) {
                    continue;
                }

                Integer templateId = Integer.parseInt(templateIdStr);

                // 检查数据库中是否已存在该设备模板
                EquipmentTemplate dbTemplate = equipmentTemplateService.getById(templateId);

                // 解析XML中的设备模板
                EquipmentTemplate xmlTemplate = parseEquipmentTemplateFromXml(templateElement);
                if (xmlTemplate == null) {
                    continue;
                }

                ConfigDiffResult.EquipmentTemplateDiff templateDiff = new ConfigDiffResult.EquipmentTemplateDiff();
                templateDiff.setEquipmentTemplateId(templateId);
                templateDiff.setEquipmentTemplateName(xmlTemplate.getEquipmentTemplateName());

                if (dbTemplate == null) {
                    // 设备模板不存在，标记为新增
                    templateDiff.setDiffType(ConfigDiffResult.DiffType.NEW);

                    // 新增设备模板的所有信号、事件、控制都是新增
                    Element signalsElement = templateElement.element("Signals");
                    if (signalsElement != null) {
                        markSignalsAsNew(signalsElement, templateDiff);
                    }

                    Element eventsElement = templateElement.element("Events");
                    if (eventsElement != null) {
                        markEventsAsNew(eventsElement, templateDiff);
                    }

                    Element controlsElement = templateElement.element("Controls");
                    if (controlsElement != null) {
                        markControlsAsNew(controlsElement, templateDiff);
                    }
                } else {
                    // 设备模板已存在，需要进行详细对比
                    // 比较设备模板字段
                    compareField(templateDiff.getFieldDiffs(), "设备模板名称", "equipmentTemplateName",
                                dbTemplate.getEquipmentTemplateName(), xmlTemplate.getEquipmentTemplateName());
                    compareField(templateDiff.getFieldDiffs(), "协议代码", "protocolCode",
                                dbTemplate.getProtocolCode(), xmlTemplate.getProtocolCode());
                    compareField(templateDiff.getFieldDiffs(), "设备类别", "equipmentCategory",
                                dbTemplate.getEquipmentCategory(), xmlTemplate.getEquipmentCategory());
                    compareField(templateDiff.getFieldDiffs(), "设备类型", "equipmentType",
                                dbTemplate.getEquipmentType(), xmlTemplate.getEquipmentType());
                    compareField(templateDiff.getFieldDiffs(), "备注", "memo",
                                dbTemplate.getMemo(), xmlTemplate.getMemo());
                    compareField(templateDiff.getFieldDiffs(), "属性", "property",
                                dbTemplate.getProperty(), xmlTemplate.getProperty());
                    compareField(templateDiff.getFieldDiffs(), "描述", "description",
                                dbTemplate.getDescription(), xmlTemplate.getDescription());
                    compareField(templateDiff.getFieldDiffs(), "设备基础类型", "equipmentBaseType",
                                dbTemplate.getEquipmentBaseType(), xmlTemplate.getEquipmentBaseType());

                    if (!templateDiff.getFieldDiffs().isEmpty()) {
                        templateDiff.setDiffType(ConfigDiffResult.DiffType.MODIFIED);
                    }

                    // 比较信号、事件、控制（使用现有的比较方法）
                    Element signalsElement = templateElement.element("Signals");
                    if (signalsElement != null) {
                        compareSignals(signalsElement, templateId, templateDiff);
                    }

                    Element eventsElement = templateElement.element("Events");
                    if (eventsElement != null) {
                        compareEvents(eventsElement, templateId, templateDiff);
                    }

                    Element controlsElement = templateElement.element("Controls");
                    if (controlsElement != null) {
                        compareControls(controlsElement, templateId, templateDiff);
                    }
                }

                // 如果有差异或者是新增，添加到结果中
                if (templateDiff.getDiffType() != null ||
                    !templateDiff.getSignalDiffs().isEmpty() ||
                    !templateDiff.getEventDiffs().isEmpty() ||
                    !templateDiff.getControlDiffs().isEmpty()) {
                    diffResult.getEquipmentTemplateDiffs().add(templateDiff);
                }
            }

        } catch (Exception e) {
            log.error("新建监控单元设备模板对比失败", e);
        }
    }

    /**
     * 标记端口为新增
     */
    private void markPortsAsNew(Element portsElement, ConfigDiffResult diffResult) {
        @SuppressWarnings("unchecked")
        List<Element> portElements = portsElement.elements("Port");

        for (Element portElement : portElements) {
            String portIdStr = portElement.attributeValue("PortId");
            if (portIdStr != null) {
                ConfigDiffResult.PortDiff portDiff = new ConfigDiffResult.PortDiff();
                portDiff.setPortId(Integer.parseInt(portIdStr));
                portDiff.setPortName(portElement.attributeValue("PortName"));
                portDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                diffResult.getPortDiffs().add(portDiff);
            }
        }
    }

    /**
     * 标记采集单元为新增
     */
    private void markSamplerUnitsAsNew(Element samplerUnitsElement, ConfigDiffResult diffResult) {
        @SuppressWarnings("unchecked")
        List<Element> samplerUnitElements = samplerUnitsElement.elements("SamplerUnit");

        for (Element samplerUnitElement : samplerUnitElements) {
            String samplerUnitIdStr = samplerUnitElement.attributeValue("SamplerUnitId");
            if (samplerUnitIdStr != null) {
                ConfigDiffResult.SamplerUnitDiff samplerUnitDiff = new ConfigDiffResult.SamplerUnitDiff();
                samplerUnitDiff.setSamplerUnitId(Integer.parseInt(samplerUnitIdStr));
                samplerUnitDiff.setSamplerUnitName(samplerUnitElement.attributeValue("SamplerUnitName"));
                samplerUnitDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                diffResult.getSamplerUnitDiffs().add(samplerUnitDiff);
            }
        }
    }

    /**
     * 标记设备为新增
     */
    private void markEquipmentsAsNew(Element equipmentsElement, ConfigDiffResult diffResult) {
        @SuppressWarnings("unchecked")
        List<Element> equipmentElements = equipmentsElement.elements("Equipment");

        for (Element equipmentElement : equipmentElements) {
            String equipmentIdStr = equipmentElement.attributeValue("EquipmentId");
            if (equipmentIdStr != null) {
                ConfigDiffResult.EquipmentDiff equipmentDiff = new ConfigDiffResult.EquipmentDiff();
                equipmentDiff.setEquipmentId(Integer.parseInt(equipmentIdStr));
                equipmentDiff.setEquipmentName(equipmentElement.attributeValue("EquipmentName"));
                equipmentDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                diffResult.getEquipmentDiffs().add(equipmentDiff);
            }
        }
    }



    /**
     * 标记信号为新增
     */
    private void markSignalsAsNew(Element signalsElement, ConfigDiffResult.EquipmentTemplateDiff templateDiff) {
        @SuppressWarnings("unchecked")
        List<Element> signalElements = signalsElement.elements("Signal");

        for (Element signalElement : signalElements) {
            String signalIdStr = signalElement.attributeValue("SignalId");
            if (signalIdStr != null) {
                ConfigDiffResult.SignalDiff signalDiff = new ConfigDiffResult.SignalDiff();
                signalDiff.setSignalId(Integer.parseInt(signalIdStr));
                signalDiff.setSignalName(signalElement.attributeValue("SignalName"));
                signalDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                templateDiff.getSignalDiffs().add(signalDiff);
            }
        }
    }

    /**
     * 标记事件为新增
     */
    private void markEventsAsNew(Element eventsElement, ConfigDiffResult.EquipmentTemplateDiff templateDiff) {
        @SuppressWarnings("unchecked")
        List<Element> eventElements = eventsElement.elements("Event");

        for (Element eventElement : eventElements) {
            String eventIdStr = eventElement.attributeValue("EventId");
            if (eventIdStr != null) {
                ConfigDiffResult.EventDiff eventDiff = new ConfigDiffResult.EventDiff();
                eventDiff.setEventId(Integer.parseInt(eventIdStr));
                eventDiff.setEventName(eventElement.attributeValue("EventName"));
                eventDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                templateDiff.getEventDiffs().add(eventDiff);
            }
        }
    }

    /**
     * 标记控制为新增
     */
    private void markControlsAsNew(Element controlsElement, ConfigDiffResult.EquipmentTemplateDiff templateDiff) {
        @SuppressWarnings("unchecked")
        List<Element> controlElements = controlsElement.elements("Control");

        for (Element controlElement : controlElements) {
            String controlIdStr = controlElement.attributeValue("ControlId");
            if (controlIdStr != null) {
                ConfigDiffResult.ControlDiff controlDiff = new ConfigDiffResult.ControlDiff();
                controlDiff.setControlId(Integer.parseInt(controlIdStr));
                controlDiff.setControlName(controlElement.attributeValue("ControlName"));
                controlDiff.setDiffType(ConfigDiffResult.DiffType.NEW);
                templateDiff.getControlDiffs().add(controlDiff);
            }
        }
    }

    /**
     * 生成差异统计摘要
     */
    private ConfigUploadAndDiffResult.DiffSummary generateDiffSummary(ConfigDiffResult diffResult) {
        ConfigUploadAndDiffResult.DiffSummary summary = new ConfigUploadAndDiffResult.DiffSummary();

        // 统计监控单元差异
        if (diffResult.getMonitorUnitDiff() != null && diffResult.getMonitorUnitDiff().hasDifferences()) {
            summary.setMonitorUnitChanges(diffResult.getMonitorUnitDiff().getFieldDiffs().size());
        }

        // 统计设备模板差异
        summary.setEquipmentTemplateChanges(diffResult.getEquipmentTemplateDiffs().size());
        summary.setNewEquipmentTemplates((int) diffResult.getEquipmentTemplateDiffs().stream()
                .filter(diff -> diff.getDiffType() == ConfigDiffResult.DiffType.NEW).count());
        summary.setDeletedEquipmentTemplates((int) diffResult.getEquipmentTemplateDiffs().stream()
                .filter(diff -> diff.getDiffType() == ConfigDiffResult.DiffType.DELETED).count());
        summary.setModifiedEquipmentTemplates((int) diffResult.getEquipmentTemplateDiffs().stream()
                .filter(diff -> diff.getDiffType() == ConfigDiffResult.DiffType.MODIFIED).count());

        // 统计信号差异
        int totalSignalChanges = diffResult.getEquipmentTemplateDiffs().stream()
                .mapToInt(template -> template.getSignalDiffs().size()).sum();
        summary.setSignalChanges(totalSignalChanges);

        // 统计事件差异
        int totalEventChanges = diffResult.getEquipmentTemplateDiffs().stream()
                .mapToInt(template -> template.getEventDiffs().size()).sum();
        summary.setEventChanges(totalEventChanges);

        // 统计控制差异
        int totalControlChanges = diffResult.getEquipmentTemplateDiffs().stream()
                .mapToInt(template -> template.getControlDiffs().size()).sum();
        summary.setControlChanges(totalControlChanges);

        // 统计端口差异
        summary.setPortChanges(diffResult.getPortDiffs().size());

        // 统计采集单元差异
        summary.setSamplerUnitChanges(diffResult.getSamplerUnitDiffs().size());

        // 统计设备差异
        summary.setEquipmentChanges(diffResult.getEquipmentDiffs().size());

        return summary;
    }

    /**
     * 上传文件信息
     */
    private static class UploadFileInfo {
        private String uploadTaskId;
        private String filePath;
        private LocalDateTime uploadTime;
        private Integer monitorUnitId;

        // Getters and Setters
        public String getUploadTaskId() { return uploadTaskId; }
        public void setUploadTaskId(String uploadTaskId) { this.uploadTaskId = uploadTaskId; }

        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public LocalDateTime getUploadTime() { return uploadTime; }
        public void setUploadTime(LocalDateTime uploadTime) { this.uploadTime = uploadTime; }

        public Integer getMonitorUnitId() { return monitorUnitId; }
        public void setMonitorUnitId(Integer monitorUnitId) { this.monitorUnitId = monitorUnitId; }
    }

    // ==================== SO 文件处理方法 ====================

    /**
     * 处理 ZIP 文件中的 SO 文件
     * @param zipFilePath ZIP 文件路径
     * @param monitorUnitXmlPath 监控单元 XML 文件路径
     * @param report 配置导入报告，用于记录处理结果
     */
    private void processSoFiles(String zipFilePath, String monitorUnitXmlPath, ConfigImportReport report) {
        try {
            log.info("开始处理 SO 文件，ZIP: {}, XML: {}", zipFilePath, monitorUnitXmlPath);

            // 1. 解析 XML 文件，提取 SamplerUnit 信息
            Map<Integer, String> samplerIdToDllPathMap = extractSamplerIdToDllPathMapping(monitorUnitXmlPath);
            if (samplerIdToDllPathMap.isEmpty()) {
                log.info("XML 文件中没有找到 SamplerUnit 信息");
                return;
            }

            // 2. 检查 samplerid 到 DllPath 的映射是否唯一
            if (!validateSamplerIdToDllPathUniqueness(samplerIdToDllPathMap)) {
                String message = "SamplerId 到 DllPath 的映射不唯一，放弃 SO 文件处理";
                log.warn(message);
                addSoProcessingMessage(report, message);
                return;
            }

            // 3. 获取 ZIP 文件中的 SO 文件列表
            Set<String> soFilesInZip = extractSoFilesFromZip(zipFilePath);
            log.info("ZIP 文件中找到 {} 个 SO 文件: {}", soFilesInZip.size(), soFilesInZip);

            // 4. 处理每个 SamplerUnit
            List<String> processMessages = new ArrayList<>();
            for (Map.Entry<Integer, String> entry : samplerIdToDllPathMap.entrySet()) {
                Integer samplerId = entry.getKey();
                String dllPath = entry.getValue();

                String message = processSingleSamplerUnit(samplerId, dllPath, soFilesInZip, zipFilePath);
                if (message != null) {
                    processMessages.add(message);
                }
            }

            // 5. 将处理结果添加到配置导入报告中
            if (!processMessages.isEmpty()) {
                // 添加标题和所有处理消息
                addSoProcessingMessage(report, "SO 文件处理结果：");
                for (String message : processMessages) {
                    addSoProcessingMessage(report, message);
                }
            }

            log.info("SO 文件处理完成");

        } catch (Exception e) {
            log.error("处理 SO 文件时发生错误", e);
            addSoProcessingMessage(report, "SO 文件处理失败: " + e.getMessage());
        }
    }

    /**
     * 从 XML 文件中提取 SamplerUnit 的 SamplerId 到 DllPath 的映射
     */
    private Map<Integer, String> extractSamplerIdToDllPathMapping(String xmlFilePath) {
        Map<Integer, String> mapping = new HashMap<>();

        try {
            SAXReader reader = new SAXReader();
            Document document = reader.read(new File(xmlFilePath));
            Element root = document.getRootElement();

            // 查找所有 SamplerUnit 元素
            List<Element> samplerUnits = root.element("MonitorUnit").element("SamplerUnits").elements("SamplerUnit");

            for (Element samplerUnit : samplerUnits) {
                Integer samplerId = XMLUtil.getAttributeAsInteger(samplerUnit, "SamplerId");
                String dllPath = samplerUnit.attributeValue("DllPath");

                if (samplerId != null && dllPath != null && !dllPath.trim().isEmpty()) {
                    mapping.put(samplerId, dllPath.trim());
                }
            }

            log.info("从 XML 中提取到 {} 个 SamplerUnit 映射", mapping.size());

        } catch (Exception e) {
            log.error("解析 XML 文件失败: {}", xmlFilePath, e);
        }

        return mapping;
    }

    /**
     * 验证 SamplerId 到 DllPath 的映射是否唯一
     */
    private boolean validateSamplerIdToDllPathUniqueness(Map<Integer, String> samplerIdToDllPathMap) {
        // 检查是否有重复的 SamplerId 对应不同的 DllPath
        Map<Integer, Set<String>> samplerIdToDllPaths = new HashMap<>();

        for (Map.Entry<Integer, String> entry : samplerIdToDllPathMap.entrySet()) {
            Integer samplerId = entry.getKey();
            String dllPath = entry.getValue();

            samplerIdToDllPaths.computeIfAbsent(samplerId, k -> new HashSet<>()).add(dllPath);
        }

        for (Map.Entry<Integer, Set<String>> entry : samplerIdToDllPaths.entrySet()) {
            if (entry.getValue().size() > 1) {
                log.warn("SamplerId {} 对应多个不同的 DllPath: {}", entry.getKey(), entry.getValue());
                return false;
            }
        }

        return true;
    }

    /**
     * 从 ZIP 文件中提取 SO 文件列表
     */
    private Set<String> extractSoFilesFromZip(String zipFilePath) {
        Set<String> soFiles = new HashSet<>();

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                String entryName = entry.getName();
                // 查找 SO 文件夹中的 .so 文件
                if (entryName.toLowerCase().contains("so/") && entryName.toLowerCase().endsWith(".so")) {
                    // 提取文件名（不包含路径）
                    String fileName = entryName.substring(entryName.lastIndexOf('/') + 1);
                    soFiles.add(fileName);
                }
            }
        } catch (Exception e) {
            log.error("读取 ZIP 文件中的 SO 文件失败: {}", zipFilePath, e);
        }

        return soFiles;
    }

    /**
     * 处理单个 SamplerUnit
     */
    private String processSingleSamplerUnit(Integer samplerId, String dllPath, Set<String> soFilesInZip, String zipFilePath) {
        try {
            // 1. 检查 ZIP 文件中是否包含对应的 SO 文件
            if (!soFilesInZip.contains(dllPath)) {
                return String.format("采集单元指定了 SamplerId %d，DllPath %s 的动态库，但 ZIP 文件中未找到对应的 SO 文件",
                    samplerId, dllPath);
            }

            // 2. 查询 tsl_sampler 表中的记录
            Sampler sampler = samplerService.findById(samplerId);
            if (sampler == null) {
                return String.format("采集单元指定了 SamplerId %d，DllPath %s 的动态库需要手动新增",
                    samplerId, dllPath);
            }

            // 3. 检查 DllPath 是否匹配
            if (!dllPath.equalsIgnoreCase(sampler.getDllPath())) {
                return String.format("SamplerId %d 在数据库中的 DllPath (%s) 与配置文件中的 DllPath (%s) 不匹配",
                    samplerId, sampler.getDllPath(), dllPath);
            }

            // 4. 检查是否已上传
            if (sampler.getUploadProtocolFile() == null || !sampler.getUploadProtocolFile()) {
                // 未上传，需要进行协议上传逻辑
                return handleProtocolUpload(sampler, dllPath, zipFilePath);
            } else {
                // 已上传，对比 MD5
                return handleMd5Comparison(sampler, dllPath, zipFilePath);
            }

        } catch (Exception e) {
            log.error("处理 SamplerUnit 失败: SamplerId={}, DllPath={}", samplerId, dllPath, e);
            return String.format("处理 SamplerId %d，DllPath %s 时发生错误: %s", samplerId, dllPath, e.getMessage());
        }
    }

    /**
     * 处理协议上传逻辑
     */
    private String handleProtocolUpload(Sampler sampler, String dllPath, String zipFilePath) {
        try {
            // 从 ZIP 文件中提取 SO 文件内容
            byte[] soFileContent = extractSoFileFromZip(zipFilePath, dllPath);
            if (soFileContent == null) {
                return String.format("无法从 ZIP 文件中提取 SO 文件: %s", dllPath);
            }

            // 确定协议类型（根据采集器架构）
            ProtocolTypeEnum protocolType = determineProtocolType(sampler);
            if (protocolType == null) {
                return String.format("无法确定 SamplerId %d 的协议类型", sampler.getSamplerId());
            }

            // 调用真正的协议文件上传接口
            samplerService.uploadProtocolFile(
                sampler,
                protocolType,
                soFileContent,
                dllPath
            );

            log.info("已成功上传 SamplerId {} 的协议文件: {}", sampler.getSamplerId(), dllPath);
            return null; // 成功处理，无需返回消息

        } catch (Exception e) {
            log.error("处理协议上传失败: SamplerId={}, DllPath={}", sampler.getSamplerId(), dllPath, e);
            return String.format("SamplerId %d 的协议文件上传处理失败: %s", sampler.getSamplerId(), e.getMessage());
        }
    }

    /**
     * 确定采集器的协议类型
     */
    private ProtocolTypeEnum determineProtocolType(Sampler sampler) {
        // 根据采集器的信息确定协议类型
        // 这里可以根据实际业务逻辑来判断，比如根据采集器名称、协议代码等

        // 默认使用新架构 (335X)
        // 如果有特定的判断逻辑，可以在这里添加
        if (sampler.getSamplerName() != null && sampler.getSamplerName().contains("9200")) {
            return ProtocolTypeEnum.ARCHITECTURE_9200;
        }

        // 默认返回新架构
        return ProtocolTypeEnum.ARCHITECTURE_335X;
    }

    /**
     * 处理 MD5 对比
     */
    private String handleMd5Comparison(Sampler sampler, String dllPath, String zipFilePath) {
        try {
            // 从 ZIP 文件中提取 SO 文件内容
            byte[] soFileContent = extractSoFileFromZip(zipFilePath, dllPath);
            if (soFileContent == null) {
                return String.format("无法从 ZIP 文件中提取 SO 文件: %s", dllPath);
            }

            // 计算新文件的 MD5
            String newMd5 = calculateMd5(soFileContent);

            // 获取本地文件的 MD5 进行对比
            String localMd5 = getLocalSoFileMd5(sampler, dllPath);

            if (localMd5 == null) {
                return String.format("SamplerName %s 对应的 %s 本地文件不存在，需要在协议管理中手动上传",
                    sampler.getSamplerName(), dllPath);
            }

            if (!newMd5.equals(localMd5)) {
                return String.format("SamplerName %s 对应的 %s 导入文件与本地文件不一致，需要在协议管理中手动上传",
                    sampler.getSamplerName(), dllPath);
            }

            // MD5 一致，无需处理
            log.info("SamplerId {} 的 SO 文件 MD5 一致，无需更新", sampler.getSamplerId());
            return null;

        } catch (Exception e) {
            log.error("MD5 对比失败: SamplerId={}, DllPath={}", sampler.getSamplerId(), dllPath, e);
            return String.format("SamplerId %d 的 MD5 对比失败: %s", sampler.getSamplerId(), e.getMessage());
        }
    }

    /**
     * 获取本地 SO 文件的 MD5 值
     */
    private String getLocalSoFileMd5(Sampler sampler, String dllPath) {
        try {
            // 确定协议类型
            ProtocolTypeEnum protocolType = determineProtocolType(sampler);
            if (protocolType == null) {
                log.warn("无法确定 SamplerId {} 的协议类型", sampler.getSamplerId());
                return null;
            }

            // 构建本地文件路径
            String localFilePath = PathUtil.pathJoin(
                "south-omc-siteweb/workspace/protocol",
                sampler.getProtocolCode(),
                protocolType.getLibPath(),
                dllPath
            );

            File localFile = new File(localFilePath);
            if (!localFile.exists()) {
                log.warn("本地 SO 文件不存在: {}", localFilePath);
                return null;
            }

            // 使用 DigestUtil 计算文件 MD5
            return DigestUtil.md5Hex(localFile);

        } catch (Exception e) {
            log.error("获取本地 SO 文件 MD5 失败: SamplerId={}, DllPath={}", sampler.getSamplerId(), dllPath, e);
            return null;
        }
    }

    /**
     * 从 ZIP 文件中提取指定的 SO 文件内容
     */
    private byte[] extractSoFileFromZip(String zipFilePath, String soFileName) {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                String entryName = entry.getName();
                // 查找匹配的 SO 文件
                if (entryName.toLowerCase().contains("so/") && entryName.endsWith(soFileName)) {
                    // 读取文件内容
                    byte[] buffer = new byte[1024];
                    java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        baos.write(buffer, 0, len);
                    }
                    return baos.toByteArray();
                }
            }
        } catch (Exception e) {
            log.error("从 ZIP 文件中提取 SO 文件失败: {}", soFileName, e);
        }
        return null;
    }

    /**
     * 计算文件内容的 MD5 值
     */
    private String calculateMd5(byte[] content) {
        try {
            return DigestUtil.md5Hex(content);
        } catch (Exception e) {
            log.error("计算 MD5 失败", e);
            throw new RuntimeException("计算 MD5 失败", e);
        }
    }

    /**
     * 查找监控单元 XML 文件
     */
    private File findMonitorUnitXmlFile(File[] xmlFiles) {
        for (File xmlFile : xmlFiles) {
            String fileName = xmlFile.getName().toLowerCase();
            // 查找包含 "monitorunits" 的 XML 文件
            if (fileName.contains("monitorunits") && fileName.endsWith(".xml")) {
                return xmlFile;
            }
        }
        return null;
    }

    // 注意：原有的 appendTaskMessage 方法已移除，SO 文件处理结果现在直接添加到 ConfigImportReport 中

    /**
     * 向配置导入报告中添加 SO 文件处理消息
     */
    private void addSoProcessingMessage(ConfigImportReport report, String message) {
        if (report == null || message == null) {
            return;
        }

        // 获取现有的消息信息
        String existingMessage = report.getMessage();
        String newMessage;

        if (existingMessage == null || existingMessage.trim().isEmpty()) {
            // 如果没有现有消息，直接使用新消息
            newMessage = message;
        } else {
            // 如果有现有消息，追加新消息
            newMessage = existingMessage + "\n" + message;
        }

        // 限制消息长度，避免过长
        if (newMessage.length() > 3000) {
            // 保留最后3000个字符，并在开头添加省略标记
            newMessage = "...\n" + newMessage.substring(newMessage.length() - 2996);
        }

        report.setMessage(newMessage);
        log.debug("已向配置导入报告添加 SO 处理消息: {}", message);
    }
}

