package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.EventCondition;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.EventConditionMapper;
import com.siteweb.tcs.siteweb.service.IEventConditionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Event Condition Service Implementation
 */
@Slf4j
@Service
public class EventConditionServiceImpl extends ServiceImpl<EventConditionMapper, EventCondition> implements IEventConditionService {

    @Autowired
    private EventConditionMapper eventConditionMapper;

    @Autowired
    private PrimaryKeyValueServiceImpl primaryKeyValueService;

    private static final int GENERATE_EVENT_CONDITION_ID_FLAG = -1;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(List<EventCondition> conditions) {
        if (CollUtil.isEmpty(conditions)) {
            return;
        }
        conditions.stream().filter(e -> ObjectUtil.equals(GENERATE_EVENT_CONDITION_ID_FLAG,e.getEventConditionId())).forEach(r->r.setEventConditionId(findMaxEventConditionByEquipmentTemplateId(r.getEquipmentTemplateId(),r.getEventId())));
        conditions.forEach(condition -> condition.setId(null));
        saveBatch(conditions);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<EventCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }

        // 按模板ID和事件ID分组处理
        conditions.forEach(condition -> {
            // 删除该条件的原始数据（如果存在）
            LambdaQueryWrapper<EventCondition> queryWrapper = new LambdaQueryWrapper<EventCondition>()
                    .eq(EventCondition::getEquipmentTemplateId, condition.getEquipmentTemplateId())
                    .eq(EventCondition::getEventId, condition.getEventId())
                    .eq(EventCondition::getEventConditionId, condition.getEventConditionId());
            remove(queryWrapper);

            // 保存新的条件
            save(condition);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByEvent(Integer equipmentTemplateId, Integer eventId) {
        eventConditionMapper.deleteByEvent(equipmentTemplateId, eventId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEventCondition(Integer equipmentTemplateId, Integer eventId, List<EventCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }

        // 删除原有的条件
        deleteByEvent(equipmentTemplateId, eventId);

        // 设置事件ID并保存新条件
        conditions.forEach(condition -> {
            condition.setEquipmentTemplateId(equipmentTemplateId);
            condition.setEventId(eventId);
            condition.setId(null);
        });

        saveBatch(conditions);
    }

    private Integer findMaxEventConditionByEquipmentTemplateId(Integer equipmentTemplateId, Integer eventId) {
        Integer maxEventConditionId = eventConditionMapper.findMaxEventConditionByEquipmentTemplateId(equipmentTemplateId,eventId);
        if (Objects.nonNull(maxEventConditionId)) {
            return ++maxEventConditionId;
        }
        return primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EVENT_CONDITION, 0);
    }

    @Override
    public EventCondition findMaxEventConditionByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventConditionMapper.findMaxEventByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchsaveLianTongEventConditions() {
        try {
            List<EventCondition> conditions = new ArrayList<>();

            // 创建联通事件条件
            // 这里需要实现批量保存联通事件条件的逻辑
            // 由于没有具体的数据，这里只提供一个空实现
            log.warn("batchsaveLianTongEventConditions method is not fully implemented");
            return true;
        } catch (Exception e) {
            log.error("Failed to batch save LianTong event conditions", e);
            return false;
        }
    }

    @Override
    public void createEventCondition(EventCondition eventCondition) {
        if (Objects.equals(eventCondition.getEventId(), GENERATE_EVENT_CONDITION_ID_FLAG)) {
            Integer eventConditionId = findMaxEventConditionByEquipmentTemplateId(eventCondition.getEquipmentTemplateId(), eventCondition.getEventId());
            eventCondition.setEventConditionId(eventConditionId);
        }
        eventConditionMapper.insert(eventCondition);
    }

    @Override
    public void copyEventCondition(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<EventCondition> eventConditionList = eventConditionMapper.selectList(Wrappers.lambdaQuery(EventCondition.class)
                .eq(EventCondition::getEquipmentTemplateId, originEquipmentTemplateId));
        eventConditionList.forEach(condition -> {
            condition.setEquipmentTemplateId(destEquipmentTemplateId);
            condition.setId(null);
        });
        batchCreate(eventConditionList);
    }

    @Override
    public List<EventCondition> findByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId) {
        return eventConditionMapper.selectList(Wrappers.lambdaQuery(EventCondition.class)
                .eq(EventCondition::getEquipmentTemplateId, equipmentTemplateId)
                .eq(EventCondition::getEventId, eventId));
    }

    @Override
    public void deleteByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId) {
        deleteByEvent(equipmentTemplateId, eventId);
    }

    @Override
    public void batchCreateEventCondition(List<EventCondition> eventConditions) {
        batchCreate(eventConditions);
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        eventConditionMapper.delete(Wrappers.lambdaQuery(EventCondition.class)
                .eq(EventCondition::getEquipmentTemplateId, equipmentTemplateId));
    }
}
