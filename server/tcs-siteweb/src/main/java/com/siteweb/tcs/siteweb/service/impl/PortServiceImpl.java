package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.mapper.PortMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.service.IPortService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.util.I18n;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Port Service Implementation
 */
@Slf4j
@Service
public class PortServiceImpl extends ServiceImpl<PortMapper, Port> implements IPortService {

    private static final String PORT_NAME_TEMPLATE = "COM%s";
    private static final int DEFAULT_PORT_TYPE = 34; // 虚拟端口
    private static final String DEFAULT_SETTING = "comm_host_dev.so";
    private static final String DEFAULT_DESCRIPTION = "TCS create";
    private static final int DEFAULT_LINK_SAMPLER_UNIT_ID = 0;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Autowired
    private PortMapper portMapper;

    @Autowired
    private I18n i18n;

    @Override
    public void verification(Port port) {
        // 端口类型不能为空
        if (ObjectUtil.isNull(port.getPortType())) {
            throw new BusinessException("端口类型不能为空");
        }
        // 端口名称不能为空
        if (ObjectUtil.isNull(port.getPortName())) {
            throw new BusinessException("端口名称不能为空");
        }
        // 端口号不能为空
        if (ObjectUtil.isNull(port.getPortNo())) {
            throw new BusinessException("端口号不能为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Port createPort(Port port) {
        // 端口类型不能为空
        if (ObjectUtil.isNull(port.getPortType())) {
            throw new BusinessException("端口类型不能为空");
        }
        // 端口名称不能为空
        if (ObjectUtil.isNull(port.getPortName())) {
            throw new BusinessException("端口名称不能为空");
        }
        // 端口号不能为空
        if (ObjectUtil.isNull(port.getPortNo())) {
            throw new BusinessException("端口号不能为空");
        }
        if (ObjectUtil.isNull(port.getSetting())) {
            port.setSetting("");
        }
        if (port.getPortId() == null || port.getPortId().equals(0)) {
            Integer portId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_PORT, 0);
            port.setPortId(portId);
        }
        if (portMapper.insert(port) > 0) {
            changeEventService.sendCreate(port);
            operationDetailService.recordOperationLog(String.valueOf(port.getId()), OperationObjectTypeEnum.PORT, i18n.T("port.name"), i18n.T("add"), "", port.getPortName());
        }
        return port;
    }

    /**
     * 获取监控单元下的最大端口号
     *
     * @param monitorUnitId 监控单元ID
     * @return 最大端口号
     */
    @Override
    public Integer getMaxPortByMonitorUnitId(Integer monitorUnitId) {
        if (monitorUnitId == null) {
            return null;
        }
        Port maxPort = this.getOne(new QueryWrapper<Port>()
                .eq("MonitorUnitId", monitorUnitId)
                .orderByDesc("PortNo")
                .last("LIMIT 1"));
        return maxPort != null ? maxPort.getPortNo() : 1;
    }

    @Override
    public List<Port> findByMonitorUnitId(Integer monitorUnitId) {
        if (monitorUnitId == null) {
            return null;
        }
       return list(new QueryWrapper<Port>()
                .eq("MonitorUnitId", monitorUnitId)
                .orderByDesc("PortNo"));
    }

    /**
     * 获取最大PortId
     *
     * @return 最大PortId
     */
    private Integer getMaxPortId() {
        Port maxPort = this.getOne(new QueryWrapper<Port>()
                .orderByDesc("PortId")
                .last("LIMIT 1"));
        return maxPort != null ? maxPort.getPortId() : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByMonitorUnitId(Integer monitorUnitId) {
        if (monitorUnitId == null) {
            log.warn("Cannot delete ports: monitor unit ID is null");
            return false;
        }

        try {
            int count = (int) count(new QueryWrapper<Port>()
                    .eq("MonitorUnitId", monitorUnitId));

            if (count == 0) {
                log.info("No ports found for monitor unit ID: {}", monitorUnitId);
                return true;
            }

            boolean result = remove(new QueryWrapper<Port>()
                    .eq("MonitorUnitId", monitorUnitId));

            if (result) {
                log.info("Deleted {} ports for monitor unit ID: {}", count, monitorUnitId);
            } else {
                log.warn("Failed to delete ports for monitor unit ID: {}", monitorUnitId);
            }

            return result;
        } catch (Exception e) {
            log.error("Error deleting ports for monitor unit ID: {}", monitorUnitId, e);
            throw e;
        }
    }
    @Override
    public Port findByPortId(Integer portId) {
        return portMapper.selectOne(new LambdaQueryWrapper<>(Port.class).eq(Port::getPortId, portId));
    }
    @Override
    public boolean deleteByPortId(Integer portId) {
        Port originPort = findByPortId(portId);
        if (originPort != null) {
            portMapper.delete(Wrappers.lambdaQuery(Port.class).eq(Port::getPortId, portId));
            changeEventService.sendDelete(originPort);
            operationDetailService.recordOperationLog(originPort.getId().toString(), OperationObjectTypeEnum.PORT, i18n.T("port.name"), i18n.T("delete"), originPort.getPortName(), "");
        }
        return originPort != null;
    }

    @Override
    public boolean updatePort(Port port) {
        Port originPort = findByPortId(port.getPortId());
        int rows = portMapper.updateById(port);
        if (rows > 0) {
            operationDetailService.compareEntitiesRecordLog(originPort, port);
            changeEventService.sendUpdate(port);
        }
        return rows > 0;
    }
}
