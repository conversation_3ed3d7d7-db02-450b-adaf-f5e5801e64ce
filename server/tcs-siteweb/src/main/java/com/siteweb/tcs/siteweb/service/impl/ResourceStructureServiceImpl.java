package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.ResourceStructureMapper;
import com.siteweb.tcs.siteweb.dto.ResourceStructureDTO;
import com.siteweb.tcs.siteweb.dto.StationDTO;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.entity.Station;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import com.siteweb.tcs.siteweb.entity.StationStructureMap;
import com.siteweb.tcs.siteweb.entity.SysConfig;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.enums.StructureTypeEnum;
import com.siteweb.tcs.siteweb.enums.SysConfigEnum;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.I18n;
import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * Resource Structure Service Implementation
 */
@Slf4j
@Service
public class ResourceStructureServiceImpl extends ServiceImpl<ResourceStructureMapper, ResourceStructure> implements IResourceStructureService {

    @Autowired
    private ResourceStructureMapper resourceStructureMapper;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Autowired
    private I18n i18n;

    @Autowired
    private IDataItemService dataItemService;

    @Autowired
    private IStationService stationService;

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    @Autowired
    private IStationStructureService stationStructureService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IHouseService houseService;

    @Value("${resource.structure.generate.uuid:false}")
    private boolean generateUUID;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ResourceStructure getStructureByID(Integer structureId) {
        if (structureId == null) {
            return null;
        }
        return getById(structureId);
    }

    @Override
    public ResourceStructure getRootStructure() {
        // Assuming root is identified by parentResourceStructureId being null or a specific value (e.g., 0)
        // Adjust this logic if root identification is different (e.g., a specific ID like 1)
        return getOne(new LambdaQueryWrapper<ResourceStructure>()
                .isNull(ResourceStructure::getParentResourceStructureId) // Or .eq(ResourceStructure::getParentResourceStructureId, 0) if that's the convention
                .last("LIMIT 1")); // To ensure only one root is returned if multiple qualify
    }

    @Override
    public List<ResourceStructure> getAllStructures() {
        return list(); // Simply returns all records from the table
    }

    @Override
    public List<ResourceStructure> getChildrenByParentId(Integer parentId) {
        if (parentId == null) {
            return List.of(); // Or handle as an error/empty list depending on requirements
        }
        return list(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getParentResourceStructureId, parentId)
                .orderByAsc(ResourceStructure::getSortValue)); // Assuming children should be sorted
    }

    @Override
    public Integer getTreeRootId() {
        QueryWrapper<ResourceStructure> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parentresourcestructureid", 0);
        queryWrapper.select("resourcestructureid");
        try {
            ResourceStructure resourceStructure = baseMapper.selectOne(queryWrapper);
            return resourceStructure.getResourceStructureId();
        } catch (Exception e) {
            log.error("Query resource structure tree root error: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public ResourceStructure findByOriginIdAndStructureType(Integer originId, Integer structureType) {
        if (originId == null || structureType == null) {
            return null;
        }

        return getOne(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getStructureTypeId, structureType)
                .last("LIMIT 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByID(Integer resourceStructureId) {
        if (resourceStructureId == null) {
            log.warn("Cannot delete resource structure: ID is null");
            return false;
        }

        try {
            // First check if the resource structure exists
            ResourceStructure resourceStructure = getById(resourceStructureId);
            if (resourceStructure == null) {
                log.warn("Resource structure not found with ID: {}", resourceStructureId);
                return false;
            }

            // Delete the resource structure
            boolean result = removeById(resourceStructureId);
            if (result) {
                log.info("Resource structure deleted successfully: {}", resourceStructureId);
            } else {
                log.warn("Failed to delete resource structure: {}", resourceStructureId);
            }

            return result;
        } catch (Exception e) {
            log.error("Error deleting resource structure: {}", e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ResourceStructure resourceStructure) {
        if (resourceStructure == null || resourceStructure.getResourceStructureId() == null) {
            log.warn("Cannot update resource structure: entity or ID is null");
            return false;
        }

        try {
            // First check if the resource structure exists
            ResourceStructure existingStructure = getById(resourceStructure.getResourceStructureId());
            if (existingStructure == null) {
                log.warn("Resource structure not found with ID: {}", resourceStructure.getResourceStructureId());
                return false;
            }

            // Update the resource structure
            boolean result = updateById(resourceStructure);
            if (result) {
                log.info("Resource structure updated successfully: {}", resourceStructure.getResourceStructureId());
            } else {
                log.warn("Failed to update resource structure: {}", resourceStructure.getResourceStructureId());
            }

            return result;
        } catch (Exception e) {
            log.error("Error updating resource structure: {}", e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        }
    }

    @Override
    public List<ResourceStructure> findByParentResourceStructureId(Integer parentResourceStructureId) {
        if (parentResourceStructureId == null) {
            log.warn("Cannot find resource structures: parent resource structure ID is null");
            return List.of();
        }

        return list(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getParentResourceStructureId, parentResourceStructureId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(ResourceStructure resourceStructure) {
        try {
            // 如果 resourceStructureId 为 null 或 0，则生成一个新的 ID
            if (resourceStructure.getResourceStructureId() == null || resourceStructure.getResourceStructureId() == 0) {
                Integer resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
                resourceStructure.setResourceStructureId(resourceStructureId);
            }

            // 主动清空 sceneId
            resourceStructure.setSceneId(null);

            // 设置层级路径
            String levelOfPath = buildLevelOfPath(resourceStructure.getParentResourceStructureId(), resourceStructure.getResourceStructureId());
            resourceStructure.setLevelOfPath(levelOfPath);

            // 如果 generateUUID 为 true，则设置 extendedField 字段
            // 设置guid
            if (generateUUID) {
                JsonNode extValue = resourceStructure.getExtendedField();
                ObjectMapper mapper = new ObjectMapper();

                ObjectNode newValue = mapper.createObjectNode();
                newValue.put("guid", UUID.randomUUID().toString());

                ArrayNode arrayNode;
                if (extValue != null && extValue.isArray()) {
                    arrayNode = (ArrayNode) extValue;
                } else {
                    arrayNode = mapper.createArrayNode();
                }

                arrayNode.add(newValue);

                resourceStructure.setExtendedField(arrayNode);
            }

            // 插入资源结构
            int result = resourceStructureMapper.insert(resourceStructure);

            if (result > 0) {
                // 发送创建事件
                changeEventService.sendCreate(resourceStructure);

                // 记录操作日志todo xsx 权限后面要测试
                operationDetailService.recordOperationLog(resourceStructure.getResourceStructureId().toString(), OperationObjectTypeEnum.RESOURCE_STRUCTURE, i18n.T("resource.structure.name"), i18n.T("add"), "", resourceStructure.getResourceStructureName());

                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("Failed to create resource structure: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResourceStructure findResourceStructureByOriginIdAndParentIdAndStructureTypeId(Integer originId, Integer parentId, Integer structureTypeId) {
        return resourceStructureMapper.selectOne(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getOriginParentId, parentId)
                .eq(ResourceStructure::getStructureTypeId, structureTypeId));
    }

    /**
     * 构建层级路径
     * 
     * @param parentResourceStructureId 父资源结构ID
     * @param resourceStructureId 当前资源结构ID
     * @return 层级路径
     */
    private String buildLevelOfPath(Integer parentResourceStructureId, Integer resourceStructureId) {
        if (parentResourceStructureId == null || parentResourceStructureId == 0) {
            // 根节点
            return resourceStructureId.toString();
        }
        
        // 获取父节点的层级路径
        ResourceStructure parentStructure = getById(parentResourceStructureId);
        if (parentStructure == null) {
            // 父节点不存在，返回当前节点ID
            return resourceStructureId.toString();
        }
        
        String parentLevelOfPath = parentStructure.getLevelOfPath();
        if (parentLevelOfPath == null || parentLevelOfPath.trim().isEmpty()) {
            // 父节点没有层级路径，使用父节点ID
            return parentResourceStructureId + "." + resourceStructureId;
        }
        
        // 追加到父节点的层级路径
        return parentLevelOfPath + "." + resourceStructureId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Station createDefaultStation(ResourceStructureDTO resourceStructureDTO) {
        // 查找中心数据项
        List<DataItem> centerDataItem = dataItemService.findByEntryId(DataEntryEnum.DATA_ENTRY);
        if (centerDataItem.isEmpty()) {
            throw new RuntimeException("中心数据项不存在");
        }
        
        if (resourceStructureDTO.getStation() == null) {
            throw new RuntimeException("局站信息不存在");
        }
        
        StationDTO stationDTO = resourceStructureDTO.getStation();
        Station station = new Station();
        BeanUtil.copyProperties(stationDTO, station);
        station.setCenterId(centerDataItem.get(0).getItemId());
        station.setUpdateTime(java.time.LocalDateTime.now());
        
        // 查询系统配置
        SysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);
        if (sysConfig != null && "2".equals(sysConfig.getConfigValue())) {
            station.setStationCategory(101);
        } else if (station.getStationCategory() == null) {
            station.setStationCategory(1);
        }

        if (station.getStationGrade() == null) {
            station.setStationGrade(1);
        }
        if (station.getStationName() == null) {
            station.setStationName(resourceStructureDTO.getResourceStructureName());
        }
        
        // 设置默认值
        station.setStationState(1);
        station.setEnable(true);
        station.setConnectState(2);
        station.setContainNode(false);
        
        stationService.create(station);
        
        // 增加station structure map记录
        StationStructureMap stationStructureMap = new StationStructureMap();
        stationStructureMap.setStationId(station.getStationId());
        
        // StructureId为StructureGroupId = 1 的StationStructure的记录
        if (resourceStructureDTO.getStationStructureId() == null) {
            List<StationStructure> stationStructures = stationStructureService.getStructureByStructureGroupId(1);
            if (!stationStructures.isEmpty()) {
                stationStructureMap.setStructureId(stationStructures.get(0).getStructureId());
            }
        } else {
            stationStructureMap.setStructureId(resourceStructureDTO.getStationStructureId());
        }

        stationStructureMapService.create(stationStructureMap);
        
        // 记录操作日志 - 假设当前用户ID为-1，实际应该从上下文获取
        operationDetailService.recordOperationLog(station.getStationId().toString(),
            OperationObjectTypeEnum.STATION, i18n.T("station.name"), i18n.T("add"), "", station.getStationName());
        
        return station;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createStationTemplate(ResourceStructure resourceStructure) {
        // 非电信场景不处理
        if (resourceStructure.getSceneId() == null || resourceStructure.getSceneId() == 1) {
            return false;
        }
        
        if (resourceStructure.getOriginId() == null) {
            return false;
        }
        
        Station station = stationService.findByStationId(resourceStructure.getOriginId());
        if (station == null) {
            return false;
        }
        
        List<House> houses = houseService.findHouseByStationId(station.getStationId());
        if (houses.isEmpty()) {
            return false;
        }
        
        for (House house : houses) {
            ResourceStructure houseStructure = getResourceStructure(resourceStructure, house, station);
            this.create(houseStructure);
        }
        
        return true;
    }

    private static ResourceStructure getResourceStructure(ResourceStructure resourceStructure, House house, Station station) {
        ResourceStructure houseStructure = new ResourceStructure();
        houseStructure.setResourceStructureName(house.getHouseName());
        houseStructure.setParentResourceStructureId(resourceStructure.getResourceStructureId());
        houseStructure.setSortValue(1);
        houseStructure.setOriginId(house.getHouseId());
        houseStructure.setOriginParentId(station.getStationId());
        houseStructure.setSceneId(2);
        houseStructure.setDisplay(true);
        houseStructure.setLevelOfPath(resourceStructure.getLevelOfPath());
        houseStructure.setStructureTypeId(StructureTypeEnum.STATION_HOUSE.getValue());
        return houseStructure;
    }

}
