package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.StationStructureMap;
import com.siteweb.tcs.siteweb.mapper.StationStructureMapMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IStationStructureMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Station Structure Map Service Implementation
 */
@Slf4j
@Service
public class StationStructureMapServiceImpl extends ServiceImpl<StationStructureMapMapper, StationStructureMap> implements IStationStructureMapService {

    @Autowired
    private StationStructureMapMapper stationStructureMapMapper;
    @Autowired
    private IChangeEventService changeEventService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByStationId(Integer stationId) {
        if (stationId == null) {
            log.warn("Cannot delete station structure map: station ID is null");
            return false;
        }

        try {
            int count = (int) count(new LambdaQueryWrapper<StationStructureMap>()
                    .eq(StationStructureMap::getStationId, stationId));

            if (count == 0) {
                log.info("No station structure map found for station ID: {}", stationId);
                return true;
            }

            boolean result = remove(new LambdaQueryWrapper<StationStructureMap>()
                    .eq(StationStructureMap::getStationId, stationId));

            if (result) {
                log.info("Deleted {} station structure maps for station ID: {}", count, stationId);
            } else {
                log.warn("Failed to delete station structure maps for station ID: {}", stationId);
            }

            return result;
        } catch (Exception e) {
            log.error("Error deleting station structure maps for station ID: {}", stationId, e);
            throw e;
        }
    }

    @Override
    public StationStructureMap findStationStructureMapByStationId(Integer stationId) {
        if (stationId == null) {
            log.warn("Cannot find station structure map: station ID is null");
            return null;
        }

        return getOne(new LambdaQueryWrapper<StationStructureMap>()
                .eq(StationStructureMap::getStationId, stationId)
                .last("LIMIT 1"));
    }

    @Override
    public List<StationStructureMap> findStationStructureMapByStructureId(Integer structureId) {
        if (structureId == null) {
            log.warn("Cannot find station structure maps: structure ID is null");
            return List.of();
        }

        return list(new LambdaQueryWrapper<StationStructureMap>()
                .eq(StationStructureMap::getStructureId, structureId));
    }

    @Override
    public boolean create(StationStructureMap stationStructureMap) {
        int result = stationStructureMapMapper.insert(stationStructureMap);
        if (result > 0) {
            changeEventService.sendCreate(stationStructureMap);
        }
        return result > 0;
    }
}

