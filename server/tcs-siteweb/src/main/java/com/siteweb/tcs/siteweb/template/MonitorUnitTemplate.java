package com.siteweb.tcs.siteweb.template;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.siteweb.template.dto.monitor.unit.MonitorUnitTemplateDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * 监控单元模板
 */
@Component
public class MonitorUnitTemplate {

    private List<MonitorUnitTemplateDTO> templates;

    @Autowired
    private ObjectMapper objectMapper;

    private List<MonitorUnitTemplateDTO> getTemplates() {
        if (templates == null) {
            ClassPathResource resource = new ClassPathResource("templates/monitor-unit/template.json");
            try {
                templates = objectMapper.readValue(resource.getInputStream(), new TypeReference<List<MonitorUnitTemplateDTO>>() {
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return templates;
    }

    public MonitorUnitTemplateDTO getTemplate(int category) {
        for (MonitorUnitTemplateDTO temp : getTemplates()) {
            if (temp.getCategory() == category) {
                return temp;
            }
        }
        return null;
    }
} 