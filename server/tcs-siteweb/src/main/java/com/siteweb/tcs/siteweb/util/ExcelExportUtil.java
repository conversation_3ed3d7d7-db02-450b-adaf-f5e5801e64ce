package com.siteweb.tcs.siteweb.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.URLEncodeUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.siteweb.tcs.siteweb.dto.excel.SheetDataWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;

/**
 * @Description:
 */
@Slf4j
@UtilityClass
public class ExcelExportUtil {
    public <T> void exportExcel(HttpServletResponse response, Collection<T> result, Class<T> clazz, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        fileName = URLEncodeUtil.encode(fileName);
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcelFactory.write(response.getOutputStream(), clazz).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("sheet").doWrite(result);
    }


    public void exportExcelMultiSheet(HttpServletResponse response, Map<String, SheetDataWrapper> sheetDataMap, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncodeUtil.encode(fileName);
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
        try (ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build()) {
            int sheetNo = 0;
            for (Map.Entry<String, SheetDataWrapper> entry : sheetDataMap.entrySet()) {
                String sheetName = entry.getKey();
                SheetDataWrapper wrapper = entry.getValue();
                Class<?> headerClass = wrapper.getHeadClass(); // 直接从 Wrapper 获取 Class
                Collection<?> data = wrapper.getData(); // 获取数据，可能是空集合
                // 现在我们可以安全地使用 headerClass 来定义表头
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(sheetNo++, sheetName)
                        .head(headerClass) // 使用预先指定的 Class 设置表头
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .build();
                // 写入数据，EasyExcel 会处理空集合的情况（只写入表头）
                excelWriter.write(data, writeSheet);
            }
        }
    }

    /**
     * 导出受保护的excel
     *
     * @param password 密码
     */
    public void exportProtectionExcelMultiSheet(HttpServletResponse response, Map<String, Collection<?>> sheetDataMap, String fileName, String password) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        fileName = URLEncodeUtil.encode(fileName);
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        try (ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build()) {
            int sheetNo = 0;
            for (Map.Entry<String, Collection<?>> entry : sheetDataMap.entrySet()) {
                String sheetName = entry.getKey();
                Collection<?> data = entry.getValue();
                if (CollUtil.isNotEmpty(data)) {
                    Class<?> clazz = data.iterator().next().getClass();
                    WriteSheet writeSheet = EasyExcelFactory.writerSheet(sheetNo, sheetName)
                            .head(clazz)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();
                    excelWriter.write(data, writeSheet);
                    // 加密工作簿
                    Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
                    Sheet sheetAt = workbook.getSheetAt(sheetNo);
                    sheetAt.protectSheet(password);
                    sheetNo++;
                }
            }
        }
    }

}
