package com.siteweb.tcs.siteweb.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 监控单元密码状态VO
 * Description: 用于返回监控单元是否设置了密码的状态信息
 * Author: <EMAIL>
 * Creation Date: 2024/6/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorUnitPasswordVO {

    private Integer monitorUnitId;

    /**
     * 是否设置了密码
     */
    private boolean hasPassword;
}
