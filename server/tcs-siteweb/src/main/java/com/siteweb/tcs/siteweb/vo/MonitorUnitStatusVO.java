package com.siteweb.tcs.siteweb.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 监控单元状态VO
 * 用于返回监控单元的基本信息和状态
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorUnitStatusVO {
    
    /**
     * 监控单元ID
     */
    private Integer id;
    
    /**
     * 监控单元名称
     */
    private String name;
    
    /**
     * 监控单元类型
     */
    private Integer type;
    
    /**
     * 监控单元类型名称
     */
    private String typeName;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 同步状态
     * true: 已同步
     * false: 未同步
     */
    private Boolean syncStatus;
    
    /**
     * 连接状态
     * 1: 离线
     * 2: 在线
     */
    private Integer connectStatus;

    
    /**
     * 获取监控单元类型名称
     */
    public String getTypeName() {
        if (type == null) {
            return "未知";
        }
        // 根据监控单元类型枚举返回名称
        switch (type) {
            case 1:
                return "FSU";
            case 2:
                return "RMU";
            case 3:
                return "MU";
            case 4:
                return "跨站MU";
            case 5:
                return "RMU下MU";
            case 6:
                return "跨站RMU下MU";
            case 7:
                return "GFSU3";
            default:
                return "未知类型";
        }
    }
}
