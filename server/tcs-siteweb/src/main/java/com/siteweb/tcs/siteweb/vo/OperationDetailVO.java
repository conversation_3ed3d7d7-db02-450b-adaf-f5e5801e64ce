package com.siteweb.tcs.siteweb.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperationDetailVO {
    /**
     * 操作人
     */
    private String userName;
    
    /**
     * 对象主键id
     */
    private String objectId;
    
    /**
     * 对象类型
     */
    private Integer objectType;
    
    /**
     * 属性名称
     */
    private String propertyName;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 旧值
     */
    private String oldValue;
    
    /**
     * 新值
     */
    private String newValue;
    
    /**
     * 对象名称
     */
    private String objectName;
} 