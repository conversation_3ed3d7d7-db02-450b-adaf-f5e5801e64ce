package com.siteweb.tcs.siteweb.vo;

import lombok.Data;

/**
 * 采样单元带端口信息VO
 * 用于设备管理模块的采样单元查询
 */
@Data
public class SamplerUnitWithPortVO {
    
    /**
     * 采样单元ID
     */
    private Integer samplerUnitId;
    
    /**
     * 采样单元名称
     */
    private String samplerUnitName;
    
    /**
     * 端口ID
     */
    private Integer portId;
    
    /**
     * 端口名称
     */
    private String portName;
    
    /**
     * 端口号
     */
    private Integer portNo;
    
    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;
    
    /**
     * 采集器ID
     */
    private Integer samplerId;
    
    /**
     * 采集器名称
     */
    private String samplerName;
    
    /**
     * 采样单元地址
     */
    private Integer address;
    
    /**
     * 采样间隔
     */
    private Double spUnitInterval;
    
    /**
     * DLL路径
     */
    private String dllPath;
    
    /**
     * 连接状态
     */
    private Integer connectState;
    
    /**
     * 描述
     */
    private String description;
}
