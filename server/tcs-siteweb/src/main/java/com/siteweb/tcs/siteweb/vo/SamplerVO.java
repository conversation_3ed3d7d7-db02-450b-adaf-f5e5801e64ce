package com.siteweb.tcs.siteweb.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SamplerVO {
    private Integer samplerId;
    private String samplerName;
    private Short samplerType;
    private String protocolCode;
    private String dllCode;
    private String dllVersion;
    private String protocolFilePath;
    private String dllFilePath;
    private String dllPath;
    private String setting;
    private String description;
    private String soCode;
    private String soPath;
    private String equipmentTemplateName;
    /**
     * 是否上传协议文件
     */
    private Boolean uploadProtocolFile;
}
