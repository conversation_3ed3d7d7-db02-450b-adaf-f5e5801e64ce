greeting.message=你好, {0}!
uploadFile.notEmpty=上传的文件不能为空
uploadFile.Illegal=非法的文件
uploadFile.exist=文件已经存在
uploadFile.notExist=文件不存在
uploadFile.fileName.noEmpty=文件名不能为空
uploadFile.exist.changeName=文件{0}已经存在,请更换文件名称
uploadFile.protocolType.notExist=协议类型不存在
center.name.empty=请输入监控中心名称
ungrouped.site=未分组局站
managed.site=-管理局站
equipment.MUHOST.equipment=RMU-MUHOST设备
add=新增
update=更新
port=端口
monitor.equipmentTemplate.name=设备模板名称
monitor.signal.id=信号id
monitor.signal.name=信号名称
monitor.sampler.name=采集器名称
monitor.communicationStateSignal.doNotAllowDelete=设备通讯状态信号不允许删除
working.state=工作状态
monitor.equipment.communicationState=设备通讯状态
monitor.equipment.communicationFail=通讯异常
monitor.equipment.communicationOk=通讯正常
event.eventName=事件名称
event.eventCategory=事件类别
event.startType=开始类型
event.endType=结束类型
event.startExpression=开始表达式
event.suppressExpression=抑制表达式
event.enable=有效
event.visible=可见
event.displayIndex=显示顺序
event.associatedSignal=关联信号
event.affiliatedRectifier=所属模块
event.turnover=翻转时间
event.id=事件id
event.signalId=事件关联信号id
worksite.name=工作站名称
modify=修改
work.site.ip=工作站IP
work.site.work.state=工作站启用状态
monitor.equipmentTemplate.existsChild=存在子模板
monitor.equipmentTemplate.existsEquipmentTemplateReference={0}存在设备模板引用
monitor.equipmentTemplate.existsEquipmentReference={0}存在设备引用
monitor.equipmentTemplate.existsSamplerUnitReference={0}存在采集单元引用
monitor.protocol.noExists=协议不存在
delete=删除
monitor.equipment.monitoringEquipment=监控设备
space.alarm.state=空间告警状态
insufficient.space.alarm=空间不足告警
workstation.selfdiagnostic.monitor=%s%s工作站自诊断虚拟监控单元
monitor.unit=监控单元
monitor.unit.name=监控单元名称
port.name=端口名称
site.self.Diagnosis.Equip=工作站自诊断设备
sampler.unit.name=采集单元名称
diag.dacilities.room=自诊断设备室
equipment.name=设备名称
house.default.name=默认局房
file.not.exist=文件不存在
invalid.structure.id=无效的层级ID：{0}
parameter.cannot.be.null=参数 {0} 不能为空
center.name=中心名称
center.name.special=监控中心名称不允许包含特殊字符
center.name.length=监控中心名称长度不允许超过128位
ip.format.error=IP地址格式错误
center.id=请输入中心ID
center.id.length=中心ID必须为2位或3位数字
center.exist=中心ID已存在
control.add=新增控制
control.name=控制名称
existing.control.name=原模板已经具有控制[{0}]，请重新选择
not.existing.control.name=原模板不具有控制[{0}]，请重新选择
control.parameter.meaning=控制参数含义
control.meaning=含义
channel.number.exist=通道号在该设备中已经存在，请输入新的通道号!
select.sampler.unit.reference=请选择引用的采集单元
select.channel.number.reference=请选择引用的通道号
monitor.signal.expression.not.empty=请输入信号表达式
error.duplicate.name={0}名称"{1}"重复。
error.duplicate.ipAddress={0}Ip地址"{1}"重复。
error.duplicate.portNo={0}端口号"{1}"重复。
monitor.signal.expression.across.site=只有通道为-2的信号可以设置跨站信号
monitor.signal.expression.already.configured=已配置了表达式值，确实需要配置请先清除！
monitor.signal=信号
monitor.event=事件
monitor.control=控制
monitor.equipment=设备
monitor.equipmentBaseType=设备基类
monitor.templateSignal.delete=替换模板后，设备无此信号，信号ID:{0}
monitor.templateEvent.delete=替换模板后，设备无此事件，事件ID:{0}
monitor.templateControl.delete=替换模板后，设备无此控制，控制ID:{0}
monitor.templateSignal.add=替换模板后，设备新增信号，信号ID:{0}
monitor.templateSignal.settingInstance=替换模板后，设备新增信号，必须配置实例才能使用，信号ID:{0}
monitor.templateSignal.checkInstance=替换模板后，设备新增信号，请检查配置实例是否正确，信号ID:{0}
monitor.templateEvent.add=替换模板后，设备新增事件，事件ID:{0}
monitor.templateControl.add=替换模板后，设备新增控制，控制ID:{0}
monitor.templateSignal.originalSetting=替换模板后，将会使用原有实例配置，信号ID:{0} 原始配置
monitor.templateEquipment.resetting=替换模板后，告警过滤表达式需重新配置
monitor.templateSignal.signalReference=所选设备模板中被删除的信号被别的设备所引用，不允许关联到该模板
monitor.equipmentTemplate.name.exist=设备模板名称已存在
monitor.xml.station.info=StationId:局站ID，StationName: 局站名称
monitor.xml.app.name=APP配置
ftp.username.and.password=ftp用户名和密码
site.web.communication=与SiteWeb通讯
equipment.communication=与智能设备通讯
save.local.file=本地文件存储
controls=控制
monitor.equipmentTemplateList=设备模板列表
monitor.xml.channel.maps=采集单元通道号
equipment.list=设备列表
equipment.event=设备事件
equipment.signal=设备信号
equipment.control=设备控制
signal.id=信号id
signal.signalName=信号名称
signal.signalCategory=信号种类
signal.signalType=信号分类
signal.channelNo=通道号
signal.channelType=通道类型
signal.expression=表达式
signal.dataType=数据类型
signal.showPrecision=精度
signal.unit=单位
signal.storeInterval=存储周期
signal.absValueThreshold=绝对值阈值
signal.percentThreshold=百分比阀值
signal.staticsPeriod=统计周期
signal.baseTypeId=基类信号ID
signal.enable=有效
signal.visible=可见
signal.chargeStoreInterVal=电池存储周期
signal.chargeAbsValue=电池存盘阀值
signal.displayIndex=显示顺序
alarm.linkage=告警联动
business.expression.configuration=业务表达式配置
equipmentTemplate.equipmentTemplateName=设备模板名称
equipmentTemplate.memo=备注
equipmentTemplate.equipmentCategory=设备种类
equipmentTemplate.equipmentType=设备类型
equipmentTemplate.property=属性
equipmentTemplate.equipmentStyle=设备型号
equipmentTemplate.unit=单位
equipmentTemplate.vendor=厂商
equipmentTemplate.stationCategory=局站类型
equipmentTemplate.equipmentBaseType=设备基类
monitor.equipmentInstance=设备实例化
monitor.equipment.doesNotExist=设备信息不存在
wildcard.format=名称通配符必须包含{0}
signalBase.baseNameExt.format=不空的扩展信号名必须包含{0}
signalBase.baseTypeId.overflow=基类信号ID不能为空,可扩展信号已经用完
signalBase.isSystem.delete=系统内置信号基类，不允许删除
signalBase.isSystem.update=系统内置信号基类，不允许修改
signalBase.baseTypeId.delete=基类信号已经被信号引用,不可删除
signalBase.baseTypeId.update=基类信号已经被信号引用,不可修改
eventBase.baseNameExt.format=不空的扩展事件名必须包含{0}
eventBase.baseTypeId.overflow=基类事件ID不能为空,可扩展事件已经用完
eventBase.isSystem.delete=系统内置事件基类，不允许删除
eventBase.isSystem.update=系统内置事件基类，不允许修改
eventBase.baseTypeId.delete=基类事件条件已经被事件引用,不可删除
eventBase.baseTypeId.update=基类事件条件已经被事件引用,不可修改
commandBase.baseNameExt.format=不空的扩展控制名必须包含{0}
commandBase.baseTypeId.overflow=基类控制ID不能为空,可扩展控制已经用完
commandBase.isSystem.delete=系统内置控制基类，不允许删除
commandBase.isSystem.update=系统内置控制基类，不允许修改
commandBase.baseTypeId.delete=基类控制已经被控制引用,不可删除
commandBase.baseTypeId.update=基类控制已经被控制引用,不可修改
abortNumber.format=截止序号必须是1到999之间的整数
baseNameExt.batchAdd=名称扩展表达式为空的不能批量新增
baseNameExt.batchUpdate=名称扩展表达式为空的不能批量修改
error.conflic.portno=端口 {0} 冲突:
error.conflic.port={0}_{1}_{2}
error.conflic.portsetting=端口设置 {0} 冲突:
error.conflic.port.setting={0}_{1}_{2}
error.conflic.port.setting.notfound=没有发现RMU下端口或端口设置冲突
control.controlName=控制名称
control.controlCategory=控制种类
control.cmdToken=命令标识
control.baseTypeId=基类控制ID
control.controlSeverity=控制重要性
control.signalId=关联信号
control.timeOut=超时时间
control.retry=重试次数
control.enable=有效
control.visible=可见
control.displayIndex=显示顺序
control.commandType=命令类型
control.controlType=控制类型
control.dataType=数据类型
control.maxValue=最大值
control.minValue=最小值
control.defaultValue=默认值
control.moduleNo=模数
manage.room.name=管理房间
error.duplicate.structure.name=同一层级下存在同名的层级
error.structure.has.children=层级下存在子层级，不允许删除
error.structure.has.equipment=层级下存在设备，不允许删除
error.monitor.unit.has.equipment=监控单元下存在设备，不允许删除
monitor.signal.not.exist=信号不存在
monitor.signal.expression.empty=信号表达式为空
master=主
slave=从
shutdown=停机
running=运行
error.structure.not.create.station=层级上未创建局站
batchTool.driverTemplateType.NotExists=驱动模板类型不存在
batchTool.driverTemplate.NotExists=驱动模板不存在
batchTool.driverTemplate.DefaultTemplateProhibitDeletion=默认模板禁止删除
batchTool.driverTemplate.DefaultTemplateFileNotExists=驱动模板{0}文件不存在

# 批量工具
virtualEquipment.notExist.virtualSignal=虚拟设备信号不存在
virtualEquipment.notExist.originChannel=源设备通道号不存在
virtualEquipment.notExist.virtualEquipmentStation=虚拟设备局站不存在
virtualEquipment.notExist.virtualMonitUnit=虚拟监控单元不存在
virtualEquipment.notExist.sampleUnit=虚拟采集单元不存在
virtualEquipment.notExist.virtualEquipmentHouse=虚拟设备局房不存在
virtualEquipment.notExist.virtualTemplate=虚拟模板不存在
virtualEquipment.notExist.virtualEquipmentCategory=虚拟设备类型不存在
virtualEquipment.notExist.originEquipment=源设备不存在
virtualEquipment.cannotAcross.monitUnit=虚拟设备与源设备不能跨监控单元
virtualEquipment.quantityTooLarge=导入的虚拟设备过多，请分批导入，最大支持{0}个虚拟设备导入
common.msg.equipmentExists=系统中已存在同名设备，请更换名称重试
virtualEquipment.virtualTemplate=虚拟模板
virtualEquipment.crossSiteVirtualTemplate=跨站虚拟模板

# 设备操作日志
equipment.stationId=局站
equipment.equipmentName=设备名称
equipment.equipmentNo=设备编码
equipment.equipmentModule=设备模块
equipment.equipmentStyle=设备型号
equipment.assetState=资产状态
equipment.price=资产价格
equipment.usedLimit=资产寿命
equipment.usedDate=启用时间
equipment.buyDate=购买时间
equipment.vendor=设备厂商
equipment.unit=单位
equipment.equipmentCategory=设备类型
equipment.equipmentType=设备分类
equipment.equipmentClass=设备大类
equipment.equipmentState=设备状态
equipment.eventExpression=告警过滤表达式
equipment.startDelay=开始延时
equipment.endDelay=结束延时
equipment.property=设备属性
equipment.equipmentTemplateId=设备模板Id
equipment.houseId=局房Id
equipment.monitorUnitId=监控单元Id
equipment.workStationId=监控主机Id
equipment.parentEquipmentId=上一级设备
equipment.ratedCapacity=额定容量
equipment.installedModule=已安装模块
equipment.projectName=工程名
equipment.contractNo=合同号
equipment.resourceStructureId=层级ID
station.name=局站名称
resource.structure.name=层级名称
resourcestructure.resourceStructureName=层级名称
resourcestructure.structureTypeId=层级类型
monitorunit.monitorUnitName=监控单元名称
monitorunit.monitorUnitCategory=监控单元类型
monitorunit.ipAddress=IP地址
monitorunit.workStationId=工作站ID
monitorunit.rdsServer=RDS服务器
monitorunit.dataServer=数据服务器
monitorunit.projectName=项目名称
monitorunit.contractNo=合同号
port.portName=端口名称
port.portNo=端口号
port.portType=端口类型
port.setting=端口设置
port.phoneNumber=电话号码
samplerunit.samplerUnitName=采集单元名称
samplerunit.address=地址
samplerunit.dllPath=采集单元动态库
samplerunit.spUnitInterval=采集周期
samplerunit.phoneNumber=电话号码
monitor.unit.category.empty=监控单元类型不能为空
error.username.or.password=用户名或密码错误
monitor.equipment.repeatName=设备名称重复
baseStation.house.name=基站
important.signal.house.name=重要信号
house.houseName=局房名称

# 标准化字典
standardDic.remoteControl=遥控
standardDic.remoteSignal=遥信
standardDic.remoteMeasure=遥测
standardDic.eventText=告警
standardDic.mapping.existsError=此标准化字典已经被映射不允许删除
standardDic.signal=信号标准化字典
standardDic.signalId.length=信号id必须为6位数
StandardDicSig.equipmentLogicClass=设备类型
StandardDicSig.signalLogicClass=信号量类型
StandardDicSig.signalStandardName=信号标准名
StandardDicSig.storeInterval=存储周期
StandardDicSig.absValueThreshold=绝对值阈值
StandardDicSig.statisticsPeriod=统计周期
StandardDicSig.percentThreshold=百分比阈值
StandardDicSig.stationCategory=站点类别
StandardDicSig.description=描述
StandardDicSig.extendFiled1=单位
StandardDicSig.extendFiled2=设备子类

standardDic.eventId.length=告警id必须为6位数
standardDic.event=告警标准化字典
StandardDicEvent.equipmentLogicClass=设备类型
StandardDicEvent.eventLogicClass=告警逻辑分类类
StandardDicEvent.eventStandardName=事件标准名称
StandardDicEvent.signalStandardName=告警标准名
StandardDicEvent.eventClass=告警逻辑子类
StandardDicEvent.eventSeverity=告警等级
StandardDicEvent.compareValue=告警门限
StandardDicEvent.startDelay=告警延迟
StandardDicEvent.meanings=告警解释
StandardDicEvent.equipmentAffect=对设备影响
StandardDicEvent.businessAffect=对业务影响
StandardDicEvent.description=提出依据
StandardDicEvent.extendFiled1=节点告警级别
StandardDicEvent.extendFiled2=基站告警级别
StandardDicEvent.extendFiled3=数据中心告警级别
StandardDicEvent.stationCategory=站点类别

standardDic.controlId.length=控制id必须为6位数
standardDic.control=控制标准化字典
StandardDicControl.equipmentLogicClass=设备类型
StandardDicControl.controlLogicClass=控制量类型
StandardDicControl.controlStandardName=控制标准名
StandardDicControl.stationCategory=站点类别
StandardDicControl.description=备注
StandardDicControl.extendFiled1=单位
StandardDicControl.extendFiled2=设备子类

#移动客户类型
cmccCustomerType.china=中国移动
cmccCustomerType.guangDong=广东移动
cmccCustomerType.jiangsuDong=江苏移动

standardized.dictionary.script.sql=标准化字典脚本.sql
standardized.dictionary.xml=标准化字典.xml
standardized.dictionary.table=标准化字典表
standardized.control=标准化控制
standardized.event=标准化告警
standardized.signal=标准化信号
scene.id.empty=场景不能为空
monitor.equipmentTemplate.name.length=设备模板名称长度不允许超过128位
monitor.equipment.houseIdIsNull=设备局房不能为空
monitor.equipment.stationIdIsNull=设备局站不能为空
monitor.equipment.monitorUnitIdIsNull=设备监控单元不能为空
site.self.diagnosis.equipment=自诊断设备
io.equipment=IO设备
import.template.error=导入模板错误
monitor.equipment.uncategorized=未分类设备模板
resource.structure.root.delete.error=不允许删除根节点
dictionary.id.exists=该字典id已经存在
base.station=基站
equipment.room=机房
error.device.template.notFound=设备模板不存在

# ResourceStructure相关消息
resource.structure.name.cannot.be.empty=资源结构名称不能为空
resource.structure.not.found=要更新的资源结构不存在
parent.resource.structure.not.found=指定的父资源结构不存在
resource.structure.save.failed=资源结构保存失败
internal.server.error=内部服务器错误

# Chinese messages
welcome=欢迎
hello=你好

# 设备 (Equipment)
siteweb.equipment.stationId=局站ID
siteweb.equipment.equipmentId=设备ID
siteweb.equipment.equipmentName=设备名称
siteweb.equipment.equipmentNo=设备编码
siteweb.equipment.equipmentModule=设备模块
siteweb.equipment.equipmentStyle=设备型号
siteweb.equipment.assetState=资产状态
siteweb.equipment.price=资产价格
siteweb.equipment.usedLimit=资产寿命
siteweb.equipment.usedDate=启用时间
siteweb.equipment.buyDate=购买时间
siteweb.equipment.vendor=设备厂商
siteweb.equipment.unit=单位
siteweb.equipment.equipmentCategory=设备类别
siteweb.equipment.equipmentType=设备类型
siteweb.equipment.equipmentClass=设备大类
siteweb.equipment.equipmentState=设备状态
siteweb.equipment.eventExpression=告警过滤表达式
siteweb.equipment.startDelay=开始延时
siteweb.equipment.endDelay=结束延时
siteweb.equipment.property=设备属性
siteweb.equipment.description=设备描述
siteweb.equipment.equipmentTemplateId=设备模板ID
siteweb.equipment.houseId=局房ID
siteweb.equipment.monitorUnitId=监控单元ID
siteweb.equipment.workStationId=工作站ID
siteweb.equipment.samplerUnitId=采集单元ID
siteweb.equipment.displayIndex=显示顺序
siteweb.equipment.connectState=连接状态
siteweb.equipment.updateTime=更新时间
siteweb.equipment.parentEquipmentId=父设备ID
siteweb.equipment.ratedCapacity=额定容量
siteweb.equipment.installedModule=已安装模块
siteweb.equipment.projectName=项目名称
siteweb.equipment.contractNo=合同号
siteweb.equipment.installTime=安装时间
siteweb.equipment.equipmentSN=设备序列号
siteweb.equipment.so=SO
siteweb.equipment.resourceStructureId=资源结构ID
siteweb.equipment.extValue=扩展值
siteweb.equipment.photo=照片
siteweb.equipment.changeType=变更类型

# 设备模板 (EquipmentTemplate)
siteweb.equipmenttemplate.equipmentTemplateId=设备模板ID
siteweb.equipmenttemplate.equipmentTemplateName=设备模板名称
siteweb.equipmenttemplate.parentTemplateId=父模板ID
siteweb.equipmenttemplate.memo=备注
siteweb.equipmenttemplate.protocolCode=协议代码
siteweb.equipmenttemplate.equipmentCategory=设备类别
siteweb.equipmenttemplate.equipmentType=设备类型
siteweb.equipmenttemplate.property=属性
siteweb.equipmenttemplate.description=描述
siteweb.equipmenttemplate.equipmentStyle=设备型号
siteweb.equipmenttemplate.unit=单位
siteweb.equipmenttemplate.vendor=厂商
siteweb.equipmenttemplate.photo=照片
siteweb.equipmenttemplate.equipmentBaseType=设备基类
siteweb.equipmenttemplate.stationCategory=局站类别
siteweb.equipmenttemplate.extendField1=扩展字段1

# 信号 (Signal)
siteweb.signal.id=ID
siteweb.signal.equipmentTemplateId=设备模板ID
siteweb.signal.signalId=信号ID
siteweb.signal.enable=启用
siteweb.signal.visible=可见
siteweb.signal.description=描述
siteweb.signal.signalName=信号名称
siteweb.signal.signalCategory=信号类别
siteweb.signal.signalType=信号类型
siteweb.signal.channelNo=通道号
siteweb.signal.channelType=通道类型
siteweb.signal.expression=表达式
siteweb.signal.dataType=数据类型
siteweb.signal.showPrecision=显示精度
siteweb.signal.unit=单位
siteweb.signal.storeInterval=存储周期
siteweb.signal.absValueThreshold=绝对值阈值
siteweb.signal.percentThreshold=百分比阈值
siteweb.signal.staticsPeriod=统计周期
siteweb.signal.baseTypeId=基类信号ID
siteweb.signal.chargeStoreInterVal=电池存储周期
siteweb.signal.chargeAbsValue=电池绝对值
siteweb.signal.displayIndex=显示顺序
siteweb.signal.mdbSignalId=MDB信号ID
siteweb.signal.moduleNo=模块号

# 事件 (Event)
siteweb.event.id=ID
siteweb.event.equipmentTemplateId=设备模板ID
siteweb.event.eventId=事件ID
siteweb.event.eventName=事件名称
siteweb.event.startType=开始类型
siteweb.event.endType=结束类型
siteweb.event.startExpression=开始表达式
siteweb.event.suppressExpression=抑制表达式
siteweb.event.eventCategory=事件类别
siteweb.event.signalId=信号ID
siteweb.event.enable=启用
siteweb.event.visible=可见
siteweb.event.description=描述
siteweb.event.displayIndex=显示顺序
siteweb.event.moduleNo=模块号

# 控制 (Control)
siteweb.control.id=ID
siteweb.control.equipmentTemplateId=设备模板ID
siteweb.control.controlId=控制ID
siteweb.control.controlName=控制名称
siteweb.control.controlCategory=控制类别
siteweb.control.cmdToken=命令标识
siteweb.control.baseTypeId=基类控制ID
siteweb.control.controlSeverity=控制重要性
siteweb.control.signalId=关联信号ID
siteweb.control.timeOut=超时时间
siteweb.control.retry=重试次数
siteweb.control.description=描述
siteweb.control.enable=启用
siteweb.control.visible=可见
siteweb.control.displayIndex=显示顺序
siteweb.control.commandType=命令类型
siteweb.control.controlType=控制类型
siteweb.control.dataType=数据类型
siteweb.control.maxValue=最大值
siteweb.control.minValue=最小值
siteweb.control.defaultValue=默认值
siteweb.control.moduleNo=模块号

# 监控单元 (MonitorUnit)
siteweb.monitorunit.monitorUnitId=监控单元ID
siteweb.monitorunit.monitorUnitName=监控单元名称
siteweb.monitorunit.monitorUnitCategory=监控单元类别
siteweb.monitorunit.monitorUnitCode=监控单元编码
siteweb.monitorunit.workStationId=工作站ID
siteweb.monitorunit.stationId=局站ID
siteweb.monitorunit.ipAddress=IP地址
siteweb.monitorunit.runMode=运行模式
siteweb.monitorunit.configFileCode=配置文件代码
siteweb.monitorunit.configUpdateTime=配置更新时间
siteweb.monitorunit.sampleConfigCode=采样配置代码
siteweb.monitorunit.softwareVersion=软件版本
siteweb.monitorunit.description=描述
siteweb.monitorunit.startTime=启动时间
siteweb.monitorunit.heartbeatTime=心跳时间
siteweb.monitorunit.connectState=连接状态
siteweb.monitorunit.updateTime=更新时间
siteweb.monitorunit.isSync=是否同步
siteweb.monitorunit.syncTime=同步时间
siteweb.monitorunit.isConfigOK=配置是否正确
siteweb.monitorunit.configFileCode_Old=旧配置文件代码
siteweb.monitorunit.sampleConfigCode_Old=旧采样配置代码
siteweb.monitorunit.appConfigId=应用配置ID
siteweb.monitorunit.canDistribute=可分发
siteweb.monitorunit.enable=启用
siteweb.monitorunit.rdsServer=RDS服务器
siteweb.monitorunit.dataServer=数据服务器
siteweb.monitorunit.installTime=安装时间
siteweb.monitorunit.fsu=FSU

# 采集器 (Sampler)
siteweb.sampler.samplerId=采集器ID
siteweb.sampler.samplerName=采集器名称
siteweb.sampler.samplerType=采集器类型
siteweb.sampler.protocolCode=协议代码
siteweb.sampler.dllCode=DLL代码
siteweb.sampler.dllVersion=DLL版本
siteweb.sampler.protocolFilePath=协议文件路径
siteweb.sampler.dllFilePath=DLL文件路径
siteweb.sampler.dllPath=DLL路径
siteweb.sampler.setting=设置
siteweb.sampler.description=描述
siteweb.sampler.soCode=SO代码
siteweb.sampler.soPath=SO路径
siteweb.sampler.uploadProtocolFile=上传协议文件

# 采集单元 (SamplerUnit)
siteweb.samplerunit.id=ID
siteweb.samplerunit.samplerUnitId=采集单元ID
siteweb.samplerunit.portId=端口ID
siteweb.samplerunit.monitorUnitId=监控单元ID
siteweb.samplerunit.samplerId=采集器ID
siteweb.samplerunit.parentSamplerUnitId=父采集单元ID
siteweb.samplerunit.samplerType=采集器类型
siteweb.samplerunit.samplerUnitName=采集单元名称
siteweb.samplerunit.address=地址
siteweb.samplerunit.spUnitInterval=采集周期
siteweb.samplerunit.dllPath=DLL路径
siteweb.samplerunit.connectState=连接状态
siteweb.samplerunit.updateTime=更新时间
siteweb.samplerunit.phoneNumber=电话号码
siteweb.samplerunit.description=描述

