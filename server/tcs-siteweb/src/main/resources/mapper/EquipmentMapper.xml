<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.EquipmentMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Equipment">
        <id column="EquipmentId" property="equipmentId" />
        <result column="EquipmentName" property="equipmentName" />
        <result column="EquipmentCategory" property="equipmentCategory" />
        <result column="EquipmentTemplateId" property="equipmentTemplateId" />
        <result column="MonitorUnitId" property="monitorUnitId" />
        <result column="StationId" property="stationId" />
        <result column="HouseId" property="houseId" />
        <result column="SamplerUnitId" property="samplerUnitId" />
        <result column="ResourceStructureId" property="resourceStructureId" />
        <result column="DisplayIndex" property="displayIndex" />
        <result column="UpdateTime" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        EquipmentId, EquipmentName, EquipmentCategory, EquipmentTemplateId, MonitorUnitId,
        StationId, HouseId, SamplerUnitId, ResourceStructureId, DisplayIndex, UpdateTime
    </sql>

    <!-- 根据分类映射更新设备分类 -->
    <update id="updateEquipmentCategoryByCategoryIdMap">
        UPDATE tbl_equipment e
        SET e.EquipmentCategory = (
            SELECT m.BusinessCategoryId
            FROM tbl_categoryidmap m
            WHERE m.BusinessId = #{businessId}
            AND m.CategoryTypeId = #{categoryTypeId}
            AND m.OriginalCategoryId = e.EquipmentCategory
        )
        WHERE e.EquipmentId = #{equipmentId}
        AND EXISTS (
            SELECT 1
            FROM tbl_categoryidmap m
            WHERE m.BusinessId = #{businessId}
            AND m.CategoryTypeId = #{categoryTypeId}
            AND m.OriginalCategoryId = e.EquipmentCategory
        )
    </update>

    <!-- ==================== 设备管理相关SQL ==================== -->

    <!-- 获取设备树结构 -->
    <select id="getEquipmentTree" resultType="com.siteweb.tcs.siteweb.vo.EquipmentTreeVO">
        SELECT
            e.EquipmentId as equipmentId,
            e.EquipmentName as equipmentName,
            e.EquipmentCategory as equipmentCategory,
            e.EquipmentTemplateId as equipmentTemplateId,
            et.EquipmentTemplateName as equipmentTemplateName,
            e.MonitorUnitId as monitorUnitId,
            e.StationId as stationId,
            e.HouseId as houseId,
            e.ResourceStructureId as resourceStructureId,
            e.DisplayIndex as displayIndex
        FROM tbl_equipment e
        LEFT JOIN tbl_equipmenttemplate et ON e.EquipmentTemplateId = et.EquipmentTemplateId
        ORDER BY e.DisplayIndex, e.EquipmentName
    </select>

    <!-- 根据资源结构ID查询设备列表 -->
    <select id="findByStructureId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_equipment
        WHERE ResourceStructureId = #{structureId}
        ORDER BY DisplayIndex, EquipmentName
    </select>

    <!-- 获取简化设备列表（上一级设备） -->
    <select id="findSimplifyEquipments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_equipment
        ORDER BY DisplayIndex, EquipmentName
    </select>


    <select id="findSimplifyEquipmentById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_equipment
        where EquipmentId = #{equipmentId}
        ORDER BY DisplayIndex, EquipmentName
    </select>

    <!-- 批量更新设备 -->
    <update id="batchUpdateEquipment">
        <foreach collection="equipments" item="equipment" separator=";">
            UPDATE tbl_equipment
            SET EquipmentName = #{equipment.equipmentName},
                EquipmentCategory = #{equipment.equipmentCategory},
                EquipmentTemplateId = #{equipment.equipmentTemplateId},
                MonitorUnitId = #{equipment.monitorUnitId},
                SamplerUnitId = #{equipment.samplerUnitId},
                ResourceStructureId = #{equipment.resourceStructureId},
                DisplayIndex = #{equipment.displayIndex},
                UpdateTime =
                <choose>
                    <when test="_databaseId == 'mysql'">
                        NOW()
                    </when>
                    <when test="_databaseId == 'postgresql'">
                        CURRENT_TIMESTAMP
                    </when>
                    <otherwise>
                        CURRENT_TIMESTAMP()
                    </otherwise>
                </choose>
            WHERE EquipmentId = #{equipment.equipmentId}
        </foreach>
    </update>

    <!-- 根据设备模板ID查询设备引用信息 -->
    <select id="findReferenceByEquipmentTemplateId" resultType="com.siteweb.tcs.siteweb.vo.EquipmentReferenceVO">
        SELECT a.EquipmentId,
               a.EquipmentName,
               b.StationId,
               b.MonitorUnitId,
               b.MonitorUnitName,
               b.IpAddress
        FROM tbl_equipment a
                 LEFT JOIN tsl_monitorunit b on a.MonitorUnitId = b.MonitorUnitId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findEquipmentDetail" resultType="com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO">
        SELECT
        te.StationId,
        te.EquipmentId,
        te.EquipmentName,
        te.EquipmentTemplateid AS equipmentTemplateId,
        tet.EquipmentTemplateName,
        te.ParentEquipmentId,
        te.WorkstationId,
        tw.WorkStationName,
        te.MonitorUnitId,
        mon.MonitorUnitName,
        te.EquipmentCategory,
        te.EquipmentType,
        te.EquipmentClass,
        te.EquipmentState,
        te.Vendor,
        te.Unit,
        te.EquipmentStyle,
        te.EquipmentModule,
        te.StartDelay,
        te.EndDelay,
        te.Property,
        tet.EquipmentBaseType,
        te.EventExpression,
        te.EquipmentNo,
        te.AssetState,
        te.BuyDate,
        te.UsedDate,
        te.UsedLimit,
        te.Price,
        te.RatedCapacity,
        te.installedmodule,
        te.Description,
        tep.ProjectName,
        tep.ContractNo,
        te.SamplerUnitId
        FROM
        tbl_equipment te
        LEFT JOIN tbl_equipmenttemplate tet ON
        te.EquipmentTemplateid = tet.EquipmentTemplateId
        LEFT JOIN tbl_equipmentprojectinfo tep ON
        te.StationId = tep.StationId
        AND te.EquipmentId = tep.EquipmentId
        AND te.MonitorUnitId = tep.MonitorUnitId
        LEFT JOIN tbl_workstation tw ON te.WorkStationId = tw.WorkStationId LEFT JOIN tsl_monitorunit mon ON te.MonitorUnitId = mon.MonitorUnitId
        WHERE
        te.EquipmentId = #{equipmentId}
    </select>

    <select id="findEquipmentDetailOMC" resultType="com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO">
        SELECT
        te.StationId,
        te.EquipmentId,
        te.EquipmentName,
        te.EquipmentTemplateid AS equipmentTemplateId,
        tet.EquipmentTemplateName,
        te.ParentEquipmentId,
        te.WorkstationId,
        te.MonitorUnitId,
        mon.MonitorUnitName,
        te.EquipmentCategory,
        te.EquipmentType,
        te.EquipmentClass,
        te.EquipmentState,
        te.Vendor,
        te.Unit,
        te.EquipmentStyle,
        te.EquipmentModule,
        te.StartDelay,
        te.EndDelay,
        te.Property,
        tet.EquipmentBaseType,
        te.EventExpression,
        te.EquipmentNo,
        te.AssetState,
        te.BuyDate,
        te.UsedDate,
        te.UsedLimit,
        te.Price,
        te.RatedCapacity,
        te.installedmodule,
        te.Description,
        te.SamplerUnitId
        FROM
        tbl_equipment te
        LEFT JOIN tbl_equipmenttemplate tet ON te.EquipmentTemplateid = tet.EquipmentTemplateId
        LEFT JOIN tsl_monitorunit mon ON te.MonitorUnitId = mon.MonitorUnitId
        WHERE
        te.EquipmentId = #{equipmentId}
    </select>

    <!-- 批量更新设备模板ID -->
    <update id="batchUpdateEquipmentTemplate">
        UPDATE tbl_equipment
        SET EquipmentTemplateId = #{destTemplateId}
        WHERE EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </update>

    <update id="updateBatchByIdsSyncTemplate">
        UPDATE tbl_equipment
        SET
        EquipmentCategory = #{equipmentCategory},
        Vendor = #{vendor},
        Unit = #{unit},
        EquipmentStyle = #{equipmentStyle}
        WHERE
        EquipmentTemplateId = #{equipmentTemplateId}
    </update>
</mapper>
