package com.siteweb.tcs.south.cmcc;

import com.siteweb.tcs.plugin.common.SouthLifeCycleEvent;
import com.siteweb.tcs.plugin.common.SouthLifeCycleEventType;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (2025-07-02)
 **/
@Slf4j
@Service
public class FsuLauncher {

    @Autowired
    @Qualifier("cmcc-gateway-sharding")
    private ActorRef cmccFsuShading;

    @Autowired
    private FSUProvider fsuProvider;

    public void launchAll() {
        var fsus = fsuProvider.getAllFsu();
        log.info("launch All Fsu {}", fsus.size());
        for (var fsu : fsus) {
            launchFsu(fsu);
        }
    }

    private void launchFsu(CMCCFsu fsu) {
        var event = new SouthLifeCycleEvent();
        event.setGatewayId(fsu.getGatewayId());
        event.setEvent(SouthLifeCycleEventType.LOAD);
        event.setGatewayInfo(fsu);
        cmccFsuShading.tell(event, ActorRef.noSender());
    }
}
