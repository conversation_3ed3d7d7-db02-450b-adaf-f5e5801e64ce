package com.siteweb.tcs.south.cmcc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR> (2025-08-04)
 **/
@Data
@Component
@ConfigurationProperties(prefix = "plugin.gateway.backup")
public class BackupProperties {

    /**
     * 最大记录数量限制
     */
    private int maxFileCount;

    /**
     * 最长保留时间（超出自动删除）
     */
    private Duration deleteAfter;

    /**
     * 保留最近3份记录（即便是过期）
     */
    private int minFileCount;
}
