package com.siteweb.tcs.south.cmcc.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.siteweb.tcs.common.config.TCSUidGenerator;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.util.PluginResourceHelper;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 数据源配置
 */
@Slf4j
@Configuration
@EnableConfigurationProperties
@EnableTransactionManagement(proxyTargetClass = true)
@MapperScan(basePackages = {"com.siteweb.tcs.south.cmcc.dal.mapper"}, sqlSessionFactoryRef = "cmccSqlSessionFactory")
public class DataSourceConfig {

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Value("${plugin.middleware.redis.primary}")
    private String redisResourceId;

    @Value("${plugin.id}")
    private String pluginId;

    @Autowired
    private ResourceRegistry resourceRegistry;

    @Autowired
    private TCSUidGenerator tcsUidGenerator;



    @Autowired
    private PluginContext pluginContext;

    @Autowired
    private PluginResourceHelper pluginResourceHelper;

    @Bean(name = "cmccDataSource")
    public DataSource cmccDataSource() {
        // 使用带引用计数的方法，传入插件ID作为引用者
        log.info("init cmccDataSource...");
        return resourceRegistry.getDataSource(dbResourceId, pluginId);
    }
//


    @Bean(name = "cmccRedis")
    public RedisTemplate cmccRedis() {
        // 使用带引用计数的方法，传入插件ID作为引用者
        log.info("init cmccRedis...");
        return resourceRegistry.getRedisTemplate(redisResourceId, pluginId);
    }







    /**
     * 为独立上下文创建事务管理器
     * 使用sitewebDataSource作为数据源
     * <p>
     * 注意：这个Bean必须命名为"transactionManager"，这样Spring的@Transactional注解
     * 才能自动找到并使用它进行事务管理
     */
    @Bean(name = {"transactionManager", "cmccTransactionManager"})
    @Primary
    public DataSourceTransactionManager transactionManager(@Qualifier("cmccDataSource") DataSource dataSource) {
        log.info("init transactionManager...");
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager(dataSource);
        // 设置事务管理器的一些属性
        transactionManager.setDefaultTimeout(100); // 默认事务超时时间30秒
        transactionManager.setRollbackOnCommitFailure(true); // 提交失败时回滚
        transactionManager.setValidateExistingTransaction(true); // 验证现有事务
        return transactionManager;
    }

    @Bean(name = "cmccSqlSessionFactory")
    public SqlSessionFactory cmccSqlSessionFactory(@Qualifier("cmccDataSource") DataSource dataSource) throws Exception {
        log.info("init cmccSqlSessionFactory...");
        ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
        try {
            // 切换为插件的 ClassLoader
            Thread.currentThread().setContextClassLoader(pluginContext.getPluginClassLoader());
            MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
            bean.setDataSource(dataSource);
            var xmlResources = pluginContext.resolveResources("mapper/" + pluginId + "/*.xml");

            log.info("CMCC MYBATIS MAPPERS COUNT : " + xmlResources.length);

            //var xmlResources = pluginResourceHelper.getMapperResourcesByPluginPath(pluginId, pluginContext.getPluginClassLoader())
            bean.setMapperLocations(xmlResources);
            GlobalConfig globalConfig = new GlobalConfig();
            globalConfig.setBanner(false);
            globalConfig.setDbConfig(new GlobalConfig.DbConfig());
            globalConfig.setIdentifierGenerator(tcsUidGenerator);
            bean.setGlobalConfig(globalConfig);

            return bean.getObject();

        } finally {
            Thread.currentThread().setContextClassLoader(originalClassLoader);
        }
    }
} 