package com.siteweb.tcs.south.cmcc.connector.commands;

import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取FSU信息用户命令
 * 
 * 根据中国移动B接口技术规范实现
 * 用于向FSU请求获取设备信息
 * 
 * <AUTHOR> for CMCC Get FSU Info Feature
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetFsuInfoUserCommand extends CMCCSouthUserCommand {


    /**
     * 构造函数
     */
    public GetFsuInfoUserCommand() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     */
    public GetFsuInfoUserCommand(String gatewayId, String initiator) {
        super(gatewayId, initiator);
    }

    @Override
    public PK_TypeName getRequestType() {
        return PK_TypeName.GET_FSUINFO;
    }

    /**
     * 创建获取全部信息命令
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     * @return 获取全部信息命令
     */
    public static GetFsuInfoUserCommand createInfo(String gatewayId, String initiator) {
        GetFsuInfoUserCommand command = new GetFsuInfoUserCommand(gatewayId, initiator);
        return command;
    }


}
