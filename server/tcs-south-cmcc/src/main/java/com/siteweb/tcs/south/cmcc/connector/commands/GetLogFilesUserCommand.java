package com.siteweb.tcs.south.cmcc.connector.commands;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;

/**
 * 获取日志文件用户命令
 * 
 * 根据移动B接口技术规范5.7.6节实现
 * 通过FTP从FSU的\logs\目录获取日志文件
 * 文件命名格式：FSUID_YYYYMMDD.log
 * 
 * <AUTHOR> for CMCC FTP Log Files Feature
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetLogFilesUserCommand extends CmccFTPUserCommand {

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 构造函数
     */
    public GetLogFilesUserCommand() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     */
    public GetLogFilesUserCommand(String gatewayId, String initiator) {
        super(gatewayId, initiator);
    }

    @Override
    public FTPOperationType getFTPOperationType() {
        return FTPOperationType.GET_LOG_FILES;
    }

    /**
     * 创建获取日志文件命令
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     * @param date 日期
     * @return 获取日志文件命令
     */
    public static GetLogFilesUserCommand create(String gatewayId, String initiator, LocalDate date) {
        GetLogFilesUserCommand command = new GetLogFilesUserCommand(gatewayId, initiator);
        command.setDate(date);
        return command;
    }

    @Override
    public String toString() {
        return String.format("GetLogFilesUserCommand{ gatewayId='%s', initiator='%s', date='%s' }", 
                           getGatewayId(), getInitiator(), date);
    }
}
