package com.siteweb.tcs.south.cmcc.connector.commands;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;

/**
 * 获取性能数据文件用户命令
 * 
 * 根据移动B接口技术规范5.7.5节实现
 * 通过FTP从FSU的\Measurement\目录获取监控点性能数据文件（CSV格式）
 * 文件命名格式：PM_FSUID_YYYYMMDDHHmm.csv
 * 
 * <AUTHOR> for CMCC FTP Performance Data Feature
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetPerformanceDataUserCommand extends CmccFTPUserCommand {

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 构造函数
     */
    public GetPerformanceDataUserCommand() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     */
    public GetPerformanceDataUserCommand(String gatewayId, String initiator) {
        super(gatewayId, initiator);
    }

    @Override
    public FTPOperationType getFTPOperationType() {
        return FTPOperationType.GET_PERFORMANCE_DATA;
    }

    /**
     * 创建获取性能数据命令
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     * @param date 日期
     * @return 获取性能数据命令
     */
    public static GetPerformanceDataUserCommand create(String gatewayId, String initiator, LocalDate date) {
        GetPerformanceDataUserCommand command = new GetPerformanceDataUserCommand(gatewayId, initiator);
        command.setDate(date);
        return command;
    }

    @Override
    public String toString() {
        return String.format("GetPerformanceDataUserCommand{ gatewayId='%s', initiator='%s', date='%s' }", 
                           getGatewayId(), getInitiator(), date);
    }
}
