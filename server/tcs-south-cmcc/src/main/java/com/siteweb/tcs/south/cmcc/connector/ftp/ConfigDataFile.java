package com.siteweb.tcs.south.cmcc.connector.ftp;

import lombok.Data;

/**
 * 配置数据文件
 * 
 * 对应移动B接口规范5.7.1节：批量获取监控对象的配置数据
 * 存储在FSU的\Config\目录下的XML格式配置文件
 * 
 * <AUTHOR> for CMCC FTP Config Data
 */
@Data
public class ConfigDataFile {
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 文件内容（XML格式）
     */
    private String content;
    
    /**
     * 文件大小（字节）
     */
    private long fileSize;
    
    /**
     * 最后修改时间
     */
    private java.time.LocalDateTime lastModified;
}
