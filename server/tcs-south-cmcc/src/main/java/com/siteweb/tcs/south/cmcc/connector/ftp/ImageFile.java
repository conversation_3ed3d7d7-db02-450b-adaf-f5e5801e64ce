package com.siteweb.tcs.south.cmcc.connector.ftp;

import lombok.Data;

/**
 * 图像文件
 * 
 * 对应移动B接口规范5.7.2节：获取监控图像文件
 * 存储在FSU的\PIC\目录下的JPG/JPEG格式图像文件
 * 
 * <AUTHOR> for CMCC FTP Image Data
 */
@Data
public class ImageFile {
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件内容（Base64编码，用于前端显示）
     */
    private String fileContent;

    /**
     * 文件数据（原始字节数组）
     */
    private byte[] fileData;

    /**
     * 文件大小（字节）
     */
    private long fileSize;
    
    /**
     * 图像宽度
     */
    private Integer width;
    
    /**
     * 图像高度
     */
    private Integer height;
    
    /**
     * 拍摄时间
     */
    private java.time.LocalDateTime captureTime;
    
    /**
     * 最后修改时间
     */
    private java.time.LocalDateTime lastModified;
}
