package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-07-25)
 **/
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GetDevConfAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private DevConfDataResponseInfo info = new DevConfDataResponseInfo();

    public GetDevConfAckMessage() {
        super(PK_TypeName.GET_DEV_CONF_ACK);
    }


}
