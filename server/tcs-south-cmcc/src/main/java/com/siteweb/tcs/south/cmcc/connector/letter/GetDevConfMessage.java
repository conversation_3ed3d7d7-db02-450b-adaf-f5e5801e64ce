package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-07-25)
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "Request")
public class GetDevConfMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public GetDevConfMessage(String fsuId) {
        super(PK_TypeName.GET_DEV_CONF);
        this.info.setFsuId(fsuId);
    }

    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

    }

}
