package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-07-01)
 **/

@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class GetFTPMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public GetFTPMessage() {
        super(PK_TypeName.GET_FTP);
    }

    /**
     * 创建获取FTP信息请求消息
     * @param fsuId FSU ID号
     * @return 获取FTP信息请求消息
     */
    public static GetFTPMessage create(String fsuId) {
        GetFTPMessage message = new GetFTPMessage();
        message.getInfo().setFsuId(fsuId);
        return message;
    }

    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;
    }
}
