package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取FSU注册信息请求报文
 * 
 * 根据中国移动B接口技术规范5.6.7章节实现
 * SC向FSU发送获取FSU向SC注册信息，FSU返回注册信息
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.7
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class GetLoginInfoMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public GetLoginInfoMessage() {
        super(PK_TypeName.GET_LOGININFO);
    }

    /**
     * 创建获取FSU注册信息请求消息
     * @param fsuId FSU ID号
     * @return 获取FSU注册信息请求消息
     */
    public static GetLoginInfoMessage create(String fsuId) {
        GetLoginInfoMessage message = new GetLoginInfoMessage();
        message.getInfo().setFsuId(fsuId);
        return message;
    }


    @Setter
    @Getter
    public static class Info  {
        /**
         * FSU ID号
         * 长度：FSUID_LEN (20字节)
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;
    }
}
