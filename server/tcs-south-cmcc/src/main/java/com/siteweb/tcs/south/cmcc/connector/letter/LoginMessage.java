package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-05-08)
 **/

@Getter
@JacksonXmlRootElement(localName = "Request")
public class LoginMessage extends MobileBRequestMessage {

    public LoginMessage() {
        super(PK_TypeName.LOGIN);
    }

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private final Info info = new Info();

    @Setter
    @Getter
    public static class Info {

        /**
         * 用户名
         */
        @JacksonXmlProperty(localName = "UserName")
        @JsonProperty("UserName")
        private String userName;

        /**
         * 口令 采用MD5进行加密
         */
        @JacksonXmlProperty(localName = "PassWord")
        @JsonProperty("Password")
        private String password;

        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        /**
         * FSU的内网IP
         */
        @JacksonXmlProperty(localName = "FSUIP")
        @JsonProperty("FSUIP")
        private String fsuIp;

        /**
         * FSU的MAC地址
         */
        @JacksonXmlProperty(localName = "FSUMAC")
        @JsonProperty("FSUMAC")
        private String fsuMac;

        /**
         * FSU版本号
         */
        @JacksonXmlProperty(localName = "FSUVER")
        @JsonProperty("FSUVER")
        private String fsuVer;
    }

}
