package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 时间同步应答报文
 * 
 * 根据中国移动B接口技术规范5.6.11章节实现
 * FSU返回时间同步结果，包含同步成功/失败状态和失败原因
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.11
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@JacksonXmlRootElement(localName = "Response")
public class TimeCheckAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public TimeCheckAckMessage() {
        super(PK_TypeName.TIME_CHECK_ACK);
    }

    /**
     * 构造成功响应
     * @param fsuId FSU ID号
     * @return 成功响应消息
     */
    public static TimeCheckAckMessage success(String fsuId) {
        TimeCheckAckMessage message = new TimeCheckAckMessage();
        message.getInfo().setFsuId(fsuId);
        message.getInfo().setResult(EnumResult.SUCCESS);
        message.getInfo().setFailureCause("NULL");
        return message;
    }

    /**
     * 构造失败响应
     * @param fsuId FSU ID号
     * @param failureCause 失败原因
     * @return 失败响应消息
     */
    public static TimeCheckAckMessage failure(String fsuId, String failureCause) {
        TimeCheckAckMessage message = new TimeCheckAckMessage();
        message.getInfo().setFsuId(fsuId);
        message.getInfo().setResult(EnumResult.FAILURE);
        message.getInfo().setFailureCause(failureCause);
        return message;
    }

    @Setter
    @Getter
    public static class Info extends StandardResponseInfo {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;
    }
}
