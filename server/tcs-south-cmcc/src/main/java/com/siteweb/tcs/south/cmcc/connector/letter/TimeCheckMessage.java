package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import com.siteweb.tcs.south.cmcc.connector.protocol.TTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 时间同步请求报文
 * 
 * 根据中国移动B接口技术规范5.6.11章节实现
 * SC向FSU发送标准时间信息，FSU按参数更新时间并返回对时结果
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class TimeCheckMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public TimeCheckMessage() {
        super(PK_TypeName.TIME_CHECK);
    }

    /**
     * 创建时间同步请求消息
     * @param fsuId FSU ID号
     * @param time 本机时间
     * @return 时间同步请求消息
     */
    public static TimeCheckMessage create(String fsuId, TTime time) {
        TimeCheckMessage message = new TimeCheckMessage();
        message.getInfo().setFsuId(fsuId);
        message.getInfo().setTime(time);
        return message;
    }

    /**
     * 转换为SOAP XML格式
     * @return SOAP XML字符串
     */
    public String toSoapXml() {
        return String.format(
            "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
            "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
            "  <soap:Body>\n" +
            "    <Request>\n" +
            "      <PkType>%s</PkType>\n" +
            "      <Info>\n" +
            "        <FSUID>%s</FSUID>\n" +
            "        <Time>%s</Time>\n" +
            "      </Info>\n" +
            "    </Request>\n" +
            "  </soap:Body>\n" +
            "</soap:Envelope>",
            getPkType(),
            info.getFsuId(),
            info.getTime().toFormattedString()
        );
    }

    @Setter
    @Getter
    public static class Info extends MobileBResponseMessage.StandardResponseInfo {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        /**
         * 本机时间
         */
        @JsonProperty("Time")
        @JacksonXmlProperty(localName = "Time")
        private TTime time;
    }
}
