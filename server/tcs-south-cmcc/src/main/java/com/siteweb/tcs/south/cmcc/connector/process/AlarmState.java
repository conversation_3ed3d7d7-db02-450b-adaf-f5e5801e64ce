package com.siteweb.tcs.south.cmcc.connector.process;

import com.siteweb.tcs.south.cmcc.connector.protocol.TAlarm;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警状态信息
 * 
 * 用于跟踪告警的生命周期状态
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
@Data
public class AlarmState {

    /**
     * 告警状态枚举
     */
    public enum State {
        ACTIVE,   // 活跃告警
        CLEARED,  // 已清除告警
        EXPIRED   // 过期告警
    }

    /**
     * 告警唯一标识
     */
    private String alarmKey;

    /**
     * FSU ID
     */
    private String fsuId;

    /**
     * 告警信息
     */
    private TAlarm alarm;

    /**
     * 告警状态
     */
    private State state;

    /**
     * 告警开始时间
     */
    private LocalDateTime startTime;

    /**
     * 告警结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 告警持续时间（毫秒）
     */
    public long getDurationMillis() {
        if (startTime == null) {
            return 0;
        }
        
        LocalDateTime endTimeToUse = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, endTimeToUse).toMillis();
    }

    /**
     * 检查告警是否活跃
     */
    public boolean isActive() {
        return state == State.ACTIVE;
    }

    /**
     * 检查告警是否已清除
     */
    public boolean isCleared() {
        return state == State.CLEARED;
    }
} 