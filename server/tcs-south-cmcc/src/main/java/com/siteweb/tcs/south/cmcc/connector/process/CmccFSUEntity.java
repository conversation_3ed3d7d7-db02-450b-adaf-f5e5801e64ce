package com.siteweb.tcs.south.cmcc.connector.process;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.stream.common.stream.StreamGraphOption;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.service.ITcsGatewayService;
import com.siteweb.tcs.plugin.common.*;
import com.siteweb.tcs.plugin.common.message.SouthControlCmdRequest;
import com.siteweb.tcs.siteweb.util.JsonHelper;
import com.siteweb.tcs.south.cmcc.connector.CmccXmlHttpRequester;
import com.siteweb.tcs.south.cmcc.connector.FailureCauses;
import com.siteweb.tcs.south.cmcc.connector.commands.*;
import com.siteweb.tcs.south.cmcc.connector.letter.*;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import com.siteweb.tcs.south.cmcc.connector.services.CmccBackupService;
import com.siteweb.tcs.south.cmcc.connector.services.CmccConfigDiffService;
import com.siteweb.tcs.south.cmcc.connector.services.CmccDevConfService;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.enums.ConfigSyncEnum;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCControlMapper;
import com.siteweb.tcs.south.cmcc.dal.provider.ChangePersistenceProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.DeviceProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.DictionaryProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.util.CMCCSerializer;
import com.siteweb.tcs.south.cmcc.util.CmccFileUtil;
import com.siteweb.tcs.south.cmcc.web.service.CmccFsuAccessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;

import static com.siteweb.tcs.south.cmcc.connector.commands.CMCCFsuStatusData.create;

/**
 * <AUTHOR> (2025-07-03)
 **/
@Slf4j
public class CmccFSUEntity extends AbstractGatewayEntity<CMCCFsu, MobileBRawMessage> {

    private final FSUProvider fsuProvider;
    private final DeviceProvider deviceProvider;

    private final CmccFsuAccessService cmccFsuAccessService;
    private final ActorRef cmccFsuSharding;

    // 处理器服务
    private final CmccFsuMessageProcessor messageProcessor;
    private final CmccFsuScheduleManager scheduleManager;
    private final ITcsGatewayService tcsGatewayService;
    private final CmccDevConfService cmccDevConfService;
    private final ChangePersistenceProvider changePersistenceProvider;
    private final CmccXmlHttpRequester xmlHttpRequester;
    private final CmccFileUtil cmccFileUtil;
    private final CmccBackupService backupService;
    private final CmccFsuControlProcessor cmccFsuControlProcessor;
    private final DictionaryProvider dictionaryProvider;

    public CmccFSUEntity(String fsuId, String shardingName) {
        super(fsuId, shardingName, "south-cmcc-plugin", MobileBRawMessage.class);
        fsuProvider = PluginScope.getBean(FSUProvider.class);
        deviceProvider = PluginScope.getBean(DeviceProvider.class);
        cmccFsuAccessService = PluginScope.getBean(CmccFsuAccessService.class);
        cmccFsuSharding = (ActorRef) PluginScope.getBean("cmcc-gateway-sharding");
        // 初始化处理器服务
        messageProcessor = PluginScope.getBean(CmccFsuMessageProcessor.class);
        scheduleManager = PluginScope.getBean(CmccFsuScheduleManager.class);
        tcsGatewayService = PluginScope.getBean(ITcsGatewayService.class);
        cmccDevConfService = PluginScope.getBean(CmccDevConfService.class);
        changePersistenceProvider = PluginScope.getBean(ChangePersistenceProvider.class);
        xmlHttpRequester = new CmccXmlHttpRequester(super.messageTracer, this::generateServiceAddress);
        backupService = PluginScope.getBean(CmccBackupService.class);
        cmccFileUtil = PluginScope.getBean(CmccFileUtil.class);
        dictionaryProvider = PluginScope.getBean(DictionaryProvider.class);
        log.info("FSU: {} Entity is Constructor, path={}", getSelf().path().name(), getSelf().path());

        cmccFsuControlProcessor = new CmccFsuControlProcessor(fsuId, xmlHttpRequester, PluginScope.getBean(CMCCControlMapper.class));
    }


    @Override
    protected GatewayCacheManager<CMCCFsu> createGatewayCacheManager() {
        RedisTemplate<String, String> redis = PluginScope.getBean(RedisTemplate.class);
        ObjectMapper objectMapper = PluginScope.getBean(ObjectMapper.class);
        return GatewayCacheManager.create(redis, objectMapper, CMCCFsu.class);
    }

    @Override
    protected boolean fetchPendingState(String gatewayId) {
        return fsuProvider.getInPending(gatewayId);
    }

    @Override
    protected CMCCFsu fetchGatewayEntity(String fsuId) {
        return fsuProvider.getFsuByFsuid(fsuId);
    }

    @Override
    protected String generateServiceAddress(CMCCFsu gateway) {
        var port = "8080";
        if (gateway.getFsuPort() != null && !gateway.getFsuPort().isEmpty()) {
            port = gateway.getFsuPort();
        }
        return String.format("http://%s:%s/services/FSUService", gateway.getIpAddress(), port);
    }


    @Override
    protected void configureGraphOption(StreamGraphOption option, CMCCFsu gateway) {
        // 将FSU的webservice-url注入进流计算引擎
        option.setProperty("xmlHttpRequester", xmlHttpRequester);
    }


    @Override
    protected boolean handleMessageAck(MobileBRawMessage rawMessage) {
        // 消息为ACK无需回复
        if (rawMessage.getPkType().isAck()) return true;
        log.info("PKType:{} 消息还未回应, 已回应成功。", rawMessage.getPkType().name());
        responseSuccess(rawMessage);
        return true;
    }


    @Override
    protected boolean handleUnauthorizedMessage(@NotNull MobileBRawMessage message, boolean isPending) {
        if (PK_TypeName.LOGIN.equals(message.getPkType()) && (!isPending || cmccFsuAccessService.isAutoAccess())) {
            // FSU未记录在案 且 当前是登录消息
            var loginMessage = CMCCSerializer.parseReqMessage(message, LoginMessage.class);
            var newStatus = cmccFsuAccessService.handleNewFsuNetworkAccess(loginMessage);
            state.setStatus(newStatus);
            if (GatewayStatus.OFFLINE.equals(newStatus)) {
                // 自动入网成功。
                responseSuccess(message);
                return true;
            }
        }
        responseFailure(message, FailureCauses.FORBIDDEN_ACCESS);
        return true; // 中止后续流程
    }

    @Override
    protected boolean handleAnonymousMessage(MobileBRawMessage message) {
        // 处理登录消息
        if (PK_TypeName.LOGIN.equals(message.getPkType())) {
            handleLogin(message);
            state.setLastAccessTime(System.currentTimeMillis());
            return true; // 中止后续流程
        }
        // 这里假设对已接入的Gateway是完全信任的。 否则需要返回未授权并拦截此消息 message.responseFail(FailureCauses.NOT_AUTHORIZED);
        return false;
    }

    @Override
    protected boolean handleRawMessage(MobileBRawMessage message) {
        var pkType = message.getPkType();
        if (PK_TypeName.LOGIN.equals(message.getPkType())) {
            handleLogin(message);
            return true;
        }

        // 配置上送
        if (PK_TypeName.SEND_DEV_CONF_DATA.equals(pkType)) {
            var confMsg = CMCCSerializer.parseReqMessage(message, SendDevConfDataMessage.class);
            if (handleDevConfData(confMsg.getInfo())) {
                responseSuccess(message);
            }
            return true;
        }


        // 处理时间同步ACK响应
        if (PK_TypeName.TIME_CHECK_ACK.equals(pkType)) {
            messageProcessor.handleTimeCheckAck(message, state);
            return true;
        }

        // 处理获取FSU信息ACK响应
        if (PK_TypeName.GET_FSUINFO_ACK.equals(pkType)) {
            messageProcessor.handleGetFsuInfoAck(message, state);
            return true;
        }

        // 处理重启FSU ACK响应
        if (PK_TypeName.SET_FSUREBOOT_ACK.equals(pkType)) {
            messageProcessor.handleRestartFsuAck(message);
            return true;
        }

        // 处理设置FTP信息ACK响应
        if (PK_TypeName.SET_FTP_ACK.equals(pkType)) {
            messageProcessor.handleSetFtpAck(message);
            return true;
        }

        if (PK_TypeName.GET_FTP_ACK.equals(pkType)) {
            var getFTPACK = messageProcessor.handleGetFtpAck(message);
            state.getGatewayInfo().setFtpUserName(getFTPACK.getInfo().getUsername());
            state.getGatewayInfo().setFtpPassword(getFTPACK.getInfo().getPassword());
            state.getGatewayInfo().setFtpPort("21");
            fsuProvider.updateFsu(state.getGatewayInfo());
            return true;
        }

        // 处理获取FSU注册信息ACK响应
        if (PK_TypeName.GET_LOGININFO_ACK.equals(pkType)) {
            messageProcessor.handleGetLoginInfoAck(message);
            return true;
        }

        // 处理设置FSU注册信息ACK响应
        if (PK_TypeName.SET_LOGININFO_ACK.equals(pkType)) {
            messageProcessor.handleSetLoginInfoAck(message);
            return true;
        }

        // 处理更新FSU状态信息获取周期ACK响应
        if (PK_TypeName.UPDATE_FSUINFO_INTERVAL_ACK.equals(pkType)) {
            messageProcessor.handleUpdateFsuInfoIntervalAck(message);
            return true;
        }

        // 手动拉取配置
        if (PK_TypeName.GET_DEV_CONF_ACK.equals(pkType)) {
            var confMsg = CMCCSerializer.parseAckMessage(message, GetDevConfAckMessage.class);
            // 手动同步，不处理返回
            handleDevConfData(confMsg.getInfo());
            return true;
        }

        return false;
    }

    @Override
    protected void onControlHandler(SouthControlCmdRequest request) {
        cmccFsuControlProcessor.handleGatewayControl(request);
    }


    @Override
    protected void handleMessageProcessError(MobileBRawMessage message, Throwable throwable) {
        log.error("Gateway: {}, PK_Type: {}", message.getGatewayId(), message.getPkType().name(), throwable);

    }

    @Override
    protected void handleUserCommand(SouthUserCommand command) {
        if (!(command instanceof CMCCSouthUserCommand cmccCommand)) {
            return;
        }
        // 手动拉取FSU配置
        if (command instanceof PullFsuDevConfUserCommand pullFsuDevConfUserCommand) {
            cmccDevConfService.manuallyPullFsuDevConf(xmlHttpRequester, self(), pullFsuDevConfUserCommand);
            return;
        }
        // 手动下发配置
        if (command instanceof SetFusDevConfUserCommand setFusDevConfUserCommand) {
            cmccDevConfService.manuallySetFsuDevConf(xmlHttpRequester, setFusDevConfUserCommand);
            return;
        }
        // 获取报文日志
        if (command instanceof GetTracerMessagesUserCommand getTracerMessagesUserCommand) {
            log.info("获取报文日志 GatewayId:{}", command.getGatewayId());
            getTracerMessagesUserCommand.tell(UserCommandResponse.success("Ok", messageTracer.toJson()));
            return;
        }
        // 报文跟踪状态
        if (command instanceof TracerStateUserCommand tracerStateUserCommand) {
            if (tracerStateUserCommand.getEnable() != null) {
                // 如果enable 不为null时设置
                this.messageTracer.setEnabled(tracerStateUserCommand.getEnable());
            }
            tracerStateUserCommand.tell(UserCommandResponse.success("Ok", messageTracer.getEnabled()));
            return;
        }


        // 根据命令类型选择处理方式
        if (CommandType.INTERNAL_QUERY.equals(cmccCommand.getCommandType())) {
            // 处理内部查询命令 - 直接返回状态信息
            if (cmccCommand instanceof GetFsuStatusUserCommand && cmccCommand.getSender() != null) {
                CMCCFsuStatusData statusData = create(
                        gatewayId, state
                );
                UserCommandResponse response = UserCommandResponse.success("获取FSU状态成功", statusData);
                cmccCommand.getSender().tell(response, self());
            }
        } else if (CommandType.FTP.equals(cmccCommand.getCommandType())) {
            // 处理FTP命令
            messageProcessor.handleFTPCommand(state.getGatewayInfo(), (CmccFTPUserCommand) cmccCommand);
        } else {
            // 处理B接口命令（原有逻辑）
            messageProcessor.processUserCommand(cmccCommand, self(), xmlHttpRequester);
            log.info("已发送HTTP请求: FSU={}, 命令类型={}", cmccCommand.getGatewayId(), cmccCommand.getRequestType());
        }
    }


    /**
     * 处理移动B接口的配置上送
     * 此事件应保持同步，以保证后续的数据告警与配置一致性
     */
    private boolean handleDevConfData(DevConfDataResponseInfo devConfDataResponseInfo) {
        try {
            if (ConfigSyncEnum.DOWN_TOP.equals(state.getGatewayInfo().getConfigSync())) {
                var remoteDevices = devConfDataResponseInfo.getDeviceList();
                log.info("配置同步: FSU={}, 设备数量={}", gatewayId, remoteDevices.size());
                var localDevices = deviceProvider.getAllDevice(gatewayId);
                // 备份配置
                backupService.backup(state.getGatewayInfo(), localDevices, "配置同步[从下往上]");
                var cmccConfigDiffService = new CmccConfigDiffService(state, localDevices);
                // 分析变更事件
                var changeEvent = cmccConfigDiffService.compare(remoteDevices);
                if (!changeEvent.getDevices().isEmpty()) {
                    // 调用 HUB API 变更
                    var handleResult = tcsGatewayService.handleGatewayConfigChange(changeEvent);
                    // 发送生命周期消息
                    if (handleResult.isSuccess()) {
                        // 本地持久化
                        changePersistenceProvider.transactionSaveChange(handleResult);
                        writePipeline(handleResult);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("处理设备配置数据失败: FSU={}", gatewayId, e);
            return false;
        }
    }


    /***
     * 解析登录报文,并进行身份验证
     * <AUTHOR> (2025/5/15)
     * @param message
     */
    private void handleLogin(MobileBRawMessage message) {
        var loginMessage = CMCCSerializer.parseReqMessage(message, LoginMessage.class);
        var gatewayInfo = state.getGatewayInfo();
        var pkgInfo = loginMessage.getInfo();
        log.info("Handle FSU:{} login request username:{}, password:{} .", pkgInfo.getFsuId(), pkgInfo.getUserName(), pkgInfo.getPassword());
        if (pkgInfo.getUserName().equals(gatewayInfo.getUsername())) {
            if (pkgInfo.getPassword().equals(gatewayInfo.getPassword())) {
                loginOk();
                responseSuccess(message);
                // todo 这里 state.getGatewayInfo() 应提供一个变体
                if (fsuProvider.compareAndUpdate(state.getGatewayInfo(), pkgInfo)) {
                    // FSU信息变更 上送数据变更LifeCycle
                    var fsuInfo = state.getGatewayInfo();
                    var changeEvent = new GatewayConfigChangeDto();
                    changeEvent.setId(null);
                    changeEvent.setDeleted(false);
                    changeEvent.setPluginId("south-cmcc-plugin");
                    changeEvent.setSouthGatewayId(fsuInfo.getGatewayId());
                    changeEvent.setSouthGatewayName(fsuInfo.getGatewayName());
                    changeEvent.setSouthAddress(fsuInfo.getIpAddress());
                    var node = JsonHelper.toObjectNode(fsuInfo);
                    node.remove("deviceList");
                    changeEvent.setMetadata(node);
                    changeEvent.setLifeCycleEvent(LifeCycleEventEnum.UPDATE);
                    var result = tcsGatewayService.handleGatewayConfigChange(changeEvent);
                    if (!result.isSuccess()) {
                        log.error("更新hub gateway失败。 {}", result.getErrorMessage());
                    }
                    executeUpdateEvent(fsuInfo);
                    log.info("FSU信息已更新: FSU={}, IP={}, MAC={}, Version={}", fsuInfo.getGatewayId(), fsuInfo.getIpAddress(), fsuInfo.getMac(), fsuInfo.getVersion());
                }
            } else {
                log.warn("FSU {} login failure, cause:{}", pkgInfo.getFsuId(), FailureCauses.PASSWORD_ERROR);
                responseFailure(message, FailureCauses.PASSWORD_ERROR);
            }
        } else {
            log.warn("FSU {} login failure, cause:{}", pkgInfo.getFsuId(), FailureCauses.USERNAME_ERROR);
            responseFailure(message, FailureCauses.USERNAME_ERROR);
        }
    }


    @Override
    protected void onGatewayEnabled() {
        log.info("Gateway: {} 已启动", gatewayId);
        state.getGatewayInfo().setEnable(true);
        scheduleManager.addFsu(gatewayId, state.getGatewayInfo(), cmccFsuSharding, xmlHttpRequester);
    }

    @Override
    protected void onGatewayDisabled() {
        log.info("Gateway: {} 已停止", gatewayId);
        state.getGatewayInfo().setEnable(false);
        scheduleManager.removeFsu(gatewayId);
    }

    @Override
    protected void onGatewayUpdate() {
        log.info("Gateway: {} 配置已更新", gatewayId);
        scheduleManager.removeFsu(gatewayId);
        scheduleManager.addFsu(gatewayId, state.getGatewayInfo(), cmccFsuSharding, xmlHttpRequester);
    }


    protected void responseSuccess(MobileBRawMessage message) {
        var sender = message.getSender();
        if (sender != null && !message.getPkType().isAck()) {
            var response = new MobileBResponseMessage(message.getPkType().getAck());
            response.getInfo().setResult(EnumResult.SUCCESS);
            messageTracer.traceResponseFrame(message.getMsgId(), message.getPkType().name(), response.toSCResponseXml());
            message.response(response);
        }
    }


    protected void responseFailure(MobileBRawMessage message, String failureCause) {
        var sender = message.getSender();
        if (sender != null && !message.getPkType().isAck()) {
            var response = new MobileBResponseMessage(message.getPkType().getAck());
            response.getInfo().setResult(EnumResult.FAILURE);
            response.getInfo().setFailureCause(failureCause);
            messageTracer.traceResponseFrame(message.getMsgId(), message.getPkType().name(), response.toSCResponseXml());
            message.response(response);
        }
    }


}
