package com.siteweb.tcs.south.cmcc.connector.process;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.south.cmcc.connector.CmccXmlHttpRequester;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBRawMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBRequestMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBResponseMessage;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.south.cmcc.connector.protocol.TTime;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import com.siteweb.tcs.south.cmcc.util.CMCCSerializer;
import com.siteweb.tcs.south.cmcc.util.SoapHelper;
import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.common.util.HttpRequestUtil;
import com.siteweb.tcs.plugin.common.GatewayState;
import com.siteweb.tcs.plugin.common.UserCommandResponse;
import com.siteweb.tcs.south.cmcc.connector.commands.*;
import com.siteweb.tcs.south.cmcc.connector.ftp.CmccFTPHelper;
import com.siteweb.tcs.south.cmcc.connector.letter.*;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.http.javadsl.model.ContentTypes;
import org.apache.pekko.http.javadsl.model.HttpMethods;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * CMCC FSU消息处理器
 * <p>
 * 重新组织方法结构，按功能分组：
 * 1. ACK响应处理方法
 * 2. 用户命令处理方法
 * 3. FTP命令处理方法
 * 4. HTTP请求处理方法
 * 5. 工具方法和响应发送方法
 *
 * <AUTHOR> for better code organization
 */
@Slf4j
@Component
public class CmccFsuMessageProcessor {

    @Autowired
    private CmccFTPHelper cmccFTPHelper;

    // ================================
    // 1. ACK响应处理方法
    // ================================

    /**
     * 处理时间同步ACK响应
     */
    public void handleTimeCheckAck(MobileBRawMessage message, GatewayState state) {
        try {
            var timeCheckAck = CMCCSerializer.parseAckMessage(message, TimeCheckAckMessage.class);

            if (EnumResult.SUCCESS.equals(timeCheckAck.getInfo().getResult())) {
                log.info("时间同步成功: FSU={}", message.getGatewayId());
                state.setLastTimeCheckTime(new Timestamp(System.currentTimeMillis()));
            } else {
                String failureCause = timeCheckAck.getInfo().getFailureCause();
                log.warn("时间同步失败: FSU={}, 原因={}", message.getGatewayId(), failureCause);
            }
        } catch (Exception e) {
            log.error("解析TIME_CHECK_ACK失败: FSU={}", message.getGatewayId(), e);
        }
    }

    /**
     * 处理获取FSU信息ACK响应
     */
    public void handleGetFsuInfoAck(MobileBRawMessage message, GatewayState state) {
        try {
            var getFsuInfoAck = CMCCSerializer.parseAckMessage(message, GetFsuInfoAckMessage.class);

            if (EnumResult.SUCCESS.equals(getFsuInfoAck.getInfo().getResult())) {
                log.info("获取FSU信息成功: FSU={}", message.getGatewayId());
                state.setLastHeartbeatTime(new Timestamp(System.currentTimeMillis()));
            } else {
                String failureCause = getFsuInfoAck.getInfo().getFailureCause();
                log.warn("获取FSU信息失败: FSU={}, 原因={}",
                        message.getGatewayId(), failureCause);
            }
        } catch (Exception e) {
            log.error("解析GET_FSUINFO_ACK失败: FSU={}", message.getGatewayId(), e);
        }
    }

    /**
     * 处理重启FSU ACK响应
     */
    public void handleRestartFsuAck(MobileBRawMessage message) {
        try {
            var restartFsuAck = CMCCSerializer.parseAckMessage(message, RestartFsuAckMessage.class);

            if (EnumResult.SUCCESS.equals(restartFsuAck.getInfo().getResult())) {
                log.info("重启FSU成功: FSU={}", message.getGatewayId());
            } else {
                String failureCause = restartFsuAck.getInfo().getFailureCause();
                log.warn("重启FSU失败: FSU={}, 原因={}", message.getGatewayId(), failureCause);
            }
        } catch (Exception e) {
            log.error("解析SET_FSUREBOOT_ACK失败: FSU={}", message.getGatewayId(), e);
        }
    }

    /**
     * 处理设置FTP信息ACK响应
     */
    public void handleSetFtpAck(MobileBRawMessage message) {
        try {
            var setFtpAck = CMCCSerializer.parseAckMessage(message, SetFtpAckMessage.class);

            if (EnumResult.SUCCESS.equals(setFtpAck.getInfo().getResult())) {
                log.info("设置FTP信息成功: FSU={}", message.getGatewayId());
            } else {
                String failureCause = setFtpAck.getInfo().getFailureCause();
                log.warn("设置FTP信息失败: FSU={}, 原因={}", message.getGatewayId(), failureCause);
            }
        } catch (Exception e) {
            log.error("解析SET_FTP_ACK失败: FSU={}", message.getGatewayId(), e);
        }
    }

    /**
     * 处理获取FTP信息ACK响应
     */
    public GetFTPAckMessage handleGetFtpAck(MobileBRawMessage message) {
        try {
            var getFtpAck = CMCCSerializer.parseAckMessage(message, GetFTPAckMessage.class);

            if (EnumResult.SUCCESS.equals(getFtpAck.getInfo().getResult())) {
                log.info("获取FTP信息成功: FSU={}, 用户名={}", message.getGatewayId(),
                        getFtpAck.getInfo().getUsername());
            } else {
                String failureCause = getFtpAck.getInfo().getFailureCause();
                log.warn("获取FTP信息失败: FSU={}, 原因={}", message.getGatewayId(), failureCause);
            }
            return getFtpAck;
        } catch (Exception e) {
            log.error("解析GET_FTP_ACK失败: FSU={}", message.getGatewayId(), e);
        }
        return null;
    }

    /**
     * 处理获取FSU注册信息ACK响应
     */
    public void handleGetLoginInfoAck(MobileBRawMessage message) {
        try {
            var getLoginInfoAck = CMCCSerializer.parseAckMessage(message, GetLoginInfoAckMessage.class);

            if (EnumResult.SUCCESS.equals(getLoginInfoAck.getInfo().getResult())) {
                log.info("获取FSU注册信息成功: FSU={}, 用户名={}", message.getGatewayId(),
                        getLoginInfoAck.getInfo().getUserName());
            } else {
                String failureCause = getLoginInfoAck.getInfo().getFailureCause();
                log.warn("获取FSU注册信息失败: FSU={}, 原因={}", message.getGatewayId(), failureCause);
            }
        } catch (Exception e) {
            log.error("解析GET_LOGININFO_ACK失败: FSU={}", message.getGatewayId(), e);
        }
    }

    /**
     * 处理设置FSU注册信息ACK响应
     */
    public void handleSetLoginInfoAck(MobileBRawMessage message) {
        try {
            var setLoginInfoAck = CMCCSerializer.parseAckMessage(message, SetLoginInfoAckMessage.class);

            if (EnumResult.SUCCESS.equals(setLoginInfoAck.getInfo().getResult())) {
                log.info("设置FSU注册信息成功: FSU={}", message.getGatewayId());
            } else {
                String failureCause = setLoginInfoAck.getInfo().getFailureCause();
                log.warn("设置FSU注册信息失败: FSU={}, 原因={}", message.getGatewayId(), failureCause);
            }
        } catch (Exception e) {
            log.error("解析SET_LOGININFO_ACK失败: FSU={}", message.getGatewayId(), e);
        }
    }

    /**
     * 处理更新FSU状态信息获取周期ACK响应
     */
    public void handleUpdateFsuInfoIntervalAck(MobileBRawMessage message) {
        try {
            var updateIntervalAck = CMCCSerializer.parseAckMessage(message, UpdateFsuInfoIntervalAckMessage.class);

            if (EnumResult.SUCCESS.equals(updateIntervalAck.getInfo().getResult())) {
                log.info("更新FSU状态信息获取周期成功: FSU={}", message.getGatewayId());
            } else {
                String failureCause = updateIntervalAck.getInfo().getFailureCause();
                log.warn("更新FSU状态信息获取周期失败: FSU={}, 原因={}", message.getGatewayId(), failureCause);
            }
        } catch (Exception e) {
            log.error("解析UPDATE_FSUINFO_INTERVAL_ACK失败: FSU={}", message.getGatewayId(), e);
        }
    }

    // ================================
    // 2. 用户命令处理方法
    // ================================

    /**
     * 处理用户命令的HTTP请求（异步，不等待结果）
     *
     * @param command       用户命令
     * @param cmccFsuEntity entity actor
     */
    public void processUserCommand(CMCCSouthUserCommand command, ActorRef cmccFsuEntity, CmccXmlHttpRequester http) {
        try {
            var reqMessage = buildCommandXmlRawMessage(command);
            http.send(reqMessage).whenComplete((rawMessage, throwable) -> {
                if (throwable != null) {
                    log.error("手动拉取FSU配置同步失败 {}", throwable.getMessage());
                    command.getSender().tell(UserCommandResponse.failure(throwable.getMessage()), ActorRef.noSender());
                } else {
                    cmccFsuEntity.tell(rawMessage, ActorRef.noSender());
                    command.getSender().tell(UserCommandResponse.success("拉取数据成功，结果查看日志。", getMessInfo(rawMessage)), ActorRef.noSender());
                }
            });
        } catch (Exception e) {
            log.error("构建HTTP请求失败: FSU={}", command.getGatewayId(), e);
        }
    }

    private Object getMessInfo(MobileBRawMessage rawMessage) {
        switch (rawMessage.getPkType()) {
            case GET_FSUINFO_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, GetFsuInfoAckMessage.class).getInfo();
            case GET_FTP_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, GetFTPAckMessage.class).getInfo();
            case GET_LOGININFO_ACK:
                return CMCCSerializer.
                        parseAckMessage(rawMessage, GetLoginInfoAckMessage.class).getInfo();
            case SET_FTP_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, SetFtpAckMessage.class).getInfo();
            case TIME_CHECK_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, TimeCheckAckMessage.class).getInfo();
            case SET_LOGININFO_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, SetLoginInfoAckMessage.class).getInfo();
            case UPDATE_FSUINFO_INTERVAL_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, UpdateFsuInfoIntervalAckMessage.class).getInfo();
            case SET_FSUREBOOT_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, RestartFsuAckMessage.class).getInfo();
            default:
                return CMCCSerializer.parseAckMessage(rawMessage, MobileBResponseMessage.class).getInfo();

        }
    }

    /**
     * 处理单个定时任务请求（为独立调度器提供支持）
     *
     * @param task 单个HTTP请求任务
     */
    public void processScheduledRequest(HttpRequestTask task) {
        if (task == null) {
            return;
        }

        sendHttpRequest(task.xmlContent(), task.url())
                .whenComplete((response, throwable) -> {
                    if (throwable != null) {
                        log.error("Scheduled HTTP request failed for URL {}: {}", task.url(), throwable.getMessage());
                    } else {
                        // 解析和发送消息给Actor
                        try {
                            var unescapeXml = StringEscapeUtils.unescapeXml(response);
                            var parsedFsuId = SoapHelper.parseXmlFSUId(unescapeXml);
                            var pkType = SoapHelper.parseXmlPkType(unescapeXml);

                            MobileBRawMessage rawMessage = new MobileBRawMessage(parsedFsuId, pkType, unescapeXml, ActorRef.noSender());

                            if (task.shardingActor() != null) {
                                task.shardingActor().tell(rawMessage, ActorRef.noSender());
                            }
                        } catch (Exception e) {
                            log.error("Failed to parse response for scheduled request to URL {}: {}", task.url(), e);
                        }
                    }
                });
    }

    // 定义任务记录
    public record HttpRequestTask(String url, String xmlContent, ActorRef shardingActor) {
    }

    // ================================
    // 3. FTP命令处理方法
    // ================================

    /**
     * 处理FTP命令
     *
     * @param ftpCommand FTP命令
     */
    public void handleFTPCommand(CMCCFsu fsuInfo, CmccFTPUserCommand ftpCommand) {
        log.info("处理FTP命令: FSU={}, 操作类型={}", ftpCommand.getGatewayId(), ftpCommand.getFTPOperationType());

        // 异步处理FTP命令，避免阻塞Actor
        java.util.concurrent.CompletableFuture.runAsync(() -> {
            try {
                processFTPCommand(ftpCommand, fsuInfo);
            } catch (Exception e) {
                log.error("处理FTP命令异常: FSU={}, 操作类型={}",
                        ftpCommand.getGatewayId(), ftpCommand.getFTPOperationType(), e);

                // 发送失败响应
                sendFTPCommandResponse(ftpCommand, false, "FTP命令处理异常: " + e.getMessage(), null);
            }
        });
    }

    /**
     * 处理FTP命令的具体逻辑
     *
     * @param ftpCommand FTP命令
     * @param fsuInfo    FSU信息
     */
    private void processFTPCommand(CmccFTPUserCommand ftpCommand, CMCCFsu fsuInfo) {
        log.info("开始处理FTP命令: FSU={}, 操作类型={}",
                ftpCommand.getGatewayId(), ftpCommand.getFTPOperationType());

        switch (ftpCommand.getFTPOperationType()) {
            case GET_CONFIG_DATA:
                handleGetConfigData(ftpCommand, fsuInfo);
                break;
            case UPLOAD_FILE:
                handleUploadFile(ftpCommand, fsuInfo);
                break;
            case GET_IMAGE_FILES:
                handleGetImageFiles(ftpCommand, fsuInfo);
                break;
            case GET_ALARM_FILES:
                handleGetAlarmFiles(ftpCommand, fsuInfo);
                break;
            case GET_PERFORMANCE_DATA:
                handleGetPerformanceData(ftpCommand, fsuInfo);
                break;
            case GET_LOG_FILES:
                handleGetLogFiles(ftpCommand, fsuInfo);
                break;
            default:
                log.warn("不支持的FTP操作类型: {}", ftpCommand.getFTPOperationType());
                sendFTPCommandResponse(ftpCommand, false, "不支持的FTP操作类型", null);
                break;
        }
    }

    /**
     * 处理获取配置数据命令
     */
    private void handleGetConfigData(CmccFTPUserCommand ftpCommand, CMCCFsu fsuInfo) {
        cmccFTPHelper.getConfigDataAsync(fsuInfo)
                .thenAccept(configDataFiles -> {
                    log.info("获取配置数据成功: FSU={}, 文件数量={}",
                            ftpCommand.getGatewayId(), configDataFiles.size());
                    sendFTPCommandResponse(ftpCommand, true, "获取配置数据成功", configDataFiles);
                })
                .exceptionally(throwable -> {
                    log.error("获取配置数据失败: FSU={}", ftpCommand.getGatewayId(), throwable);
                    sendFTPCommandResponse(ftpCommand, false, "获取配置数据失败: " + throwable.getMessage(), null);
                    return null;
                });
    }

    /**
     * 处理上传文件命令
     */
    private void handleUploadFile(CmccFTPUserCommand ftpCommand, CMCCFsu fsuInfo) {
        if (!(ftpCommand instanceof UploadFileUserCommand uploadCmd)) {
            sendFTPCommandResponse(ftpCommand, false, "命令类型错误", null);
            return;
        }

        cmccFTPHelper.uploadFileAsync(fsuInfo, uploadCmd.getFileName(), uploadCmd.getFileData())
                .thenAccept(success -> {
                    if (success) {
                        log.info("文件上传成功: FSU={}, 文件={}",
                                ftpCommand.getGatewayId(), uploadCmd.getFileName());
                        sendFTPCommandResponse(ftpCommand, true, "文件上传成功", null);
                    } else {
                        log.warn("文件上传失败: FSU={}, 文件={}",
                                ftpCommand.getGatewayId(), uploadCmd.getFileName());
                        sendFTPCommandResponse(ftpCommand, false, "文件上传失败", null);
                    }
                })
                .exceptionally(throwable -> {
                    log.error("文件上传异常: FSU={}, 文件={}",
                            ftpCommand.getGatewayId(), uploadCmd.getFileName(), throwable);
                    sendFTPCommandResponse(ftpCommand, false, "文件上传异常: " + throwable.getMessage(), null);
                    return null;
                });
    }

    /**
     * 处理获取图像文件命令
     */
    private void handleGetImageFiles(CmccFTPUserCommand ftpCommand, CMCCFsu fsuInfo) {
        cmccFTPHelper.getImageFilesAsync(fsuInfo)
                .thenAccept(imageFiles -> {
                    log.info("获取图像文件成功: FSU={}, 文件数量={}",
                            ftpCommand.getGatewayId(), imageFiles.size());
                    sendFTPCommandResponse(ftpCommand, true, "获取图像文件成功", imageFiles);
                })
                .exceptionally(throwable -> {
                    log.error("获取图像文件失败: FSU={}", ftpCommand.getGatewayId(), throwable);
                    sendFTPCommandResponse(ftpCommand, false, "获取图像文件失败: " + throwable.getMessage(), null);
                    return null;
                });
    }

    /**
     * 处理获取告警文件命令
     */
    private void handleGetAlarmFiles(CmccFTPUserCommand ftpCommand, CMCCFsu fsuInfo) {
        if (!(ftpCommand instanceof GetAlarmFilesUserCommand alarmCmd)) {
            sendFTPCommandResponse(ftpCommand, false, "命令类型错误", null);
            return;
        }

        cmccFTPHelper.getAlarmFilesAsync(fsuInfo, alarmCmd.getDate())
                .thenAccept(alarmFiles -> {
                    log.info("获取告警文件成功: FSU={}, 日期={}, 文件数量={}",
                            ftpCommand.getGatewayId(), alarmCmd.getDate(), alarmFiles.size());
                    sendFTPCommandResponse(ftpCommand, true, "获取告警文件成功", alarmFiles);
                })
                .exceptionally(throwable -> {
                    log.error("获取告警文件失败: FSU={}, 日期={}",
                            ftpCommand.getGatewayId(), alarmCmd.getDate(), throwable);
                    sendFTPCommandResponse(ftpCommand, false, "获取告警文件失败: " + throwable.getMessage(), null);
                    return null;
                });
    }

    /**
     * 处理获取性能数据命令
     */
    private void handleGetPerformanceData(CmccFTPUserCommand ftpCommand, CMCCFsu fsuInfo) {
        if (!(ftpCommand instanceof GetPerformanceDataUserCommand perfCmd)) {
            sendFTPCommandResponse(ftpCommand, false, "命令类型错误", null);
            return;
        }

        cmccFTPHelper.getPerformanceDataAsync(fsuInfo, perfCmd.getDate())
                .thenAccept(perfFiles -> {
                    log.info("获取性能数据成功: FSU={}, 日期={}, 文件数量={}",
                            ftpCommand.getGatewayId(), perfCmd.getDate(), perfFiles.size());
                    sendFTPCommandResponse(ftpCommand, true, "获取性能数据成功", perfFiles);
                })
                .exceptionally(throwable -> {
                    log.error("获取性能数据失败: FSU={}, 日期={}",
                            ftpCommand.getGatewayId(), perfCmd.getDate(), throwable);
                    sendFTPCommandResponse(ftpCommand, false, "获取性能数据失败: " + throwable.getMessage(), null);
                    return null;
                });
    }

    /**
     * 处理获取日志文件命令
     */
    private void handleGetLogFiles(CmccFTPUserCommand ftpCommand, CMCCFsu fsuInfo) {
        if (!(ftpCommand instanceof GetLogFilesUserCommand logCmd)) {
            sendFTPCommandResponse(ftpCommand, false, "命令类型错误", null);
            return;
        }

        cmccFTPHelper.getLogFilesAsync(fsuInfo, logCmd.getDate())
                .thenAccept(logFiles -> {
                    log.info("获取日志文件成功: FSU={}, 日期={}, 文件数量={}",
                            ftpCommand.getGatewayId(), logCmd.getDate(), logFiles.size());
                    sendFTPCommandResponse(ftpCommand, true, "获取日志文件成功", logFiles);
                })
                .exceptionally(throwable -> {
                    log.error("获取日志文件失败: FSU={}, 日期={}",
                            ftpCommand.getGatewayId(), logCmd.getDate(), throwable);
                    sendFTPCommandResponse(ftpCommand, false, "获取日志文件失败: " + throwable.getMessage(), null);
                    return null;
                });
    }

    // ================================
    // 4. HTTP请求处理方法
    // ================================

    /**
     * 发送HTTP请求
     */
    private CompletableFuture<String> sendHttpRequest(String xmlContent, String url) {
        return HttpRequestUtil.processWithDirectHttp(
                xmlContent,
                url,
                HttpMethods.POST,
                ContentTypes.TEXT_XML_UTF8
        ).thenCompose(response -> {
            return response.entity().toStrict(30000, ClusterContext.getMaterializer())
                    .thenCompose(entity -> entity.getDataBytes()
                            .runFold("", (acc, chunk) -> acc + chunk.utf8String(), ClusterContext.getMaterializer())
                    );
        }).toCompletableFuture();
    }

    // ================================
    // 5. 工具方法和响应发送方法
    // ================================

    /**
     * 构建命令XML
     */
    private String buildCommandXml(CMCCSouthUserCommand command) {
        if (command instanceof TimeCheckUserCommand timeCheckCmd) {
            TTime syncTime = timeCheckCmd.getSyncTime() != null ?
                    timeCheckCmd.getSyncTime() : TTime.fromLocalDateTime(LocalDateTime.now());
            TimeCheckMessage timeCheckMessage = TimeCheckMessage.create(command.getGatewayId(), syncTime);
            timeCheckMessage.setTimestamp(LocalDateTime.now());
            return timeCheckMessage.toSURequestXml();

        } else if (command instanceof GetFsuInfoUserCommand) {
            GetFsuInfoMessage getFsuInfoMessage = GetFsuInfoMessage.create(command.getGatewayId());
            getFsuInfoMessage.setTimestamp(LocalDateTime.now());
            return getFsuInfoMessage.toSURequestXml();

        } else if (command instanceof RestartFsuUserCommand) {
            RestartFsuMessage restartFsuMessage = RestartFsuMessage.create(command.getGatewayId());
            restartFsuMessage.setTimestamp(LocalDateTime.now());
            return restartFsuMessage.toSURequestXml();

        } else if (command instanceof SetFtpUserCommand setFtpCmd) {
            SetFtpMessage setFtpMessage = SetFtpMessage.create(command.getGatewayId(),
                    setFtpCmd.getUserName(), setFtpCmd.getPassword());
            setFtpMessage.setTimestamp(LocalDateTime.now());
            return setFtpMessage.toSURequestXml();

        } else if (command instanceof GetFtpUserCommand) {
            GetFTPMessage getFtpMessage = GetFTPMessage.create(command.getGatewayId());
            getFtpMessage.setTimestamp(LocalDateTime.now());
            return getFtpMessage.toSURequestXml();

        } else if (command instanceof GetLoginInfoUserCommand) {
            GetLoginInfoMessage getLoginInfoMessage = GetLoginInfoMessage.create(command.getGatewayId());
            getLoginInfoMessage.setTimestamp(LocalDateTime.now());
            return getLoginInfoMessage.toSURequestXml();

        } else if (command instanceof SetLoginInfoUserCommand setLoginInfoCmd) {
            SetLoginInfoMessage setLoginInfoMessage = SetLoginInfoMessage.create(
                    setLoginInfoCmd.getUserName(), setLoginInfoCmd.getPassword());
            setLoginInfoMessage.setTimestamp(LocalDateTime.now());
            return setLoginInfoMessage.toSURequestXml();

        } else if (command instanceof UpdateFsuInfoIntervalUserCommand updateIntervalCmd) {
            String fsuId = updateIntervalCmd.isForAllFsu() ? "NULL" : command.getGatewayId();
            UpdateFsuInfoIntervalMessage updateIntervalMessage = UpdateFsuInfoIntervalMessage.create(
                    fsuId, updateIntervalCmd.getInterval());
            updateIntervalMessage.setTimestamp(LocalDateTime.now());
            return updateIntervalMessage.toSURequestXml();

        } else {
            throw new IllegalArgumentException("Unsupported command type: " + command.getClass().getSimpleName());
        }
    }

    private MobileBRequestMessage buildCommandXmlRawMessage(CMCCSouthUserCommand command) {
        if (command instanceof TimeCheckUserCommand timeCheckCmd) {
            TTime syncTime = timeCheckCmd.getSyncTime() != null ?
                    timeCheckCmd.getSyncTime() : TTime.fromLocalDateTime(LocalDateTime.now());
            TimeCheckMessage timeCheckMessage = TimeCheckMessage.create(command.getGatewayId(), syncTime);
            timeCheckMessage.setTimestamp(LocalDateTime.now());
            return timeCheckMessage;

        } else if (command instanceof GetFsuInfoUserCommand) {
            GetFsuInfoMessage getFsuInfoMessage = GetFsuInfoMessage.create(command.getGatewayId());
            getFsuInfoMessage.setTimestamp(LocalDateTime.now());
            return getFsuInfoMessage;

        } else if (command instanceof RestartFsuUserCommand) {
            RestartFsuMessage restartFsuMessage = RestartFsuMessage.create(command.getGatewayId());
            restartFsuMessage.setTimestamp(LocalDateTime.now());
            return restartFsuMessage;

        } else if (command instanceof SetFtpUserCommand setFtpCmd) {
            SetFtpMessage setFtpMessage = SetFtpMessage.create(command.getGatewayId(),
                    setFtpCmd.getUserName(), setFtpCmd.getPassword());
            setFtpMessage.setTimestamp(LocalDateTime.now());
            return setFtpMessage;

        } else if (command instanceof GetFtpUserCommand) {
            GetFTPMessage getFtpMessage = GetFTPMessage.create(command.getGatewayId());
            getFtpMessage.setTimestamp(LocalDateTime.now());
            return getFtpMessage;

        } else if (command instanceof GetLoginInfoUserCommand) {
            GetLoginInfoMessage getLoginInfoMessage = GetLoginInfoMessage.create(command.getGatewayId());
            getLoginInfoMessage.setTimestamp(LocalDateTime.now());
            return getLoginInfoMessage;

        } else if (command instanceof SetLoginInfoUserCommand setLoginInfoCmd) {
            SetLoginInfoMessage setLoginInfoMessage = SetLoginInfoMessage.create(
                    setLoginInfoCmd.getUserName(), setLoginInfoCmd.getPassword());
            setLoginInfoMessage.setTimestamp(LocalDateTime.now());
            return setLoginInfoMessage;

        } else if (command instanceof UpdateFsuInfoIntervalUserCommand updateIntervalCmd) {
            String fsuId = updateIntervalCmd.isForAllFsu() ? "NULL" : command.getGatewayId();
            UpdateFsuInfoIntervalMessage updateIntervalMessage = UpdateFsuInfoIntervalMessage.create(
                    fsuId, updateIntervalCmd.getInterval());
            updateIntervalMessage.setTimestamp(LocalDateTime.now());
            return updateIntervalMessage;
        } else if (command instanceof  PullFsuDevConfUserCommand pullFsuDevConfUserCommand){
            return new GetDevConfMessage(pullFsuDevConfUserCommand.getGatewayId());
        }else {
            throw new IllegalArgumentException("Unsupported command type: " + command.getClass().getSimpleName());
        }
    }

    /**
     * 根据协议类型解析响应消息
     */
    private MobileBResponseMessage parseResponseByType(MobileBRawMessage rawMessage, PK_TypeName pkType) {
        switch (pkType) {
            case GET_FSUINFO_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, GetFsuInfoAckMessage.class);
            case GET_FTP_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, GetFTPAckMessage.class);
            case SET_FTP_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, SetFtpAckMessage.class);
            case TIME_CHECK_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, TimeCheckAckMessage.class);
            case GET_LOGININFO_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, GetLoginInfoAckMessage.class);
            case SET_LOGININFO_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, SetLoginInfoAckMessage.class);
            case UPDATE_FSUINFO_INTERVAL_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, UpdateFsuInfoIntervalAckMessage.class);
            case SET_FSUREBOOT_ACK:
                return CMCCSerializer.parseAckMessage(rawMessage, RestartFsuAckMessage.class);
            default:
                return null;
        }
    }

    /**
     * 发送用户命令响应
     *
     * @return 是否成功发送响应
     */
    private boolean sendUserCommandResponse(MobileBResponseMessage message, ActorRef commandSender) {
        if (commandSender != null) {
            UserCommandResponse response;
            if (EnumResult.SUCCESS.equals(message.getInfo().getResult())) {
                response = UserCommandResponse.success(message.getClass().getSimpleName() + "成功", message.getInfo());
            } else {
                response = UserCommandResponse.failure(message.getClass().getSimpleName() + "失败: " +
                        (ObjectUtil.isNotNull(message) ? message.getInfo().getFailureCause() : "处理失败"));
            }
            commandSender.tell(response, ActorRef.noSender());
            return true;
        }
        return false;
    }

    /**
     * 发送FTP命令响应
     *
     * @param ftpCommand FTP命令
     * @param success    是否成功
     * @param message    响应消息
     * @param data       响应数据
     */
    private void sendFTPCommandResponse(CmccFTPUserCommand ftpCommand, boolean success, String message, Object data) {
        if (ftpCommand.getSender() != null) {
            UserCommandResponse response;
            if (success) {
                response = UserCommandResponse.success(message, data);
            } else {
                response = UserCommandResponse.failure(message);
            }

            ftpCommand.getSender().tell(response, ActorRef.noSender());
            log.debug("已发送FTP命令响应: FSU={}, 成功={}", ftpCommand.getGatewayId(), success);
        }
    }
}
