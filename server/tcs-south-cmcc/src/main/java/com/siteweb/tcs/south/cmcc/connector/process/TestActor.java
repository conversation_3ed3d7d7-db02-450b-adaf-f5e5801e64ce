package com.siteweb.tcs.south.cmcc.connector.process;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;

import java.util.Optional;

/**
 * <AUTHOR> (2025-07-15)
 **/
@Slf4j
public class TestActor extends AbstractActor {

    public TestActor() {
        log.error("[TestActor] => Constructor");
    }


    @Override
    public void preStart() throws Exception {
        log.error("[TestActor] => PreStart");
        super.preStart();
    }


    @Override
    public void preRestart(Throwable reason, Optional<Object> message) throws Exception {
        log.error("[TestActor] => PreRestart");
        super.preRestart(reason, message);
    }

    @Override
    public void postRestart(Throwable reason) throws Exception {
        log.error("[TestActor] => PostRestart");
        super.postRestart(reason);
    }

    @Override
    public void postStop() throws Exception {
        log.error("[TestActor] => PostStop");
        super.postStop();
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder().matchAny(this::handleMessage).build();
    }

    private void handleMessage(Object nsg) {
        throw new RuntimeException("测试");
    }


}
