package com.siteweb.tcs.south.cmcc.connector.protocol;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * <AUTHOR> (2025-05-08)
 **/
public enum EnumFailureCode {
    USERNAME_ERROR(1, "用户名错"),
    PASSWORD_ERROR(2, "密码错"),
    SUID_ERROR(3, "错误的SUID"),
    DEVICEID_ERROR(4, "错误的广义设备ID"),


    NOT_AUTHORIZED(1,"FSU未授权，先使用LOGIN登录."),
    NOT_CONNECTED(1,"FSU未在允许访问列表."),

    INTERNAL_SERVER_ERROR(1, "内部服务器错误"),


    SPID_ERROR(5, "错误的SPID"),
    IP_ERROR(6, "IP错误"),
    NOFILE_ERROR(7, "没有文件"),
    CONFIG_CHECK_ERROR(8, "配置验证失败"),
    CONFIG_ERROR(9, "配置方案选项值超出范围"),
    DATA_FORMAT_ERROR(10, "数据格式错"),
    CTRL_TIMEOUT(11, "控制超时"),
    CTRL_PARA_ERROR(12, "控制参数错"),
    IP_OUTOFACL_ERROR(13, "IP不在ACL范围"),
    NOFILEDIR_ERROR(14, "文件目录不存在"),
    AlarmTime_NULL(15, "告警开始时间为空或者告警开始时间的年份错误"),
    AlarmTrigger_NULL(16, "告警触发值为空"),
    AlarmLevel_NULL(17, "告警级别为空或者不规范"),
    AlarmFlag_NULL(18, "告警标识为空或不规范"),
    AlarmNum_ERROR(19, "一条报文包含超过约定数量的告警信息或者报文中的告警信息为空"),
    AlarmSN_ERROR(20, "告警序号为空或者其长度不等于10位数字或者大于4294967295（此数据为十进制整数的最大值，若大于会溢出）"),
    Alarm_ERROR(21, "告警标识为END，但告警结束时间为空"),
    AlarmSendingFailed(22, "FSU向SC发送告警失败"),
    AlarmFailed(23, "SU存在发送失败的告警"),
    OTHER_ERROR(9999, "其他错误");

    private final int code;
    private final String description;

    EnumFailureCode(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 静态方法，通过错误码获取枚举实例
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static EnumFailureCode getByCode(int code) {
        for (EnumFailureCode failureCode : EnumFailureCode.values()) {
            if (failureCode.getCode() == code) {
                return failureCode;
            }
        }
        return null;
    }
}