package com.siteweb.tcs.south.cmcc.connector.protocol;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预制 PK_TYPE
 *
 * <AUTHOR> (2025-05-08)
 **/

@Data
@NoArgsConstructor
public class PK_Type {

    @JsonProperty("Name")
    @JacksonXmlProperty(localName = "Name")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private PK_TypeName name;

}