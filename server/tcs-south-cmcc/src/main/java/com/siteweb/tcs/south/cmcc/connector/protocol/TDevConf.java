package com.siteweb.tcs.south.cmcc.connector.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import java.util.List;

/**
 * 监控对象配置信息
 */
@Data
public class TDevConf {
    /**
     * 设备ID
     * 长度为DEVICEID_LEN (26字节)
     */
    @JsonProperty("DeviceID")
    @JacksonXmlProperty(localName = "DeviceID", isAttribute = true)
    private String deviceId;
    
    /**
     * 设备名称
     * 长度为NAME_LENGTH (256字节)
     * 定义参考中国移动动环命名及编码指导意见
     */
    @JsonProperty("DeviceName")
    @JacksonXmlProperty(localName = "DeviceName", isAttribute = true)
    private String deviceName;




    /**
     * 设备所在的站点名称
     * 长度为NAME_LENGTH (256字节)
     * 定义参考中国移动动环命名及编码指导意见1.1
     */
    @JsonProperty("SiteID")
    @JacksonXmlProperty(localName = "SiteID", isAttribute = true)
    private String siteID;

    /**
     * 设备所在的机房名称
     * 长度为NAME_LENGTH (256字节)
     * 定义参考中国移动动环命名及编码指导意见1.2
     */
    @JsonProperty("RoomID")
    @JacksonXmlProperty(localName = "RoomID", isAttribute = true)
    private String roomID;


    /**
     * 设备所在的站点名称
     * 长度为NAME_LENGTH (256字节)
     * 定义参考中国移动动环命名及编码指导意见1.1
     */
    @JsonProperty("SiteName")
    @JacksonXmlProperty(localName = "SiteName", isAttribute = true)
    private String siteName;
    
    /**
     * 设备所在的机房名称
     * 长度为NAME_LENGTH (256字节)
     * 定义参考中国移动动环命名及编码指导意见1.2
     */
    @JsonProperty("RoomName")
    @JacksonXmlProperty(localName = "RoomName", isAttribute = true)
    private String roomName;
    
    /**
     * 设备类型（按动环标准化定义）EnumDeviceType
     */
    @JsonProperty("DeviceType")
    @JacksonXmlProperty(localName = "DeviceType", isAttribute = true)
    private String deviceType;
    
    /**
     * 设备子类型（按动环标准化定义）EnumDeviceSubType
     */
    @JsonProperty("DeviceSubType")
    @JacksonXmlProperty(localName = "DeviceSubType", isAttribute = true)
    private String deviceSubType;
    
    /**
     * 设备型号
     * 长度为DES_LENGTH (120字节)
     */
    @JsonProperty("Model")
    @JacksonXmlProperty(localName = "Model", isAttribute = true)
    private String model;
    
    /**
     * 设备品牌
     * 长度为DES_LENGTH (120字节)
     */
    @JsonProperty("Brand")
    @JacksonXmlProperty(localName = "Brand", isAttribute = true)
    private String brand;
    
    /**
     * 额定容量
     */
    @JsonProperty("RatedCapacity")
    @JacksonXmlProperty(localName = "RatedCapacity", isAttribute = true)
    private Float ratedCapacity;
    
    /**
     * 版本
     * 长度为VER_LENGTH (20字节)
     */
    @JsonProperty("Version")
    @JacksonXmlProperty(localName = "Version", isAttribute = true)
    private String version;
    
    /**
     * 启用时间
     * 长度为TIME_LEN (19字节)
     */
    @JsonProperty("BeginRunTime")
    @JacksonXmlProperty(localName = "BeginRunTime", isAttribute = true)
    private String beginRunTime;
    
    /**
     * 设备描述信息（包含设备的安装位置）
     * 长度为DES_LENGTH (120字节)
     */
    @JsonProperty("DevDescribe")
    @JacksonXmlProperty(localName = "DevDescribe", isAttribute = true)
    private String devDescribe;
    
    /**
     * 一个或多个监控点信号配置信息
     */
    @JacksonXmlElementWrapper(localName = "Signals")
    @JsonProperty("Signal")
    @JacksonXmlProperty(localName = "Signal")
    private List<TSignal> signals;



    @JsonProperty("ConfRemark")
    @JacksonXmlProperty(localName = "ConfRemark", isAttribute = true)
    private String confRemark;


}
