package com.siteweb.tcs.south.cmcc.connector.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBResponseMessage;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> (2025-07-28)
 **/

@Setter
@Getter
public class TFSUResponseResult extends MobileBResponseMessage.StandardResponseInfo {
    /**
     * FSU ID号
     */
    @JsonProperty("FSUID")
    @JacksonXmlProperty(localName = "FSUID")
    private String fsuId;

    @JsonProperty("DeviceList")
    @JacksonXmlElementWrapper(localName = "FailList")
    @JacksonXmlProperty(localName = "Device")
    private List<TDeviceResponseResult> devices ;
}