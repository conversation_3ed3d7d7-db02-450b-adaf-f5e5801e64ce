package com.siteweb.tcs.south.cmcc.connector.services;

import com.siteweb.tcs.south.cmcc.connector.protocol.EnumType;
import com.siteweb.tcs.south.cmcc.connector.protocol.TDevConf;
import com.siteweb.tcs.south.cmcc.connector.protocol.TSignal;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCAlarm;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCControl;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCSignal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (2025-07-24)
 **/
@Slf4j
public class CmccConfigParser {

    private final String _fsuId;
    private final Map<String, CMCCDevice> _readonlyDeviceMap;

    public CmccConfigParser(String fsuId, Map<String, CMCCDevice> readonlyDeviceMap) {
        this._fsuId = fsuId;
        this._readonlyDeviceMap = readonlyDeviceMap;
    }


    /**
     * 将上送的配置归一化，解析为CMCCDevice格式并填充已有的本地数据
     *
     * @param sourceConf
     * @return
     */
    public CMCCDevice parseCmccDevice(TDevConf sourceConf) {
        CMCCDevice descDevice = new CMCCDevice();
        var deviceId = sourceConf.getDeviceId();
        var localDevice = _readonlyDeviceMap.get(sourceConf.getDeviceId());
        Map<String, CMCCSignal> localSignalMap = new HashMap<>();
        Map<String, CMCCAlarm> localAlarmMap = new HashMap<>();
        Map<String, CMCCControl> localControlMap = new HashMap<>();
        if (localDevice != null) {
            BeanUtils.copyProperties(localDevice, descDevice);
            localSignalMap = localDevice.getSignalList().stream().collect(Collectors.toMap(CMCCSignal::getSpId, v -> v));
            localAlarmMap = localDevice.getAlarmList().stream().collect(Collectors.toMap(CMCCAlarm::getSpId, v -> v));
            localControlMap = localDevice.getControlList().stream().collect(Collectors.toMap(CMCCControl::getSpId, v -> v));
        }
        descDevice.setFsuId(_fsuId);
        descDevice.setDeviceId(sourceConf.getDeviceId());
        descDevice.setDeviceName(sourceConf.getDeviceName());
        descDevice.setSiteName(sourceConf.getSiteName());
        descDevice.setRoomName(sourceConf.getRoomName());
        descDevice.setDeviceType(sourceConf.getDeviceType());
        descDevice.setDeviceSubType(sourceConf.getDeviceSubType());
        descDevice.setBrand(sourceConf.getBrand());
        descDevice.setModel(sourceConf.getModel());
        descDevice.setVersion(sourceConf.getVersion());
        descDevice.setDevDescribe(sourceConf.getDevDescribe());
        descDevice.setBeginRunTime(sourceConf.getBeginRunTime());
        // Initialize lists
        List<CMCCSignal> signals = new ArrayList<>();
        List<CMCCAlarm> alarms = new ArrayList<>();
        List<CMCCControl> controls = new ArrayList<>();

        // Set lists to device
        descDevice.setSignalList(signals);
        descDevice.setAlarmList(alarms);
        descDevice.setControlList(controls);

        for (var tSignal : sourceConf.getSignals()) {
            var spType = EnumType.tryParse(tSignal.getType());
            if (EnumType.INVALID.equals(spType)) {
                log.error("设备SP类型错误，{}为EnumType的无效类型值", tSignal.getType());
                continue;
            }
            var spId = tSignal.getId() + tSignal.getSignalNumber();
            switch (spType) {
                case AI, DI: { // 信号
                    var signal = parseCMCCSignal(tSignal, spType, localSignalMap.get(spId), deviceId);
                    signals.add(signal);
                    break;
                }
                case AO, DO: { // 控制
                    var control = parseCMCCControl(tSignal, spType, localControlMap.get(spId), deviceId);
                    controls.add(control);
                    break;
                }
                case ALARM: { // 告警
                    var alarm = parseCMCCAlarm(tSignal, localAlarmMap.get(spId), deviceId);
                    alarms.add(alarm);
                    break;
                }
                default: {
                    log.warn("FSU：{},设备：{},信号点：{} 无效的SPType({})", _fsuId, deviceId, tSignal.getId(), spType);
                }
            }
        }
        return descDevice;
    }


    private CMCCAlarm parseCMCCAlarm(TSignal tSignal, CMCCAlarm localAlarm, String deviceId) {
        CMCCAlarm alarm = new CMCCAlarm();
        if (localAlarm != null) {
            BeanUtils.copyProperties(localAlarm, alarm);
        }
        alarm.setDeviceId(deviceId);
        alarm.setFsuId(_fsuId);
        alarm.setSpType(EnumType.ALARM);
        alarm.setOriginSpId(tSignal.getId());
        alarm.setSpId(tSignal.getId() + tSignal.getSignalNumber());
        alarm.setAlarmName(tSignal.getSignalName());
        alarm.setAlarmLevel(tSignal.getAlarmLevel());
        alarm.setSignalNumber(tSignal.getSignalNumber());
        alarm.setNmAlarmId(tSignal.getNmAlarmID());
        alarm.setThreshold(tSignal.getThreshold());

        alarm.setMeaning(tSignal.getSignalName());// TODO FILL ALARM STANDARD NAME
        return alarm;
    }


    private CMCCControl parseCMCCControl(TSignal tSignal, EnumType spType, CMCCControl localControl, String deviceId) {
        CMCCControl control = new CMCCControl();
        if (localControl != null) {
            BeanUtils.copyProperties(localControl, control);
        }
        control.setDeviceId(deviceId);
        control.setFsuId(_fsuId);
        control.setSpType(spType);
        control.setOriginSpId(tSignal.getId());
        control.setSpId(tSignal.getId() + tSignal.getSignalNumber());
        control.setControlName(tSignal.getSignalName());
        control.setSignalNumber(tSignal.getSignalNumber());
        // 开关量 解析含义
        if (EnumType.DO.equals(spType)) {
            Map<Integer, String> meanings = loadMeanings(control.getSpId(), tSignal.getDescribe(), "1&待命");
            control.setMeanings(meanings);
        }
        return control;
    }


    private CMCCSignal parseCMCCSignal(TSignal tSignal, EnumType spType, CMCCSignal localSignal, String deviceId) {
        CMCCSignal signal = new CMCCSignal();
        if (localSignal != null) {
            BeanUtils.copyProperties(localSignal, signal);
        }
        signal.setDeviceId(deviceId);
        signal.setFsuId(_fsuId);
        signal.setSpType(spType);
        signal.setOriginSpId(tSignal.getId());
        signal.setSpId(tSignal.getId() + tSignal.getSignalNumber());
        signal.setSignalName(tSignal.getSignalName());
        signal.setSignalNumber(tSignal.getSignalNumber());
        if (EnumType.AO.equals(spType)) {
            // 数字量 保存为含义
            Map<Integer, String> meanings = loadMeanings(signal.getSpId(), tSignal.getDescribe(), "0&0;1&1");
            signal.setMeanings(meanings);
        } else {
            // 模拟量保存为单位
            signal.setUnit(tSignal.getDescribe());
        }

        return signal;
    }


    /**
     * 解析 Meanings</br>
     * 1. 优先从本地的Dic表中取</br>
     * 2. DIC表没有 使用describe</br>
     * 3. describe没有使用 defaultMeanings</br>
     *
     * @param spId
     * @param describe
     * @param defaultMeanings
     * @return
     */
    private Map<Integer, String> loadMeanings(String spId, String describe, String defaultMeanings) {
//        var str = dicMeanings.get(spId);
        if (describe == null) describe = defaultMeanings;
        Map<Integer, String> meanings = parseMeanings(describe);
        return meanings;
    }


    private Map<Integer, String> parseMeanings(String describe) {
        Map<Integer, String> result = new HashMap<>();
        if (describe == null || describe.isEmpty()) return result;
        var values = describe.split(";");
        for (String val : values) {
            var kv = val.split("&");
            if (kv.length != 2) continue;
            var key = Integer.parseInt(kv[0]);
            result.put(key, kv[1]);
        }
        return result;
    }

}
