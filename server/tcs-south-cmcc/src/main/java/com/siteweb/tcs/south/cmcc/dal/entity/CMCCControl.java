package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * CMCC控制点信息实体
 * <AUTHOR> (2025-05-16)
 **/
@Data
@NoArgsConstructor
@TableName("cmcc_controls")
public class CMCCControl implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * FSU ID
     */
    @TableField("fsu_id")
    private String fsuId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;


    /**
     * 控制点类型
     */
    @TableField("sp_type")
    private EnumType spType;

    /**
     * 控制点ID 带signalNumber
     */
    @TableField("sp_id")
    private String spId;

    /**
     * 原始SPID 不带signalNumber
     */
    @TableField("origin_sp_id")
    private String originSpId;

    /**
     *
     */
    @TableField("sp_hub_id")
    private Long spHubId;


    /**
     * 控制点名称
     */
    @TableField("control_name")
    private String controlName;

    /**
     * 控制含义
     */
    @TableField("meanings")
    private Map<Integer,String> meanings = new HashMap<>();


    @TableField("signal_number")
    private String signalNumber;

    /**
     * 最大值
     */
    @TableField("max_value")
    private Double maxValue;

    /**
     * 最小值
     */
    @TableField("min_value")
    private Double minValue;

}
