package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationObject;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-08-04)
 **/
@Data
@TableName("cmcc_operation_log")
public class CMCCOperationLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("op_time")
    private LocalDateTime time;

    @TableField("user_id")
    private Integer userId;

    @TableField("username")
    private String userName;

    @TableField("object_type")
    private OperationObject objectType;

    @TableField("object_id")
    private String objectId;

    @TableField("op_type")
    private OperationType operationType;

    @TableField("description")
    private String description;

}
