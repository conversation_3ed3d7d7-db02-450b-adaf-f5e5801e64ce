package com.siteweb.tcs.south.cmcc.dal.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR> (2025-07-16)
 **/
public enum ConfigSyncEnum implements IEnum<String> {
    /**
     * 禁用配置同步
     */
    DISABLED("DISABLED", "禁用配置同步"),
    /**
     * 仅允许从上往下
     */
    TOP_DOWN("TOP_DOWN", "仅允许从上往下"),
    /**
     * 仅允许从下往上
     */
    DOWN_TOP("DOWN_TOP", "仅允许从下往上"),

    ;

    @Getter
    @JsonValue
    @EnumValue
    private final String value;

    @Getter
    private final String description;

    ConfigSyncEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }


    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ConfigSyncEnum fromValue(String value) {
        for (ConfigSyncEnum status : ConfigSyncEnum.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + value);
    }

}
