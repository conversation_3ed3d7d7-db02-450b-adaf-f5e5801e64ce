package com.siteweb.tcs.south.cmcc.dal.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR> (2025-05-16)
 **/
public enum FTPType implements IEnum<Integer> {
    FTP(0,"普通FTP"),
    SFTP(1,"SSH FTP"),
    ;

    @Getter
    @JsonValue
    @EnumValue
    private final int code;

    @Getter
    private final String description;

    FTPType(int code, String description) {
        this.code = code;
        this.description = description;
    }


    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static FTPType fromValue(int i) {
        for (FTPType status : FTPType.values()) {
            if (status.code ==i) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }

    @Override
    public Integer getValue() {
        return this.code;
    }

}
