package com.siteweb.tcs.south.cmcc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCControl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> (2025-05-21)
 **/
@Mapper
public interface CMCCControlMapper extends BaseMapper<CMCCControl> {
    int insertBatch(@Param("list") List<CMCCControl> controlList);

    int updateBatch(@Param("list") List<CMCCControl> controlList);

    CMCCControl findControlFormDevice(String fsuId, String deviceId, String controlId);
}
