package com.siteweb.tcs.south.cmcc.exception;

import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;

/**
 * CMCC 协议技术错误
 * <AUTHOR> (2025-07-02)
 **/
public enum CMCCTechnicalErrorCode implements TechnicalErrorCode {

    CMCC_DESC_HTTP_HOST_ERROR("CMCC_DESC_HTTP_HOST_ERROR","Http请求失败，可能时网络问题或主机问题"),

    CMCC_INVALID_MESSAGE("CMCC_INVALID_MESSAGE", "无效的CMCC报文"),

    CMCC_NULL_MESSAGE("CMCC_NULL_MESSAGE", "空的CMCC报文"),

    CMCC_PARSE_MESSAGE_ERROR("CMCC_PARSE_MESSAGE_ERROR", "解析报文错误"),

    CMCC_INVALID_PK_TYPE("CMCC_INVALID_PK_TYPE", "无效的PK_Type")

    ;

    private final String code;
    private final String message;

    CMCCTechnicalErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
