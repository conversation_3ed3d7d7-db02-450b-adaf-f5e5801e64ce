package com.siteweb.tcs.south.cmcc.stream.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.EventMessage;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.tcs.south.cmcc.connector.letter.GetDataAckMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.GetDataMessage;
import com.siteweb.tcs.south.cmcc.stream.options.GetDataShapeOption;
import com.siteweb.tcs.south.cmcc.connector.CmccXmlHttpRequester;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.hub.domain.letter.LifeCycleEvent;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
@Shape(type = "cmcc-get-data")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeColor(bkColor = "#c0edc0")
//@ShapeDefaultOptions(SwitchDefaultOption.class)
@ShapeInlet(id = 0x01, type = EventMessage.class)
@ShapeInlet(id = 0x02, type = GetDataAckMessage.class)
public class GetDataShape extends AbstractShape {

    @Recoverable
    private GetDataShapeOption option;

    private final ActorRef pipeline;

    private final String fsuId;

    private final CmccXmlHttpRequester xmlHttpRequester;


    private boolean workIdle = true;


    public GetDataShape(ShapeRuntimeContext context) {
        super(context);
        pipeline = (ActorRef) context.getGraphOptions("pipelineActorRef");
        fsuId = (String) context.getGraphOptions("fsuId");
        xmlHttpRequester = (CmccXmlHttpRequester) context.getGraphOptions("xmlHttpRequester");
    }


    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof GetDataShapeOption getDataShapeOption) {
            option = getDataShapeOption;
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {
        if (in instanceof EventMessage event && this.workIdle) {
            sendRequestMessage();
        } else if (in instanceof GetDataAckMessage getDataAckMessage && !this.workIdle) {
            processGetDataAckMessage(getDataAckMessage);
        }
    }

    private void sendRequestMessage() {
        var getData = new GetDataMessage();
        this.workIdle = false;
        getData.setTimestamp(LocalDateTime.now());
        getData.getInfo().setFsuId(fsuId);
        xmlHttpRequester.send(getData,GetDataAckMessage.class)
                .whenComplete((ack, throwable) -> {
                    if (throwable != null) {
                        log.error("[{}] GET_DATA 返回数据异常: {} ", fsuId, throwable.getMessage());
                        this.workIdle = true;
                        return;
                    }
                    // 转发给自己处理，不要在异步内处理数据
                    self().tell(ack, ActorRef.noSender());
                });
    }



    private void processGetDataAckMessage(GetDataAckMessage getDataAckMessage) {
        this.workIdle = true;
        var info = getDataAckMessage.getInfo();
        if (EnumResult.SUCCESS.equals(info.getResult())) {
            var devices = info.getValues().getDeviceList();
            for (var device : devices) {
                //TODO  实时数据的 LifeCycleEvent
                LifeCycleEvent event = new LifeCycleEvent();
                event.setThingType(ThingType.SIGNAL);
                event.setEventType(LifeCycleEventType.FIELD_UPDATE);
                event.setForeignGatewayId(fsuId);
                event.setForeignDeviceId(device.getId());
                event.setForeignConfigChange(device.getSignal());
                pipeline.tell(device, self());
            }
        } else {
            log.error("[{}] GET_DATA_ACK 失败：{}", fsuId, info.getFailureCause());
        }
    }


}
