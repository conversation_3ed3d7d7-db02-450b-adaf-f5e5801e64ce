package com.siteweb.tcs.south.cmcc.util;

import com.siteweb.tcs.common.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;
import java.util.List;

/**
 * CMCC插件文件操作工具类
 * 封装 tcs-common 的 FileUtil，自动添加插件工作区路径前缀
 * 所有文件操作都会在路径前添加 ${pluginId}/workspace
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Slf4j
@Component
public class CmccFileUtil {

    @Autowired
    private FileUtil fileUtil;

    @Value("${plugin.id}")
    private String pluginId;

    /**
     * 构建完整的文件路径
     * 在用户提供的路径前添加 pluginId/workspace 前缀
     * 
     * @param userPath 用户提供的相对路径
     * @return 完整的文件路径
     */
    private String buildFullPath(String userPath) {
        // 确保路径格式正确
        String workspacePath = "plugins/" + pluginId + "/workspace";
        
        // 处理用户路径，移除开头的斜杠（如果有的话）
        String cleanUserPath = userPath;
        if (cleanUserPath.startsWith("/")) {
            cleanUserPath = cleanUserPath.substring(1);
        }
        
        // 构建完整路径
        String fullPath = Paths.get(workspacePath, cleanUserPath).toString().replace("\\", "/");
        
        log.debug("构建文件路径: {} -> {}", userPath, fullPath);
        return fullPath;
    }

    /**
     * 写入文件到插件工作区
     * 
     * @param path 相对于插件工作区的路径
     * @param fileName 文件名
     * @param content 文件内容
     * @return 是否成功
     */
    public boolean writeFile(String path, String fileName, byte[] content) {
        String fullPath = buildFullPath(path);
        log.debug("CMCC插件写入文件: {}/{}", fullPath, fileName);
        return fileUtil.writeFile(fullPath, fileName, content);
    }

    /**
     * 从插件工作区读取文件
     * 
     * @param path 相对于插件工作区的路径
     * @param fileName 文件名
     * @return 文件内容，不存在时返回null
     */
    public byte[] readFile(String path, String fileName) {
        String fullPath = buildFullPath(path);
        log.debug("CMCC插件读取文件: {}/{}", fullPath, fileName);
        return fileUtil.readFile(fullPath, fileName);
    }

    /**
     * 从插件工作区删除文件
     * 
     * @param path 相对于插件工作区的路径
     * @param fileName 文件名
     * @return 是否成功
     */
    public boolean deleteFile(String path, String fileName) {
        String fullPath = buildFullPath(path);
        log.debug("CMCC插件删除文件: {}/{}", fullPath, fileName);
        return fileUtil.deleteFile(fullPath, fileName);
    }

    /**
     * 检查插件工作区中的文件是否存在
     * 
     * @param path 相对于插件工作区的路径
     * @param fileName 文件名
     * @return 是否存在
     */
    public boolean fileExists(String path, String fileName) {
        String fullPath = buildFullPath(path);
        return fileUtil.fileExists(fullPath, fileName);
    }

    /**
     * 在插件工作区中创建目录
     * 
     * @param path 相对于插件工作区的路径
     * @return 是否成功
     */
    public boolean createDirectory(String path) {
        String fullPath = buildFullPath(path);
        log.debug("CMCC插件创建目录: {}", fullPath);
        return fileUtil.createDirectory(fullPath);
    }

    /**
     * 列出插件工作区中目录的文件
     * 
     * @param path 相对于插件工作区的路径
     * @return 文件名列表
     */
    public List<String> listFiles(String path) {
        String fullPath = buildFullPath(path);
        log.debug("CMCC插件列出文件: {}", fullPath);
        return fileUtil.listFiles(fullPath);
    }

    /**
     * 在插件工作区中复制文件
     * 
     * @param sourcePath 源文件相对路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件相对路径
     * @param targetFileName 目标文件名
     * @return 是否成功
     */
    public boolean copyFile(String sourcePath, String sourceFileName, String targetPath, String targetFileName) {
        String fullSourcePath = buildFullPath(sourcePath);
        String fullTargetPath = buildFullPath(targetPath);
        log.debug("CMCC插件复制文件: {}/{} -> {}/{}", fullSourcePath, sourceFileName, fullTargetPath, targetFileName);
        return fileUtil.copyFile(fullSourcePath, sourceFileName, fullTargetPath, targetFileName);
    }

    /**
     * 在插件工作区中移动文件
     * 
     * @param sourcePath 源文件相对路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件相对路径
     * @param targetFileName 目标文件名
     * @return 是否成功
     */
    public boolean moveFile(String sourcePath, String sourceFileName, String targetPath, String targetFileName) {
        String fullSourcePath = buildFullPath(sourcePath);
        String fullTargetPath = buildFullPath(targetPath);
        log.debug("CMCC插件移动文件: {}/{} -> {}/{}", fullSourcePath, sourceFileName, fullTargetPath, targetFileName);
        return fileUtil.moveFile(fullSourcePath, sourceFileName, fullTargetPath, targetFileName);
    }

    /**
     * 删除插件工作区中的目录
     * 
     * @param path 相对于插件工作区的路径
     * @param recursive 是否递归删除
     * @return 是否成功
     */
    public boolean deleteDirectory(String path, boolean recursive) {
        String fullPath = buildFullPath(path);
        log.debug("CMCC插件删除目录: {}, recursive: {}", fullPath, recursive);
        return fileUtil.deleteDirectory(fullPath, recursive);
    }

    // ==================== 便捷方法 ====================

    /**
     * 写入文本文件到插件工作区
     * 
     * @param path 相对于插件工作区的路径
     * @param fileName 文件名
     * @param content 文本内容
     * @return 是否成功
     */
    public boolean writeTextFile(String path, String fileName, String content) {
        return writeFile(path, fileName, content.getBytes());
    }

    /**
     * 从插件工作区读取文本文件
     * 
     * @param path 相对于插件工作区的路径
     * @param fileName 文件名
     * @return 文本内容，不存在时返回null
     */
    public String readTextFile(String path, String fileName) {
        byte[] content = readFile(path, fileName);
        return content != null ? new String(content) : null;
    }

    /**
     * 获取插件工作区的根路径
     * 
     * @return 插件工作区根路径
     */
    public String getWorkspaceRoot() {
        return pluginId + "/workspace";
    }

    /**
     * 获取相对于插件工作区的完整路径（用于调试或日志）
     * 
     * @param relativePath 相对路径
     * @return 完整路径
     */
    public String getFullPath(String relativePath) {
        return buildFullPath(relativePath);
    }

    /**
     * 确保插件工作区根目录存在
     * 
     * @return 是否成功
     */
    public boolean ensureWorkspaceExists() {
        return createDirectory("");
    }
}
