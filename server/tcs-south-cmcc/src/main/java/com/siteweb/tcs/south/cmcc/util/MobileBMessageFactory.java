package com.siteweb.tcs.south.cmcc.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.tcs.south.cmcc.connector.letter.*;
import com.siteweb.tcs.south.cmcc.exception.CMCCTechnicalErrorCode;
import com.siteweb.tcs.common.util.DataMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;

/**
 * 匿名的消息解析器
 *
 * <AUTHOR> (2025-05-08)
 **/
@Slf4j
public class MobileBMessageFactory {


    public static MobileBRequestMessage parseMobileBMessage(MobileBRawMessage rawMessage) throws JsonProcessingException {
        return (MobileBRequestMessage) parseMobileBMessage(rawMessage.getContent());
    }


    public static MobileBMessage parseMobileBMessage(String xml) throws JsonProcessingException {
        // 解析JSON字符串为TeleBMessage对象
        var unescapeXml = StringEscapeUtils.unescapeXml(xml);
        if (unescapeXml == null) {
            log.info("");
        }
        var pkType = SoapHelper.parseXmlPkType(unescapeXml);
        if (pkType == null) throw CMCCTechnicalErrorCode.CMCC_INVALID_MESSAGE.toException();
        String xmlData = "";
        if (pkType.isAck()) {
            xmlData = SoapHelper.getResponsePayload(xml);
        } else {
            xmlData = SoapHelper.getRequestPayload(xml);
        }
        return switch (pkType) {
            // 实时数据上送
            case SEND_DATA -> DataMapper.fromXml(xmlData, SendDataMessage.class);
            case SEND_DATA_ACK -> DataMapper.fromXml(xmlData, SendDataAckMessage.class);
            // 请求实时数据
            case GET_DATA -> DataMapper.fromXml(xmlData, GetDataMessage.class);
            case GET_DATA_ACK -> DataMapper.fromXml(xmlData, GetDataAckMessage.class);
            // 告警上送
            case SEND_ALARM -> DataMapper.fromXml(xmlData, SendAlarmMessage.class);
            case SEND_ALARM_ACK -> DataMapper.fromXml(xmlData, SendAlarmAckMessage.class);

            default -> throw CMCCTechnicalErrorCode.CMCC_INVALID_PK_TYPE.toException(pkType.name());
        };
    }


}
