package com.siteweb.tcs.south.cmcc.util;

import com.siteweb.tcs.south.cmcc.exception.CMCCTechnicalErrorCode;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import org.apache.commons.text.StringEscapeUtils;

/**
 * 操作WebService SOAP 报文
 * <AUTHOR> (2025-05-08)
 **/
public class SoapHelper {

    private static final String SOAP_SC_RESPONSE_MESSAGE_START = "<SOAP-ENV:Envelope\n" +
            "    xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\"\n" +
            "    xmlns:SOAP-ENC=\"http://schemas.xmlsoap.org/soap/encoding/\"\n" +
            "    xmlns:ns1=\"http://LSCService.chinamobile.com\"\n" +
            "    xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"\n" +
            "    xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
            "    \n" +
            "    <SOAP-ENV:Body SOAP-ENV:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\n" +
            "        <ns1:invokeResponse>\n" +
            "            <invokeReturn>" +
            "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
            "";
    private static final String SOAP_SC_RESPONSE_MESSAGE_END = "" +
            "</invokeReturn>\n" +
            "        </ns1:invokeResponse>\n" +
            "    </SOAP-ENV:Body>\n" +
            "</SOAP-ENV:Envelope>";


    private static final String SOAP_SU_REQUEST_MESSAGE_START = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<SOAP-ENV:Envelope \n" +
            "    xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" \n" +
            "    xmlns:SOAP-ENC=\"http://schemas.xmlsoap.org/soap/encoding/\" \n" +
            "    xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" \n" +
            "    xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" \n" +
            "    xmlns:ns1=\"http://FSUService.chinamobile.com\">\n" +
            "\n" +
            "    <SOAP-ENV:Body SOAP-ENV:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\n" +
            "        <ns1:invoke>\n" +
            "            <xmlData>" +
            "";
    private static final String SOAP_SU_REQUEST_MESSAGE_END = "" +
            "</xmlData>\n" +
            "        </ns1:invoke>\n" +
            "    </SOAP-ENV:Body>\n" +
            "</SOAP-ENV:Envelope>";




    private static final String FSU_NODE_START = "<FSUID>";
    private static final int FSU_NODE_START_LEN = FSU_NODE_START.length();
    private static final String FSU_NODE_END = "</FSUID>";

    private static final String PK_TYPE_NAME_START = "<Name>";
    private static final int PK_TYPE_NAME_START_LEN = PK_TYPE_NAME_START.length();
    private static final String PK_TYPE_NAME_END = "</Name>";

    /**
     * 获取报文内的FSUID
     * @param xml
     * @return
     */
    public static String parseXmlFSUId(String xml) {
        int start = xml.indexOf(FSU_NODE_START);
        if (start > -1) {
            int end = xml.indexOf(FSU_NODE_END, start);
            return xml.substring(start + FSU_NODE_START_LEN, end).trim();
        }
        throw CMCCTechnicalErrorCode.CMCC_INVALID_MESSAGE.toException();
    }

    /**
     * 获取报文内的PK_NAME
     * @param xml
     * @return
     */
    public static PK_TypeName parseXmlPkType(String xml) {
        int start = xml.indexOf(PK_TYPE_NAME_START);
        if (start > -1) {
            int end = xml.indexOf(PK_TYPE_NAME_END, start);
            var strEnum = xml.substring(start + PK_TYPE_NAME_START_LEN, end).trim();
            return PK_TypeName.valueOf(strEnum);
        }
        throw CMCCTechnicalErrorCode.CMCC_INVALID_MESSAGE.toException();
    }

    /**
     * 获取SOAP请求报文内容
     * @param soapMessage
     * @return
     * @throws RuntimeException
     */
    public static String getRequestPayload(String soapMessage)  {
        int startIndex = soapMessage.indexOf("<scs:xmlData>");
        int endIndex = soapMessage.indexOf("</scs:xmlData>");
        int skipLength = "<scs:xmlData>".length();
        if (startIndex == -1 || endIndex == -1) {
            startIndex = soapMessage.indexOf("<xmlData>");
            skipLength = "<xmlData>".length();
            startIndex = soapMessage.indexOf("<", startIndex + skipLength);
            endIndex = soapMessage.indexOf("</xmlData>");
        }
        if (endIndex != -1 && startIndex < endIndex) {
            var str = soapMessage.substring(startIndex, endIndex);
            return StringEscapeUtils.unescapeXml(str.translateEscapes());
        } else {
            throw CMCCTechnicalErrorCode.CMCC_INVALID_MESSAGE.toException();
        }
    }

    /**
     * 获取SOAP响应报文内容
     * @param soapMessage
     * @return
     * @throws RuntimeException
     */
    public static String getResponsePayload(String soapMessage)  {
        int startIndex = soapMessage.indexOf("<sus:invokeReturn>");
        if (startIndex == -1) {
            startIndex = soapMessage.indexOf("<invokeReturn>");
        }
        if (startIndex != -1) {
            startIndex += "<invokeReturn>".length();
            int endIndex = soapMessage.indexOf("</invokeReturn>", startIndex);
            if (endIndex != -1) {
                return StringEscapeUtils.unescapeXml(soapMessage.substring(startIndex, endIndex));
            }
        }
        throw CMCCTechnicalErrorCode.CMCC_INVALID_MESSAGE.toException();
    }


    /**
     * 组成SU的SOAP 请求报文
     * @param payload
     * @return
     */
    public static String packetToSuRequestSoap(String payload) {
        return SOAP_SU_REQUEST_MESSAGE_START + payload + SOAP_SU_REQUEST_MESSAGE_END;
    }


    /**
     * 组成SC的SOAP响应报文
     * @param payload
     * @return
     */
    public static String packetToSCResponseSoap(String payload) {
        return SOAP_SC_RESPONSE_MESSAGE_START + payload + SOAP_SC_RESPONSE_MESSAGE_END;
    }


}