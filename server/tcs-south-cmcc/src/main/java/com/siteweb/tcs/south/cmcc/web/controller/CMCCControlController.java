package com.siteweb.tcs.south.cmcc.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCControl;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCControlWithStandardization;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CMCC控制Controller
 * <AUTHOR> (2025-07-30)
 */
@Slf4j
@Api(tags = "CMCC控制管理")
@RestController
@RequestMapping("/cmcc-control")
public class CMCCControlController {

    @Autowired
    private ICMCCControlService cmccControlService;

    /**
     * 根据FSUid和DeviceId查询控制列表
     */
    @ApiOperation("根据FSUid和DeviceId查询控制列表")
    @GetMapping("/list")
    public ResponseEntity<ResponseResult> list(
            @ApiParam("FSU ID") @RequestParam(required = false) String fsuId,
            @ApiParam("设备ID") @RequestParam(required = false) String deviceId) {

        List<CMCCControl> result;
        if (StringUtils.hasText(fsuId) && StringUtils.hasText(deviceId)) {
            result = cmccControlService.listByFsuIdAndDeviceId(fsuId, deviceId);
        } else if (StringUtils.hasText(fsuId)) {
            result = cmccControlService.listByFsuId(fsuId);
        } else if (StringUtils.hasText(deviceId)) {
            result = cmccControlService.listByDeviceId(deviceId);
        } else {
            result = cmccControlService.list();
        }
        
        return ResponseHelper.successful(result);
    }

    /**
     * 根据FSUid和DeviceId分页查询控制
     */
    @ApiOperation("根据FSUid和DeviceId分页查询控制")
    @GetMapping("/page")
    public ResponseEntity<ResponseResult> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("FSU ID") @RequestParam(required = false) String fsuId,
            @ApiParam("设备ID") @RequestParam(required = false) String deviceId) {

        IPage<CMCCControl> result;
        if (StringUtils.hasText(fsuId) && StringUtils.hasText(deviceId)) {
            result = cmccControlService.pageByFsuIdAndDeviceId(current, size, fsuId, deviceId);
        } else if (StringUtils.hasText(fsuId)) {
            result = cmccControlService.pageByFsuId(current, size, fsuId);
        } else if (StringUtils.hasText(deviceId)) {
            result = cmccControlService.pageByDeviceId(current, size, deviceId);
        } else {
            result = cmccControlService.page(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(current, size));
        }
        
        return ResponseHelper.successful(result);
    }

    /**
     * 根据ID查询控制
     */
    @ApiOperation("根据ID查询控制")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseResult> getById(@ApiParam("控制ID") @PathVariable Long id) {
        CMCCControl result = cmccControlService.getControlById(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据FSUid和DeviceId查询控制列表（包含标准化信息）
     */
    @ApiOperation("根据FSUid和DeviceId查询控制列表（包含标准化信息）")
    @GetMapping("/standardization/list")
    public ResponseEntity<ResponseResult> listWithStandardization(
            @ApiParam("FSU ID") @RequestParam(required = false) String fsuId,
            @ApiParam("设备ID") @RequestParam(required = false) String deviceId) {
        List<CMCCControlWithStandardization> result = cmccControlService.listWithStandardizationByFsuIdAndDeviceId(fsuId, deviceId);
        return ResponseHelper.successful(result);
    }
}
