package com.siteweb.tcs.south.cmcc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsuInit;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCFsuInitDTO;
import com.siteweb.tcs.south.cmcc.web.dto.CmccPullInitConfigDTO;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCFsuInitService;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCConfigGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CMCC FSU初始化配置控制器
 * CMCC FSU Init Controller
 */
@Slf4j
@RestController
@RequestMapping("/fsu-init")
public class CMCCFsuInitController {
    
    private static final Logger log = LoggerFactory.getLogger(CMCCFsuInitController.class);

    @Autowired
    private ICMCCFsuInitService cmccFsuInitService;

    @Autowired
    private ICMCCConfigGeneratorService cmccConfigGeneratorService;

    /**
     * 根据FSU ID获取FSU配置信息
     * @param fsuId FSU唯一标识
     * @return FSU配置信息
     */
    @GetMapping(value = "/get/{fsuId}")
    public ResponseEntity<ResponseResult> getFsu(@PathVariable String fsuId) {
        try {
            CMCCFsuInit fsu = cmccFsuInitService.getByFsuId(fsuId);
            if (fsu != null) {
                return ResponseHelper.successful(fsu);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Error getting FSU: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 根据SiteWeb监控单元ID查询FSU配置及其设备信息
     * @param siteWebMuId SiteWeb监控单元ID
     * @return FSU配置DTO（包含设备列表）
     */
    @GetMapping(value = "/fsu-with-devices/{siteWebMuId}")
    public ResponseEntity<ResponseResult> getFsuWithDevicesBySiteWebMuId(@PathVariable Integer siteWebMuId) {
        try {
            CMCCFsuInitDTO result = cmccFsuInitService.getFsuWithDevicesBySiteWebMuId(siteWebMuId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Error getting FSU with devices by SiteWeb monitor unit ID: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 根据站点ID获取FSU列表
     * @param siteId 站点ID
     * @return FSU列表
     */
    @GetMapping(value = "/list-by-site/{siteId}")
    public ResponseEntity<ResponseResult> listFsusBySiteId(@PathVariable String siteId) {
        try {
            List<CMCCFsuInit> fsus = cmccFsuInitService.listBySiteId(siteId);
            return ResponseHelper.successful(fsus);
        } catch (Exception e) {
            log.error("Error listing FSUs by site ID: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 保存或更新FSU配置信息
     * @param fsuDto FSU配置DTO
     * @return 保存结果
     */
    @PostMapping(value = "/save-or-update")
    public ResponseEntity<ResponseResult> saveOrUpdateFsu(@RequestBody CMCCFsuInitDTO fsuDto) {
        try {
            boolean result = cmccFsuInitService.saveOrUpdateFsu(fsuDto);
            return ResponseHelper.successful(result);
        } catch (IllegalArgumentException e) {
            log.warn("FSU配置校验失败: {}", e.getMessage());
            return ResponseHelper.failed(e.getMessage());
        } catch (Exception e) {
            log.error("Error saving or updating FSU: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 删除FSU配置信息
     * @param fsuId FSU唯一标识
     * @return 删除结果
     */
    @DeleteMapping(value = "/delete/{fsuId}")
    public ResponseEntity<ResponseResult> deleteFsu(@PathVariable String fsuId) {
        try {
            boolean result = cmccFsuInitService.removeById(fsuId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Error deleting FSU: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }


    @PostMapping(value = "/pull-init-config")
    public ResponseEntity<ResponseResult> pullConfig(@RequestBody CmccPullInitConfigDTO cmccPullInitConfigDTO) {
        try {
            CMCCFsuInitDTO result = cmccFsuInitService.pullInitConfig(cmccPullInitConfigDTO);
            log.info("成功拉取FSU初始化配置，FSU ID: {}", result.getFsuId());
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("拉取FSU初始化配置失败: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 覆盖FSU配置信息
     * @param fsuDto FSU配置DTO
     * @return 覆盖结果
     */
    @PostMapping(value = "/override-config")
    public ResponseEntity<ResponseResult> overrideConfig(@RequestBody CMCCFsuInitDTO fsuDto) {
        try {
            boolean result = cmccFsuInitService.overrideConfig(fsuDto);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Error overriding FSU config: {}", e.getMessage(), e);
            return ResponseHelper.failed(e.getMessage());
        }
    }

    /**
     * 批量生成站点映射配置文件
     * @param siteWebMuIds 监控单元ID列表
     * @return 生成结果
     */
    @PostMapping(value = "/generate-site-mapping-configs")
    public ResponseEntity<ResponseResult> generateSiteMappingConfigs(@RequestBody List<Integer> siteWebMuIds) {
        try {
            if (siteWebMuIds == null || siteWebMuIds.isEmpty()) {
                return ResponseHelper.failed("监控单元ID列表不能为空");
            }
            
            int successCount = cmccConfigGeneratorService.generateSiteMappingConfigs(siteWebMuIds);
            log.info("批量生成配置文件完成，成功生成 {} 个配置文件", successCount);
            
            return ResponseHelper.successful(successCount);
        } catch (Exception e) {
            log.error("批量生成站点映射配置文件失败: {}", e.getMessage(), e);
            return ResponseHelper.failed("批量生成配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 生成单个站点映射配置文件
     * @param siteWebMuId 监控单元ID
     * @return 生成结果
     */
    @PostMapping(value = "/generate-site-mapping-config/{siteWebMuId}")
    public ResponseEntity<ResponseResult> generateSiteMappingConfig(@PathVariable Integer siteWebMuId) {
        try {
            if (siteWebMuId == null) {
                return ResponseHelper.failed("监控单元ID不能为空");
            }
            
            boolean result = cmccConfigGeneratorService.generateSiteMappingConfig(siteWebMuId);
            
            if (result) {
                log.info("成功生成监控单元 {} 的配置文件", siteWebMuId);
                return ResponseHelper.successful(true);
            } else {
                log.warn("监控单元 {} 的配置文件生成失败", siteWebMuId);
                return ResponseHelper.failed("配置文件生成失败");
            }
        } catch (Exception e) {
            log.error("生成监控单元 {} 的配置文件失败: {}", siteWebMuId, e.getMessage(), e);
            return ResponseHelper.failed("生成配置文件失败: " + e.getMessage());
        }
    }
}
