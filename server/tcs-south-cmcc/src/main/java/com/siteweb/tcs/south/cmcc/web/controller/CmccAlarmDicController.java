package com.siteweb.tcs.south.cmcc.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccAlarmDic;
import com.siteweb.tcs.south.cmcc.web.dto.CmccAlarmDicDTO;
import com.siteweb.tcs.south.cmcc.web.service.ICmccAlarmDicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * CMCC告警字典Controller
 */
@Slf4j
@Api(tags = "CMCC告警字典管理")
@RestController
@RequestMapping("/alarm-dic")
public class CmccAlarmDicController {

    @Resource
    private ICmccAlarmDicService cmccAlarmDicService;

    /**
     * 分页查询告警字典
     */
    @ApiOperation("分页查询告警字典")
    @GetMapping("/page")
    public ResponseEntity<ResponseResult> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("标准版本") @RequestParam(required = false) Integer standardVersion,
            @ApiParam("设备类型") @RequestParam(required = false) Integer deviceType,
            @ApiParam("告警逻辑类别") @RequestParam(required = false) Integer alarmLogicClass) {
        
        log.info("分页查询告警字典: current={}, size={}, standardVersion={}, deviceType={}, alarmLogicClass={}", 
                current, size, standardVersion, deviceType, alarmLogicClass);
        
        Page<CmccAlarmDic> page = new Page<>(current, size);
        QueryWrapper<CmccAlarmDic> queryWrapper = new QueryWrapper<>();
        
        if (standardVersion != null) {
            queryWrapper.eq("StandardVersion", standardVersion);
        }
        if (deviceType != null) {
            queryWrapper.eq("DeviceType", deviceType);
        }
        if (alarmLogicClass != null) {
            queryWrapper.eq("AlarmLogicClass", alarmLogicClass);
        }
        
        Page<CmccAlarmDic> result = cmccAlarmDicService.page(page, queryWrapper);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据ID查询告警字典
     */
    @ApiOperation("根据ID查询告警字典")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseResult> getById(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("根据ID查询告警字典: {}", id);
        CmccAlarmDic result = cmccAlarmDicService.getById(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据告警标准ID查询告警字典
     */
    @ApiOperation("根据告警标准ID查询告警字典")
    @GetMapping("/alarm-standard/{alarmStandardId}")
    public ResponseEntity<ResponseResult> getByAlarmStandardId(@ApiParam("告警标准ID") @PathVariable String alarmStandardId) {
        log.info("根据告警标准ID查询告警字典: {}", alarmStandardId);
        CmccAlarmDic result = cmccAlarmDicService.getByAlarmStandardId(alarmStandardId);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据设备类型查询告警字典列表
     */
    @ApiOperation("根据设备类型查询告警字典列表")
    @GetMapping("/device-type/{deviceType}")
    public ResponseEntity<ResponseResult> listByDeviceType(@ApiParam("设备类型") @PathVariable Integer deviceType) {
        log.info("根据设备类型查询告警字典列表: {}", deviceType);
        List<CmccAlarmDic> result = cmccAlarmDicService.listByDeviceType(deviceType);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据标准版本查询告警字典列表
     */
    @ApiOperation("根据标准版本查询告警字典列表")
    @GetMapping("/standard-version/{standardVersion}")
    public ResponseEntity<ResponseResult> listByStandardVersion(@ApiParam("标准版本") @PathVariable Integer standardVersion) {
        log.info("根据标准版本查询告警字典列表: {}", standardVersion);
        List<CmccAlarmDic> result = cmccAlarmDicService.listByStandardVersion(standardVersion);
        return ResponseHelper.successful(result);
    }

    /**
     * 新增告警字典
     */
    @ApiOperation("新增告警字典")
    @PostMapping
    public ResponseEntity<ResponseResult> save(@RequestBody CmccAlarmDic cmccAlarmDic) {
        log.info("新增告警字典: {}", cmccAlarmDic);
        boolean result = cmccAlarmDicService.saveAlarmDic(cmccAlarmDic);
        return ResponseHelper.successful(result);
    }

    /**
     * 更新告警字典
     */
    @ApiOperation("更新告警字典")
    @PutMapping
    public ResponseEntity<ResponseResult> update(@RequestBody CmccAlarmDic cmccAlarmDic) {
        log.info("更新告警字典: {}", cmccAlarmDic);
        boolean result = cmccAlarmDicService.updateAlarmDic(cmccAlarmDic);
        return ResponseHelper.successful(result);
    }

    /**
     * 删除告警字典
     */
    @ApiOperation("删除告警字典")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseResult> delete(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("删除告警字典: {}", id);
        boolean result = cmccAlarmDicService.deleteAlarmDic(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 批量删除告警字典
     */
    @ApiOperation("批量删除告警字典")
    @DeleteMapping("/batch")
    public ResponseEntity<ResponseResult> deleteBatch(@RequestBody List<Integer> ids) {
        log.info("批量删除告警字典: {}", ids);
        boolean result = cmccAlarmDicService.removeByIds(ids);
        return ResponseHelper.successful(result);
    }

    /**
     * 查询所有告警字典数据（包含字典名称）
     */
    @ApiOperation("查询所有告警字典数据（包含字典名称）")
    @GetMapping("/all-with-names")
    public ResponseEntity<ResponseResult> getAllWithNames() {
        log.info("查询所有告警字典数据（包含字典名称）");
        List<CmccAlarmDicDTO> result = cmccAlarmDicService.listAllWithNames();
        return ResponseHelper.successful(result);
    }
} 