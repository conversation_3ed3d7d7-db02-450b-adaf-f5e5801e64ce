package com.siteweb.tcs.south.cmcc.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccStandardVersion;
import com.siteweb.tcs.south.cmcc.web.service.ICmccStandardVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * CMCC标准版本Controller
 */
@Slf4j
@Api(tags = "CMCC标准版本管理")
@RestController
@RequestMapping("/standard-version")
public class CmccStandardVersionController {

    @Resource
    private ICmccStandardVersionService cmccStandardVersionService;

    /**
     * 分页查询标准版本
     */
    @ApiOperation("分页查询标准版本")
    @GetMapping("/page")
    public ResponseEntity<ResponseResult> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        log.info("分页查询标准版本: current={}, size={}", current, size);
        
        Page<CmccStandardVersion> page = new Page<>(current, size);
        Page<CmccStandardVersion> result = cmccStandardVersionService.page(page);
        return ResponseHelper.successful(result);
    }

    /**
     * 查询所有标准版本
     */
    @ApiOperation("查询所有标准版本")
    @GetMapping("/list")
    public ResponseEntity<ResponseResult> list() {
        log.info("查询所有标准版本");
        List<CmccStandardVersion> result = cmccStandardVersionService.list();
        return ResponseHelper.successful(result);
    }

    /**
     * 根据ID查询标准版本
     */
    @ApiOperation("根据ID查询标准版本")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseResult> getById(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("根据ID查询标准版本: {}", id);
        CmccStandardVersion result = cmccStandardVersionService.getById(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 新增标准版本
     */
    @ApiOperation("新增标准版本")
    @PostMapping
    public ResponseEntity<ResponseResult> save(@RequestBody CmccStandardVersion cmccStandardVersion) {
        log.info("新增标准版本: {}", cmccStandardVersion);
        boolean result = cmccStandardVersionService.saveStandardVersion(cmccStandardVersion);
        return ResponseHelper.successful(result);
    }

    /**
     * 更新标准版本
     */
    @ApiOperation("更新标准版本")
    @PutMapping
    public ResponseEntity<ResponseResult> update(@RequestBody CmccStandardVersion cmccStandardVersion) {
        log.info("更新标准版本: {}", cmccStandardVersion);
        boolean result = cmccStandardVersionService.updateStandardVersion(cmccStandardVersion);
        return ResponseHelper.successful(result);
    }

    /**
     * 删除标准版本
     */
    @ApiOperation("删除标准版本")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseResult> delete(@ApiParam("主键ID") @PathVariable Integer id) {
        log.info("删除标准版本: {}", id);
        boolean result = cmccStandardVersionService.deleteStandardVersion(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 批量删除标准版本
     */
    @ApiOperation("批量删除标准版本")
    @DeleteMapping("/batch")
    public ResponseEntity<ResponseResult> deleteBatch(@RequestBody List<Integer> ids) {
        log.info("批量删除标准版本: {}", ids);
        boolean result = cmccStandardVersionService.removeByIds(ids);
        return ResponseHelper.successful(result);
    }
} 