package com.siteweb.tcs.south.cmcc.web.controller;

/**
 * <AUTHOR> (2025-05-21)
 **/

import com.siteweb.tcs.common.o11y.message.ProbeActionRequest;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.hub.domain.GatewayHub;
import com.siteweb.tcs.siteweb.util.JsonHelper;
import com.siteweb.tcs.south.cmcc.connector.services.CmccBackupService;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationObject;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationType;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.OperationLogProvider;
import com.siteweb.tcs.south.cmcc.web.dto.CreateCmccFSUDTO;
import com.siteweb.tcs.south.cmcc.web.service.CmccFsuAccessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.pattern.Patterns;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.concurrent.CompletionStage;

@Slf4j
@RestController
@RequestMapping("api/cmcc/2016/fsu/")
public class FSUController {

    @Autowired
    @Qualifier("gateway-hub")
    private ActorRef gatewayHub;

    @Autowired
    private FSUProvider fsuProvider;

    @Autowired
    private CmccFsuAccessService cmccFsuAccessService;

    @Autowired
    private CmccBackupService backupService;
    @Autowired
    private PluginContext pluginContext;


    @Autowired
    private OperationLogProvider operationLogProvider;

    /**
     * 获取待注册FSU列表
     * <p>用于查询所有尚未注册到系统的FSU设备信息。</p>
     *
     * @return 待注册FSU列表（账号密码字段建议前端隐藏）
     */
    @GetMapping(value = "pending/list", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> pendingList() {
        log.info("获取待注册FSU列表");
        var pendingList = fsuProvider.getPendingList();
        // TODO 需要对pendingList的账号密码进行隐藏
        return ResponseHelper.successful(pendingList);
    }


    /**
     * 测试接口（内部调试用）
     * <p>用于测试与网关Hub的通信能力。</p>
     *
     * @return 测试结果
     */
    @GetMapping(value = "test", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public CompletionStage<ResponseEntity<ResponseResult>> TEST() {
        return Patterns.askWithReplyTo(
                        gatewayHub,
                        (ActorRef replyTo) -> {
                            var replyMsg = new ProbeActionRequest();
                            replyMsg.setActionType(ProbeActionRequest.ActionType.GET_LOGS);
                            replyMsg.setSender(replyTo);
                            return new GatewayHub.ForwardMessage("tcs-south-cmcc", "10000000001", replyMsg);
                        },
                        Duration.ofSeconds(1)
                ).thenApply(ResponseHelper::successful)
                .exceptionally((throwable) -> {
                    return ResponseHelper.failed(throwable.toString());
                });


    }


    /**
     * 获取已注册FSU列表
     * <p>查询所有已注册到系统的FSU设备信息。</p>
     *
     * @return 已注册FSU列表（账号密码字段建议前端隐藏）
     */
    @GetMapping(value = "list", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> fsuList() {
        log.info("获取FSU列表");
        // TODO 需要对fsuList的账号密码进行隐藏
        return ResponseHelper.successful(fsuProvider.getAllFsu());
    }

    /**
     * 根据id获取FSU信息
     */
    @GetMapping(value = "get/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> getFsu(@PathVariable("fsuid") String fsuId) {
        log.info("获取FSU: {}", fsuId);
        return ResponseHelper.successful(fsuProvider.getFsuByFsuid(fsuId));
    }

    /**
     * 从待注册FSU批准入网
     * <p>将待注册FSU批准为正式FSU并入网。</p>
     *
     * @param createCmccFSUDTO 待注册FSU信息及新名称
     * @return 操作结果
     */
    @PostMapping(value = "approved", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> createFromPending(@RequestBody CreateCmccFSUDTO createCmccFSUDTO) {
        log.info("从待注册FSU创建FSU: {}", createCmccFSUDTO.getFsuId());
        // TODO 需要对fsuList的账号密码进行隐藏
        var pendingFsu = fsuProvider.getPendingByFsuId(createCmccFSUDTO.getFsuId());
        if (pendingFsu == null) return ResponseHelper.failed("not found pending fsu");

        operationLogProvider.record(OperationObject.FSU, pendingFsu.getFsuId(), OperationType.APPROVED, "plugin.cmcc.fsu.manual-approved");
        var fsu = CMCCFsu.fromPending(pendingFsu);
        fsu.setEnable(true);
        fsu.setFsuPort(createCmccFSUDTO.getFsuPort());
        fsu.setGatewayName(createCmccFSUDTO.getFsuName());
        fsu.setRegionId(createCmccFSUDTO.getRegionId());
        fsu.setConfigSync(createCmccFSUDTO.getConfigSync());
        fsu.setEnable(createCmccFSUDTO.isEnable());
        return ResponseHelper.successful(cmccFsuAccessService.approvedFsuNetworkAccess(fsu));
    }


    /**
     * 新建FSU并批准入网
     * <p>直接创建FSU并批准其入网。</p>
     *
     * @param fsu FSU实体信息
     * @return 操作结果
     */
    @PostMapping(value = "create", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> createFsu(@RequestBody CMCCFsu fsu) {
        log.info("从待注册FSU创建FSU: {}", fsu.getGatewayName());
        operationLogProvider.record(OperationObject.FSU, fsu.getGatewayId(), OperationType.CREATE, "plugin.cmcc.fsu.manual-create");
        return ResponseHelper.successful(cmccFsuAccessService.approvedFsuNetworkAccess(fsu));
    }


    /**
     * 启用指定FSU
     * <p>将指定FSU状态设置为启用。</p>
     *
     * @param fsuId FSU ID
     * @return 操作结果
     */
    @PutMapping(value = "enable/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> enableFsu(@PathVariable("fsuid") String fsuId) {
        log.info("启用FSU: {}", fsuId);
        return ResponseHelper.successful(cmccFsuAccessService.enableFsu(fsuId));
    }

    /**
     * 禁用指定FSU
     * <p>将指定FSU状态设置为禁用。</p>
     *
     * @param fsuId FSU ID
     * @return 操作结果
     */
    @PutMapping(value = "disable/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> disableFsu(@PathVariable("fsuid") String fsuId) {
        log.info("禁用FSU: {}", fsuId);
        return ResponseHelper.successful(cmccFsuAccessService.disableFsu(fsuId));
    }


    /**
     * 更新FSU信息
     * <p>修改指定FSU的配置信息。</p>
     *
     * @param fsu FSU实体信息
     * @return 操作结果
     */
    @PutMapping(value = "update", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> updateFsu(@RequestBody CMCCFsu fsu) {
        log.info("更新FSU: {}", fsu.getGatewayId());
        cmccFsuAccessService.updateFsu(fsu);
        return ResponseHelper.successful();
    }


    /**
     * 删除指定FSU
     * <p>从系统中移除指定FSU。</p>
     *
     * @param fsuId FSU ID
     * @return 操作结果
     */
    @DeleteMapping(value = "delete/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> deleteFsu(@PathVariable("fsuid") String fsuId) {
        log.info("删除FSU: {}", fsuId);
        cmccFsuAccessService.deleteFsu(fsuId);
        return ResponseHelper.successful(true);
    }


    /**
     * 获取FSU备份列表
     */
    @GetMapping(value = "backup/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> backupFileList(@PathVariable("fsuid") String fsuId) throws IOException {
        log.info("获取FSU设备备份列表");
        return ResponseHelper.successful(backupService.getBackupRecord(fsuId));
    }

    /**
     * 手动备份FSU
     */
    @PostMapping(value = "backup/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<ResponseResult> backup(@PathVariable("fsuid") String fsuId) throws IOException {
        log.info("手动备份FSU");
        var result = backupService.backup(fsuId, "手动备份");
        return ResponseHelper.successful(result);
    }


    @GetMapping(value = "backup/{fsuid}/download", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseEntity<String> downloadBackupFile(@PathVariable("fsuid") String fsuid, @RequestParam("file") String file) throws IOException {
        log.info("下载FSU备份");
        var data = backupService.getBackupData(fsuid, file);
        var bytes = JsonHelper.toJson(data);
        // 4. 设置 HTTP 响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setContentDisposition(ContentDisposition.attachment()
                .filename(file, StandardCharsets.UTF_8)
                .build());
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }
}
