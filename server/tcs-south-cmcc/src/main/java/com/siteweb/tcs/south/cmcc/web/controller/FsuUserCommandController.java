package com.siteweb.tcs.south.cmcc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.connector.commands.*;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationObject;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationType;
import com.siteweb.tcs.south.cmcc.dal.provider.OperationLogProvider;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.web.service.CmccFsuUserCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

/**
 * FSU用户命令控制器
 * <p>
 * 专门处理FSU的操作性命令，如时间同步、重启、FTP设置等
 * 与FSUController分离，便于管理和扩展
 *
 * <AUTHOR> for CMCC FSU User Command Management
 */
@Slf4j
@RestController
@RequestMapping("api/cmcc/2016/fsu/command/")
public class FsuUserCommandController {

    @Autowired
    private CmccFsuUserCommandService cmccFsuUserCommandService;
    @Autowired
    private FSUProvider fsuProvider;

    @Autowired
    private OperationLogProvider operationLogProvider;

    /**
     * FSU时间同步接口
     * <p>对指定FSU进行时间同步操作。</p>
     *
     * @param fsuId FSU ID
     * @param force 是否强制同步（可选，默认false）
     * @return 时间同步结果
     */
    @PostMapping(value = "timecheck/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> timeCheck(
            @PathVariable("fsuId") String fsuId,
            @RequestParam(value = "force", defaultValue = "false") boolean force) {

        log.info("收到FSU时间同步请求: fsuId={}, force={}", fsuId, force);

        try {
            TimeCheckUserCommand command = TimeCheckUserCommand.create(fsuId, "FsuUserCommandController");
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.time-sync");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("FSU时间同步异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("时间同步异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("FSU时间同步请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * FSU状态查询接口
     * <p>查询指定FSU的运行状态和配置信息。</p>
     *
     * @param fsuId FSU ID
     * @return FSU状态信息
     */
    @PostMapping(value = "getinfo/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> getInfo(
            @PathVariable("fsuId") String fsuId) {

        log.info("收到FSU获取信息请求: fsuId={}", fsuId);

        try {
            GetFsuInfoUserCommand command = GetFsuInfoUserCommand.createInfo(fsuId, "FsuUserCommandController");
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.get-info");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("获取FSU信息异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("获取FSU信息异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("获取FSU信息请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * FSU重启接口
     * <p>对指定FSU进行重启操作。此操作用于FSU的升级等场景。</p>
     *
     * @param fsuId FSU ID
     * @return 重启操作结果
     */
    @PostMapping(value = "restart/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> restart(
            @PathVariable("fsuId") String fsuId) {

        log.info("收到FSU重启请求: fsuId={}", fsuId);

        try {
            RestartFsuUserCommand command = RestartFsuUserCommand.create(fsuId, "FsuUserCommandController");
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.restart");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("FSU重启异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("FSU重启异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("FSU重启请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * 设置FSU的FTP信息接口
     * <p>设置指定FSU的FTP用户名和密码。</p>
     *
     * @param fsuId    FSU ID
     * @param userName FTP用户名
     * @param password FTP密码
     * @return 设置FTP信息结果
     */
    @PostMapping(value = "setftp/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> setFtp(
            @PathVariable("fsuId") String fsuId,
            @RequestParam("userName") String userName,
            @RequestParam("password") String password) {

        log.info("收到设置FSU FTP信息请求: fsuId={}, userName={}", fsuId, userName);

        try {
            SetFtpUserCommand command = SetFtpUserCommand.create(fsuId, "FsuUserCommandController", userName, password);
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.set-ftp");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            CMCCFsu fsuByFsuid = fsuProvider.getFsuByFsuid(fsuId);
                            fsuByFsuid.setFtpUserName(userName);
                            fsuByFsuid.setFtpPassword(password);
                            fsuProvider.updateFsu(fsuByFsuid);

                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("设置FSU FTP信息异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("设置FTP信息异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("设置FSU FTP信息请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * 获取FSU的FTP信息接口
     * <p>获取指定FSU的FTP用户名和密码信息。</p>
     *
     * @param fsuId FSU ID
     * @return FTP信息
     */
    @PostMapping(value = "getftp/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> getFtp(
            @PathVariable("fsuId") String fsuId) {

        log.info("收到获取FSU FTP信息请求: fsuId={}", fsuId);

        try {
            GetFtpUserCommand command = GetFtpUserCommand.create(fsuId, "FsuUserCommandController");
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.get-ftp");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("获取FSU FTP信息异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("获取FTP信息异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("获取FSU FTP信息请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * 获取FSU注册信息接口
     * <p>获取指定FSU的注册用户名和密码信息。</p>
     *
     * @param fsuId FSU ID
     * @return FSU注册信息
     */
    @PostMapping(value = "getlogininfo/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> getLoginInfo(
            @PathVariable("fsuId") String fsuId) {

        log.info("收到获取FSU注册信息请求: fsuId={}", fsuId);

        try {
            GetLoginInfoUserCommand command = GetLoginInfoUserCommand.create(fsuId, "FsuUserCommandController");
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.get-login");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("获取FSU注册信息异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("获取注册信息异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("获取FSU注册信息请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * 设置FSU注册信息接口
     * <p>设置指定FSU的注册用户名和密码。</p>
     *
     * @param fsuId    FSU ID
     * @param userName 用户名
     * @param password 密码
     * @return 设置结果
     */
    @PostMapping(value = "setlogininfo/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> setLoginInfo(
            @PathVariable("fsuId") String fsuId,
            @RequestParam("userName") String userName,
            @RequestParam("password") String password) {

        log.info("收到设置FSU注册信息请求: fsuId={}, userName={}", fsuId, userName);

        try {
            SetLoginInfoUserCommand command = SetLoginInfoUserCommand.create(
                    fsuId, "FsuUserCommandController", userName, password);
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.set-login");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            CMCCFsu fsuByFsuid = fsuProvider.getFsuByFsuid(fsuId);
                            fsuByFsuid.setUsername(userName);
                            fsuByFsuid.setPassword(password);
                            fsuProvider.updateFsu(fsuByFsuid);
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("设置FSU注册信息异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("设置注册信息异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("设置FSU注册信息请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * 更新FSU状态信息获取周期接口
     * <p>更新指定FSU的状态信息获取周期。</p>
     *
     * @param fsuId    FSU ID
     * @param interval 状态信息获取周期值，以秒（s）为单位
     * @return 更新结果
     */
    @PostMapping(value = "updateinterval/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> updateFsuInfoInterval(
            @PathVariable("fsuId") String fsuId,
            @RequestParam("interval") Short interval) {

        log.info("收到更新FSU状态信息获取周期请求: fsuId={}, interval={}", fsuId, interval);

        try {
            UpdateFsuInfoIntervalUserCommand command = UpdateFsuInfoIntervalUserCommand.create(
                    fsuId, "FsuUserCommandController", interval);
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.update-interval");
            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("更新FSU状态信息获取周期异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("更新状态获取周期异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("更新FSU状态信息获取周期请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * 批量更新FSU状态信息获取周期接口
     * <p>向所有FSU发送更新状态信息获取周期的请求。</p>
     *
     * @param interval 状态信息获取周期值，以秒（s）为单位
     * @return 批量更新结果
     */
    @PostMapping(value = "updateinterval/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> batchUpdateFsuInfoInterval(
            @RequestParam("interval") Short interval) {

        log.info("收到批量更新FSU状态信息获取周期请求: interval={}", interval);

        try {
            UpdateFsuInfoIntervalUserCommand command = UpdateFsuInfoIntervalUserCommand.createForAllFsu(
                    "FsuUserCommandController", interval);

            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response);
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("批量更新FSU状态信息获取周期异常", throwable);
                        return ResponseHelper.failed("批量更新状态获取周期异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("批量更新FSU状态信息获取周期请求处理失败", e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }

    /**
     * 批量FSU时间同步接口
     * <p>对多个FSU进行批量时间同步操作。</p>
     *
     * @param fsuIds FSU ID数组
     * @param force  是否强制同步（可选，默认false）
     * @return 批量时间同步结果
     */
    @PostMapping(value = "timecheck/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> batchTimeCheck(
            @RequestParam("fsuIds") String[] fsuIds,
            @RequestParam(value = "force", defaultValue = "false") boolean force) {

        log.info("收到批量FSU时间同步请求: 数量={}, force={}", fsuIds.length, force);

        try {
            java.util.List<TimeCheckUserCommand> commands = new java.util.ArrayList<>();
            for (String fsuId : fsuIds) {
                TimeCheckUserCommand command = TimeCheckUserCommand.create(fsuId, "FsuUserCommandController");
                commands.add(command);
            }

            return cmccFsuUserCommandService.executeCommands(
                            commands.stream()
                                    .map(cmd -> (CMCCSouthUserCommand) cmd)
                                    .collect(java.util.stream.Collectors.toList())
                    )
                    .thenApply(responses -> {
                        long successCount = responses.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
                        log.info("批量FSU时间同步完成: 总数={}, 成功={}", responses.size(), successCount);

                        java.util.Map<String, Object> result = new java.util.HashMap<>();
                        result.put("total", responses.size());
                        result.put("success", successCount);
                        result.put("failed", responses.size() - successCount);
                        result.put("details", responses);

                        return ResponseHelper.successful(result);
                    })
                    .exceptionally(throwable -> {
                        log.error("批量FSU时间同步异常", throwable);
                        return ResponseHelper.failed("批量时间同步异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("批量FSU时间同步请求处理失败", e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }


    /**
     * 手动拉取FSU配置并同步至本地
     *
     * @param fsuId FSUID
     * @return
     */
    @PostMapping(value = "pull-fsu-dev-conf/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> pullFsuDevConf(@PathVariable("fsuId") String fsuId) {
        log.info("收到拉取FSU配置同步请求: fsuId={}", fsuId);
        // 创建拉取配置命令
        PullFsuDevConfUserCommand command = PullFsuDevConfUserCommand.create(fsuId);
        operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.pull-fsu-dev-conf");
        return cmccFsuUserCommandService.executeCommand(command)
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        return ResponseHelper.successful(response);
                    } else {
                        return ResponseHelper.failed(response.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("拉取FSU配置同步异常: fsuId={}", fsuId, throwable);
                    return ResponseHelper.failed("拉取FSU配置同步异常: " + throwable.getMessage());
                });
    }

    /**
     * 手动拉取FSU配置并同步至本地
     *
     * @param fsuId FSUID
     * @return
     */
    @PostMapping(value = "set-fsu-dev-conf/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> seFsuDevConf(@PathVariable("fsuId") String fsuId) {
        log.info("收到下发FSU配置请求: fsuId={}", fsuId);
        // 创建拉取配置命令
        SetFusDevConfUserCommand command = SetFusDevConfUserCommand.create(fsuId);
        operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.set-fsu-dev-conf");
        return cmccFsuUserCommandService.executeCommand(command)
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        return ResponseHelper.successful(response);
                    } else {
                        return ResponseHelper.failed(response.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("下发FSU配置异常: fsuId={}", fsuId, throwable);
                    return ResponseHelper.failed("下发FSU配置异常: " + throwable.getMessage());
                });
    }


    /**
     * 获取FSU报文跟踪日志
     *
     * @param fsuId FSUID
     * @return
     */
    @GetMapping(value = "tracer-message/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> tracerMessages(@PathVariable("fsuId") String fsuId) {
        return cmccFsuUserCommandService.executeCommand(GetTracerMessagesUserCommand.create(fsuId))
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        var data = response.getData();
                        return ResponseHelper.successful(data);
                    } else {
                        return ResponseHelper.failed(response.getMessage());
                    }
                })
                .exceptionally(throwable -> {

                    log.error("获取FSU报文列表异常: fsuId={}", fsuId, throwable);
                    return ResponseHelper.failed("获取FSU报文列表异常: " + throwable.getMessage());
                });
    }

    /**
     * 设置FSU报文跟踪开关
     *
     * @param fsuId FSUID
     * @return
     */
    @PutMapping(value = "tracer-state/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> tracerEnable(@PathVariable("fsuId") String fsuId, @RequestParam("enable") boolean enable) {

        if (enable) {
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.set-tracer-state-true");
        } else {
            operationLogProvider.record(OperationObject.FSU, fsuId, OperationType.USER_CMD, "plugin.cmcc.fsu.user.cmd.set-tracer-state-false");
        }


        return cmccFsuUserCommandService.executeCommand(TracerStateUserCommand.create(fsuId, enable))
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        var data = response.getData();
                        return ResponseHelper.successful(data);
                    } else {
                        return ResponseHelper.failed(response.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("获取FSU报文列表异常: fsuId={}", fsuId, throwable);
                    return ResponseHelper.failed("获取FSU报文列表异常: " + throwable.getMessage());
                });
    }

    /**
     * 获取FSU报文跟踪开关
     *
     * @param fsuId
     * @return
     */
    @GetMapping(value = "tracer-state/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> tracerState(@PathVariable("fsuId") String fsuId) {
        return cmccFsuUserCommandService.executeCommand(TracerStateUserCommand.create(fsuId, null))
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        var data = response.getData();
                        return ResponseHelper.successful(data);
                    } else {
                        return ResponseHelper.failed(response.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("获取FSU报文列表异常: fsuId={}", fsuId, throwable);
                    return ResponseHelper.failed("获取FSU报文列表异常: " + throwable.getMessage());
                });
    }


    /**
     * 获取FSU报文跟踪开关
     *
     * @param fsuId
     * @return
     */
    @GetMapping(value = "probe/{fsuId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CompletableFuture<ResponseEntity<ResponseResult>> getFsuProbe(@PathVariable("fsuId") String fsuId) {
        return cmccFsuUserCommandService.executeCommand(TracerStateUserCommand.create(fsuId, null))
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        var data = response.getData();
                        return ResponseHelper.successful(data);
                    } else {
                        return ResponseHelper.failed(response.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("获取FSU报文列表异常: fsuId={}", fsuId, throwable);
                    return ResponseHelper.failed("获取FSU报文列表异常: " + throwable.getMessage());
                });
    }


    /**
     * 查询CMCCFSU状态信息
     * <p>获取FSU设备的实时状态信息，包括网关信息、心跳时间、连接状态等</p>
     *
     * @param fsuId FSU ID
     * @return FSU实时状态信息
     */
    @GetMapping(value = "status/{fsuid}", produces = MediaType.APPLICATION_XML_VALUE, consumes = MediaType.APPLICATION_XML_VALUE)
    public CompletionStage<ResponseEntity<ResponseResult>> getFsuStatus(@PathVariable("fsuid") String fsuId) {
        log.info("查询FSU状态: {}", fsuId);

        try {
            GetFsuStatusUserCommand command = GetFsuStatusUserCommand.create(fsuId, "FSUController");

            return cmccFsuUserCommandService.executeCommand(command)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            return ResponseHelper.successful(response.getData());
                        } else {
                            return ResponseHelper.failed(response.getMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("查询FSU状态异常: fsuId={}", fsuId, throwable);
                        return ResponseHelper.failed("查询FSU状态异常: " + throwable.getMessage());
                    });
        } catch (Exception e) {
            log.error("查询FSU状态请求处理失败: fsuId={}", fsuId, e);
            return CompletableFuture.completedFuture(
                    ResponseHelper.failed("请求处理失败: " + e.getMessage())
            );
        }
    }


}
