package com.siteweb.tcs.south.cmcc.web.dto;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCAlarm;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC告警信息（包含标准化信息）
 * <AUTHOR> (2025-08-06)
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CMCCAlarmWithStandardization extends CMCCAlarm {

    /**
     * 是否已标准化
     */
    private Boolean standardized;

    /**
     * 标准化名称
     */
    private String standardName;

    /**
     * 标准化ID
     */
    private String standardId;

    /**
     * 构造函数
     */
    public CMCCAlarmWithStandardization() {
        super();
    }

    /**
     * 从CMCCAlarm构造
     */
    public CMCCAlarmWithStandardization(CMCCAlarm alarm) {
        super();
        if (alarm != null) {
            this.setId(alarm.getId());
            this.setFsuId(alarm.getFsuId());
            this.setDeviceId(alarm.getDeviceId());
            this.setSpId(alarm.getSpId());
            this.setOriginSpId(alarm.getOriginSpId());
            this.setSpHubId(alarm.getSpHubId());
            this.setSpType(alarm.getSpType());
            this.setAlarmLevel(alarm.getAlarmLevel());
            this.setAlarmName(alarm.getAlarmName());
            this.setSignalNumber(alarm.getSignalNumber());
            this.setNmAlarmId(alarm.getNmAlarmId());
            this.setMeaning(alarm.getMeaning());
            this.setThreshold(alarm.getThreshold());
            this.setUnit(alarm.getUnit());
        }
    }
}
