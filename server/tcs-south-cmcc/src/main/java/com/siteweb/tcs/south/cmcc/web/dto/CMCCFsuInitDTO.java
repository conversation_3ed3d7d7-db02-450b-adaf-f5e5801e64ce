package com.siteweb.tcs.south.cmcc.web.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDeviceInit;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-08-09 10:34
 **/
@Data
public class CMCCFsuInitDTO {
    /**
     * 移动FSU唯一标识
     */
    private String fsuId;

    /**
     * SiteWeb监控单元ID
     */
    private Integer siteWebMuId;

    /**
     * 移动FSU端口
     */
    private Integer fsuPort;

    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 移动FSU自诊断设备名称
     */
    private String type76DeviceName;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * FSU类型
     */
    private Integer fsuType;

    /**
     * 站点ID
     */
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * FTP用户名
     */
    private String ftpUser;

    /**
     * FTP密码
     */
    private String ftpPwd;

    /**
     * 登录用户名
     */
    private String loginUser;

    /**
     * 登录密码
     */
    private String loginPwd;

    /**
     * 是否启用ACL
     */
    private Boolean enableAcl;

    /**
     * 平台编号
     */
    private Integer platFormNo;

    /**
     * 主平台IP
     */
    private String scip;

    /**
     * 主平台端口
     */
    private Integer scPort;

    /**
     * 主平台URL后缀
     */
    private String scUrlSuffix;

    /**
     * 主平台备用IP
     */
    private String scipBak;

    /**
     * 主平台切换模式
     */
    private Integer scSwitchMode;

    /**
     * 平台名称
     */
    private String platFormName;

    /**
     * 最后的平台名称
     */
    private String lastPlatFormName;

    /**
     * 备用平台IP
     */
    private String scip1;

    /**
     * 备用平台端口
     */
    private Integer scPort1;

    /**
     * 备用平台URL后缀
     */
    private String scUrlSuffix1;

    /**
     * 备用平台备用IP
     */
    private String scipBak1;

    /**
     * 备用平台切换模式
     */
    private Integer scSwitchMode1;

    /**
     * 备用平台名称
     */
    private String platFormName1;

    /**
     * 备用最后平台名称
     */
    private String lastPlatFormName1;

    /**
     * 移动自诊断设备id1
     */
    private String type76DeviceId;

    /**
     * 设备数量
     */
    private Integer deviceNum;

    /**
     * 设备列表
     */
    private List<CMCCDeviceInitDTO> cmccDeviceInitDTOList;
}
