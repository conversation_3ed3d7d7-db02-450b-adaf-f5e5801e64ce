package com.siteweb.tcs.south.cmcc.web.dto;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCSignal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC信号信息（包含标准化信息）
 * <AUTHOR> (2025-08-06)
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CMCCSignalWithStandardization extends CMCCSignal {

    /**
     * 是否已标准化
     */
    private Boolean standardized;

    /**
     * 标准化名称
     */
    private String standardName;

    /**
     * 标准化ID
     */
    private String standardId;

    /**
     * 构造函数
     */
    public CMCCSignalWithStandardization() {
        super();
    }

    /**
     * 从CMCCSignal构造
     */
    public CMCCSignalWithStandardization(CMCCSignal signal) {
        super();
        if (signal != null) {
            this.setId(signal.getId());
            this.setFsuId(signal.getFsuId());
            this.setDeviceId(signal.getDeviceId());
            this.setSpId(signal.getSpId());
            this.setOriginSpId(signal.getOriginSpId());
            this.setSpHubId(signal.getSpHubId());
            this.setSignalName(signal.getSignalName());
            this.setSpType(signal.getSpType());
            this.setSignalNumber(signal.getSignalNumber());
            this.setMeanings(signal.getMeanings());
            this.setVisible(signal.getVisible());
            this.setUnit(signal.getUnit());
        }
    }
}
