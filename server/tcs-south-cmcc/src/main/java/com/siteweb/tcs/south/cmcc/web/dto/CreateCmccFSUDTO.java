package com.siteweb.tcs.south.cmcc.web.dto;

import com.siteweb.tcs.south.cmcc.dal.enums.ConfigSyncEnum;
import lombok.Data;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-01 14:23
 **/
@Data
public class CreateCmccFSUDTO {

    /**
     * 要批准的FSUID
     */
    private String fsuId;

    /**
     * 给FSU一个名字
     */
    private String fsuName;

    /**
     * FSU的端口 默认8080
     */
    private String fsuPort;

    /**
     * FSU挂载到哪个Region
     */
    private long regionId;

    /**
     * 入网后是否启用FSU
     */
    private boolean enable ;

    /**
     * 配置同步
     */
    private ConfigSyncEnum configSync;



}
