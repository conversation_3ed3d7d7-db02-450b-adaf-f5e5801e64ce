package com.siteweb.tcs.south.cmcc.web.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 设备标准化使用率统计DTO
 * <AUTHOR> (2025-08-06)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceStandardizationUsage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 告警标准化统计
     */
    private StandardizationUsageDetail alarmUsage;

    /**
     * 信号标准化统计
     */
    private StandardizationUsageDetail signalUsage;

    /**
     * 控制标准化统计
     */
    private StandardizationUsageDetail controlUsage;

    /**
     * 标准化使用率详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StandardizationUsageDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 该设备类型应有的标准化项总数
         */
        private Integer totalStandardCount;

        /**
         * 该设备已实现的标准化项数量
         */
        private Integer implementedStandardCount;

        /**
         * 标准化项实现率（已实现标准化项 / 应有标准化项 * 100%）
         */
        private BigDecimal standardImplementationRate;

        /**
         * 该设备实际项总数量
         */
        private Integer totalActualCount;

        /**
         * 该设备已标准化的实际项数量
         */
        private Integer standardizedActualCount;

        /**
         * 实际项标准化率（已标准化实际项 / 实际项总数 * 100%）
         */
        private BigDecimal actualStandardizationRate;

        /**
         * 已实现的标准化项列表
         */
        private List<ImplementedStandardItem> implementedStandardItems;

        /**
         * 未实现的标准化项列表
         */
        private List<UnimplementedStandardItem> unimplementedStandardItems;
    }

    /**
     * 已实现的标准化项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImplementedStandardItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 标准化ID
         */
        private String standardId;

        /**
         * 标准化名称
         */
        private String standardName;

        /**
         * 标准化类型（alarm/signal/control）
         */
        private String standardType;

        /**
         * 对应的实际项数量
         */
        private Integer actualItemCount;

        /**
         * 对应的实际项列表（ID和名称）
         */
        private List<ActualItem> actualItems;
    }

    /**
     * 未实现的标准化项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UnimplementedStandardItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 标准化ID
         */
        private String standardId;

        /**
         * 标准化名称
         */
        private String standardName;

        /**
         * 标准化类型（alarm/signal/control）
         */
        private String standardType;
    }

    /**
     * 实际项信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActualItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 实际项ID
         */
        private String actualId;

        /**
         * 实际项名称
         */
        private String actualName;

        /**
         * 原始SP ID
         */
        private String originSpId;
    }
}
