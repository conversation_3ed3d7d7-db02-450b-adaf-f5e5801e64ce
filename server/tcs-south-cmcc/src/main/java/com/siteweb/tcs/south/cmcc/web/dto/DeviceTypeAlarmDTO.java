package com.siteweb.tcs.south.cmcc.web.dto;

import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 设备类型告警DTO
 * 用于表示设备类型及其对应的告警类型列表
 */
@ApiModel("设备类型告警DTO")
public class DeviceTypeAlarmDTO {

    @ApiModelProperty("设备类型信息")
    private DictionaryItem deviceType;

    @ApiModelProperty("该设备类型下的告警类型列表")
    private List<DictionaryItem> alarmTypes;

    @ApiModelProperty("告警类型数量")
    private Integer alarmTypeCount;

    public DeviceTypeAlarmDTO() {
    }

    public DeviceTypeAlarmDTO(DictionaryItem deviceType, List<DictionaryItem> alarmTypes) {
        this.deviceType = deviceType;
        this.alarmTypes = alarmTypes;
        this.alarmTypeCount = alarmTypes != null ? alarmTypes.size() : 0;
    }

    public DictionaryItem getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(DictionaryItem deviceType) {
        this.deviceType = deviceType;
    }

    public List<DictionaryItem> getAlarmTypes() {
        return alarmTypes;
    }

    public void setAlarmTypes(List<DictionaryItem> alarmTypes) {
        this.alarmTypes = alarmTypes;
        this.alarmTypeCount = alarmTypes != null ? alarmTypes.size() : 0;
    }

    public Integer getAlarmTypeCount() {
        return alarmTypeCount;
    }

    public void setAlarmTypeCount(Integer alarmTypeCount) {
        this.alarmTypeCount = alarmTypeCount;
    }
} 