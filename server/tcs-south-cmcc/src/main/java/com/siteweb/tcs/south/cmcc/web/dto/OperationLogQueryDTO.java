package com.siteweb.tcs.south.cmcc.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationObject;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-08-04)
 **/
@Data
public class OperationLogQueryDTO {
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private OperationType operationType;
    private OperationObject objectType;
    private String objectId;
}
