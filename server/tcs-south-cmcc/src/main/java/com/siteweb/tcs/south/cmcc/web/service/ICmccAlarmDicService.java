package com.siteweb.tcs.south.cmcc.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccAlarmDic;
import com.siteweb.tcs.south.cmcc.web.dto.CmccAlarmDicDTO;

import java.util.List;

/**
 * CMCC告警字典Service接口
 */
public interface ICmccAlarmDicService extends IService<CmccAlarmDic> {

    /**
     * 根据告警标准ID查询告警字典
     * @param alarmStandardId 告警标准ID
     * @return CmccAlarmDic对象
     */
    CmccAlarmDic getByAlarmStandardId(String alarmStandardId);

    /**
     * 根据设备类型查询告警字典列表
     * @param deviceType 设备类型
     * @return 告警字典列表
     */
    List<CmccAlarmDic> listByDeviceType(Integer deviceType);

    /**
     * 根据标准版本查询告警字典列表
     * @param standardVersion 标准版本
     * @return 告警字典列表
     */
    List<CmccAlarmDic> listByStandardVersion(Integer standardVersion);

    /**
     * 根据告警逻辑类别查询告警字典列表
     * @param alarmLogicClass 告警逻辑类别
     * @return 告警字典列表
     */
    List<CmccAlarmDic> listByAlarmLogicClass(Integer alarmLogicClass);

    /**
     * 保存告警字典
     * @param cmccAlarmDic 告警字典对象
     * @return 是否保存成功
     */
    boolean saveAlarmDic(CmccAlarmDic cmccAlarmDic);

    /**
     * 更新告警字典
     * @param cmccAlarmDic 告警字典对象
     * @return 是否更新成功
     */
    boolean updateAlarmDic(CmccAlarmDic cmccAlarmDic);

    /**
     * 根据ID删除告警字典
     * @param id 主键ID
     * @return 是否删除成功
     */
    boolean deleteAlarmDic(Integer id);

    /**
     * 查询所有告警字典数据（包含字典名称）
     * @return 包含字典名称的告警字典列表
     */
    List<CmccAlarmDicDTO> listAllWithNames();
} 