package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDeviceInit;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsuInit;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCConfigGeneratorService;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCDeviceInitService;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCFsuInitService;
import com.siteweb.tcs.south.cmcc.util.CmccFileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * CMCC配置文件生成服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class CMCCConfigGeneratorServiceImpl implements ICMCCConfigGeneratorService {
    
    private static final Logger log = LoggerFactory.getLogger(CMCCConfigGeneratorServiceImpl.class);
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private ICMCCFsuInitService cmccFsuInitService;
    
    @Autowired
    private ICMCCDeviceInitService cmccDeviceInitService;
    
    @Autowired
    private CmccFileUtil cmccFileUtil;

    
    @Override
    public int generateSiteMappingConfigs(List<Integer> siteWebMuIds) {
        int successCount = 0;
        
        for (Integer siteWebMuId : siteWebMuIds) {
            try {
                if (generateSiteMappingConfig(siteWebMuId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("生成监控单元 {} 的配置文件失败", siteWebMuId, e);
            }
        }
        
        log.info("批量生成配置文件完成，总数: {}, 成功: {}", siteWebMuIds.size(), successCount);
        return successCount;
    }
    
    @Override
    public boolean generateSiteMappingConfig(Integer siteWebMuId) {
        try {
            // 1. 获取FSU配置信息
            CMCCFsuInit fsuInit = cmccFsuInitService.getBySiteWebMuId(siteWebMuId);
            if (fsuInit == null) {
                log.warn("监控单元 {} 的FSU配置不存在，跳过生成", siteWebMuId);
                return false;
            }
            
            // 2. 获取设备配置列表
            List<CMCCDeviceInit> deviceInits = cmccDeviceInitService.listBySiteWebMuId(siteWebMuId);
            
            // 3. 生成配置文件内容
            String configContent = generateConfigContent(fsuInit, deviceInits);
            
            // 4. 确保目录存在
            String directoryPath = "/cmb-init/" + siteWebMuId;
            if (!cmccFileUtil.createDirectory(directoryPath)) {
                log.error("创建目录失败: {}", directoryPath);
                return false;
            }
            
            // 5. 写入配置文件
            String fileName = "cmb_init_list.ini";
            boolean writeSuccess = cmccFileUtil.writeTextFile(directoryPath, fileName, configContent);
            
            if (writeSuccess) {
                log.info("成功生成监控单元 {} 的配置文件: {}/{}", siteWebMuId, directoryPath, fileName);
                return true;
            } else {
                log.error("写入配置文件失败: {}/{}", directoryPath, fileName);
                return false;
            }
            
        } catch (Exception e) {
            log.error("生成监控单元 {} 的配置文件时发生异常", siteWebMuId, e);
            return false;
        }
    }
    
    /**
     * 生成配置文件内容
     */
    private String generateConfigContent(CMCCFsuInit fsuInit, List<CMCCDeviceInit> deviceInits) {
        StringBuilder content = new StringBuilder();
        
        // 生成 [FSUINFO] 节
        content.append("[FSUINFO]\n");
        content.append("Type76DeviceID=").append(safeGetString(fsuInit.getType76DeviceId())).append("\n");
        content.append("IFName=").append(safeGetString(fsuInit.getScip())).append("\n");
        content.append("Type76DeviceName=").append(safeGetString(fsuInit.getType76DeviceName())).append("\n");
        content.append("FSUTYPE=").append(safeGetInteger(fsuInit.getFsuType())).append("\n");
        content.append("RoomName=").append(safeGetString(fsuInit.getRoomName())).append("\n");
        content.append("RoomId=").append(safeGetString(fsuInit.getRoomId())).append("\n");
        content.append("FTPPwd=").append(safeGetString(fsuInit.getFtpPwd())).append("\n");
        content.append("FTPUser=").append(safeGetString(fsuInit.getFtpUser())).append("\n");
        content.append("SiteName=").append(safeGetString(fsuInit.getSiteName())).append("\n");
        content.append("SiteID=").append(safeGetString(fsuInit.getSiteId())).append("\n");
        content.append("FSUID=").append(safeGetString(fsuInit.getFsuId())).append("\n");
        content.append("FSUPort=").append(safeGetInteger(fsuInit.getFsuPort())).append("\n");
        content.append("EnableSendSyncAlarm=false\n"); // 固定值
        content.append("EnableACL=").append(fsuInit.getEnableAcl() != null && fsuInit.getEnableAcl() ? "true" : "false").append("\n");
        content.append("LoginPwd=").append(safeGetString(fsuInit.getLoginPwd())).append("\n");
        content.append("LoginUser=").append(safeGetString(fsuInit.getLoginUser())).append("\n");
        content.append("SCSwitchMode1=").append(safeGetInteger(fsuInit.getScSwitchMode1())).append("\n");
        content.append("PlatFormName=").append(safeGetString(fsuInit.getPlatFormName())).append("\n");
        content.append("SCSwitchMode=").append(safeGetInteger(fsuInit.getScSwitchMode())).append("\n");
        content.append("SCURLSuffix=").append(safeGetString(fsuInit.getScUrlSuffix())).append("\n");
        content.append("SCPort=").append(safeGetInteger(fsuInit.getScPort())).append("\n");
        content.append("SCIP=").append(safeGetString(fsuInit.getScip())).append("\n");
        content.append("PlatFormNo=").append(safeGetInteger(fsuInit.getPlatFormNo())).append("\n");
        
        // 生成 [DEVICE_NUM] 节
        content.append("[DEVICE_NUM]\n");
        content.append("DeviceNum=").append(deviceInits.size()).append("\n");
        
        // 生成设备配置节
        for (int i = 0; i < deviceInits.size(); i++) {
            CMCCDeviceInit device = deviceInits.get(i);
            int deviceIndex = i + 1;
            
            content.append("[DEVICE").append(deviceIndex).append("]\n");
            content.append("RatedCapacity=").append(device.getRatedCapacity() != null ? String.format("%.2f", device.getRatedCapacity()) : "0.00").append("\n");
            content.append("DeviceSubType=").append(formatDeviceType(device.getDeviceSubType())).append("\n");
            content.append("DeviceType=").append(safeGetInteger(device.getDeviceType())).append("\n");
            content.append("SiteName=").append(safeGetString(fsuInit.getSiteName())).append("\n");
            content.append("SiteID=").append(safeGetString(fsuInit.getSiteId())).append("\n");
            content.append("RoomName=").append(safeGetString(device.getRoomName())).append("\n");
            content.append("NamePrefix=").append(safeGetString(device.getNamePrefix())).append("\n");
            content.append("SiteWebEquipId=").append(safeGetInteger(device.getSiteWebEquipId())).append("\n");
            content.append("BeginRunTime=").append(device.getBeginRunTime() != null ? device.getBeginRunTime().format(DATE_FORMATTER) : "").append("\n");
            content.append("Brand=").append(safeGetString(device.getBrand())).append("\n");
            content.append("Model=").append(safeGetString(device.getModel())).append("\n");
            content.append("RoomId=").append(safeGetLong(device.getRoomId())).append("\n");
            content.append("DeviceId=").append(safeGetLong(device.getDeviceId())).append("\n");
            content.append("DeviceName=").append(safeGetString(device.getDeviceName())).append("\n");
            content.append("Version=").append(safeGetString(device.getVersion())).append("\n");
            content.append("DevDescribe=").append(safeGetString(device.getDevDescribe())).append("\n");
        }
        
        content.append("\n");
        
        return content.toString();
    }
    
    /**
     * 安全获取字符串值
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }
    
    /**
     * 安全获取整数值
     */
    private String safeGetInteger(Integer value) {
        return value != null ? value.toString() : "0";
    }
    
    /**
     * 安全获取长整数值
     */
    private String safeGetLong(Long value) {
        return value != null ? value.toString() : "0";
    }
    
    /**
     * 格式化设备类型为两位数字符串
     */
    private String formatDeviceType(Integer deviceType) {
        if (deviceType == null) {
            return "01";
        }
        return String.format("%02d", deviceType);
    }
}