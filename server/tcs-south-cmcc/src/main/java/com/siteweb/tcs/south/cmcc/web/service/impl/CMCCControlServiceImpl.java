package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCControl;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccSignalDic;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCControlMapper;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCControlWithStandardization;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCControlService;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCDeviceService;
import com.siteweb.tcs.south.cmcc.web.service.ICmccSignalDicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CMCC控制Service实现类
 * <AUTHOR> (2025-07-30)
 */
@Slf4j
@Service
public class CMCCControlServiceImpl extends ServiceImpl<CMCCControlMapper, CMCCControl> implements ICMCCControlService {

    @Autowired
    private ICMCCDeviceService cmccDeviceService;

    @Autowired
    private ICmccSignalDicService cmccSignalDicService;

    @Override
    public List<CMCCControl> listByFsuId(String fsuId) {
        LambdaQueryWrapper<CMCCControl> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCControl::getFsuId, fsuId);
        }
        return list(wrapper);
    }

    @Override
    public List<CMCCControl> listByDeviceId(String deviceId) {
        LambdaQueryWrapper<CMCCControl> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCControl::getDeviceId, deviceId);
        }
        return list(wrapper);
    }

    @Override
    public List<CMCCControl> listByFsuIdAndDeviceId(String fsuId, String deviceId) {
        LambdaQueryWrapper<CMCCControl> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCControl::getFsuId, fsuId);
        }
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCControl::getDeviceId, deviceId);
        }
        return list(wrapper);
    }

    @Override
    public IPage<CMCCControl> pageByFsuId(long current, long size, String fsuId) {
        Page<CMCCControl> page = new Page<>(current, size);
        LambdaQueryWrapper<CMCCControl> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCControl::getFsuId, fsuId);
        }
        return page(page, wrapper);
    }

    @Override
    public IPage<CMCCControl> pageByDeviceId(long current, long size, String deviceId) {
        Page<CMCCControl> page = new Page<>(current, size);
        LambdaQueryWrapper<CMCCControl> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCControl::getDeviceId, deviceId);
        }
        return page(page, wrapper);
    }

    @Override
    public IPage<CMCCControl> pageByFsuIdAndDeviceId(long current, long size, String fsuId, String deviceId) {
        Page<CMCCControl> page = new Page<>(current, size);
        LambdaQueryWrapper<CMCCControl> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCControl::getFsuId, fsuId);
        }
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCControl::getDeviceId, deviceId);
        }
        return page(page, wrapper);
    }

    @Override
    public CMCCControl getControlById(Long id) {
        return getById(id);
    }

    @Override
    public List<CMCCControlWithStandardization> listWithStandardizationByFsuIdAndDeviceId(String fsuId, String deviceId) {
        log.info("查询控制标准化信息，FSUid: {}, DeviceId: {}", fsuId, deviceId);

        // 获取控制列表
        List<CMCCControl> controls = listByFsuIdAndDeviceId(fsuId, deviceId);
        if (controls.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取设备信息以获取设备类型
        String deviceType = null;
        if (StringUtils.hasText(deviceId)) {
            List<CMCCDevice> devices = cmccDeviceService.listByFsuId(fsuId);
            for (CMCCDevice device : devices) {
                if (deviceId.equals(device.getDeviceId())) {
                    deviceType = device.getDeviceType();
                    break;
                }
            }
        }

        // 获取控制字典（semaphoretype = 1 或 2）
        List<CmccSignalDic> controlDics = new ArrayList<>();
        if (StringUtils.hasText(deviceType)) {
            try {
                Integer deviceTypeInt = Integer.valueOf(deviceType);
                List<CmccSignalDic> allSignalDics = cmccSignalDicService.listByDeviceType(deviceTypeInt);
                controlDics = allSignalDics.stream()
                        .filter(dic -> dic.getSemaphoreType() != null &&
                                      (dic.getSemaphoreType() == 1 || dic.getSemaphoreType() == 2))
                        .collect(Collectors.toList());
            } catch (NumberFormatException e) {
                log.warn("设备类型转换失败，deviceType: {}", deviceType);
            }
        }

        // 构建标准化信息
        List<CMCCControlWithStandardization> result = new ArrayList<>();
        for (CMCCControl control : controls) {
            CMCCControlWithStandardization controlWithStd = new CMCCControlWithStandardization(control);

            // 检查是否标准化
            CmccSignalDic matchedDic = findMatchingControlDic(control, controlDics);
            if (matchedDic != null) {
                controlWithStd.setStandardized(true);
                controlWithStd.setStandardName(matchedDic.getStandardSignalName());
                controlWithStd.setStandardId(matchedDic.getSignalStandardId());
            } else {
                controlWithStd.setStandardized(false);
            }

            result.add(controlWithStd);
        }

        return result;
    }

    /**
     * 查找匹配的控制字典
     */
    private CmccSignalDic findMatchingControlDic(CMCCControl control, List<CmccSignalDic> controlDics) {
        if (control.getOriginSpId() == null || controlDics.isEmpty()) {
            return null;
        }
        return controlDics.stream()
                .filter(dic -> dic.getSignalStandardId() != null && dic.getSignalStandardId().endsWith(control.getOriginSpId()))
                .findFirst()
                .orElse(null);
    }
}
