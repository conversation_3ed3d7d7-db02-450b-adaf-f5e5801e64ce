package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDeviceInit;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCDeviceInitMapper;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCDeviceInitDTO;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCDeviceInitService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CMCC设备初始化服务实现类
 * CMCC Device Init Service Implementation
 */
@Service
public class CMCCDeviceInitServiceImpl extends ServiceImpl<CMCCDeviceInitMapper, CMCCDeviceInit> implements ICMCCDeviceInitService {
    
    private static final Logger log = LoggerFactory.getLogger(CMCCDeviceInitServiceImpl.class);

    @Resource
    private CMCCDeviceInitMapper cmccDeviceInitMapper;

    @Override
    public CMCCDeviceInit getByDeviceIdAndSiteWebEquipId(Long deviceId, Integer siteWebEquipId) {
        if (deviceId == null || siteWebEquipId == null) {
            log.warn("Device ID or SiteWeb Equipment ID cannot be null");
            return null;
        }
        return cmccDeviceInitMapper.selectByDeviceIdAndSiteWebEquipId(deviceId, siteWebEquipId);
    }

    @Override
    public List<CMCCDeviceInit> listByRoomId(Long roomId) {
        if (roomId == null) {
            log.warn("Room ID cannot be null");
            return List.of();
        }
        return cmccDeviceInitMapper.selectByRoomId(roomId);
    }

    @Override
    public List<CMCCDeviceInit> listByDeviceType(Integer deviceType) {
        if (deviceType == null) {
            log.warn("Device type cannot be null");
            return List.of();
        }
        return cmccDeviceInitMapper.selectByDeviceType(deviceType);
    }

    @Override
    public List<CMCCDeviceInit> listByDeviceNameLike(String deviceName) {
        if (deviceName == null || deviceName.trim().isEmpty()) {
            log.warn("Device name cannot be null or empty");
            return List.of();
        }
        return cmccDeviceInitMapper.selectByDeviceNameLike(deviceName.trim());
    }

    @Override
    public CMCCDeviceInit getBySiteWebEquipId(Integer siteWebEquipId) {
        if (siteWebEquipId == null) {
            log.warn("SiteWeb Equipment ID cannot be null");
            return null;
        }
        return cmccDeviceInitMapper.selectBySiteWebEquipId(siteWebEquipId);
    }

    @Override
    public List<CMCCDeviceInitDTO> listDTOBySiteWebMuId(Integer siteWebMuId) {
        if (siteWebMuId == null) {
            log.warn("SiteWeb监控单元ID不能为空");
            return List.of();
        }
        
        try {
            List<CMCCDeviceInit> deviceList = cmccDeviceInitMapper.selectBySiteWebMuId(siteWebMuId);
            return deviceList.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据SiteWeb监控单元ID查询设备失败: siteWebMuId={}, error={}", siteWebMuId, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<CMCCDeviceInit> listBySiteWebMuId(Integer siteWebMuId) {
        if (siteWebMuId == null) {
            log.warn("SiteWeb监控单元ID不能为空");
            return List.of();
        }

        try {
            List<CMCCDeviceInit> deviceList = cmccDeviceInitMapper.selectBySiteWebMuId(siteWebMuId);
            return deviceList;
        } catch (Exception e) {
            log.error("根据SiteWeb监控单元ID查询设备失败: siteWebMuId={}, error={}", siteWebMuId, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<CMCCDeviceInit> deviceList) {
        if (deviceList == null || deviceList.isEmpty()) {
            log.warn("Device list cannot be null or empty");
            return false;
        }
        
        try {
            int result = cmccDeviceInitMapper.batchInsert(deviceList);
            if (result > 0) {
                log.info("Successfully batch inserted {} device records", result);
                return true;
            } else {
                log.warn("Batch insert failed, no records inserted");
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred during batch insert: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByDeviceIdAndSiteWebEquipId(Long deviceId, Integer siteWebEquipId) {
        if (deviceId == null || siteWebEquipId == null) {
            log.warn("Device ID or SiteWeb Equipment ID cannot be null");
            return false;
        }
        
        try {
            int result = cmccDeviceInitMapper.deleteByDeviceIdAndSiteWebEquipId(deviceId, siteWebEquipId);
            if (result > 0) {
                log.info("Successfully deleted device record: DeviceId={}, SiteWebEquipId={}", deviceId, siteWebEquipId);
                return true;
            } else {
                log.warn("No device record found to delete: DeviceId={}, SiteWebEquipId={}", deviceId, siteWebEquipId);
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred during device deletion: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateDevice(CMCCDeviceInitDTO deviceDto) {
        if (deviceDto == null) {
            throw new IllegalArgumentException("设备配置不能为空");
        }
        
        if (deviceDto.getSiteWebEquipId() == null) {
            throw new IllegalArgumentException("SiteWeb设备ID不能为空");
        }
        
        // 必填字段验证
        if (deviceDto.getDeviceId() == null || deviceDto.getDeviceId() <= 0) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        
        if (deviceDto.getDeviceName() == null || deviceDto.getDeviceName().trim().isEmpty()) {
            throw new IllegalArgumentException("设备名称不能为空");
        }
        
        if (deviceDto.getDeviceType() == null || deviceDto.getDeviceType() <= 0) {
            throw new IllegalArgumentException("设备类型不能为空");
        }
        
        if (deviceDto.getDeviceSubType() == null || deviceDto.getDeviceSubType() <= 0) {
            throw new IllegalArgumentException("设备子类型不能为空");
        }
        
        try {
            // 检查设备是否已存在（以主键 SiteWebEquipId 判断）
            CMCCDeviceInit existingDevice = cmccDeviceInitMapper.selectBySiteWebEquipId(deviceDto.getSiteWebEquipId());
            
            // 如果是新增或者更新设备ID，检查在同一FSU下的唯一性
            if (deviceDto.getDeviceId() != null && deviceDto.getSiteWebMuId() != null) {
                // 检查DeviceId在同一个FSU（SiteWebMuId）下是否重复
                QueryWrapper<CMCCDeviceInit> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("SiteWebMuId",deviceDto.getSiteWebMuId());
                queryWrapper.eq("DeviceId",deviceDto.getDeviceId());
                List<CMCCDeviceInit> devicesInSameFsu = cmccDeviceInitMapper.selectList(queryWrapper);
                for (CMCCDeviceInit device : devicesInSameFsu) {
                    // 如果找到相同DeviceId的设备，且不是当前正在编辑的设备，则报错
                    if (device.getDeviceId().equals(deviceDto.getDeviceId()) && 
                        !device.getSiteWebEquipId().equals(deviceDto.getSiteWebEquipId())) {
                        throw new IllegalArgumentException("设备ID '" + deviceDto.getDeviceId() + "' 在当前FSU下已存在，同一FSU下的设备ID必须唯一");
                    }
                }
            }
            
            // 转换DTO为Entity
            CMCCDeviceInit device = convertToEntity(deviceDto);
            
            boolean result;
            if (existingDevice != null) {
                // 更新现有设备
                result = updateById(device);
                if (result) {
                    log.info("Successfully updated device: DeviceId={}, SiteWebEquipId={}", device.getDeviceId(), device.getSiteWebEquipId());
                }
            } else {
                // 插入新设备
                result = save(device);
                if (result) {
                    log.info("Successfully inserted new device: DeviceId={}, SiteWebEquipId={}", device.getDeviceId(), device.getSiteWebEquipId());
                }
            }
            
            return result;
        } catch (IllegalArgumentException e) {
            log.warn("设备配置校验失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error occurred during save or update device: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean removeBySiteWebEquipId(Integer siteWebEquipId) {
        try {
            LambdaQueryWrapper<CMCCDeviceInit> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CMCCDeviceInit::getSiteWebEquipId, siteWebEquipId);
            
            boolean result = this.remove(queryWrapper);
            log.info("删除设备配置成功: SiteWebEquipId={}", siteWebEquipId);
            return result;
        } catch (Exception e) {
            log.error("删除设备配置失败: SiteWebEquipId={}, Error: {}", siteWebEquipId, e.getMessage(), e);
            throw new RuntimeException("删除设备配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 将DTO转换为Entity
     */
    private CMCCDeviceInit convertToEntity(CMCCDeviceInitDTO dto) {
        CMCCDeviceInit entity = new CMCCDeviceInit();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 将CMCCDeviceInit实体转换为CMCCDeviceInitDTO
     * @param deviceInit 设备初始化实体
     * @return 设备初始化DTO
     */
    private CMCCDeviceInitDTO convertToDTO(CMCCDeviceInit deviceInit) {
        if (deviceInit == null) {
            return null;
        }
        
        CMCCDeviceInitDTO dto = new CMCCDeviceInitDTO();
        BeanUtils.copyProperties(deviceInit, dto);
        return dto;
    }
}
