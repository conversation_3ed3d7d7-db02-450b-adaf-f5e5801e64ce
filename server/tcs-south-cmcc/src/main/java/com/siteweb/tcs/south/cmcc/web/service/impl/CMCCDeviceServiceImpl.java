package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.*;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCDeviceMapper;
import com.siteweb.tcs.south.cmcc.web.dto.DeviceStandardizationInfo;
import com.siteweb.tcs.south.cmcc.web.dto.DeviceStandardizationUsage;
import com.siteweb.tcs.south.cmcc.web.dto.FsuStandardizationStatistics;
import com.siteweb.tcs.south.cmcc.web.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CMCC设备Service实现类
 * <AUTHOR> (2025-07-30)
 */
@Slf4j
@Service
public class CMCCDeviceServiceImpl extends ServiceImpl<CMCCDeviceMapper, CMCCDevice> implements ICMCCDeviceService {

    @Autowired
    private ICMCCAlarmService cmccAlarmService;

    @Autowired
    private ICMCCSignalService cmccSignalService;

    @Autowired
    private ICMCCControlService cmccControlService;

    @Autowired
    private IDictionaryItemService dictionaryItemService;

    @Autowired
    private ICmccAlarmDicService cmccAlarmDicService;

    @Autowired
    private ICmccSignalDicService cmccSignalDicService;

    @Override
    public List<CMCCDevice> listByFsuId(String fsuId) {
        LambdaQueryWrapper<CMCCDevice> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCDevice::getFsuId, fsuId);
        }
        return list(wrapper);
    }

    @Override
    public IPage<CMCCDevice> pageByFsuId(long current, long size, String fsuId) {
        Page<CMCCDevice> page = new Page<>(current, size);
        LambdaQueryWrapper<CMCCDevice> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCDevice::getFsuId, fsuId);
        }
        return page(page, wrapper);
    }

    @Override
    public CMCCDevice getDeviceById(Long id) {
        return getById(id);
    }

    @Override
    public List<DeviceStandardizationInfo> listStandardizationInfoByFsuId(String fsuId) {
        log.info("查询FSU设备标准化信息，FSUid: {}", fsuId);

        // 获取设备列表
        List<CMCCDevice> devices = listByFsuId(fsuId);
        if (devices.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取设备类型字典（categoryId = 2）
        List<DictionaryItem> deviceTypeDictionary = dictionaryItemService.listByCategoryId(2);
        Map<Integer, String> deviceTypeMap = deviceTypeDictionary.stream()
                .collect(Collectors.toMap(DictionaryItem::getItemId, DictionaryItem::getItemValue));

        List<DeviceStandardizationInfo> result = new ArrayList<>();

        for (CMCCDevice device : devices) {
            DeviceStandardizationInfo info = buildDeviceStandardizationInfo(device, deviceTypeMap);
            result.add(info);
        }

        return result;
    }

    @Override
    public FsuStandardizationStatistics getStandardizationStatistics(String fsuId) {
        log.info("查询FSU标准化统计信息，FSUid: {}", fsuId);

        List<DeviceStandardizationInfo> deviceInfos = listStandardizationInfoByFsuId(fsuId);

        FsuStandardizationStatistics statistics = new FsuStandardizationStatistics();
        statistics.setFsuId(fsuId);
        statistics.setTotalDeviceCount(deviceInfos.size());

        if (deviceInfos.isEmpty()) {
            // 设置默认值
            statistics.setStandardizedTypeDeviceCount(0);
            statistics.setFullyStandardizedDeviceCount(0);
            statistics.setOverallStandardizationRate(BigDecimal.ZERO);
            statistics.setDeviceTypeStandardizationRate(BigDecimal.ZERO);
            statistics.setTotalAlarmCount(0);
            statistics.setStandardizedAlarmCount(0);
            statistics.setAlarmStandardizationRate(BigDecimal.ZERO);
            statistics.setTotalSignalCount(0);
            statistics.setStandardizedSignalCount(0);
            statistics.setSignalStandardizationRate(BigDecimal.ZERO);
            statistics.setTotalControlCount(0);
            statistics.setStandardizedControlCount(0);
            statistics.setControlStandardizationRate(BigDecimal.ZERO);
            return statistics;
        }

        // 统计各项数据
        int standardizedTypeDeviceCount = 0;
        int fullyStandardizedDeviceCount = 0;
        int totalAlarmCount = 0;
        int standardizedAlarmCount = 0;
        int totalSignalCount = 0;
        int standardizedSignalCount = 0;
        int totalControlCount = 0;
        int standardizedControlCount = 0;

        for (DeviceStandardizationInfo info : deviceInfos) {
            if (Boolean.TRUE.equals(info.getDeviceTypeStandardized())) {
                standardizedTypeDeviceCount++;
            }
            if (Boolean.TRUE.equals(info.getFullyStandardized())) {
                fullyStandardizedDeviceCount++;
            }

            totalAlarmCount += info.getTotalAlarmCount();
            standardizedAlarmCount += info.getStandardizedAlarmCount();
            totalSignalCount += info.getTotalSignalCount();
            standardizedSignalCount += info.getStandardizedSignalCount();
            totalControlCount += info.getTotalControlCount();
            standardizedControlCount += info.getStandardizedControlCount();
        }

        // 计算各项标准化率
        statistics.setStandardizedTypeDeviceCount(standardizedTypeDeviceCount);
        statistics.setFullyStandardizedDeviceCount(fullyStandardizedDeviceCount);
        statistics.setOverallStandardizationRate(calculateRate(fullyStandardizedDeviceCount, deviceInfos.size()));
        statistics.setDeviceTypeStandardizationRate(calculateRate(standardizedTypeDeviceCount, deviceInfos.size()));

        statistics.setTotalAlarmCount(totalAlarmCount);
        statistics.setStandardizedAlarmCount(standardizedAlarmCount);
        statistics.setAlarmStandardizationRate(calculateRate(standardizedAlarmCount, totalAlarmCount));

        statistics.setTotalSignalCount(totalSignalCount);
        statistics.setStandardizedSignalCount(standardizedSignalCount);
        statistics.setSignalStandardizationRate(calculateRate(standardizedSignalCount, totalSignalCount));

        statistics.setTotalControlCount(totalControlCount);
        statistics.setStandardizedControlCount(standardizedControlCount);
        statistics.setControlStandardizationRate(calculateRate(standardizedControlCount, totalControlCount));

        return statistics;
    }

    /**
     * 构建设备标准化信息
     */
    private DeviceStandardizationInfo buildDeviceStandardizationInfo(CMCCDevice device, Map<Integer, String> deviceTypeMap) {
        DeviceStandardizationInfo info = new DeviceStandardizationInfo();
        info.setDeviceId(device.getDeviceId());
        info.setDeviceName(device.getDeviceName());
        info.setDeviceType(device.getDeviceType());

        // 检查设备类型标准化
        boolean deviceTypeStandardized = false;
        String deviceTypeStandardName = null;
        if (StringUtils.hasText(device.getDeviceType())) {
            try {
                Integer deviceTypeInt = Integer.valueOf(device.getDeviceType());
                deviceTypeStandardName = deviceTypeMap.get(deviceTypeInt);
                deviceTypeStandardized = deviceTypeStandardName != null;
            } catch (NumberFormatException e) {
                log.warn("设备类型转换失败，deviceId: {}, deviceType: {}", device.getDeviceId(), device.getDeviceType());
            }
        }
        info.setDeviceTypeStandardized(deviceTypeStandardized);
        info.setDeviceTypeStandardName(deviceTypeStandardName);

        // 获取设备的告警、信号、控制数据
        List<CMCCAlarm> alarms = cmccAlarmService.listByDeviceId(device.getDeviceId());
        List<CMCCSignal> signals = cmccSignalService.listByDeviceId(device.getDeviceId());
        List<CMCCControl> controls = cmccControlService.listByDeviceId(device.getDeviceId());

        // 计算告警标准化信息
        calculateAlarmStandardization(info, alarms, device.getDeviceType());

        // 计算信号标准化信息
        calculateSignalStandardization(info, signals, device.getDeviceType());

        // 计算控制标准化信息
        calculateControlStandardization(info, controls, device.getDeviceType());

        // 判断是否完全标准化
        boolean fullyStandardized = deviceTypeStandardized &&
                isRateFullyStandardized(info.getAlarmStandardizationRate()) &&
                isRateFullyStandardized(info.getSignalStandardizationRate()) &&
                isRateFullyStandardized(info.getControlStandardizationRate());
        info.setFullyStandardized(fullyStandardized);

        // 计算整体标准化率
        BigDecimal overallRate = calculateOverallStandardizationRate(
                info.getAlarmStandardizationRate(),
                info.getSignalStandardizationRate(),
                info.getControlStandardizationRate()
        );
        info.setOverallStandardizationRate(overallRate);

        return info;
    }

    /**
     * 计算告警标准化信息
     */
    private void calculateAlarmStandardization(DeviceStandardizationInfo info, List<CMCCAlarm> alarms, String deviceType) {
        info.setTotalAlarmCount(alarms.size());

        if (alarms.isEmpty()) {
            info.setStandardizedAlarmCount(0);
            info.setAlarmStandardizationRate(BigDecimal.valueOf(100));
            return;
        }

        int standardizedCount = 0;
        if (StringUtils.hasText(deviceType)) {
            try {
                Integer deviceTypeInt = Integer.valueOf(deviceType);
                List<CmccAlarmDic> alarmDics = cmccAlarmDicService.listByDeviceType(deviceTypeInt);

                for (CMCCAlarm alarm : alarms) {
                    if (isAlarmStandardized(alarm, alarmDics)) {
                        standardizedCount++;
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("设备类型转换失败，deviceType: {}", deviceType);
            }
        }

        info.setStandardizedAlarmCount(standardizedCount);
        info.setAlarmStandardizationRate(calculateRate(standardizedCount, alarms.size()));
    }

    /**
     * 计算信号标准化信息
     */
    private void calculateSignalStandardization(DeviceStandardizationInfo info, List<CMCCSignal> signals, String deviceType) {
        info.setTotalSignalCount(signals.size());

        if (signals.isEmpty()) {
            info.setStandardizedSignalCount(0);
            info.setSignalStandardizationRate(BigDecimal.valueOf(100));
            return;
        }

        int standardizedCount = 0;
        if (StringUtils.hasText(deviceType)) {
            try {
                Integer deviceTypeInt = Integer.valueOf(deviceType);
                List<CmccSignalDic> signalDics = cmccSignalDicService.listByDeviceType(deviceTypeInt);
                // 过滤信号类型（semaphoretype = 3 或 4）
                List<CmccSignalDic> signalTypeDics = signalDics.stream()
                        .filter(dic -> dic.getSemaphoreType() != null &&
                                      (dic.getSemaphoreType() == 3 || dic.getSemaphoreType() == 4))
                        .collect(Collectors.toList());

                for (CMCCSignal signal : signals) {
                    if (isSignalStandardized(signal, signalTypeDics)) {
                        standardizedCount++;
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("设备类型转换失败，deviceType: {}", deviceType);
            }
        }

        info.setStandardizedSignalCount(standardizedCount);
        info.setSignalStandardizationRate(calculateRate(standardizedCount, signals.size()));
    }

    /**
     * 计算控制标准化信息
     */
    private void calculateControlStandardization(DeviceStandardizationInfo info, List<CMCCControl> controls, String deviceType) {
        info.setTotalControlCount(controls.size());

        if (controls.isEmpty()) {
            info.setStandardizedControlCount(0);
            info.setControlStandardizationRate(BigDecimal.valueOf(100));
            return;
        }

        int standardizedCount = 0;
        if (StringUtils.hasText(deviceType)) {
            try {
                Integer deviceTypeInt = Integer.valueOf(deviceType);
                List<CmccSignalDic> signalDics = cmccSignalDicService.listByDeviceType(deviceTypeInt);
                // 过滤控制类型（semaphoretype = 1 或 2）
                List<CmccSignalDic> controlTypeDics = signalDics.stream()
                        .filter(dic -> dic.getSemaphoreType() != null &&
                                      (dic.getSemaphoreType() == 1 || dic.getSemaphoreType() == 2))
                        .collect(Collectors.toList());

                for (CMCCControl control : controls) {
                    if (isControlStandardized(control, controlTypeDics)) {
                        standardizedCount++;
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("设备类型转换失败，deviceType: {}", deviceType);
            }
        }

        info.setStandardizedControlCount(standardizedCount);
        info.setControlStandardizationRate(calculateRate(standardizedCount, controls.size()));
    }

    /**
     * 检查告警是否标准化
     */
    private boolean isAlarmStandardized(CMCCAlarm alarm, List<CmccAlarmDic> alarmDics) {
        if (alarm.getOriginSpId() == null || alarmDics.isEmpty()) {
            return false;
        }
        return alarmDics.stream().anyMatch(dic ->
            dic.getAlarmStandardId() != null && dic.getAlarmStandardId().endsWith(alarm.getOriginSpId())
        );
    }

    /**
     * 检查信号是否标准化
     */
    private boolean isSignalStandardized(CMCCSignal signal, List<CmccSignalDic> signalDics) {
        if (signal.getOriginSpId() == null || signalDics.isEmpty()) {
            return false;
        }
        return signalDics.stream().anyMatch(dic ->
            dic.getSignalStandardId() != null && dic.getSignalStandardId().endsWith(signal.getOriginSpId())
        );
    }

    /**
     * 检查控制是否标准化
     */
    private boolean isControlStandardized(CMCCControl control, List<CmccSignalDic> controlDics) {
        if (control.getOriginSpId() == null || controlDics.isEmpty()) {
            return false;
        }
        return controlDics.stream().anyMatch(dic ->
            dic.getSignalStandardId() != null && dic.getSignalStandardId().endsWith(control.getOriginSpId())
        );
    }

    /**
     * 计算百分比率
     */
    private BigDecimal calculateRate(int numerator, int denominator) {
        if (denominator == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(numerator)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP);
    }

    /**
     * 判断标准化率是否为100%
     */
    private boolean isRateFullyStandardized(BigDecimal rate) {
        return rate != null && rate.compareTo(BigDecimal.valueOf(100)) == 0;
    }

    /**
     * 计算整体标准化率（各维度平均值）
     */
    private BigDecimal calculateOverallStandardizationRate(BigDecimal alarmRate, BigDecimal signalRate, BigDecimal controlRate) {
        BigDecimal sum = BigDecimal.ZERO;
        int count = 0;

        if (alarmRate != null) {
            sum = sum.add(alarmRate);
            count++;
        }
        if (signalRate != null) {
            sum = sum.add(signalRate);
            count++;
        }
        if (controlRate != null) {
            sum = sum.add(controlRate);
            count++;
        }

        if (count == 0) {
            return BigDecimal.ZERO;
        }

        return sum.divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP);
    }

    @Override
    public DeviceStandardizationUsage getDeviceStandardizationUsage(String deviceId) {
        log.info("查询设备标准化使用率统计，deviceId: {}", deviceId);

        // 获取设备信息
        CMCCDevice device = getDeviceByDeviceId(deviceId);
        if (device == null) {
            log.warn("设备不存在，deviceId: {}", deviceId);
            return createEmptyDeviceUsage(deviceId);
        }

        DeviceStandardizationUsage usage = new DeviceStandardizationUsage();
        usage.setDeviceId(device.getDeviceId());
        usage.setDeviceName(device.getDeviceName());
        usage.setDeviceType(device.getDeviceType());

        // 获取设备类型名称
        List<DictionaryItem> deviceTypeDictionary = dictionaryItemService.listByCategoryId(2);
        String deviceTypeName = null;
        if (StringUtils.hasText(device.getDeviceType())) {
            try {
                Integer deviceTypeInt = Integer.valueOf(device.getDeviceType());
                deviceTypeName = deviceTypeDictionary.stream()
                        .filter(item -> deviceTypeInt.equals(item.getItemId()))
                        .map(DictionaryItem::getItemValue)
                        .findFirst()
                        .orElse(null);
            } catch (NumberFormatException e) {
                log.warn("设备类型转换失败，deviceType: {}", device.getDeviceType());
            }
        }
        usage.setDeviceTypeName(deviceTypeName);

        if (!StringUtils.hasText(device.getDeviceType())) {
            // 设置默认值
            usage.setAlarmUsage(createEmptyUsageDetail());
            usage.setSignalUsage(createEmptyUsageDetail());
            usage.setControlUsage(createEmptyUsageDetail());
            return usage;
        }

        try {
            Integer deviceTypeInt = Integer.valueOf(device.getDeviceType());

            // 获取该设备类型的所有标准化字典
            List<CmccAlarmDic> alarmDics = cmccAlarmDicService.listByDeviceType(deviceTypeInt);
            List<CmccSignalDic> allSignalDics = cmccSignalDicService.listByDeviceType(deviceTypeInt);
            List<CmccSignalDic> signalDics = allSignalDics.stream()
                    .filter(dic -> dic.getSemaphoreType() != null &&
                                  (dic.getSemaphoreType() == 3 || dic.getSemaphoreType() == 4))
                    .collect(Collectors.toList());
            List<CmccSignalDic> controlDics = allSignalDics.stream()
                    .filter(dic -> dic.getSemaphoreType() != null &&
                                  (dic.getSemaphoreType() == 1 || dic.getSemaphoreType() == 2))
                    .collect(Collectors.toList());

            // 获取该设备的实际数据
            List<CMCCAlarm> alarms = cmccAlarmService.listByDeviceId(deviceId);
            List<CMCCSignal> signals = cmccSignalService.listByDeviceId(deviceId);
            List<CMCCControl> controls = cmccControlService.listByDeviceId(deviceId);

            // 计算告警使用率
            usage.setAlarmUsage(calculateDeviceAlarmUsage(alarmDics, alarms));

            // 计算信号使用率
            usage.setSignalUsage(calculateDeviceSignalUsage(signalDics, signals));

            // 计算控制使用率
            usage.setControlUsage(calculateDeviceControlUsage(controlDics, controls));

        } catch (NumberFormatException e) {
            log.warn("设备类型转换失败，deviceType: {}", device.getDeviceType());
            usage.setAlarmUsage(createEmptyUsageDetail());
            usage.setSignalUsage(createEmptyUsageDetail());
            usage.setControlUsage(createEmptyUsageDetail());
        }

        return usage;
    }

    /**
     * 根据设备ID获取设备信息
     */
    private CMCCDevice getDeviceByDeviceId(String deviceId) {
        LambdaQueryWrapper<CMCCDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMCCDevice::getDeviceId, deviceId);
        return getOne(wrapper);
    }

    /**
     * 创建空的设备使用率统计
     */
    private DeviceStandardizationUsage createEmptyDeviceUsage(String deviceId) {
        DeviceStandardizationUsage usage = new DeviceStandardizationUsage();
        usage.setDeviceId(deviceId);
        usage.setAlarmUsage(createEmptyUsageDetail());
        usage.setSignalUsage(createEmptyUsageDetail());
        usage.setControlUsage(createEmptyUsageDetail());
        return usage;
    }

    /**
     * 创建空的使用率详情
     */
    private DeviceStandardizationUsage.StandardizationUsageDetail createEmptyUsageDetail() {
        DeviceStandardizationUsage.StandardizationUsageDetail detail = new DeviceStandardizationUsage.StandardizationUsageDetail();
        detail.setTotalStandardCount(0);
        detail.setImplementedStandardCount(0);
        detail.setStandardImplementationRate(BigDecimal.ZERO);
        detail.setTotalActualCount(0);
        detail.setStandardizedActualCount(0);
        detail.setActualStandardizationRate(BigDecimal.ZERO);
        detail.setImplementedStandardItems(new ArrayList<>());
        detail.setUnimplementedStandardItems(new ArrayList<>());
        return detail;
    }

    /**
     * 根据设备类型获取设备列表
     */
    private List<CMCCDevice> getDevicesByType(String deviceType, String fsuId) {
        LambdaQueryWrapper<CMCCDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CMCCDevice::getDeviceType, deviceType);
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCDevice::getFsuId, fsuId);
        }
        return list(wrapper);
    }

    /**
     * 根据设备列表获取告警列表
     */
    private List<CMCCAlarm> getAlarmsByDeviceType(List<CMCCDevice> devices) {
        if (devices.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> deviceIds = devices.stream().map(CMCCDevice::getDeviceId).collect(Collectors.toList());
        LambdaQueryWrapper<CMCCAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CMCCAlarm::getDeviceId, deviceIds);
        return cmccAlarmService.list(wrapper);
    }

    /**
     * 根据设备列表获取信号列表
     */
    private List<CMCCSignal> getSignalsByDeviceType(List<CMCCDevice> devices) {
        if (devices.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> deviceIds = devices.stream().map(CMCCDevice::getDeviceId).collect(Collectors.toList());
        LambdaQueryWrapper<CMCCSignal> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CMCCSignal::getDeviceId, deviceIds);
        return cmccSignalService.list(wrapper);
    }

    /**
     * 根据设备列表获取控制列表
     */
    private List<CMCCControl> getControlsByDeviceType(List<CMCCDevice> devices) {
        if (devices.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> deviceIds = devices.stream().map(CMCCDevice::getDeviceId).collect(Collectors.toList());
        LambdaQueryWrapper<CMCCControl> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CMCCControl::getDeviceId, deviceIds);
        return cmccControlService.list(wrapper);
    }

    /**
     * 计算设备告警使用率
     */
    private DeviceStandardizationUsage.StandardizationUsageDetail calculateDeviceAlarmUsage(
            List<CmccAlarmDic> alarmDics, List<CMCCAlarm> alarms) {

        DeviceStandardizationUsage.StandardizationUsageDetail detail = new DeviceStandardizationUsage.StandardizationUsageDetail();
        detail.setTotalStandardCount(alarmDics.size());
        detail.setTotalActualCount(alarms.size());

        // 找出已实现的标准化项和对应的实际项
        Map<String, List<CMCCAlarm>> implementedStandardMap = new HashMap<>();
        int standardizedCount = 0;

        for (CMCCAlarm alarm : alarms) {
            if (alarm.getOriginSpId() != null) {
                CmccAlarmDic matchedDic = alarmDics.stream()
                        .filter(dic -> dic.getAlarmStandardId() != null && dic.getAlarmStandardId().endsWith(alarm.getOriginSpId()))
                        .findFirst()
                        .orElse(null);
                if (matchedDic != null) {
                    standardizedCount++;
                    implementedStandardMap.computeIfAbsent(matchedDic.getAlarmStandardId(), k -> new ArrayList<>()).add(alarm);
                }
            }
        }

        detail.setImplementedStandardCount(implementedStandardMap.size());
        detail.setStandardizedActualCount(standardizedCount);
        detail.setStandardImplementationRate(calculateRate(implementedStandardMap.size(), alarmDics.size()));
        detail.setActualStandardizationRate(calculateRate(standardizedCount, alarms.size()));

        // 构建已实现的标准化项列表
        List<DeviceStandardizationUsage.ImplementedStandardItem> implementedItems = new ArrayList<>();
        for (CmccAlarmDic dic : alarmDics) {
            List<CMCCAlarm> matchedAlarms = implementedStandardMap.get(dic.getAlarmStandardId());
            if (matchedAlarms != null && !matchedAlarms.isEmpty()) {
                List<DeviceStandardizationUsage.ActualItem> actualItems = matchedAlarms.stream()
                        .map(alarm -> new DeviceStandardizationUsage.ActualItem(
                                alarm.getSpId(), alarm.getAlarmName(), alarm.getOriginSpId()))
                        .collect(Collectors.toList());
                implementedItems.add(new DeviceStandardizationUsage.ImplementedStandardItem(
                        dic.getAlarmStandardId(), dic.getAlarmStandardName(), "alarm",
                        matchedAlarms.size(), actualItems));
            }
        }
        detail.setImplementedStandardItems(implementedItems);

        // 找出未实现的标准化项
        List<DeviceStandardizationUsage.UnimplementedStandardItem> unimplementedItems = alarmDics.stream()
                .filter(dic -> !implementedStandardMap.containsKey(dic.getAlarmStandardId()))
                .map(dic -> new DeviceStandardizationUsage.UnimplementedStandardItem(
                        dic.getAlarmStandardId(), dic.getAlarmStandardName(), "alarm"))
                .collect(Collectors.toList());
        detail.setUnimplementedStandardItems(unimplementedItems);

        return detail;
    }

    /**
     * 计算设备信号使用率
     */
    private DeviceStandardizationUsage.StandardizationUsageDetail calculateDeviceSignalUsage(
            List<CmccSignalDic> signalDics, List<CMCCSignal> signals) {

        DeviceStandardizationUsage.StandardizationUsageDetail detail = new DeviceStandardizationUsage.StandardizationUsageDetail();
        detail.setTotalStandardCount(signalDics.size());
        detail.setTotalActualCount(signals.size());

        // 找出已实现的标准化项和对应的实际项
        Map<String, List<CMCCSignal>> implementedStandardMap = new HashMap<>();
        int standardizedCount = 0;

        for (CMCCSignal signal : signals) {
            if (signal.getOriginSpId() != null) {
                CmccSignalDic matchedDic = signalDics.stream()
                        .filter(dic -> dic.getSignalStandardId() != null && dic.getSignalStandardId().endsWith(signal.getOriginSpId()))
                        .findFirst()
                        .orElse(null);
                if (matchedDic != null) {
                    standardizedCount++;
                    implementedStandardMap.computeIfAbsent(matchedDic.getSignalStandardId(), k -> new ArrayList<>()).add(signal);
                }
            }
        }

        detail.setImplementedStandardCount(implementedStandardMap.size());
        detail.setStandardizedActualCount(standardizedCount);
        detail.setStandardImplementationRate(calculateRate(implementedStandardMap.size(), signalDics.size()));
        detail.setActualStandardizationRate(calculateRate(standardizedCount, signals.size()));

        // 构建已实现的标准化项列表
        List<DeviceStandardizationUsage.ImplementedStandardItem> implementedItems = new ArrayList<>();
        for (CmccSignalDic dic : signalDics) {
            List<CMCCSignal> matchedSignals = implementedStandardMap.get(dic.getSignalStandardId());
            if (matchedSignals != null && !matchedSignals.isEmpty()) {
                List<DeviceStandardizationUsage.ActualItem> actualItems = matchedSignals.stream()
                        .map(signal -> new DeviceStandardizationUsage.ActualItem(
                                signal.getSpId(), signal.getSignalName(), signal.getOriginSpId()))
                        .collect(Collectors.toList());
                implementedItems.add(new DeviceStandardizationUsage.ImplementedStandardItem(
                        dic.getSignalStandardId(), dic.getStandardSignalName(), "signal",
                        matchedSignals.size(), actualItems));
            }
        }
        detail.setImplementedStandardItems(implementedItems);

        // 找出未实现的标准化项
        List<DeviceStandardizationUsage.UnimplementedStandardItem> unimplementedItems = signalDics.stream()
                .filter(dic -> !implementedStandardMap.containsKey(dic.getSignalStandardId()))
                .map(dic -> new DeviceStandardizationUsage.UnimplementedStandardItem(
                        dic.getSignalStandardId(), dic.getStandardSignalName(), "signal"))
                .collect(Collectors.toList());
        detail.setUnimplementedStandardItems(unimplementedItems);

        return detail;
    }

    /**
     * 计算设备控制使用率
     */
    private DeviceStandardizationUsage.StandardizationUsageDetail calculateDeviceControlUsage(
            List<CmccSignalDic> controlDics, List<CMCCControl> controls) {

        DeviceStandardizationUsage.StandardizationUsageDetail detail = new DeviceStandardizationUsage.StandardizationUsageDetail();
        detail.setTotalStandardCount(controlDics.size());
        detail.setTotalActualCount(controls.size());

        // 找出已实现的标准化项和对应的实际项
        Map<String, List<CMCCControl>> implementedStandardMap = new HashMap<>();
        int standardizedCount = 0;

        for (CMCCControl control : controls) {
            if (control.getOriginSpId() != null) {
                CmccSignalDic matchedDic = controlDics.stream()
                        .filter(dic -> dic.getSignalStandardId() != null && dic.getSignalStandardId().endsWith(control.getOriginSpId()))
                        .findFirst()
                        .orElse(null);
                if (matchedDic != null) {
                    standardizedCount++;
                    implementedStandardMap.computeIfAbsent(matchedDic.getSignalStandardId(), k -> new ArrayList<>()).add(control);
                }
            }
        }

        detail.setImplementedStandardCount(implementedStandardMap.size());
        detail.setStandardizedActualCount(standardizedCount);
        detail.setStandardImplementationRate(calculateRate(implementedStandardMap.size(), controlDics.size()));
        detail.setActualStandardizationRate(calculateRate(standardizedCount, controls.size()));

        // 构建已实现的标准化项列表
        List<DeviceStandardizationUsage.ImplementedStandardItem> implementedItems = new ArrayList<>();
        for (CmccSignalDic dic : controlDics) {
            List<CMCCControl> matchedControls = implementedStandardMap.get(dic.getSignalStandardId());
            if (matchedControls != null && !matchedControls.isEmpty()) {
                List<DeviceStandardizationUsage.ActualItem> actualItems = matchedControls.stream()
                        .map(control -> new DeviceStandardizationUsage.ActualItem(
                                control.getSpId(), control.getControlName(), control.getOriginSpId()))
                        .collect(Collectors.toList());
                implementedItems.add(new DeviceStandardizationUsage.ImplementedStandardItem(
                        dic.getSignalStandardId(), dic.getStandardSignalName(), "control",
                        matchedControls.size(), actualItems));
            }
        }
        detail.setImplementedStandardItems(implementedItems);

        // 找出未实现的标准化项
        List<DeviceStandardizationUsage.UnimplementedStandardItem> unimplementedItems = controlDics.stream()
                .filter(dic -> !implementedStandardMap.containsKey(dic.getSignalStandardId()))
                .map(dic -> new DeviceStandardizationUsage.UnimplementedStandardItem(
                        dic.getSignalStandardId(), dic.getStandardSignalName(), "control"))
                .collect(Collectors.toList());
        detail.setUnimplementedStandardItems(unimplementedItems);

        return detail;
    }

}
