package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCSignal;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccSignalDic;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCSignalMapper;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCSignalWithStandardization;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCDeviceService;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCSignalService;
import com.siteweb.tcs.south.cmcc.web.service.ICmccSignalDicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CMCC信号Service实现类
 * <AUTHOR> (2025-07-30)
 */
@Slf4j
@Service
public class CMCCSignalServiceImpl extends ServiceImpl<CMCCSignalMapper, CMCCSignal> implements ICMCCSignalService {

    @Autowired
    private ICMCCDeviceService cmccDeviceService;

    @Autowired
    private ICmccSignalDicService cmccSignalDicService;

    @Override
    public List<CMCCSignal> listByFsuId(String fsuId) {
        LambdaQueryWrapper<CMCCSignal> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCSignal::getFsuId, fsuId);
        }
        return list(wrapper);
    }

    @Override
    public List<CMCCSignal> listByDeviceId(String deviceId) {
        LambdaQueryWrapper<CMCCSignal> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCSignal::getDeviceId, deviceId);
        }
        return list(wrapper);
    }

    @Override
    public List<CMCCSignal> listByFsuIdAndDeviceId(String fsuId, String deviceId) {
        LambdaQueryWrapper<CMCCSignal> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCSignal::getFsuId, fsuId);
        }
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCSignal::getDeviceId, deviceId);
        }
        return list(wrapper);
    }

    @Override
    public IPage<CMCCSignal> pageByFsuId(long current, long size, String fsuId) {
        Page<CMCCSignal> page = new Page<>(current, size);
        LambdaQueryWrapper<CMCCSignal> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCSignal::getFsuId, fsuId);
        }
        return page(page, wrapper);
    }

    @Override
    public IPage<CMCCSignal> pageByDeviceId(long current, long size, String deviceId) {
        Page<CMCCSignal> page = new Page<>(current, size);
        LambdaQueryWrapper<CMCCSignal> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCSignal::getDeviceId, deviceId);
        }
        return page(page, wrapper);
    }

    @Override
    public IPage<CMCCSignal> pageByFsuIdAndDeviceId(long current, long size, String fsuId, String deviceId) {
        Page<CMCCSignal> page = new Page<>(current, size);
        LambdaQueryWrapper<CMCCSignal> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(fsuId)) {
            wrapper.eq(CMCCSignal::getFsuId, fsuId);
        }
        if (StringUtils.hasText(deviceId)) {
            wrapper.eq(CMCCSignal::getDeviceId, deviceId);
        }
        return page(page, wrapper);
    }

    @Override
    public CMCCSignal getSignalById(Long id) {
        return getById(id);
    }

    @Override
    public List<CMCCSignalWithStandardization> listWithStandardizationByFsuIdAndDeviceId(String fsuId, String deviceId) {
        log.info("查询信号标准化信息，FSUid: {}, DeviceId: {}", fsuId, deviceId);

        // 获取信号列表
        List<CMCCSignal> signals = listByFsuIdAndDeviceId(fsuId, deviceId);
        if (signals.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取设备信息以获取设备类型
        String deviceType = null;
        if (StringUtils.hasText(deviceId)) {
            List<CMCCDevice> devices = cmccDeviceService.listByFsuId(fsuId);
            for (CMCCDevice device : devices) {
                if (deviceId.equals(device.getDeviceId())) {
                    deviceType = device.getDeviceType();
                    break;
                }
            }
        }

        // 获取信号字典（semaphoretype = 3 或 4）
        List<CmccSignalDic> signalDics = new ArrayList<>();
        if (StringUtils.hasText(deviceType)) {
            try {
                Integer deviceTypeInt = Integer.valueOf(deviceType);
                List<CmccSignalDic> allSignalDics = cmccSignalDicService.listByDeviceType(deviceTypeInt);
                signalDics = allSignalDics.stream()
                        .filter(dic -> dic.getSemaphoreType() != null &&
                                      (dic.getSemaphoreType() == 3 || dic.getSemaphoreType() == 4))
                        .collect(Collectors.toList());
            } catch (NumberFormatException e) {
                log.warn("设备类型转换失败，deviceType: {}", deviceType);
            }
        }

        // 构建标准化信息
        List<CMCCSignalWithStandardization> result = new ArrayList<>();
        for (CMCCSignal signal : signals) {
            CMCCSignalWithStandardization signalWithStd = new CMCCSignalWithStandardization(signal);

            // 检查是否标准化
            CmccSignalDic matchedDic = findMatchingSignalDic(signal, signalDics);
            if (matchedDic != null) {
                signalWithStd.setStandardized(true);
                signalWithStd.setStandardName(matchedDic.getStandardSignalName());
                signalWithStd.setStandardId(matchedDic.getSignalStandardId());
            } else {
                signalWithStd.setStandardized(false);
            }

            result.add(signalWithStd);
        }

        return result;
    }

    /**
     * 查找匹配的信号字典
     */
    private CmccSignalDic findMatchingSignalDic(CMCCSignal signal, List<CmccSignalDic> signalDics) {
        if (signal.getOriginSpId() == null || signalDics.isEmpty()) {
            return null;
        }
        return signalDics.stream()
                .filter(dic -> dic.getSignalStandardId() != null && dic.getSignalStandardId().endsWith(signal.getOriginSpId()))
                .findFirst()
                .orElse(null);
    }
}
