package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.google.common.base.Objects;
import com.siteweb.stream.core.dto.StreamGraphTemplate;
import com.siteweb.stream.core.mapper.StreamGraphMapper;
import com.siteweb.stream.core.provider.StreamGraphProvider;
import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.service.ITcsGatewayService;
import com.siteweb.tcs.plugin.common.GatewayStatus;
import com.siteweb.tcs.plugin.common.SouthLifeCycleEvent;
import com.siteweb.tcs.plugin.common.SouthLifeCycleEventType;
import com.siteweb.tcs.siteweb.util.JsonHelper;
import com.siteweb.tcs.south.cmcc.connector.letter.LoginMessage;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCPendingFsu;
import com.siteweb.tcs.south.cmcc.dal.enums.ConfigSyncEnum;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationObject;
import com.siteweb.tcs.south.cmcc.dal.enums.OperationType;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.dal.provider.OperationLogProvider;
import com.siteweb.tcs.south.cmcc.exception.CMCCBusinessErrorCode;
import com.siteweb.tcs.south.cmcc.web.service.CmccFsuAccessService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-07-18)
 **/
@Slf4j
@Service
@EnableAspectJAutoProxy(exposeProxy = true) // 关键点！
public class CmccFsuAccessServiceImpl implements CmccFsuAccessService {

    @Autowired
    private FSUProvider fsuProvider;

    @Autowired
    private OperationLogProvider operationLogProvider;

    @Autowired
    private ITcsGatewayService tcsGatewayService;

    @Autowired
    @Qualifier("cmcc-gateway-sharding")
    private ActorRef cmccFsuShading;

    @Autowired
    @Qualifier("cmcc-graph-template")
    private StreamGraphTemplate cmccGraphTemplate;

    @Autowired
    private StreamGraphProvider streamGraphProvider;

    @Autowired
    private StreamGraphMapper streamGraphMapper;

    /**
     * 是否开启自动入网
     */
    @Getter
    @Value("${plugin.gateway.auto-access.enable}")
    private boolean autoAccess;

    /**
     * 默认FSU端口
     */
    @Getter
    @Value("${plugin.gateway.default-port}")
    private String defaultPort;


    /**
     * 入网后自动启用
     */
    @Getter
    @Value("${plugin.gateway.auto-access.default-enable}")
    private boolean defaultEnable;


    /**
     * 入网后同步方向
     */
    @Getter
    @Value("${plugin.gateway.auto-access.default-config-sync}")
    private String defaultConfigSync;


    /**
     * 处理新的FSU入网
     *
     * @param loginMessage
     * @return
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "cmccTransactionManager")
    public GatewayStatus handleNewFsuNetworkAccess(LoginMessage loginMessage) {
        var pendingFsu = new CMCCPendingFsu();
        pendingFsu.setFsuId(loginMessage.getInfo().getFsuId());
        pendingFsu.setMac(loginMessage.getInfo().getFsuMac());
        pendingFsu.setVersion(loginMessage.getInfo().getFsuVer());
        pendingFsu.setIpAddress(loginMessage.getInfo().getFsuIp());
        pendingFsu.setUsername(loginMessage.getInfo().getUserName());
        pendingFsu.setPassword(loginMessage.getInfo().getPassword());
        pendingFsu.setCreateTime(LocalDateTime.now());

        if (!autoAccess) {
            // 不允许自动接入，进入待接入列表
            log.info("CMCC 发现新的FSU[{}]，加入待接入FSU列表。", pendingFsu.getFsuId());
            fsuProvider.savePending(pendingFsu);
            return GatewayStatus.PENDING;
        } else {
            // 开启自动接入
            log.info("CMCC 发现新的FSU[{}]，正在处理自动入网。", pendingFsu.getFsuId());
//            UserContext.setUser(new UserContext.UserInfo(0, "System"));
            operationLogProvider.record(OperationObject.FSU, pendingFsu.getFsuId(), OperationType.APPROVED, "plugin.cmcc.auto-approved");
            var fsu = CMCCFsu.fromPending(pendingFsu);
            fsu.setEnable(defaultEnable);
            if ("*************".equals(fsu.getIpAddress())){
                fsu.setFsuPort(String.valueOf(8081));
            }
            else {
                fsu.setFsuPort(defaultPort);

            }
            fsu.setConfigSync(ConfigSyncEnum.fromValue(defaultConfigSync));
            fsu.setGatewayName(pendingFsu.getFsuId());
            ((CmccFsuAccessService) AopContext.currentProxy()).approvedFsuNetworkAccess(fsu);
            return GatewayStatus.OFFLINE;
        }
    }


    /**
     * 批准入网/创建FSU
     *
     * @param fsu
     * @return
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "cmccTransactionManager")
    public CMCCFsu approvedFsuNetworkAccess(CMCCFsu fsu) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            System.out.println("当前事务已激活");
        }
        log.info("CMCC 正在处理FSU[{}]入网流程", fsu.getGatewayId());
        fsu.setId(null);
        var changeEvent = new GatewayConfigChangeDto();
        changeEvent.setId(null);
        changeEvent.setDeleted(false);
        changeEvent.setPluginId("south-cmcc-plugin");
        changeEvent.setSouthGatewayId(fsu.getGatewayId());
        changeEvent.setSouthGatewayName(fsu.getGatewayName());
        changeEvent.setSouthAddress(fsu.getIpAddress());
        var node = JsonHelper.toObjectNode(fsu);
        node.remove("deviceList");
        changeEvent.setMetadata(node);
        changeEvent.setLifeCycleEvent(LifeCycleEventEnum.CREATE);
        var result = tcsGatewayService.handleGatewayConfigChange(changeEvent);
        if (!result.isSuccess()) {
            log.error("创建[HubGateway]失败1： {}", result.getErrorMessage());
            log.error("创建[HubGateway]失败2：{}", result.getDetailMessage());
            throw CMCCBusinessErrorCode.GATEWAY_CREATE_FAILURE.toException();
        }
        if (result.getConfigData() instanceof GatewayConfigChangeDto changeDto) {
            fsu.setGatewayHubId(changeDto.getId());
        }
        // 创建FSU的流图
        var streamGraph = cmccGraphTemplate.toStreamGraph(fsu.getGatewayId());
        streamGraphMapper.insert(streamGraph);
        try {
            fsu.setGraphId(streamGraph.getStreamGraphId());
            fsuProvider.saveFsu(fsu);
        } catch (Exception ex) {
            streamGraphMapper.deleteById(streamGraph.getStreamGraphId());
            throw CMCCBusinessErrorCode.FSU_CREATE_FAILURE.toException(ex);
        }
        // 发送FSULifeCycle至Proxy
        var event = new SouthLifeCycleEvent();
        event.setGatewayId(fsu.getGatewayId());
        event.setGatewayInfo(fsu);
        event.setEvent(SouthLifeCycleEventType.CREATE);
        cmccFsuShading.tell(event, ActorRef.noSender());
        fsuProvider.deletePendingFsu(fsu.getGatewayId());
        return fsu;
    }


    /**
     * 禁用FSU
     *
     * @param fsuId
     */
    public boolean disableFsu(String fsuId) {
        var fsu = fsuProvider.getFsuByFsuid(fsuId);
        if (fsu == null || !fsu.isEnable()) {
            throw StandardBusinessErrorCode.DATA_VALIDATION_FAILED.toException(fsuId);
        }
        fsuProvider.disableFsu(fsuId);
        fsu.setEnable(false);
        operationLogProvider.record(OperationObject.FSU, fsu.getGatewayId(), OperationType.DISABLE, "plugin.cmcc.fsu.user.cmd.disable");
        var event = new SouthLifeCycleEvent();
        event.setGatewayId(fsuId);
        event.setGatewayInfo(fsu);
        event.setEvent(SouthLifeCycleEventType.DISABLE);
        cmccFsuShading.tell(event, ActorRef.noSender());
        return true;
    }

    /**
     * 启用FSU
     *
     * @param fsuId
     */
    public boolean enableFsu(String fsuId) {
        var fsu = fsuProvider.getFsuByFsuid(fsuId);
        if (fsu == null || fsu.isEnable()) {
            throw StandardBusinessErrorCode.DATA_VALIDATION_FAILED.toException(fsuId);
        }
        fsuProvider.enableFsu(fsuId);
        fsu.setEnable(true);
        operationLogProvider.record(OperationObject.FSU, fsu.getGatewayId(), OperationType.ENABLE, "plugin.cmcc.fsu.user.cmd.enable");
        var event = new SouthLifeCycleEvent();
        event.setGatewayId(fsuId);
        event.setGatewayInfo(fsu);
        event.setEvent(SouthLifeCycleEventType.ENABLE);
        cmccFsuShading.tell(event, ActorRef.noSender());
        return true;
    }


    /**
     * 更新FSU
     *
     * @param updateFsu
     */
    public void updateFsu(CMCCFsu updateFsu) {
        var fsu = fsuProvider.getFsuByFsuid(updateFsu.getGatewayId());
        if (fsu == null || fsu.isEnable()) {
            throw StandardBusinessErrorCode.DATA_VALIDATION_FAILED.toException(updateFsu.getGatewayId());
        }
        var changeMsg = "";
        if (!Objects.equal(fsu.getFsuPort(), updateFsu.getFsuPort())) {
            changeMsg += "FsuPort: " + fsu.getFsuPort() + "=>" + updateFsu.getFsuPort() + ";";
            fsu.setFsuPort(updateFsu.getFsuPort());
        }
        if (!Objects.equal(fsu.getGatewayName(), updateFsu.getGatewayName())) {
            changeMsg += "FsuName: " + fsu.getGatewayName() + "=>" + updateFsu.getGatewayName() + ";";
            fsu.setGatewayName(updateFsu.getGatewayName());
        }
        if (!Objects.equal(fsu.getRegionId(), updateFsu.getRegionId())) {
            changeMsg += "Region: " + fsu.getRegionId() + "=>" + updateFsu.getRegionId() + ";";
            fsu.setRegionId(updateFsu.getRegionId());
        }
        if (!Objects.equal(fsu.getConfigSync(), updateFsu.getConfigSync())) {
            changeMsg += "ConfigSync: " + fsu.getConfigSync() + "=>" + updateFsu.getConfigSync() + ";";
            fsu.setConfigSync(updateFsu.getConfigSync());
        }
        if (fsuProvider.updateFsu(fsu)) {
            operationLogProvider.record(OperationObject.FSU, fsu.getGatewayId(), OperationType.UPDATE, "plugin.cmcc.fsu.update", changeMsg);
            var event = new SouthLifeCycleEvent();
            event.setGatewayId(fsu.getGatewayId());
            event.setGatewayInfo(fsu);
            event.setEvent(SouthLifeCycleEventType.UPDATE);
            cmccFsuShading.tell(event, ActorRef.noSender());
        }
    }


    /**
     * 删除FSU
     *
     * @param fsuId
     */
    public void deleteFsu(String fsuId) {
        var fsu = fsuProvider.getFsuByFsuid(fsuId);
        if (fsu == null) throw StandardBusinessErrorCode.DATA_NOT_FOUND.toException(fsuId);
        operationLogProvider.record(OperationObject.FSU, fsu.getGatewayId(), OperationType.DELETE, "plugin.cmcc.fsu.delete");
        var changeEvent = new GatewayConfigChangeDto();
        changeEvent.setId(fsu.getGatewayHubId());
        changeEvent.setDeleted(true);
        changeEvent.setPluginId("south-cmcc-plugin");
        changeEvent.setSouthGatewayId(fsu.getGatewayId());
        changeEvent.setSouthGatewayName(fsu.getGatewayName());
        changeEvent.setSouthAddress(fsu.getIpAddress());
        var node = JsonHelper.toObjectNode(fsu);
        node.remove("deviceList");
        changeEvent.setMetadata(node);
        changeEvent.setLifeCycleEvent(LifeCycleEventEnum.DELETE);
        var result = tcsGatewayService.handleGatewayConfigChange(changeEvent);
        if (result.isSuccess()) {
            if (fsuProvider.deleteFsu(fsuId)) {
                streamGraphProvider.deleteGraph(fsu.getGraphId());
                var event = new SouthLifeCycleEvent();
                event.setGatewayId(fsuId);
                event.setGatewayInfo(null);
                event.setEvent(SouthLifeCycleEventType.DELETE);
                cmccFsuShading.tell(event, ActorRef.noSender());
            }
        }
    }


}
