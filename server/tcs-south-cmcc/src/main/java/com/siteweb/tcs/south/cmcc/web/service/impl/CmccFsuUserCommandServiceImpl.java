package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.siteweb.tcs.plugin.common.UserCommandResponse;
import com.siteweb.tcs.siteweb.util.JsonHelper;
import com.siteweb.tcs.south.cmcc.connector.commands.CMCCSouthUserCommand;
import com.siteweb.tcs.south.cmcc.dal.provider.FSUProvider;
import com.siteweb.tcs.south.cmcc.web.service.CmccFsuUserCommandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.pattern.Patterns;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 */
@Service
@Slf4j
public class CmccFsuUserCommandServiceImpl implements CmccFsuUserCommandService {

    @Autowired
    private FSUProvider fsuProvider;

    @Autowired
    @Qualifier("cmcc-gateway-sharding")
    private ActorRef cmccFsuShading;

    /**
     * 执行用户命令
     *
     * @param command 用户命令
     * @return 命令执行结果的异步Future
     */
    @Override
    public CompletableFuture<UserCommandResponse> executeCommand(CMCCSouthUserCommand command) {
        log.info("执行用户命令: 类型={}, FSU={}",
                command.getClass().getSimpleName(), command.getGatewayId());

        try {
            // 使用Patterns.ask发送命令并等待响应
            long COMMAND_TIMEOUT_SECONDS = 10;
            var askFuture = Patterns.askWithReplyTo(
                    cmccFsuShading,
                    (ActorRef replyTo) -> {
                        command.setSender(replyTo);
                        return command;
                    },
                    Duration.ofSeconds(COMMAND_TIMEOUT_SECONDS)
            );

            // 将Scala Future转换为Java CompletableFuture
            return askFuture.toCompletableFuture()
                    .thenApply(response -> {
                        if (response instanceof UserCommandResponse userCommandResponse) {
                            log.info("命令执行完成: 成功={} {}", userCommandResponse.isSuccess(), JsonHelper.toSafeJson(userCommandResponse.getData()));
                            return userCommandResponse;
                        } else {
                            log.warn("收到未知响应类型: 响应类型={}",
                                    response.getClass().getSimpleName());
                            return UserCommandResponse.failure("收到未知响应类型: " + response.getClass().getSimpleName());
                        }
                    })
                    .exceptionally(throwable -> {
                        log.error("命令执行失败: FSU={}",
                                command.getGatewayId(), throwable);

                        // 判断是否为超时异常
                        if (throwable.getCause() instanceof java.util.concurrent.TimeoutException) {
                            return UserCommandResponse.timeout();
                        } else {
                            return UserCommandResponse.failure("命令执行异常: " + throwable.getMessage());
                        }
                    });

        } catch (Exception e) {
            log.error("发送命令失败:  FSU={}", command.getGatewayId(), e);
            return CompletableFuture.completedFuture(
                    UserCommandResponse.failure("发送命令失败: " + e.getMessage())
            );
        }
    }

    /**
     * 批量执行用户命令
     *
     * @param commands 用户命令列表
     * @return 命令执行结果列表的异步Future
     */
    @Override
    public CompletableFuture<java.util.List<UserCommandResponse>> executeCommands(
            java.util.List<CMCCSouthUserCommand> commands) {

        log.info("批量执行用户命令: 数量={}", commands.size());

        // 创建所有命令的Future数组
        @SuppressWarnings("unchecked")
        CompletableFuture<UserCommandResponse>[] futures = commands.stream()
                .map(this::executeCommand)
                .toArray(CompletableFuture[]::new);

        // 等待所有命令完成
        return CompletableFuture.allOf(futures)
                .thenApply(v -> {
                    java.util.List<UserCommandResponse> results = new java.util.ArrayList<>();
                    for (CompletableFuture<UserCommandResponse> future : futures) {
                        try {
                            results.add(future.get(1, TimeUnit.SECONDS)); // 短超时，因为已经完成
                        } catch (Exception e) {
                            log.error("获取批量命令结果失败", e);
                            results.add(UserCommandResponse.failure("unknown", "获取结果失败: " + e.getMessage()));
                        }
                    }
                    log.info("批量命令执行完成: 总数={}, 成功={}",
                            results.size(),
                            results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
                    return results;
                });
    }
}
