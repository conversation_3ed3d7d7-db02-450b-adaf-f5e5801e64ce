package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccSignalDic;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import com.siteweb.tcs.south.cmcc.dal.mapper.CmccSignalDicMapper;
import com.siteweb.tcs.south.cmcc.web.dto.CmccSignalDicDTO;
import com.siteweb.tcs.south.cmcc.web.service.ICmccSignalDicService;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CMCC信号字典Service实现类
 */
@Service
public class CmccSignalDicServiceImpl extends ServiceImpl<CmccSignalDicMapper, CmccSignalDic> implements ICmccSignalDicService {

    @Resource
    private CmccSignalDicMapper cmccSignalDicMapper;

    @Resource
    private IDictionaryItemService dictionaryItemService;

    private static final String standardAlarmIdTemplate = "0500-002-%s-00-%s";

    @Override
    public CmccSignalDic getBySignalStandardId(String signalStandardId) {
        return cmccSignalDicMapper.selectBySignalStandardId(signalStandardId);
    }

    @Override
    public List<CmccSignalDic> listByDeviceType(Integer deviceType) {
        return cmccSignalDicMapper.selectByDeviceType(deviceType);
    }

    @Override
    public List<CmccSignalDic> listByStandardVersion(Integer standardVersion) {
        return cmccSignalDicMapper.selectByStandardVersion(standardVersion);
    }

    @Override
    public List<CmccSignalDic> listBySemaphoreType(Integer semaphoreType) {
        return cmccSignalDicMapper.selectBySemaphoreType(semaphoreType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSignalDic(CmccSignalDic cmccSignalDic) {
        //处理标准化信号id

        String deviceTypeStr = String.format("%03d", cmccSignalDic.getDeviceType());
        String standardSignalId = String.format(standardAlarmIdTemplate, deviceTypeStr, cmccSignalDic.getSignalStandardId());
        cmccSignalDic.setSignalStandardId(standardSignalId);
        return save(cmccSignalDic);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSignalDic(CmccSignalDic cmccSignalDic) {
        return updateById(cmccSignalDic);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSignalDic(Integer id) {
        return removeById(id);
    }

    @Override
    public List<CmccSignalDicDTO> listAllWithNames() {
        // 获取所有信号字典数据
        List<CmccSignalDic> signalDicList = list();
        
        // 获取设备类型字典（CategoryId=2）
        List<DictionaryItem> deviceTypes = dictionaryItemService.listByCategoryId(2);
        Map<Integer, String> deviceTypeMap = deviceTypes.stream()
                .collect(Collectors.toMap(DictionaryItem::getItemId, DictionaryItem::getItemValue));
        
        // 获取信号量类型字典（CategoryId=6）
        List<DictionaryItem> semaphoreTypes = dictionaryItemService.listByCategoryId(6);
        Map<Integer, String> semaphoreTypeMap = semaphoreTypes.stream()
                .collect(Collectors.toMap(DictionaryItem::getItemId, DictionaryItem::getItemValue));
        
        // 转换为DTO
        List<CmccSignalDicDTO> result = new ArrayList<>();
        for (CmccSignalDic signalDic : signalDicList) {
            CmccSignalDicDTO dto = new CmccSignalDicDTO();
            dto.setId(signalDic.getId());
            dto.setStandardVersion(signalDic.getStandardVersion());
            dto.setSignalStandardId(signalDic.getSignalStandardId());
            dto.setDeviceType(signalDic.getDeviceType());
            dto.setDeviceSubType(signalDic.getDeviceSubType());
            dto.setStandardSignalName(signalDic.getStandardSignalName());
            dto.setDescription(signalDic.getDescription());
            dto.setSemaphoreType(signalDic.getSemaphoreType());
            dto.setUnit(signalDic.getUnit());
            dto.setMeaning(signalDic.getMeaning());
            dto.setExtendFiled(signalDic.getExtendFiled());
            
            // 设置字典名称
            dto.setDeviceTypeName(deviceTypeMap.get(signalDic.getDeviceType()));
            dto.setSemaphoreTypeName(semaphoreTypeMap.get(signalDic.getSemaphoreType()));
            
            result.add(dto);
        }
        
        return result;
    }
} 