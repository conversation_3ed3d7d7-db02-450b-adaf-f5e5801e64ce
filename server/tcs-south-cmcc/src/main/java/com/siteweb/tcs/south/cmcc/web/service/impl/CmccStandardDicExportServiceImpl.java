package com.siteweb.tcs.south.cmcc.web.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.XmlUtil;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccAlarmDic;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccSignalDic;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import com.siteweb.tcs.south.cmcc.web.service.ICmccAlarmDicService;
import com.siteweb.tcs.south.cmcc.web.service.ICmccSignalDicService;
import com.siteweb.tcs.south.cmcc.web.service.ICmccStandardDicExportService;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CmccStandardDicExportServiceImpl implements ICmccStandardDicExportService {

    @Autowired
    private ICmccAlarmDicService cmccAlarmDicService;

    @Autowired
    private ICmccSignalDicService cmccSignalDicService;

    @Autowired
    private IDictionaryItemService dictionaryItemService;

    @Override
    public String exportXmlStr(Integer standardVersion) {
        Document document = XmlUtil.createXml();
        document.setXmlStandalone(false);
        Element standardDicElement = document.createElement("StandardDic");
        standardDicElement.setAttribute("StandardName", "CMCC标准化字典表");

        // 获取当前时间并格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
        String currentTime = dateFormat.format(new Date());
        standardDicElement.setAttribute("CreateTime", currentTime);

        // 获取信号节点
        Element signalsElement = getSignalsElement(document, standardVersion);
        standardDicElement.appendChild(signalsElement);

        // 获取告警节点
        Element eventsElement = getEventsElement(document, standardVersion);
        standardDicElement.appendChild(eventsElement);

        // 获取控制节点
        Element controlsElement = getControlsElement(document, standardVersion);
        standardDicElement.appendChild(controlsElement);

        return XmlUtil.toStr(standardDicElement, true);
    }

    @Override
    public String generateXmlFile(Integer standardVersion) {
        try {
            String xmlContent = exportXmlStr(standardVersion);
            String filePath = getXmlFilePath(standardVersion);
            
            // 确保目录存在
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 写入文件
            try (FileWriter writer = new FileWriter(file, false)) {
                writer.write(xmlContent);
            }
            
            log.info("CMCC标准字典XML文件生成成功: {}", filePath);
            return filePath;
        } catch (IOException e) {
            log.error("生成CMCC标准字典XML文件失败", e);
            throw new RuntimeException("生成XML文件失败: " + e.getMessage());
        }
    }

    @Override
    public String getXmlFilePath(Integer standardVersion) {
        return Paths.get("plugins", "south-cmcc-plugin", "workspace", "standard-dic", "cmb_dictionary.xml")
                .toString();
    }

    private Element getSignalsElement(Document document, Integer standardVersion) {
        Element signals = document.createElement("Signals");
        signals.setAttribute("Name", "标准化信号");
        
        List<CmccSignalDic> signalDicList = cmccSignalDicService.listByStandardVersion(standardVersion)
                .stream()
                .filter(signal -> signal.getSemaphoreType() != null &&
                        (signal.getSemaphoreType() == 3 || signal.getSemaphoreType() == 4))
                .toList();;
        for (CmccSignalDic signalDic : signalDicList) {
            Element signalElement = document.createElement("Signal");
            signalElement.setAttribute("Name", signalDic.getStandardSignalName());
            signalElement.setAttribute("Unit", Convert.convert(String.class, signalDic.getUnit(), ""));
            signalElement.setAttribute("Code", signalDic.getSignalStandardId());
            signalElement.setAttribute("StoreInterval", ""); // CMCC字典中没有存储周期字段
            signalElement.setAttribute("Relativeval", ""); // CMCC字典中没有百分比阈值字段
            signalElement.setAttribute("Absoluteval", ""); // CMCC字典中没有绝对值阈值字段
            signals.appendChild(signalElement);
        }
        return signals;
    }

    private Element getEventsElement(Document document, Integer standardVersion) {
        Element events = document.createElement("Events");
        events.setAttribute("Name", "标准化事件");
        
        List<CmccAlarmDic> alarmDicList = cmccAlarmDicService.listByStandardVersion(standardVersion);
        for (CmccAlarmDic alarmDic : alarmDicList) {
            Element eventElement = document.createElement("Event");
            eventElement.setAttribute("Name", alarmDic.getAlarmStandardName());
            eventElement.setAttribute("Meaning", Convert.convert(String.class, alarmDic.getMeaning(), ""));
            eventElement.setAttribute("Code", alarmDic.getAlarmStandardId());
            
            // 设置各种告警级别 - 使用CMCC特有的告警级别字段
            eventElement.setAttribute("AlarmLevelOfTXJL", getUserGrade(alarmDic.getCommunicationBuildingAlarmLevel()));
            eventElement.setAttribute("AlarmLevelOfCSJD", getUserGrade(alarmDic.getTransmissionNodeAlarmLevel()));
            eventElement.setAttribute("AlarmLevelOfTXJZ", getUserGrade(alarmDic.getCommunicationBaseStationAlarmLevel()));
            eventElement.setAttribute("AlarmLevelOfIDC", getUserGrade(alarmDic.getIdcAlarmLevel()));

            eventElement.setAttribute("Threshold", "");
            eventElement.setAttribute("StartOper", "");
            eventElement.setAttribute("StartDelay", "");
            eventElement.setAttribute("EndDelay", "");
            eventElement.setAttribute("Hyteresis", "");
            events.appendChild(eventElement);
        }
        return events;
    }

    private Element getControlsElement(Document document, Integer standardVersion) {
        Element controls = document.createElement("Controls");
        controls.setAttribute("Name", "标准化控制");

        // 获取控制类型的信号字典数据 (semaphoreType为1遥控或2遥调)
        List<CmccSignalDic> controlSignalList = cmccSignalDicService.listByStandardVersion(standardVersion)
                .stream()
                .filter(signal -> signal.getSemaphoreType() != null &&
                        (signal.getSemaphoreType() == 1 || signal.getSemaphoreType() == 2))
                .toList();

        for (CmccSignalDic signalDic : controlSignalList) {
            Element controlElement = document.createElement("Control");
            controlElement.setAttribute("Name", signalDic.getStandardSignalName());
            controlElement.setAttribute("Unit", Convert.convert(String.class, signalDic.getUnit(), ""));
            controlElement.setAttribute("Code", signalDic.getSignalStandardId());
            controls.appendChild(controlElement);
        }
        return controls;
    }

    /**
     * 获取告警等级
     * 这个告警等级是与4互补的
     * @param alarmLevel 告警等级
     * @return 转换后的告警等级字符串
     */
    private String getUserGrade(Object alarmLevel) {
        if (Objects.isNull(alarmLevel) || CharSequenceUtil.isBlank(alarmLevel.toString())) {
            return "";
        }
        Integer level = Convert.convert(Integer.class, alarmLevel);
        return String.valueOf((4 - level));
    }

    @Override
    public String generateSignalIdMapFile(Integer standardVersion) {
        try {
            String content = generateSignalIdMapContent(standardVersion);
            String filePath = getSignalIdMapFilePath(standardVersion);
            
            // 确保目录存在
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 写入文件
            try (FileWriter writer = new FileWriter(file, false)) {
                writer.write(content);
            }
            
            log.info("CMCC信号ID映射文件生成成功: {}", filePath);
            return filePath;
        } catch (IOException e) {
            log.error("生成CMCC信号ID映射文件失败", e);
            throw new RuntimeException("生成信号ID映射文件失败: " + e.getMessage());
        }
    }

    @Override
    public String getSignalIdMapFilePath(Integer standardVersion) {
        return Paths.get("plugins", "south-cmcc-plugin", "workspace", "cmb_signal_id_map", "cmb_signal_id_map.ini")
                .toString();
    }

    /**
     * 生成信号ID映射文件内容
     * @param standardVersion 标准版本
     * @return 文件内容
     */
    private String generateSignalIdMapContent(Integer standardVersion) {
        StringBuilder content = new StringBuilder();
        content.append("# CMCC信号ID映射配置文件\n");
        content.append("# 生成时间: ").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("\n");
        content.append("# 标准版本: ").append(standardVersion).append("\n\n");

        // 获取字典数据
        List<DictionaryItem> semaphoreTypeDic = dictionaryItemService.listByCategoryId(6); // 测点类型字典
        List<DictionaryItem> alarmLogicClassDic = dictionaryItemService.listByCategoryId(4); // 告警类型字典

        // 处理信号字典数据
        List<CmccSignalDic> signalDicList = cmccSignalDicService.listByStandardVersion(standardVersion);
        content.append("# ========== 信号点映射 ==========\n");
        for (CmccSignalDic signalDic : signalDicList) {
            if (signalDic.getSignalStandardId() != null && signalDic.getSignalStandardId().contains("-")) {
                // 解析SignalStandardId，获取最后一个-之后的字符串
                String[] parts = signalDic.getSignalStandardId().split("-");
                if (parts.length > 0) {
                    String key = signalDic.getSignalStandardId();
                    String value = parts[parts.length - 1] + "001";

                    // 获取测点类型名称
                    String semaphoreTypeName = getSemaphoreTypeName(signalDic.getSemaphoreType(), semaphoreTypeDic);
                    String signalName = signalDic.getStandardSignalName() != null ? signalDic.getStandardSignalName() : "";

                    // 添加注释
                    content.append("# ").append(semaphoreTypeName).append("\t").append(signalName).append("\t").append(signalName).append("\n");
                    // 添加映射
                    content.append(key).append(" = ").append(value).append("\n");
                }
            }
        }

        content.append("\n");

        // 处理告警字典数据
        List<CmccAlarmDic> alarmDicList = cmccAlarmDicService.listByStandardVersion(standardVersion);
        content.append("# ========== 告警映射 ==========\n");
        for (CmccAlarmDic alarmDic : alarmDicList) {
            if (alarmDic.getAlarmStandardId() != null && alarmDic.getAlarmStandardId().contains("-")) {
                // 解析AlarmStandardId，获取最后一个-之后的字符串
                String[] parts = alarmDic.getAlarmStandardId().split("-");
                if (parts.length > 0) {
                    String key = alarmDic.getAlarmStandardId();
                    String value = parts[parts.length - 1] + "001";

                    // 获取告警类型名称
                    String alarmLogicClassName = getAlarmLogicClassName(alarmDic.getAlarmLogicClass(), alarmLogicClassDic);
                    String alarmName = alarmDic.getAlarmStandardName() != null ? alarmDic.getAlarmStandardName() : "";

                    // 添加注释
                    content.append("# ").append(alarmLogicClassName).append("\t").append(alarmName).append("\t").append(alarmName).append("\n");
                    // 添加映射
                    content.append(key).append(" = ").append(value).append("\n");
                }
            }
        }

        return content.toString();
    }

    /**
     * 根据测点类型ID获取测点类型名称
     * @param semaphoreType 测点类型ID
     * @param semaphoreTypeDic 测点类型字典
     * @return 测点类型名称
     */
    private String getSemaphoreTypeName(Integer semaphoreType, List<DictionaryItem> semaphoreTypeDic) {
        if (semaphoreType == null || semaphoreTypeDic == null) {
            return "未知类型";
        }
        
        for (DictionaryItem item : semaphoreTypeDic) {
            if (semaphoreType.equals(item.getItemId())) {
                return item.getItemValue() != null ? item.getItemValue() : "未知类型";
            }
        }
        return "未知类型";
    }

    /**
     * 根据告警逻辑类别ID获取告警类型名称
     * @param alarmLogicClass 告警逻辑类别ID
     * @param alarmLogicClassDic 告警类型字典
     * @return 告警类型名称
     */
    private String getAlarmLogicClassName(Integer alarmLogicClass, List<DictionaryItem> alarmLogicClassDic) {
        if (alarmLogicClass == null || alarmLogicClassDic == null) {
            return "未知类型";
        }
        
        for (DictionaryItem item : alarmLogicClassDic) {
            if (alarmLogicClass.equals(item.getItemId())) {
                return item.getItemValue() != null ? item.getItemValue() : "未知类型";
            }
        }
        return "未知类型";
    }
}
