package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryCategory;
import com.siteweb.tcs.south.cmcc.dal.mapper.DictionaryCategoryMapper;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryCategoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典类别Service实现类
 */
@Service
public class DictionaryCategoryServiceImpl extends ServiceImpl<DictionaryCategoryMapper, DictionaryCategory> implements IDictionaryCategoryService {

    @Resource
    private DictionaryCategoryMapper dictionaryCategoryMapper;

    @Override
    public DictionaryCategory getByCategoryId(Integer categoryId) {
        return dictionaryCategoryMapper.selectByCategoryId(categoryId);
    }

    @Override
    public List<DictionaryCategory> listByEnable(Integer enable) {
        return dictionaryCategoryMapper.selectByEnable(enable);
    }

    @Override
    public List<DictionaryCategory> listByIsExpired(Integer isExpired) {
        return dictionaryCategoryMapper.selectByIsExpired(isExpired);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDictionaryCategory(DictionaryCategory dictionaryCategory) {
        return save(dictionaryCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDictionaryCategory(DictionaryCategory dictionaryCategory) {
        return updateById(dictionaryCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDictionaryCategory(Integer id) {
        return removeById(id);
    }
} 