package com.siteweb.tcs.south.cmcc.web.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备视图对象
 * <p>
 * 用于向前端返回设备信息
 * </p>
 */
@Data
public class DeviceVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 设备状态
     */
    private String status;
    
    /**
     * 最后一次通信时间
     */
    private LocalDateTime lastCommunicationTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 