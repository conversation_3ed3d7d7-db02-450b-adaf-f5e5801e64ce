-- CMCC 插件菜单初始化 SQL (H2 版本)
-- 版本：V1.0.1
-- 描述：中国移动南向插件菜单和权限初始化数据

-- =============================================================================
-- 1. 插入菜单数据到 tcs_menu_item 表
-- =============================================================================

-- 插入菜单项
INSERT INTO tcs_menu_item (MenuItemId, PluginId, MenuItemName, Name, ParentMenuItemId, Path, Icon, Component, ShowLink, ShowParent, Redirect, Rank, ActivePath, Auths) VALUES

-- 主菜单：移动B接口管理 (id: 2000)
(2000, 'south-cmcc-plugin', '移动B接口管理', 'CMCCPlugin', 0, '/south-cmcc', 'ep:home-filled', NULL, TRUE, FALSE, '/standard', 0, NULL, NULL),

-- 子菜单：标准化管理 (id: 2001)
(2001, 'south-cmcc-plugin', '标准化管理', 'StandardHome', 2000, '/south-cmcc/standard', 'mingcute:file-security-line', 'standard/index', TRUE, TRUE, NULL, 1, NULL, NULL),

-- 子菜单：标准化设置 (id: 2002)
(2002, 'south-cmcc-plugin', '标准化设置', 'StandardSetting', 2000, '/south-cmcc/standard-setting', 'ic:outline-settings', 'standard-config/index', TRUE, TRUE, NULL, 2, NULL, NULL),

-- 子菜单：待入网 (id: 2003)
(2003, 'south-cmcc-plugin', '待入网', 'PendingFSU', 2000, '/south-cmcc/fsu', 'ic:outline-pending-actions', 'fsu/index', TRUE, TRUE, NULL, 3, NULL, NULL),

-- 子菜单：FSU列表 (id: 2004)
(2004, 'south-cmcc-plugin', 'FSU列表', 'FsuList', 2000, '/south-cmcc/fsu-list', 'ic:outline-list', 'fsu-list/index', TRUE, TRUE, NULL, 4, NULL, NULL),

-- 子菜单：FSU详情 (id: 2005)
(2005, 'south-cmcc-plugin', 'FSU详情', 'FsuDetail', 2000, '/south-cmcc/fsu-list/detail', 'ic:outline-info', 'fsu-list/detail', FALSE, TRUE, NULL, 5, NULL, NULL);

-- =============================================================================
-- 2. 插入权限代码到 tcs_auth_code 表
-- =============================================================================

-- 插入权限代码
INSERT INTO tcs_auth_code (auth_code, auth_name, menu_item_id, plugin_id) VALUES

-- 标准化管理相关权限
('cmcc:standard:view', '标准化管理查看', 2001, 'south-cmcc-plugin'),
('cmcc:standard:signal:add', '新增信号标准化', 2001, 'south-cmcc-plugin'),
('cmcc:standard:signal:edit', '编辑信号标准化', 2001, 'south-cmcc-plugin'),
('cmcc:standard:signal:delete', '删除信号标准化', 2001, 'south-cmcc-plugin'),
('cmcc:standard:alarm:add', '新增告警标准化', 2001, 'south-cmcc-plugin'),
('cmcc:standard:alarm:edit', '编辑告警标准化', 2001, 'south-cmcc-plugin'),
('cmcc:standard:alarm:delete', '删除告警标准化', 2001, 'south-cmcc-plugin'),
('cmcc:standard:refresh', '刷新标准化数据', 2001, 'south-cmcc-plugin'),

-- 标准化设置相关权限
('cmcc:standard-setting:view', '标准化设置查看', 2002, 'south-cmcc-plugin'),
('cmcc:standard-setting:template:manage', '设备模板管理', 2002, 'south-cmcc-plugin'),
('cmcc:standard-setting:template:apply', '应用标准化到模板', 2002, 'south-cmcc-plugin'),
('cmcc:standard-setting:template:edit', '编辑设备模板', 2002, 'south-cmcc-plugin'),
('cmcc:standard-setting:config:drag', '拖拽配置标准化', 2002, 'south-cmcc-plugin'),

-- 待入网FSU相关权限
('cmcc:fsu:view', '待入网FSU查看', 2003, 'south-cmcc-plugin'),
('cmcc:fsu:approve', '批准FSU入网', 2003, 'south-cmcc-plugin'),
('cmcc:fsu:batch-approve', '批量批准FSU入网', 2003, 'south-cmcc-plugin'),
('cmcc:fsu:batch-config', '批量配置FSU', 2003, 'south-cmcc-plugin'),
('cmcc:fsu:refresh', '刷新FSU列表', 2003, 'south-cmcc-plugin'),

-- FSU列表相关权限
('cmcc:fsu-list:view', 'FSU列表查看', 2004, 'south-cmcc-plugin'),
('cmcc:fsu-list:detail', 'FSU设备详情查看', 2004, 'south-cmcc-plugin'),
('cmcc:fsu-list:region:access', 'FSU区域访问', 2004, 'south-cmcc-plugin'),
('cmcc:fsu-list:refresh', '刷新FSU列表', 2004, 'south-cmcc-plugin');

-- =============================================================================
-- 3. 角色权限分配 (只为超级管理员分配权限)
-- =============================================================================

-- 为系统管理员角色分配south-cmcc-plugin插件的所有菜单权限
INSERT INTO tcs_role_permission_map (PluginId, RoleId, PermissionId, permissionType) 
SELECT PluginId, -1, MenuItemId, 1 FROM tcs_menu_item WHERE PluginId = 'south-cmcc-plugin';

-- 只为系统管理员角色分配south-cmcc-plugin插件的所有操作权限
INSERT INTO tcs_role_permission_map (PluginId, RoleId, PermissionId, permissionType) 
SELECT 'south-cmcc-plugin', -1, auth_id, 3 FROM tcs_auth_code WHERE plugin_id = 'south-cmcc-plugin';

-- =============================================================================
-- 说明：
-- 1. MenuId 使用 2000 系列，避免与其他插件冲突
-- 2. PermissionType: 1-菜单权限, 3-操作权限
-- 3. RoleId: -1 表示系统管理员角色
-- 4. PluginId: 'south-cmcc-plugin' 标识插件归属
-- 5. 所有记录都包含创建时间和更新时间
-- =============================================================================