-- Table: cmcc_alarm_dic
CREATE TABLE cmcc_alarm_dic (
  id INT PRIMARY KEY AUTO_INCREMENT,
  StandardVersion INT,
  AlarmStandardId VARCHAR(64),
  DeviceType INT,
  AlarmLogicClass INT,
  AlarmLogicSubclass VARCHAR(1024),
  AlarmStandardName VARCHAR(512),
  Meaning VARCHAR(1024),
  CommunicationBuildingAlarmLevel INT,
  TransmissionNodeAlarmLevel INT,
  CommunicationBaseStationAlarmLevel INT,
  IDCAlarmLevel INT,
  Description VARCHAR(1024),
  ExtendFiled TEXT,
  UNIQUE (AlarmStandardId)
);

-- Table: cmcc_signal_dic
CREATE TABLE cmcc_signal_dic (
  id INT PRIMARY KEY AUTO_INCREMENT,
  StandardVersion INT,
  SignalStandardId VARCHAR(128) NOT NULL,
  DeviceType INT,
  DeviceSubType VARCHAR(1024),
  StandardSignalName VARCHAR(512) NOT NULL,
  Unit VARCHAR(16),
  SemaphoreType INT,
  Meaning VARCHAR(512),
  Description VARCHAR(1024),
  ExtendFiled TEXT,
  UNIQUE (SignalStandardId)
);

-- Table: cmcc_standard_version
CREATE TABLE cmcc_standard_version (
  idcmcc_standard_version INT PRIMARY KEY
);

-- Table: dictionary_item
CREATE TABLE dictionary_item (
  DictionaryItemId INT PRIMARY KEY AUTO_INCREMENT,
  ParentCategoryId INT,
  ParentItemId INT,
  CategoryId INT,
  ItemId INT,
  ItemValue VARCHAR(255),
  TranslateKey VARCHAR(255),
  Enable TINYINT DEFAULT 1,
  IsSystem TINYINT DEFAULT 0,
  IsDefault TINYINT DEFAULT 0,
  Description VARCHAR(1024),
  IsExpired TINYINT DEFAULT 0,
  ExtendField TEXT
);

CREATE TABLE dictionary_category (
  DictionaryCategoryId INT PRIMARY KEY AUTO_INCREMENT,
  CategoryId INT,
  CategoryValue VARCHAR(225),
  TranslateKey VARCHAR(225),
  Enable TINYINT DEFAULT 1,
  IsExpired TINYINT DEFAULT 0,
  ExtendField TEXT
);

