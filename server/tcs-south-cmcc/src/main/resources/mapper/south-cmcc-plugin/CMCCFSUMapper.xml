<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.cmcc.dal.mapper.CMCCFSUMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu">
        <id column="id" property="id" />
        <result column="fsu_id" property="fsuId" />
        <result column="graph_id" property="graphId" />
        <result column="ip_address" property="ipAddress" />
        <result column="mac" property="mac" />
        <result column="version" property="version" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="vendor" property="vendor" />
        <result column="model" property="model" />
        <result column="region_id" property="regionId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="firmware_version" property="firmwareVersion" />
        <result column="software_version" property="softwareVersion" />
        <result column="first_login_date" property="firstLoginDate" />
        <result column="last_login_date" property="lastLoginDate" />
        <result column="last_ota_date" property="lastOtaDate" />
        <result column="last_sync_factory_date" property="lastSyncFactoryDate" />
        <result column="last_sync_scheme_date" property="lastSyncSchemeDate" />
        <result column="last_su_ready_date" property="lastSuReadyDate" />
        <result column="ftp_type" property="ftpType" />
        <result column="ftp_port" property="ftpPort" />
        <result column="ftp_user_name" property="ftpUserName" />
        <result column="ftp_password" property="ftpPassword" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fsu_id, graph_id, ip_address, mac, version, username, password, vendor, model, region_id, 
        create_time, update_time, firmware_version, software_version, first_login_date, last_login_date, 
        last_ota_date, last_sync_factory_date, last_sync_scheme_date, last_su_ready_date, 
        ftp_type, ftp_port, ftp_user_name, ftp_password
    </sql>

</mapper>
