<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.cmcc.dal.mapper.CmccSignalDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.south.cmcc.dal.entity.CmccSignalDic">
        <id column="id" property="id" />
        <result column="StandardVersion" property="standardVersion" />
        <result column="SignalStandardId" property="signalStandardId" />
        <result column="DeviceType" property="deviceType" />
        <result column="DeviceSubType" property="deviceSubType" />
        <result column="StandardSignalName" property="standardSignalName" />
        <result column="Unit" property="unit" />
        <result column="SemaphoreType" property="semaphoreType" />
        <result column="Meaning" property="meaning" />
        <result column="Description" property="description" />
        <result column="ExtendFiled" property="extendFiled" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, StandardVersion, SignalStandardId, DeviceType, DeviceSubType, StandardSignalName, 
        Unit, SemaphoreType, Meaning, Description, ExtendFiled
    </sql>

    <!-- 根据信号标准ID查询信号字典 -->
    <select id="selectBySignalStandardId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_signal_dic
        WHERE SignalStandardId = #{signalStandardId}
    </select>

    <!-- 根据设备类型查询信号字典列表 -->
    <select id="selectByDeviceType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_signal_dic
        WHERE DeviceType = #{deviceType}
        ORDER BY id
    </select>

    <!-- 根据标准版本查询信号字典列表 -->
    <select id="selectByStandardVersion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_signal_dic
        <if test="standardVersion != null">
            WHERE StandardVersion = #{standardVersion}
        </if>
        ORDER BY id
    </select>

    <!-- 根据信号量类型查询信号字典列表 -->
    <select id="selectBySemaphoreType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmcc_signal_dic
        WHERE SemaphoreType = #{semaphoreType}
        ORDER BY id
    </select>

</mapper> 