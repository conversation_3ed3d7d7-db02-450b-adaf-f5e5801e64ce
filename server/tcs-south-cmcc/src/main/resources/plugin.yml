plugin:
  id: south-cmcc-plugin
  name: 中国移动南向接入插件
  description: TCS中国移动南向接入插件
  version: 1.0.0
  provider: Siteweb

  graph:
    template: cmcc-default-template.json


  # 中间件资源配置
  middleware:
    database:
      primary: cmcc-h2-config-primary  # 主数据库资源ID
    redis:
      primary: test-redis-config-001  # 主Redis资源ID
    kafka:
      primary: tcs-kafka  # 主Kafka资源ID
    http-server:
      primary: cmcc-akka-http-server-service   # 主HTTP服务器服务ID


  gateway:
    auto-access:
      enable: false                     # 开启FSU自动入网
      default-enable: true             # 入网后自动启用
      default-config-sync: DOWN_TOP    # 默认配置从下网上同步

    default-port: 8080                 # FSU的默认端口
    backup:
      max-file-count: 100              # 保留最多10次记录
      delete-after: 180d               # 备份记录保留日期
      min-file-count: 10               # 保留最近10份记录（即便是过期也）


    sharding:
      name: cmcc-gateway
      count: 10
  #fsu定时任务，时间同步 获取fsu信息
  scheduler:
    time_check:
      interval: 60
      initial-delay: 30
    get_fsu_info:
      interval: 600
      initial-delay: 30
