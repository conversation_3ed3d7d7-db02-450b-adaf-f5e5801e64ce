{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CMCC Send Alarm Configuration", "description": "Configuration schema for CMCC Send Alarm Shape based on B Interface Specification 5.6.2", "type": "object", "properties": {"fsuId": {"type": "string", "title": "FSU ID", "description": "FSU标识，长度为20字节", "maxLength": 20, "minLength": 1}, "alarmProcessing": {"type": "object", "title": "告警处理配置", "properties": {"enableValidation": {"type": "boolean", "title": "启用验证", "description": "是否启用告警数据验证", "default": true}, "enableTimeNormalization": {"type": "boolean", "title": "启用时间标准化", "description": "是否自动标准化告警时间格式", "default": true}, "maxAlarmsPerMessage": {"type": "integer", "title": "单次消息最大告警数", "description": "单个消息中允许的最大告警数量", "minimum": 1, "maximum": 1000, "default": 100}}}, "output": {"type": "object", "title": "输出配置", "properties": {"enablePipelineForward": {"type": "boolean", "title": "启用管道转发", "description": "是否将告警数据转发到处理管道", "default": true}, "enableResponseGeneration": {"type": "boolean", "title": "启用响应生成", "description": "是否生成告警确认响应", "default": true}}}, "logging": {"type": "object", "title": "日志配置", "properties": {"logLevel": {"type": "string", "title": "日志级别", "enum": ["DEBUG", "INFO", "WARN", "ERROR"], "default": "INFO"}, "enableDetailedLogging": {"type": "boolean", "title": "启用详细日志", "description": "是否记录详细的告警处理日志", "default": false}}}}, "required": ["fsuId"], "additionalProperties": false}