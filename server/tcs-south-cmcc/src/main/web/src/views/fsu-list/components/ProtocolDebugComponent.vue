<template>
  <div class="bg-gray-50 dark:bg-gray-900 p-2">
    <div class="max-w-full mx-auto">
      <!-- 简化的头部 - 只包含报文记录开关 -->
      <div class="flex justify-start mb-3">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">报文记录:</span>
          <el-switch
            v-model="isOpen"
            :loading="switchLoading"
            @change="handleSwitchChange"
            active-text="开启"
            inactive-text="关闭"
            size="small"
          />
        </div>
      </div>

      <!-- 主内容区域 - 三列布局，调整协议类型列宽度 -->
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-4">
        <!-- 左侧：协议类型 - 占2列 -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 min-h-[750px]">
          <div class="p-3 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center">
                <el-icon class="mr-2 text-green-500"><List /></el-icon>
                <h3 class="text-base font-semibold text-gray-900 dark:text-white">协议类型</h3>
              </div>
              <el-tag type="info" size="small">
                共 {{ filteredProtocolList.length }}/{{ protocolList.length }} 个协议
              </el-tag>
            </div>
            <el-input
              v-model="protocolFilter"
              placeholder="搜索协议类型..."
              size="small"
              clearable
              class="w-full"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <div class="p-3">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 max-h-[600px] overflow-auto">
              <div v-if="filteredProtocolList.length === 0" class="flex flex-col items-center justify-center py-12">
                <el-icon size="48" class="text-gray-400 mb-4"><Document /></el-icon>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ protocolList.length === 0 ? '暂无协议数据' : '未找到匹配的协议类型' }}
                </p>
              </div>
              <div v-else class="p-2 space-y-1.5">
                <div
                  v-for="item in filteredProtocolList"
                  :key="item.code"
                  class="protocol-item px-2 py-1.5 rounded-lg cursor-pointer transition-all duration-200 border"
                  :class="{
                    'bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 shadow-sm': item.code === selectedCode,
                    'bg-white dark:bg-gray-600 border-gray-200 dark:border-gray-500 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 hover:shadow-sm': item.code !== selectedCode
                  }"
                  @click="handleProtocolClick(item)"
                >
                  <div class="flex items-center justify-between">
                    <span class="font-medium truncate">{{ item.desc }}</span>
                    <el-tag type="info" size="small" effect="plain">{{ item.count || 0 }}</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：协议记录 - 占4列 -->
        <div class="lg:col-span-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 min-h-[750px]">
          <div class="p-3 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <el-icon class="mr-2 text-purple-500"><DataAnalysis /></el-icon>
                <h3 class="text-base font-semibold text-gray-900 dark:text-white">
                  报文记录
                  <span v-if="selectedCode" class="text-blue-600 dark:text-blue-400 text-sm font-normal ml-2">
                    - {{ selectedCode }}
                  </span>
                </h3>
              </div>
              <div class="flex items-center space-x-2">
                <el-tag type="info" size="small">
                  共 {{ protocolTableData.length }} 条记录
                </el-tag>
                <el-button
                  type="primary"
                  size="small"
                  :loading="tableLoading"
                  @click="refreshProtocolData"
                >
                  <el-icon class="mr-1"><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="p-2 flex-1 flex flex-col">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden flex-1 flex flex-col">
              <el-table
                :data="protocolTableData"
                v-loading="tableLoading"
                height="100%"
                size="small"
                stripe
                style="width: 100%"
                @row-click="handleRowClick"
                :row-class-name="getRowClassName"
                :default-sort="{ prop: 'timestamp', order: 'descending' }"
                class="modern-table flex-1"
              >
                <el-table-column
                  prop="insertTime"
                  label="操作时间"
                  min-width="160"
                  sortable
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <div class="flex items-center">
                      <el-icon class="mr-1 text-blue-500"><Clock /></el-icon>
                      <span class="text-xs font-medium">{{ formatTime(row.insertTime) }}</span>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column
                  prop="completed"
                  label="状态"
                  min-width="100"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <div class="flex items-center space-x-1">
                      <el-tag
                        v-if="row.exception"
                        type="danger"
                        size="small"
                        effect="plain"
                        title="有异常"
                      >
                        异常
                      </el-tag>
                      <el-tag
                        v-else-if="row.completed"
                        type="success"
                        size="small"
                        effect="plain"
                        title="已完成"
                      >
                        完成
                      </el-tag>
                      <el-tag
                        v-else
                        type="warning"
                        size="small"
                        effect="plain"
                        title="处理中"
                      >
                        处理中
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column
                  prop="duration"
                  label="用时"
                  min-width="80"
                  align="center"
                >
                  <template #default="{ row }">
                    <span class="text-xs font-mono">
                      {{ formatDuration(row) }}
                    </span>
                  </template>
                </el-table-column>
                
              </el-table>
            </div>
          </div>
        </div>

        <!-- 右侧：报文记录 - 占6列 -->
        <div class="lg:col-span-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-[750px] flex flex-col">
          <div class="p-3 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <el-icon class="mr-2 text-orange-500"><Document /></el-icon>
                <div>
                  <h3 class="text-base font-semibold text-gray-900 dark:text-white">报文详情</h3>
                  <div v-if="selectedRow" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>请求时间: {{ formatTime(selectedRow.insertTime) }}</span>
                    <span class="ml-4">用时: {{ formatDuration(selectedRow) }}</span>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2" v-if="selectedRow">
                <el-button
                  size="small"
                  @click="copyRequestToClipboard"
                  type="primary"
                  plain
                >
                  <el-icon class="mr-1"><CopyDocument /></el-icon>
                  复制请求
                </el-button>
                <el-button
                  size="small"
                  @click="copyResponseToClipboard"
                  type="primary"
                  plain
                  :disabled="!selectedRow.responsePacketInfo"
                >
                  <el-icon class="mr-1"><CopyDocument /></el-icon>
                  复制响应
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="p-2 flex-1 flex flex-col">
            <div v-if="!selectedRow" class="flex flex-col items-center justify-center py-16">
              <el-icon size="64" class="text-gray-400 mb-4"><Document /></el-icon>
              <p class="text-gray-500 dark:text-gray-400 text-center text-sm">
                请选择一个协议记录查看报文详情
              </p>
            </div>
            <div v-else class="flex-1 flex flex-col">
              <!-- 上下分屏：请求和响应/异常 -->
              <div class="flex-1 flex flex-col space-y-3">
                <!-- 上半部分：请求报文 -->
                <div class="flex-[40] flex flex-col">
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    请求报文
                  </div>
                  <div class="bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden flex-1">
                    <div v-if="!selectedRow.requestPacketInfo || !selectedRow.requestPacketInfo.trim()" class="flex flex-col items-center justify-center h-full py-8">
                      <el-icon size="32" class="text-gray-400 mb-2"><Document /></el-icon>
                      <p class="text-gray-500 dark:text-gray-400 text-center text-xs">
                        请求报文为空
                      </p>
                    </div>
                    <MonacoEditor
                      v-else
                      v-model="requestMonacoValue"
                      language="xml"
                      :theme="isDark ? 'vs-dark' : 'vs'"
                      height="100%"
                      :read-only="true"
                      :options="{
                        minimap: { enabled: false },
                        scrollBeyondLastLine: false,
                        wordWrap: 'on',
                        fontSize: 11,
                        lineNumbers: 'on',
                        folding: true,
                        contextmenu: true,
                        selectOnLineNumbers: true,
                        automaticLayout: true
                      }"
                    />
                  </div>
                </div>
                
                <!-- 下半部分：响应报文或异常信息 -->
                <div class="flex-[60] flex flex-col">
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {{ selectedRow.exception ? '错误信息' : '响应报文' }}
                  </div>
                  
                  <!-- 异常信息显示 -->
                  <div v-if="selectedRow.exception" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex-1">
                    <div class="flex items-start">
                      <el-icon class="mr-2 text-red-500 mt-0.5 flex-shrink-0"><Warning /></el-icon>
                      <div class="flex-1">
                        <div class="font-medium text-red-700 dark:text-red-400 text-sm mb-2">异常详情</div>
                        <div class="text-red-600 dark:text-red-300 text-sm leading-relaxed whitespace-pre-wrap">
                          {{ selectedRow.exception }}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 响应报文显示 -->
                  <div v-else class="bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden flex-1">
                    <div v-if="!selectedRow.responsePacketInfo || !selectedRow.responsePacketInfo.trim()" class="flex flex-col items-center justify-center h-full py-8">
                      <el-icon size="32" class="text-gray-400 mb-2"><Document /></el-icon>
                      <p class="text-gray-500 dark:text-gray-400 text-center text-xs">
                        {{ selectedRow.completed ? '响应报文为空' : '等待响应中...' }}
                      </p>
                    </div>
                    <MonacoEditor
                      v-else
                      v-model="responseMonacoValue"
                      language="xml"
                      :theme="isDark ? 'vs-dark' : 'vs'"
                      height="100%"
                      :read-only="true"
                      :options="{
                        minimap: { enabled: false },
                        scrollBeyondLastLine: false,
                        wordWrap: 'on',
                        fontSize: 11,
                        lineNumbers: 'on',
                        folding: true,
                        contextmenu: true,
                        selectOnLineNumbers: true,
                        automaticLayout: true
                      }"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from "vue";
import { useRoute } from 'vue-router';
import { ElMessage } from "element-plus";
import moment from "moment";
import {
  Document,
  Clock,
  Refresh,
  CopyDocument,
  List,
  DataAnalysis,
  Warning,
  Search,
  WarningFilled
} from "@element-plus/icons-vue";
import {
  getTracerState,
  setTracerState,
  getTracerMessages,
  transformToProtocolTypes,
  transformToTableRecords,
  formatXmlString,
  type ProtocolType,
  type ProcessedMessageRecord
} from "@/api/protocol-debug";
import MonacoEditor from "@/components/MonacoEditor.vue";
import { useGlobal } from "@pureadmin/utils";

defineOptions({
  name: "ProtocolDebugComponent"
});

// Props
interface Props {
  fsuData?: {
    gatewayId: string;
    gatewayName?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  fsuData: () => ({ gatewayId: "" })
});

// 路由信息
const route = useRoute();

// 从路由获取FSU ID (优先从路由参数获取，确保始终有值)
const fsuId = computed(() => {
  const id = route.params.id as string || route.query.fsuId as string || props.fsuData?.gatewayId;
  if (!id) {
    console.warn('FSU ID参数缺失');
  }
  return id;
});

// 暗色主题检测 - 使用项目自己的主题管理
const { $storage } = useGlobal<GlobalPropertiesApi>();
const isDark = computed(() => $storage?.layout?.darkMode || false);

// 响应式数据
const loading = ref(false);
const tableLoading = ref(false);
const switchLoading = ref(false);
const isOpen = ref(false);
const selectedCode = ref<string | null>(null);
const selectedRow = ref<ProcessedMessageRecord | null>(null);
const requestMonacoValue = ref("");
const responseMonacoValue = ref("");
const tableHeight = ref(400);
const protocolFilter = ref("");

// 移除动态高度计算，改用flex布局自动分配空间

// 协议列表和表格数据
const protocolList = ref<ProtocolType[]>([]);
const protocolTableData = ref<ProcessedMessageRecord[]>([]);
const rawProtocolData = ref<any>(null);

// 计算属性
const selectedProtocolName = computed(() => {
  const protocol = protocolList.value.find(p => p.code === selectedCode.value);
  return protocol ? protocol.desc : "";
});

const filteredProtocolList = computed(() => {
  if (!protocolFilter.value.trim()) {
    return protocolList.value;
  }
  const searchTerm = protocolFilter.value.toLowerCase();
  return protocolList.value.filter(protocol => 
    protocol.code.toLowerCase().includes(searchTerm) ||
    protocol.desc.toLowerCase().includes(searchTerm)
  );
});

// 获取所有协议类型
const getAllCmdTypes = async () => {
  if (!fsuId.value) {
    ElMessage.warning("FSU设备ID不能为空");
    return;
  }

  try {
    loading.value = true;
    
    // 调用真实API获取协议报文数据
    const response = await getTracerMessages(fsuId.value);
    
    if (response.state && response.data) {
      // 保存原始数据
      rawProtocolData.value = response.data;
      
      // 转换为协议类型列表
      protocolList.value = transformToProtocolTypes(response.data);
      
      console.log("获取协议类型成功:", protocolList.value);
    } else {
      ElMessage.error(response.err_msg || "获取协议类型失败");
    }
    
  } catch (error) {
    console.error("获取协议类型失败:", error);
    ElMessage.error("获取协议类型失败");
    
    // 发生错误时显示空状态
    protocolList.value = [];
    rawProtocolData.value = null;
  } finally {
    loading.value = false;
  }
};

// 获取历史报文数据  
const getHistoryPackets = async () => {
  if (!selectedCode.value || !rawProtocolData.value) return;
  
  try {
    tableLoading.value = true;
    
    // 从已获取的原始数据中提取对应协议的消息
    const protocolMessages = rawProtocolData.value[selectedCode.value];
    
    if (protocolMessages && Array.isArray(protocolMessages)) {
      // 转换为表格记录格式
      protocolTableData.value = transformToTableRecords(selectedCode.value, protocolMessages);
      
      console.log(`获取${selectedCode.value}协议报文成功:`, protocolTableData.value.length + "条记录");
    } else {
      protocolTableData.value = [];
      console.log(`协议${selectedCode.value}暂无报文记录`);
    }
    
  } catch (error) {
    console.error("获取历史报文失败:", error);
    ElMessage.error("获取历史报文失败");
    protocolTableData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 获取报文记录开关状态
const getHisPacketEnableStatus = async () => {
  if (!fsuId.value) return;
  
  try {
    const response = await getTracerState(fsuId.value);
    
    if (response.state) {
      isOpen.value = response.data;
      console.log("获取开关状态成功:", response.data);
    } else {
      console.error("获取开关状态失败:", response.err_msg);
      ElMessage.error(response.err_msg || "获取开关状态失败");
    }
    
  } catch (error) {
    console.error("获取开关状态失败:", error);
    ElMessage.error("获取开关状态失败");
  }
};

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-';
  try {
    return moment(timeStr).format('YYYY-MM-DD HH:mm:ss');
  } catch {
    return timeStr;
  }
};


// 格式化用时
const formatDuration = (row: ProcessedMessageRecord) => {
  if (!row.timestamp || !row.responseTimestamp) {
    return '-';
  }
  
  try {
    const startTime = new Date(row.timestamp).getTime();
    const endTime = new Date(row.responseTimestamp).getTime();
    const duration = endTime - startTime;
    
    if (duration <= 0) {
      return '-';
    }
    
    return `${duration}ms`;
  } catch (error) {
    return '-';
  }
};

// 获取行样式
const getRowClassName = ({ row }: { row: ProcessedMessageRecord }) => {
  return selectedRow.value && selectedRow.value.msgId === row.msgId ? 'selected-row' : '';
};

// 协议点击事件
const handleProtocolClick = (item: ProtocolType) => {
  requestMonacoValue.value = "";
  responseMonacoValue.value = "";
  selectedCode.value = item.code;
  selectedRow.value = null;
  getHistoryPackets();
};

// 表格行点击事件
const handleRowClick = (row: ProcessedMessageRecord, column: any, event: Event) => {
  selectedRow.value = row;
  requestMonacoValue.value = "";
  responseMonacoValue.value = "";
  
  // 延迟设置内容，避免界面闪烁
  setTimeout(() => {
    try {
      // 设置请求报文内容
      if (row.requestPacketInfo && row.requestPacketInfo.includes('<?xml')) {
        requestMonacoValue.value = formatXmlString(row.requestPacketInfo);
      } else {
        requestMonacoValue.value = row.requestPacketInfo || '';
      }
      
      // 设置响应报文内容
      if (row.responsePacketInfo) {
        if (row.responsePacketInfo.includes('<?xml')) {
          responseMonacoValue.value = formatXmlString(row.responsePacketInfo);
        } else {
          responseMonacoValue.value = row.responsePacketInfo;
        }
      }
    } catch (error) {
      console.error("格式化报文内容失败:", error);
      requestMonacoValue.value = row.requestPacketInfo || '';
      responseMonacoValue.value = row.responsePacketInfo || '';
    }
  }, 100);
};


// 开关变化事件
const handleSwitchChange = async (newVal: boolean) => {
  if (!fsuId.value) {
    ElMessage.warning("FSU设备ID不能为空");
    isOpen.value = !newVal; // 回滚状态
    return;
  }

  try {
    switchLoading.value = true;
    
    const response = await setTracerState(fsuId.value, newVal);
    
    if (response.state) {
      ElMessage.success(`报文记录已${newVal ? '开启' : '关闭'}`);
      console.log("设置开关状态成功:", newVal);
      
      // 开关状态改变后，重新获取协议数据
      if (newVal) {
        setTimeout(() => {
          getAllCmdTypes();
        }, 1000); // 延迟1秒再获取，确保开关生效
      } else {
        // 关闭时清空数据
        protocolList.value = [];
        protocolTableData.value = [];
        rawProtocolData.value = null;
        selectedCode.value = null;
        selectedRow.value = null;
        requestMonacoValue.value = "";
        responseMonacoValue.value = "";
      }
    } else {
      ElMessage.error(response.err_msg || "设置开关状态失败");
      // 回滚状态
      isOpen.value = !newVal;
    }
    
  } catch (error) {
    console.error("设置开关状态失败:", error);
    ElMessage.error("设置开关状态失败");
    // 回滚状态
    isOpen.value = !newVal;
  } finally {
    switchLoading.value = false;
  }
};

// 刷新协议数据
const refreshProtocolData = () => {
  if (!fsuId.value) {
    ElMessage.warning("FSU设备ID不能为空");
    return;
  }
  
  // 同时获取开关状态和协议数据
  Promise.all([
    getHisPacketEnableStatus(),
    getAllCmdTypes()
  ]).then(() => {
    if (selectedCode.value) {
      getHistoryPackets();
    }
  }).catch(error => {
    console.error('刷新协议调测数据失败:', error);
  });
};

// 复制文本到剪贴板的通用函数
const copyTextToClipboard = async (text: string): Promise<boolean> => {
  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级到传统的 document.execCommand 方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error("复制到剪贴板失败:", error);
    return false;
  }
};

// 复制请求报文到剪贴板
const copyRequestToClipboard = async () => {
  if (!requestMonacoValue.value) {
    ElMessage.warning("请求报文为空");
    return;
  }
  
  const success = await copyTextToClipboard(requestMonacoValue.value);
  if (success) {
    ElMessage.success("请求报文已复制到剪贴板");
  } else {
    ElMessage.error("复制失败，请手动选择内容复制");
  }
};

// 复制响应报文到剪贴板
const copyResponseToClipboard = async () => {
  if (!responseMonacoValue.value) {
    ElMessage.warning("响应报文为空");
    return;
  }
  
  const success = await copyTextToClipboard(responseMonacoValue.value);
  if (success) {
    ElMessage.success("响应报文已复制到剪贴板");
  } else {
    ElMessage.error("复制失败，请手动选择内容复制");
  }
};



// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    const containerHeight = window.innerHeight - 150;
    tableHeight.value = Math.max(500, containerHeight * 0.7);
  });
};

// 暴露方法给父组件调用
defineExpose({
  refreshProtocolData
});

// 页面挂载时初始化
onMounted(() => {
  getAllCmdTypes();
  getHisPacketEnableStatus();
  calculateTableHeight();
  
  window.addEventListener('resize', calculateTableHeight);
});
</script>

<style scoped>
/* 协议项悬停效果 - 已移除动画 */

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row .el-table__cell) {
  background-color: var(--el-color-primary-light-9) !important;
}

/* 现代化表格样式 */
.modern-table :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  background: var(--el-bg-color);
  border: none;
}

.modern-table :deep(.el-table__header) {
  background-color: var(--el-fill-color-lighter);
}

.modern-table :deep(.el-table__header th) {
  background-color: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-light);
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.modern-table :deep(.el-table__row:hover > td) {
  background-color: var(--el-fill-color-light);
}

.modern-table :deep(.el-table td) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

/* Monaco Editor 容器样式优化 */
:deep(.monaco-editor-container) {
  border-radius: 6px;
  overflow: hidden;
}

/* 自定义滚动条 */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: var(--el-text-color-placeholder);
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}

:deep(.el-button--small) {
  padding: 5px 12px;
  font-size: 12px;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
  border: none;
  font-size: 12px;
}

:deep(.el-tag--plain) {
  background-color: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
}

/* 开关样式 */
:deep(.el-switch) {
  --el-switch-border-radius: 12px;
}

:deep(.el-switch--small) {
  height: 20px;
  line-height: 20px;
}

/* 分页样式 */
:deep(.el-pagination) {
  --el-pagination-button-color: var(--el-text-color-regular);
  --el-pagination-hover-color: var(--el-color-primary);
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-radius: 4px;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 4px;
  margin: 0 2px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .p-2 {
    padding: 8px;
  }
  
  .p-3 {
    padding: 8px;
  }
  
  .gap-4 {
    gap: 12px;
  }
}

/* 深色模式适配 */
</style>