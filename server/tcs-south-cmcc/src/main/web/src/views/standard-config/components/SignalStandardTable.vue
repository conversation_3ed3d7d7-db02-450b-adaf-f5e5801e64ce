<template>
  <div class="signal-standard-table">
    <!-- 搜索框 -->
    <div class="px-4 pt-3 pb-4">
      <el-input
        v-model="searchText"
        :placeholder="getSearchPlaceholder()"
        clearable
        :prefix-icon="Search"
        size="default"
        @input="onSearchChange"
      />
    </div>

    <!-- 虚拟表格区域 -->
    <div class="table-container px-4 pb-4 flex-1 overflow-hidden">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="currentTableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        :row-class="getRowClass"
        :row-event-handlers="rowEventHandlers"
        fixed
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, h } from "vue";
import {
  ElIcon,
  ElPopover,
  ElSelect,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>utton,
  ElTag
} from "element-plus";
import { Filter, Search } from "@element-plus/icons-vue";
import { getSignalStandardList, getAlarmStandardList } from "@/api/standard";

interface Props {
  templateId: string;
  activeTab: string; // 新增：当前激活的tab
}

interface Emits {
  (e: "signal-select", data: any): void;
  (e: "alarm-select", data: any): void;
  (e: "drag-start", data: { data: any; type: string }): void;
  (e: "drag-end"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 状态变量
const loading = ref(false);
const signalTableData = ref<any[]>([]); // 信号标准化数据
const alarmTableData = ref<any[]>([]); // 告警标准化数据
const searchText = ref("");

// 表格选中相关
const selectedRowIndexes = ref<number[]>([]);
const selectedRows = ref<any[]>([]);

// 拖拽相关状态
const isDragging = ref(false);
const dragData = ref<any>(null);

// 计算当前表格数据
const tableData = computed(() => {
  if (props.activeTab === 'event') {
    return alarmTableData.value;
  } else {
    // 根据tab类型过滤信号标准化数据
    let filteredSignalData = signalTableData.value;

    if (props.activeTab === 'signal') {
      // 信号tab：过滤遥信、遥测数据 (semaphoreType: 4=遥信DI, 3=遥测AI)
      filteredSignalData = signalTableData.value.filter(item =>
        item.semaphoreType === 4 || item.semaphoreType === 3
      );
    } else if (props.activeTab === 'control') {
      // 控制tab：过滤遥控、遥调数据 (semaphoreType: 1=遥控DO, 2=遥调AO)
      filteredSignalData = signalTableData.value.filter(item =>
        item.semaphoreType === 1 || item.semaphoreType === 2
      );
    }

    return filteredSignalData;
  }
});

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 过滤器状态
const filterState = ref<FilterState>({});

// 创建只读单元格渲染器
const createReadOnlyCell = (fieldKey: string) => {
  return ({ rowData }: any) => {
    const value = rowData[fieldKey];
    const displayValue = value || "";

    return h(
      "div",
      {
        class: "read-only-cell",
        style: {
          padding: "4px 8px",
          minHeight: "24px",
          lineHeight: "24px",
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap"
        },
        title: String(displayValue) // 鼠标悬浮显示完整内容
      },
      String(displayValue)
    );
  };
};

// 初始化过滤器状态
const initFilterState = () => {
  if (props.activeTab === 'event') {
    // 告警标准化过滤器
    filterState.value = {
      alarmStandardId: {
        value: [],
        options: []
      },
      deviceTypeName: {
        value: [],
        options: []
      },
      alarmLogicClassName: {
        value: [],
        options: []
      },
      alarmLogicSubclass: {
        value: [],
        options: []
      },
      alarmStandardName: {
        value: [],
        options: []
      },
      meaning: {
        value: [],
        options: []
      },
      description: {
        value: [],
        options: []
      }
    };
  } else {
    // 信号标准化过滤器
    filterState.value = {
      signalStandardId: {
        value: [],
        options: []
      },
      deviceTypeName: {
        value: [],
        options: []
      },
      deviceSubType: {
        value: [],
        options: []
      },
      standardSignalName: {
        value: [],
        options: []
      },
      semaphoreTypeName: {
        value: [],
        options: []
      },
      meaning: {
        value: [],
        options: []
      },
      description: {
        value: [],
        options: []
      }
    };
  }
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  if (props.activeTab === 'event') {
    // 告警标准化选项
    const alarmStandardIds = [
      ...new Set(tableData.value.map(item => item.alarmStandardId).filter(Boolean))
    ];
    filterState.value.alarmStandardId.options = alarmStandardIds.map(id => ({
      label: id,
      value: id
    }));

    const alarmLogicClassNames = [
      ...new Set(tableData.value.map(item => item.alarmLogicClassName).filter(Boolean))
    ];
    filterState.value.alarmLogicClassName.options = alarmLogicClassNames.map(name => ({
      label: name,
      value: name
    }));

    const alarmLogicSubclasses = [
      ...new Set(tableData.value.map(item => item.alarmLogicSubclass).filter(Boolean))
    ];
    filterState.value.alarmLogicSubclass.options = alarmLogicSubclasses.map(subclass => ({
      label: subclass,
      value: subclass
    }));

    const alarmStandardNames = [
      ...new Set(tableData.value.map(item => item.alarmStandardName).filter(Boolean))
    ];
    filterState.value.alarmStandardName.options = alarmStandardNames.map(name => ({
      label: name,
      value: name
    }));
  } else {
    // 信号标准化选项
    const signalStandardIds = [
      ...new Set(tableData.value.map(item => item.signalStandardId).filter(Boolean))
    ];
    filterState.value.signalStandardId.options = signalStandardIds.map(id => ({
      label: id,
      value: id
    }));

    const deviceSubTypes = [
      ...new Set(tableData.value.map(item => item.deviceSubType).filter(Boolean))
    ];
    filterState.value.deviceSubType.options = deviceSubTypes.map(type => ({
      label: type,
      value: type
    }));

    const standardSignalNames = [
      ...new Set(tableData.value.map(item => item.standardSignalName).filter(Boolean))
    ];
    filterState.value.standardSignalName.options = standardSignalNames.map(name => ({
      label: name,
      value: name
    }));

    const semaphoreTypeNames = [
      ...new Set(tableData.value.map(item => item.semaphoreTypeName).filter(Boolean))
    ];
    filterState.value.semaphoreTypeName.options = semaphoreTypeNames.map(name => ({
      label: name,
      value: name
    }));
  }

  // 通用选项
  const deviceTypeNames = [
    ...new Set(tableData.value.map(item => item.deviceTypeName).filter(Boolean))
  ];
  filterState.value.deviceTypeName.options = deviceTypeNames.map(name => ({
    label: name,
    value: name
  }));

  const meanings = [
    ...new Set(tableData.value.map(item => item.meaning).filter(Boolean))
  ];
  filterState.value.meaning.options = meanings.map(meaning => ({
    label: meaning,
    value: meaning
  }));

  const descriptions = [
    ...new Set(tableData.value.map(item => item.description).filter(Boolean))
  ];
  filterState.value.description.options = descriptions.map(desc => ({
    label: desc,
    value: desc
  }));
};

// 创建过滤器头部组件
const createFilterHeader = (filterKey: string, title: string) => {
  return () => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { 
      class: "flex items-center justify-center",
      style: { padding: "8px" }
    }, [
      h("span", { 
        class: "mr-2",
        style: { fontSize: "12px" }
      }, title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { style: { padding: "8px 0" } }, [
              h("div", { style: { marginBottom: "8px" } }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || []).map(
                        (option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                      )
                  }
                )
              ]),
              h("div", { 
                style: { 
                  borderTop: "1px solid var(--el-border-color)",
                  margin: "12px -12px -12px",
                  padding: "0 12px",
                  display: "flex",
                  justifyContent: "space-between"
                }
              }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { 
              style: { cursor: "pointer" }
            }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value; // tableData已经根据tab类型过滤了信号类型

  // 应用搜索文本过滤
  if (searchText.value && searchText.value.trim() !== "") {
    const searchValue = searchText.value.toLowerCase();
    if (props.activeTab === 'event') {
      // 告警标准化搜索字段
      data = data.filter(
        item =>
          item.alarmStandardId?.toLowerCase().includes(searchValue) ||
          item.deviceTypeName?.toLowerCase().includes(searchValue) ||
          item.alarmLogicClassName?.toLowerCase().includes(searchValue) ||
          item.alarmLogicSubclass?.toLowerCase().includes(searchValue) ||
          item.alarmStandardName?.toLowerCase().includes(searchValue) ||
          item.meaning?.toLowerCase().includes(searchValue) ||
          item.description?.toLowerCase().includes(searchValue)
      );
    } else {
      // 信号标准化搜索字段
      data = data.filter(
        item =>
          item.signalStandardId?.toLowerCase().includes(searchValue) ||
          item.deviceTypeName?.toLowerCase().includes(searchValue) ||
          item.deviceSubType?.toLowerCase().includes(searchValue) ||
          item.standardSignalName?.toLowerCase().includes(searchValue) ||
          item.semaphoreTypeName?.toLowerCase().includes(searchValue) ||
          item.meaning?.toLowerCase().includes(searchValue) ||
          item.description?.toLowerCase().includes(searchValue)
      );
    }
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key];
        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 虚拟表格配置
const tableWidth = ref(800);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  const container = document.querySelector(
    ".signal-standard-table .table-container"
  ) as HTMLElement;
  if (container) {
    const rect = container.getBoundingClientRect();
    tableWidth.value = rect.width - 32; // 减去内边距
    tableHeight.value = rect.height - 32; // 减去内边距
  }
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 信号标准化表格列配置
const signalTableColumns: any[] = [
  {
    key: "signalStandardId",
    title: "信号标准ID",
    dataKey: "signalStandardId",
    width: 220,
    cellRenderer: createReadOnlyCell("signalStandardId"),
    headerCellRenderer: createFilterHeader("signalStandardId", "信号标准ID")
  },
  {
    key: "deviceTypeName",
    title: "设备类型",
    dataKey: "deviceTypeName",
    width: 150,
    cellRenderer: createReadOnlyCell("deviceTypeName"),
    headerCellRenderer: createFilterHeader("deviceTypeName", "设备类型")
  },
  {
    key: "deviceSubType",
    title: "设备子类型",
    dataKey: "deviceSubType",
    width: 150,
    cellRenderer: createReadOnlyCell("deviceSubType"),
    headerCellRenderer: createFilterHeader("deviceSubType", "设备子类型")
  },
  {
    key: "standardSignalName",
    title: "标准信号名称",
    dataKey: "standardSignalName",
    width: 160,
    cellRenderer: createReadOnlyCell("standardSignalName"),
    headerCellRenderer: createFilterHeader("standardSignalName", "标准信号名称")
  },
  {
    key: "semaphoreTypeName",
    title: "信号量类型",
    dataKey: "semaphoreTypeName",
    width: 150,
    cellRenderer: createReadOnlyCell("semaphoreTypeName"),
    headerCellRenderer: createFilterHeader("semaphoreTypeName", "信号量类型")
  },
  {
    key: "meaning",
    title: "含义",
    dataKey: "meaning",
    width: 120,
    cellRenderer: createReadOnlyCell("meaning"),
    headerCellRenderer: createFilterHeader("meaning", "含义")
  },
  {
    key: "description",
    title: "描述",
    dataKey: "description",
    width: 180,
    cellRenderer: createReadOnlyCell("description"),
    headerCellRenderer: createFilterHeader("description", "描述")
  }
];

// 告警标准化表格列配置
const alarmTableColumns: any[] = [
  {
    key: "alarmStandardId",
    title: "告警标准ID",
    dataKey: "alarmStandardId",
    width: 220,
    cellRenderer: createReadOnlyCell("alarmStandardId"),
    headerCellRenderer: createFilterHeader("alarmStandardId", "告警标准ID")
  },
  {
    key: "deviceTypeName",
    title: "设备类型",
    dataKey: "deviceTypeName",
    width: 120,
    cellRenderer: createReadOnlyCell("deviceTypeName"),
    headerCellRenderer: createFilterHeader("deviceTypeName", "设备类型")
  },
  {
    key: "alarmLogicClassName",
    title: "告警逻辑类别",
    dataKey: "alarmLogicClassName",
    width: 150,
    cellRenderer: createReadOnlyCell("alarmLogicClassName"),
    headerCellRenderer: createFilterHeader("alarmLogicClassName", "告警逻辑类别")
  },
  {
    key: "alarmLogicSubclass",
    title: "告警逻辑子类",
    dataKey: "alarmLogicSubclass",
    width: 150,
    cellRenderer: createReadOnlyCell("alarmLogicSubclass"),
    headerCellRenderer: createFilterHeader("alarmLogicSubclass", "告警逻辑子类")
  },
  {
    key: "alarmStandardName",
    title: "告警标准名称",
    dataKey: "alarmStandardName",
    width: 160,
    cellRenderer: createReadOnlyCell("alarmStandardName"),
    headerCellRenderer: createFilterHeader("alarmStandardName", "告警标准名称")
  },
  {
    key: "meaning",
    title: "含义",
    dataKey: "meaning",
    width: 120,
    cellRenderer: createReadOnlyCell("meaning"),
    headerCellRenderer: createFilterHeader("meaning", "含义")
  },
  {
    key: "description",
    title: "描述",
    dataKey: "description",
    width: 180,
    cellRenderer: createReadOnlyCell("description"),
    headerCellRenderer: createFilterHeader("description", "描述")
  }
];

// 动态计算列宽
const calculateColumnWidths = () => {
  const containerWidth = tableWidth.value;
  const minWidths = {
    signalStandardId: 180,
    deviceTypeName: 80,
    deviceSubType: 100,
    standardSignalName: 140,
    semaphoreTypeName: 80,
    meaning: 100,
    description: 150,
    // 告警相关
    alarmStandardId: 180,
    alarmLogicClassName: 110,
    alarmLogicSubclass: 110,
    alarmStandardName: 140
  };

  // 如果容器宽度足够，使用固定宽度；否则按比例缩放
  if (containerWidth > 1000) {
    return props.activeTab === 'event' ? alarmTableColumns : signalTableColumns;
  }

  // 计算比例缩放
  const baseColumns = props.activeTab === 'event' ? alarmTableColumns : signalTableColumns;
  const totalMinWidth = baseColumns.reduce((sum, col) => sum + minWidths[col.key], 0);
  const scale = Math.max(0.7, containerWidth / totalMinWidth);

  return baseColumns.map(col => ({
    ...col,
    width: Math.max(minWidths[col.key], Math.floor(col.width * scale))
  }));
};

// 当前表格列配置
const currentTableColumns = computed(() => {
  return calculateColumnWidths();
});

// 获取搜索框占位符
const getSearchPlaceholder = () => {
  if (props.activeTab === 'event') {
    return "搜索告警标准ID、设备类型、告警名称...";
  } else if (props.activeTab === 'signal') {
    return "搜索信号标准ID、设备类型、信号名称（仅显示遥信/遥测）...";
  } else if (props.activeTab === 'control') {
    return "搜索信号标准ID、设备类型、信号名称（仅显示遥控/遥调）...";
  }
  return "搜索标准化数据...";
};

// 组件初始化
onMounted(() => {
  // 初始化过滤器状态
  initFilterState();

  // 加载数据
  loadStandardData();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const container = document.querySelector(
    ".signal-standard-table .table-container"
  ) as HTMLElement;
  if (container && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateTableSize();
    });
    resizeObserver.observe(container);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", updateTableSize);
});

// 加载信号标准化数据
const loadSignalStandardData = async () => {
  loading.value = true;
  try {
    const res = await getSignalStandardList();
    if (res.state === true && res.data) {
      signalTableData.value = res.data;
    } else {
      signalTableData.value = [];
    }
  } catch (error) {
    console.error("获取信号标准化数据失败:", error);
    signalTableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 加载告警标准化数据
const loadAlarmStandardData = async () => {
  loading.value = true;
  try {
    const res = await getAlarmStandardList();
    if (res.state === true && res.data) {
      alarmTableData.value = res.data;
    } else {
      alarmTableData.value = [];
    }
  } catch (error) {
    console.error("获取告警标准化数据失败:", error);
    alarmTableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 加载标准化数据
const loadStandardData = async () => {
  if (props.activeTab === 'event') {
    await loadAlarmStandardData();
  } else {
    await loadSignalStandardData();
  }
  updateDynamicOptions();
};

// 监听activeTab变化
watch(
  () => props.activeTab,
  (newTab) => {
    // 重新初始化过滤器状态
    initFilterState();
    // 加载对应的数据
    loadStandardData();
  },
  { immediate: true }
);

// 监听模板ID变化
watch(
  () => props.templateId,
  newTemplateId => {
    if (newTemplateId) {
      loadStandardData();
    }
  }
);

// 处理搜索变化
const onSearchChange = () => {
  // 搜索逻辑已通过计算属性实现
};

// 行事件处理器
const rowEventHandlers = {
  onClick: ({ rowData, rowIndex, event }: any) => {
    // 单击时选中行
    const clickedRowIndex = filteredData.value.findIndex(
      item => item.id === rowData.id
    );
    if (clickedRowIndex !== -1) {
      // 如果按住Ctrl键，支持多选
      if (event.ctrlKey || event.metaKey) {
        const isSelected = selectedRowIndexes.value.includes(clickedRowIndex);
        if (isSelected) {
          // 取消选中
          selectedRowIndexes.value = selectedRowIndexes.value.filter(
            index => index !== clickedRowIndex
          );
        } else {
          // 添加选中
          selectedRowIndexes.value.push(clickedRowIndex);
        }
      } else {
        // 单选
        selectedRowIndexes.value = [clickedRowIndex];
      }

      // 更新选中的行数据
      selectedRows.value = selectedRowIndexes.value.map(
        index => filteredData.value[index]
      );

      // 根据tab类型发射不同的选中事件
      if (props.activeTab === 'event') {
        emit("alarm-select", rowData);
      } else {
        emit("signal-select", rowData);
      }
    }
  },
  onMousedown: ({ rowData, event }: any) => {
    // 开始拖拽
    startDrag(rowData, event);
  }
};

// 获取行样式类
const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  const isSelected = selectedRowIndexes.value.includes(rowIndex);
  return isSelected ? "selected-row" : "";
};

// 拖拽相关方法
const startDrag = (rowData: any, event: MouseEvent) => {
  // 防止文本选择
  event.preventDefault();

  let startX = event.clientX;
  let startY = event.clientY;
  let hasMoved = false;

  const handleMouseMove = (e: MouseEvent) => {
    const deltaX = Math.abs(e.clientX - startX);
    const deltaY = Math.abs(e.clientY - startY);

    // 只有移动超过5px才开始拖拽
    if (deltaX > 5 || deltaY > 5) {
      if (!hasMoved) {
        hasMoved = true;
        isDragging.value = true;
        dragData.value = rowData;

        // 创建拖拽元素
        createDragElement(rowData, e);

        // 通知父组件开始拖拽
        emit("drag-start", {
          data: rowData,
          type: props.activeTab
        });
      }

      // 更新拖拽元素位置
      updateDragElementPosition(e);
    }
  };

  const handleMouseUp = () => {
    if (hasMoved && isDragging.value) {
      // 结束拖拽
      endDrag();
    }

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 创建拖拽元素
const createDragElement = (rowData: any, event: MouseEvent) => {
  const dragElement = document.createElement('div');
  dragElement.id = 'drag-element';
  dragElement.className = 'drag-element';

  // 根据不同类型显示不同内容
  let content = '';
  if (props.activeTab === 'event') {
    content = `告警: ${rowData.alarmStandardName || rowData.alarmStandardId}`;
  } else {
    content = `信号: ${rowData.standardSignalName || rowData.signalStandardId}`;
  }

  dragElement.innerHTML = `
    <div class="drag-content">
      <i class="el-icon-document"></i>
      <span>${content}</span>
    </div>
  `;

  dragElement.style.cssText = `
    position: fixed;
    left: ${event.clientX + 10}px;
    top: ${event.clientY - 10}px;
    background: var(--el-color-primary);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 9999;
    pointer-events: none;
    opacity: 0.9;
  `;

  document.body.appendChild(dragElement);
};

// 更新拖拽元素位置
const updateDragElementPosition = (event: MouseEvent) => {
  const dragElement = document.getElementById('drag-element');
  if (dragElement) {
    dragElement.style.left = `${event.clientX + 10}px`;
    dragElement.style.top = `${event.clientY - 10}px`;
  }
};

// 结束拖拽
const endDrag = () => {
  isDragging.value = false;
  dragData.value = null;

  // 移除拖拽元素
  const dragElement = document.getElementById('drag-element');
  if (dragElement) {
    document.body.removeChild(dragElement);
  }

  // 通知父组件结束拖拽
  emit("drag-end");
};

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  // 清理选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
  window.removeEventListener("resize", updateTableSize);
});

// 设置搜索文本的方法（供父组件调用）
const setSearchText = (text: string) => {
  searchText.value = text;
  console.log(`📝 标准化库搜索文本已设置为: "${text}"`);
};

// 暴露方法给父组件
defineExpose({
  loadSignalStandardData,
  loadAlarmStandardData,
  loadStandardData,
  setSearchText
});
</script>

<style scoped>
.signal-standard-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 只读单元格样式 */
.read-only-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  color: var(--el-text-color-regular);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default;
}

/* Tailwind 工具类 */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.mr-2 {
  margin-right: 8px;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}

/* 拖拽相关样式 */
.drag-element {
  user-select: none;
}

.drag-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 拖拽时的行样式 */
:deep(.dragging-row) {
  opacity: 0.5;
  background-color: var(--el-color-primary-light-9) !important;
}
</style> 