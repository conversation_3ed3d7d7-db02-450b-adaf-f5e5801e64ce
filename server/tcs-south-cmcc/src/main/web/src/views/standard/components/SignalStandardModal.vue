<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑信号标准化' : '添加信号标准化'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="140px"
      label-position="right"
    >
      <el-form-item label="信号编码ID" prop="signalStandardId">
        <el-input
          v-model="formData.signalStandardId"
          placeholder="请输入6位信号编码ID"
          maxlength="6"
          clearable
          :disabled="isEdit"
        />
      </el-form-item>
      
      <el-form-item label="设备类型名称" prop="deviceType">
        <el-select
          v-model="formData.deviceType"
          placeholder="请选择设备类型"
          clearable
          style="width: 100%"
          @change="handleDeviceTypeChange"
        >
          <el-option
            v-for="item in deviceTypeOptions"
            :key="item.itemId"
            :label="item.itemValue"
            :value="item.itemId"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="设备子类型" prop="deviceSubType">
        <el-input
          v-model="formData.deviceSubType"
          placeholder="请输入设备子类型"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="标准信号名称" prop="standardSignalName">
        <el-input
          v-model="formData.standardSignalName"
          placeholder="请输入标准信号名称"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="信号类型名称" prop="semaphoreType">
        <el-select
          v-model="formData.semaphoreType"
          placeholder="请选择信号类型"
          clearable
          style="width: 100%"
          @change="handleSemaphoreTypeChange"
        >
          <el-option
            v-for="item in semaphoreTypeOptions"
            :key="item.itemId"
            :label="item.itemValue"
            :value="item.itemId"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="单位" prop="unit">
        <el-input
          v-model="formData.unit"
          placeholder="请输入单位"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="含义" prop="meaning">
        <el-input
          v-model="formData.meaning"
          placeholder="格式：信号数值1=含义1;信号数值2=含义2;... (可为空)"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import type { SignalStandardData, DictionaryItem } from '@/api/standard';
import { addSignalStandard, updateSignalStandard, dictionaryApi } from '@/api/standard';

// Props
interface Props {
  visible: boolean;
  data: SignalStandardData | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [];
}>();

// 表单引用
const formRef = ref<FormInstance>();
const loading = ref(false);

// 下拉选项数据
const deviceTypeOptions = ref<DictionaryItem[]>([]);
const semaphoreTypeOptions = ref<DictionaryItem[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const isEdit = computed(() => !!props.data?.id);

// 表单数据
const formData = reactive<SignalStandardData>({
  signalStandardId: '',
  deviceType: undefined,
  deviceTypeName: '',
  deviceSubType: '',
  standardSignalName: '',
  semaphoreType: undefined,
  semaphoreTypeName: '',
  unit: '',
  meaning: '',
  description: ''
});

// 自定义校验函数
const validateSignalId = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入信号编码ID'));
  } else if (!/^\d{6}$/.test(value)) {
    callback(new Error('信号编码ID必须为6位数字'));
  } else {
    callback();
  }
};

const validateMeaning = (rule: any, value: string, callback: any) => {
  if (value && value.trim()) {
    // 检查格式：数值1=含义1;数值2=含义2;...
    const pattern = /^(\d+=[^;=]+)(;\d+=[^;=]+)*;?$/;
    if (!pattern.test(value.trim())) {
      callback(new Error('含义格式错误，正确格式：信号数值1=含义1;信号数值2=含义2;...'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

// 表单验证规则
const rules: FormRules = {
  signalStandardId: [
    {required: true, message: '请输入信号编码ID', trigger: 'blur'},
    { validator: validateSignalId, trigger: 'blur' }
  ],
  deviceTypeId: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  standardSignalName: [
    { required: true, message: '请输入标准信号名称', trigger: 'blur' }
  ],
  semaphoreTypeId: [
    { required: true, message: '请选择信号类型', trigger: 'change' }
  ],
  meaning: [
    { validator: validateMeaning, trigger: 'blur' }
  ]
};

// 处理设备类型选择变化
const handleDeviceTypeChange = (value: number) => {
  const selectedItem = deviceTypeOptions.value.find(item => item.itemId === value);
  if (selectedItem) {
    formData.deviceTypeName = selectedItem.itemValue;
  }
};

// 处理信号类型选择变化
const handleSemaphoreTypeChange = (value: number) => {
  const selectedItem = semaphoreTypeOptions.value.find(item => item.itemId === value);
  if (selectedItem) {
    formData.semaphoreTypeName = selectedItem.itemValue;
  }
};

// 加载设备类型选项
const loadDeviceTypes = async () => {
  try {
    const response = await dictionaryApi.getDeviceType();
    if (response.state) {
      deviceTypeOptions.value = response.data || [];
    }
  } catch (error) {
    console.error('加载设备类型失败:', error);
  }
};

// 加载信号类型选项
const loadSemaphoreTypes = async () => {
  try {
    const response = await dictionaryApi.getSemaphoreType();
    if (response.state) {
      semaphoreTypeOptions.value = response.data || [];
    }
  } catch (error) {
    console.error('加载信号类型失败:', error);
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    signalStandardId: '',
    deviceTypeId: undefined,
    deviceTypeName: '',
    deviceSubType: '',
    standardSignalName: '',
    semaphoreTypeId: undefined,
    semaphoreTypeName: '',
    unit: '',
    meaning: '',
    description: ''
  });
  formRef.value?.clearValidate();
};

// 初始化表单数据
const initFormData = () => {
  if (props.data) {
    Object.assign(formData, props.data);
  } else {
    resetForm();
  }
};

// 监听数据变化
watch(() => props.data, initFormData, { immediate: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 确认操作
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    let response;
    if (isEdit.value) {
      response = await updateSignalStandard(formData);
    } else {
      response = await addSignalStandard(formData);
    }
    
    if (response.state === true) {
      ElMessage.success(isEdit.value ? '修改成功' : '添加成功');
      emit('confirm');
      handleClose();
    } else {
      ElMessage.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载选项数据
onMounted(() => {
  loadDeviceTypes();
  loadSemaphoreTypes();
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}
</style> 