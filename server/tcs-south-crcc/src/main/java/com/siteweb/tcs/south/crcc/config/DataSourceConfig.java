package com.siteweb.tcs.south.crcc.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置
 */
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.south.crcc.dal.mapper"}, sqlSessionFactoryRef = "crccSqlSessionFactory")
public class DataSourceConfig {

    @Bean(name = "crccDataSourceProperties")
    @ConfigurationProperties(prefix = "plugin.datasource.crcc")
    public DataSourceProperties crccDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "crccDataSource")
    public DataSource crccDataSource(@Qualifier("crccDataSourceProperties") DataSourceProperties dataSourceProperties) {
        return dataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "crccTransactionManager")
    public DataSourceTransactionManager crccTransactionManager(@Qualifier("crccDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "crccSqlSessionFactory")
    public SqlSessionFactory crccSqlSessionFactory(@Qualifier("crccDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/south-crcc-plugin/*.xml"));
        return bean.getObject();
    }
} 