package com.siteweb.tcs.south.crcc.config;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

/**
 * Flyway数据库迁移配置
 */
@Configuration
public class FlywayConfig {

    @Bean(name = "crccFlyway")
    @DependsOn("crccDataSource")
    public Flyway flyway(@Qualifier("crccDataSource") DataSource dataSource) {
        Flyway flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/south-crcc-plugin/migration")
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .table("crcc_schema_history")
            .load();

        flyway.migrate();

        return flyway;
    }
} 