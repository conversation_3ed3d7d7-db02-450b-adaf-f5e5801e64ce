package com.siteweb.tcs.south.crcc.exception;

/**
 * CRCC插件异常
 * <p>
 * 表示在CRCC插件中发生的特定异常
 * </p>
 */
public class CrccPluginException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误代码
     */
    private final int code;
    
    /**
     * 创建一个带有指定错误消息的异常
     *
     * @param message 错误消息
     */
    public CrccPluginException(String message) {
        this(message, 500);
    }
    
    /**
     * 创建一个带有指定错误消息和错误代码的异常
     *
     * @param message 错误消息
     * @param code 错误代码
     */
    public CrccPluginException(String message, int code) {
        super(message);
        this.code = code;
    }
    
    /**
     * 创建一个带有指定错误消息和原因的异常
     *
     * @param message 错误消息
     * @param cause 原因
     */
    public CrccPluginException(String message, Throwable cause) {
        this(message, 500, cause);
    }
    
    /**
     * 创建一个带有指定错误消息、错误代码和原因的异常
     *
     * @param message 错误消息
     * @param code 错误代码
     * @param cause 原因
     */
    public CrccPluginException(String message, int code, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
    
    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public int getCode() {
        return code;
    }
} 