package com.siteweb.tcs.south.crcc.util;

import lombok.extern.slf4j.Slf4j;

/**
 * CRCC 工具类
 * <p>
 * 提供通用工具方法
 * </p>
 */
@Slf4j
public class CrccUtil {
    
    private CrccUtil() {
        // 私有构造函数，防止实例化
    }
    
    /**
     * 验证设备ID格式
     *
     * @param deviceId 设备ID
     * @return 如果格式有效返回true，否则返回false
     */
    public static boolean isValidDeviceId(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return false;
        }
        
        // 示例验证逻辑 - 可以根据实际需求进行定制
        return deviceId.matches("^[a-zA-Z0-9_-]+$");
    }
    
    /**
     * 格式化CRCC协议消息
     *
     * @param message 原始消息
     * @return 格式化后的消息
     */
    public static String formatMessage(String message) {
        if (message == null) {
            return "";
        }
        
        // 示例格式化逻辑
        return message.trim();
    }
} 