import { getPluginsList } from "./build/plugins";
import { include, exclude } from "./build/optimize";
import { type UserConfigExport, type ConfigEnv, loadEnv } from "vite";
import { root, alias, wrapperEnv, __APP_INFO__ } from "./build/utils";

import path from "node:path";

export default ({ mode }: ConfigEnv): UserConfigExport => {
  const { VITE_CDN, VITE_PORT, VITE_COMPRESSION, VITE_PUBLIC_PATH } =
    wrapperEnv(loadEnv(mode, root));
  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias
    },
    // 服务端渲染
    server: {
      // 端口号
      port: VITE_PORT,
      host: "0.0.0.0",
      // 本地跨域代理 https://cn.vitejs.dev/config/server-options.html#server-proxy
      proxy: {
        "/api": {
          // 这里填写后端地址
          target: "http://*************:8080",
          changeOrigin: true
        }
      },
      // 预热文件以提前转换和缓存结果，降低启动期间的初始页面加载时长并防止转换瀑布
      warmup: {
        clientFiles: ["./index.html", "./src/{views,components}/*"]
      }
    },
    plugins: getPluginsList(VITE_CDN, VITE_COMPRESSION),
    // https://cn.vitejs.dev/config/dep-optimization-options.html#dep-optimization-options
    optimizeDeps: {
      include,
      exclude
    },
    build: {
      lib: {
        entry: path.resolve(__dirname, "src/index.ts"),
        fileName: () => "index.js",
        formats: ["umd"],
        name: "CustomUi"
      },
      minify: true,
      outDir: "./dist",
      rollupOptions: {
        external: ["vue"],
        output: {
          assetFileNames: info => {
            return info.name.endsWith(".css") ? "index.css" : "[name].[ext]";
          },
          chunkFileNames: "[name].js",
          entryFileNames: "index.js",
          globals: {
            vue: "Vue"
          }
        },
        watch: {
          buildDelay: 500, // 防抖时间，单位为毫秒
          clearScreen: false, // 构建时不清除屏幕
          exclude: ["node_modules/**"], // 排除 node_modules 目录
          include: ["src/**"] // 监听 src 目录下的所有文件
        }
      },
      target: "es2015"
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
      "process.env": JSON.stringify({})
    }
  };
};
