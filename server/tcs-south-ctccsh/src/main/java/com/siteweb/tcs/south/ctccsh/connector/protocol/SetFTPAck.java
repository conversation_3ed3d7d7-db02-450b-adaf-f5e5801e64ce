package com.siteweb.tcs.south.ctccsh.connector.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Getter;
import lombok.Setter;

/**
 * SC设置FSU FTP参数报文 命令代码:803
 */
@Setter
@Getter
@JacksonXmlRootElement(localName = "Response")
public class SetFTPAck extends TeleBMessage {

    @JsonProperty("Info")
    private Info info;

    public SetFTPAck() {
        setPkType(new PK_Type("SET_FTP_ACK", 704));
        this.info = new Info();
    }

    @Override
    public String GetFsuId() {
        return info.getSuId();
    }

    @Setter
    @Getter
    public static class Info {
        @JsonProperty("SUID")
        public String suId;

        @JsonProperty("Result")
        public String result;

        @JsonProperty("FailureCode")
        public String failureCode;

        @JsonProperty("FailureCause")
        public String failureCause;
    }
}
