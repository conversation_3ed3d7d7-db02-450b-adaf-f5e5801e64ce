package com.siteweb.tcs.south.cucc.connector;

import org.apache.pekko.actor.ActorRef;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 连接器数据存储类
 * <p>
 * 用于存储插件运行时的连接器数据和共享对象
 * </p>
 */
@Data
@Component
public class ConnectorDataHolder {
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 根Actor引用
     */
    private ActorRef rootActor;
    
    // 添加其他需要在组件间共享的数据
} 