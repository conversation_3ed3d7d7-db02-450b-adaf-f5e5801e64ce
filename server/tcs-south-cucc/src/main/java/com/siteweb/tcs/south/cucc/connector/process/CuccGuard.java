package com.siteweb.tcs.south.cucc.connector.process;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

/**
 * 联通接入插件守护者Actor
 * <p>
 * 作为插件的根Actor，负责处理和分发插件级别的消息
 * </p>
 */
@Slf4j
public class CuccGuard extends AbstractActor {

/* <<<<<<<<<<<<<<  ✨ Windsurf Command ⭐ >>>>>>>>>>>>>>>> */
    /**
     * Returns the props for creating a {@link CuccGuard} Actor.
     *
     * @return a Props for creating a CuccGuard Actor
     */
/* <<<<<<<<<<  6f28f3bc-e40b-4295-a5ce-6fade75d11af  >>>>>>>>>>> */
    public static Props props() {
        return Props.create(CuccGuard.class);
    }
    
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .matchAny(message -> {
                    log.debug("CuccGuard received message: {}", message);
                    // 处理各种消息
                })
                .build();
    }
} 