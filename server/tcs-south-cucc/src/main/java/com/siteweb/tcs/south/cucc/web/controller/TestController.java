package com.siteweb.tcs.south.cucc.web.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试Controller
 * 用于前后端联调时测试插件是否正常加载
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    /**
     * 测试接口，用于验证插件是否正常加载
     * @return "Hello World"
     */
    @GetMapping("/helloworld")
    public ResponseEntity<String> helloWorld() {
        log.info("Test endpoint accessed");
        return ResponseEntity.ok("Hello World");
    }
} 