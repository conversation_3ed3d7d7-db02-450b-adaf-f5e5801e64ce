package com.siteweb.tcs.south.cucc.web.service;

import com.siteweb.tcs.south.cucc.dal.entity.CuccDevice;
import com.siteweb.tcs.south.cucc.web.vo.CuccDeviceVO;

import java.util.List;

/**
 * 联通设备服务接口
 */
public interface CuccDeviceService {

    /**
     * 获取所有设备
     */
    List<CuccDeviceVO> listAllDevices();

    /**
     * 获取在线设备
     */
    List<CuccDeviceVO> listOnlineDevices();

    /**
     * 根据设备类型获取设备
     */
    List<CuccDeviceVO> listDevicesByType(Integer deviceType);

    /**
     * 根据ID获取设备
     */
    CuccDeviceVO getDeviceById(Long deviceId);

    /**
     * 创建设备
     */
    CuccDeviceVO createDevice(CuccDevice device);

    /**
     * 更新设备
     */
    CuccDeviceVO updateDevice(CuccDevice device);

    /**
     * 删除设备
     */
    boolean deleteDevice(Long deviceId);
} 