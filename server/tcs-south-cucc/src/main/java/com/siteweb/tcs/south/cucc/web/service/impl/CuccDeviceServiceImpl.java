package com.siteweb.tcs.south.cucc.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.south.cucc.dal.entity.CuccDevice;
import com.siteweb.tcs.south.cucc.dal.mapper.CuccDeviceMapper;
import com.siteweb.tcs.south.cucc.web.service.CuccDeviceService;
import com.siteweb.tcs.south.cucc.web.vo.CuccDeviceVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 联通设备服务实现类
 */
@Service
public class CuccDeviceServiceImpl implements CuccDeviceService {

    @Autowired
    private CuccDeviceMapper deviceMapper;

    @Override
    public List<CuccDeviceVO> listAllDevices() {
        List<CuccDevice> devices = deviceMapper.selectList(
                new LambdaQueryWrapper<CuccDevice>().eq(CuccDevice::getDeleted, 0)
        );
        return convertToVOList(devices);
    }

    @Override
    public List<CuccDeviceVO> listOnlineDevices() {
        return convertToVOList(deviceMapper.selectOnlineDevices());
    }

    @Override
    public List<CuccDeviceVO> listDevicesByType(Integer deviceType) {
        return convertToVOList(deviceMapper.selectByDeviceType(deviceType));
    }

    @Override
    public CuccDeviceVO getDeviceById(Long deviceId) {
        CuccDevice device = deviceMapper.selectById(deviceId);
        return device != null ? convertToVO(device) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CuccDeviceVO createDevice(CuccDevice device) {
        deviceMapper.insert(device);
        return convertToVO(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CuccDeviceVO updateDevice(CuccDevice device) {
        CuccDevice existingDevice = deviceMapper.selectById(device.getDeviceId());
        if (existingDevice == null) {
            return null;
        }
        deviceMapper.updateById(device);
        return convertToVO(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDevice(Long deviceId) {
        CuccDevice device = deviceMapper.selectById(deviceId);
        if (device == null) {
            return false;
        }
        deviceMapper.deleteById(deviceId);
        return true;
    }

    /**
     * 实体转VO
     */
    private CuccDeviceVO convertToVO(CuccDevice device) {
        if (device == null) {
            return null;
        }
        CuccDeviceVO vo = new CuccDeviceVO();
        BeanUtils.copyProperties(device, vo);
        return vo;
    }

    /**
     * 实体列表转VO列表
     */
    private List<CuccDeviceVO> convertToVOList(List<CuccDevice> devices) {
        return devices.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
} 