const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Plugin",
  component: Layout,
  redirect: "/plugin",
  meta: {
    icon: "ep/home-filled",
    title: "插件",
    rank: 0
  },
  children: [
    {
      path: "/plugin",
      name: "Plugin-Home",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        title: "插件模板"
      }
    }
  ]
} satisfies RouteConfigsTable;
