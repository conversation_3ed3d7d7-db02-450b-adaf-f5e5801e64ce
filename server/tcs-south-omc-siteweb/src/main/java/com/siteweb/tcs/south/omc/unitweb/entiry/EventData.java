package com.siteweb.tcs.south.omc.unitweb.entiry;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-19 13:29
 **/
@Data
@AllArgsConstructor
public class EventData {
    public Integer EventId;
    public String Name;
    public double FloatValue;
    public String ValueType;
    public String Value;
    public int CondId;
    public String Meaning;
    public int Level;
    public String StartOper;
    public String StartValue;
    public int StartDelay;
    public String EndOper;
    public String EndValue;
    public int EndDelay;
    public String BaseTypeId;
    public int State;
    public int TrigValue;
    public String StartTime;
    public String EndTime;
    public int Valid;

}
