package com.siteweb.tcs.south.omc.unitweb.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.south.omc.unitweb.EquipmentRuntimeCache;
import com.siteweb.tcs.south.omc.unitweb.UnitWebCmdEnum;
import com.siteweb.tcs.south.omc.unitweb.entiry.ControlData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-19 12:56
 **/
@Component
public class GetControlListHandler implements UnitWebHandler{

    private static final UnitWebCmdEnum type = UnitWebCmdEnum.cmd_get_ctrllist;

    @Autowired
    private UnitWebHandlerObserver unitWebHandlerObserver;

    @PostConstruct
    private void registerHandler(){
        unitWebHandlerObserver.registerHandler(type,this);
    }

    @Override
    public void handle(Integer equipmentId,String msg) {
        String[] lines = msg.split("\\r?\\n");
        Map<Integer,ControlData> resultMap = new HashMap<>();

        for (String line : lines) {
            String[] parts = line.split("`");
            Map<String, String> kvMap = Arrays.stream(parts)
                    .map(p -> p.split("=", 2))
                    .filter(kv -> kv.length == 2)
                    .collect(Collectors.toMap(kv -> kv[0], kv -> StringUtils.isBlank(kv[1])?"":kv[1]));

            try {
                ControlData data = new ControlData(
                        Integer.parseInt(kvMap.getOrDefault("CtrlId", "0")),
                        kvMap.getOrDefault("Name", ""),
                        kvMap.getOrDefault("BaseTypeId", ""),
                        Long.parseLong(kvMap.getOrDefault("SigId", "0")),
                        Double.parseDouble(kvMap.getOrDefault("FloatValue", "0")),
                        kvMap.getOrDefault("StrValue", ""),
                        Double.parseDouble(kvMap.getOrDefault("MaxValue", "0")),
                        Double.parseDouble(kvMap.getOrDefault("MinValue", "0")),
                        Integer.parseInt(kvMap.getOrDefault("Enable", "0")),
                        Integer.parseInt(kvMap.getOrDefault("Visible", "0")),
                        Integer.parseInt(kvMap.getOrDefault("CmdType", "0")),
                        Integer.parseInt(kvMap.getOrDefault("CtrlType", "0")),
                        Integer.parseInt(kvMap.getOrDefault("DataType", "0")),
                        kvMap.getOrDefault("Meanings", "")
                );
                resultMap.put(data.getCtrlId(),data);
            } catch (Exception e) {
                System.err.println("解析失败行：" + line);
            }
        }
        if(CollectionUtil.isNotEmpty(resultMap)){
            EquipmentRuntimeCache.addEquipmentControl(equipmentId,resultMap);
        }
    }
}
