package com.siteweb.tcs.south.omc.unitweb.handler;

import com.siteweb.tcs.south.omc.unitweb.UnitWebCmdEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-19 12:56
 **/
@Component
public class SendControlHandler implements UnitWebHandler{

    private static final UnitWebCmdEnum type = UnitWebCmdEnum.cmd_send_control;

    @Autowired
    private UnitWebHandlerObserver unitWebHandlerObserver;

    @PostConstruct
    private void registerHandler(){
        unitWebHandlerObserver.registerHandler(type,this);
    }

    @Override
    public void handle(Integer equipmentId,String msg) {

    }
}
