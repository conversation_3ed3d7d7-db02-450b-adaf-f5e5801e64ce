package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 控制管理Controller
 * 对应前端device-management模块的控制相关接口
 * 迁移自tcs-config的CommandController，排除包含base关键字的接口
 */
@Slf4j
@RestController
@RequestMapping("/control")
public class ControlController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 创建控制
     * 对应前端: api/config/control/create
     */
    @PostMapping("/create")
    public ResponseEntity<ResponseResult> createControl(@RequestBody ControlConfigItem control) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .createForControl(control);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to create control", e);
            return ResponseHelper.failed("创建控制失败: " + e.getMessage());
        }
    }

    /**
     * 更新控制
     * 对应前端: api/config/control/update
     */
    @PutMapping("/update")
    public ResponseEntity<ResponseResult> updateControl(@RequestBody ControlConfigItem control) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .updateForControl(control);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to update control", e);
            return ResponseHelper.failed("更新控制失败: " + e.getMessage());
        }
    }

    /**
     * 删除控制
     * 对应前端: api/config/control/delete
     */
    @DeleteMapping("/delete")
    public ResponseEntity<ResponseResult> deleteControl(@RequestParam(name = "equipmentTemplateId") int equipmentTemplateId, @RequestParam(name = "controlId") int controlId) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .deleteForControl(equipmentTemplateId, controlId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to delete control", e);
            return ResponseHelper.failed("删除控制失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除控制
     * 对应前端: api/config/control/batchdelete
     */
    @DeleteMapping("/batchdelete")
    public ResponseEntity<ResponseResult> batchDeleteControl(@RequestParam(name = "equipmentTemplateId") int equipmentTemplateId, @RequestParam(name = "controlIds") List<Integer> controlIds) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .batchDeleteForControl(equipmentTemplateId, controlIds);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to batch delete control", e);
            return ResponseHelper.failed("批量删除控制失败: " + e.getMessage());
        }
    }

    /**
     * 根据设备模板ID查询控制列表
     * 对应前端: api/config/control/list
     */
    @GetMapping("/list")
    public ResponseEntity<ResponseResult> getControlList(@RequestParam Integer equipmentTemplateId) {
        try {
            List<ControlConfigItem> result = sitewebPersistentService.getConfigAPI()
                    .findControlListForControl(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get control list", e);
            return ResponseHelper.failed("获取控制列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询控制点
     * 对应前端: api/config/control/point
     */
    @GetMapping("/point")
    public ResponseEntity<ResponseResult> getControlPoints(@RequestParam Integer equipmentTemplateId) {
        try {
            List<ControlConfigPointDTO> result = sitewebPersistentService.getConfigAPI()
                    .findControlPointsForControl(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get control points", e);
            return ResponseHelper.failed("获取控制点失败: " + e.getMessage());
        }
    }

    /**
     * 获取控制信息
     * 对应前端: api/config/control/controlinfo/{equipmentTemplateId}/{controlId}
     */
    @GetMapping("/controlinfo/{equipmentTemplateId}/{controlId}")
    public ResponseEntity<ResponseResult> getControlInfo(
            @PathVariable Integer equipmentTemplateId,
            @PathVariable Integer controlId) {
        try {
            ControlConfigItem result = sitewebPersistentService.getConfigAPI()
                    .getControlInfoForControl(equipmentTemplateId, controlId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get control info", e);
            return ResponseHelper.failed("获取控制信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备控制信息
     * 对应前端: api/config/control/info/for-equipment
     */
    @GetMapping("/info/for-equipment")
    public ResponseEntity<ResponseResult> getControlCategory(
            @RequestParam(required = false) Integer stationId,
            @RequestParam Integer equipmentId,
            @RequestParam Integer controlId) {
        try {
            ControlConfigItem result = sitewebPersistentService.getConfigAPI()
                    .getControlCategoryForControl(stationId, equipmentId, controlId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get control category", e);
            return ResponseHelper.failed("获取设备控制信息失败: " + e.getMessage());
        }
    }

    /**
     * 字段复制
     * 对应前端: api/config/control/field/copy
     */
    @PostMapping("/field/copy")
    public ResponseEntity<ResponseResult> fieldCopy(@RequestBody List<CommandFieldCopyDTO> commandFieldCopyList) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .fieldCopyForControl(commandFieldCopyList);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to copy fields", e);
            return ResponseHelper.failed("字段复制失败: " + e.getMessage());
        }
    }

    /**
     * 处理相似控制
     * 对应前端: api/config/control/similar
     */
    @PostMapping("/similar")
    public ResponseEntity<ResponseResult> disposeSimilarControl(@RequestBody SimilarDataDTO similarDataDTO) {
        try {
            sitewebPersistentService.getConfigAPI()
                    .disposeSimilarControlForControl(similarDataDTO);
            return ResponseHelper.successful();
        } catch (Exception e) {
            log.error("Failed to dispose similar control", e);
            return ResponseHelper.failed("处理相似控制失败: " + e.getMessage());
        }
    }
}
