package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.dto.SwitchTemplateDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.EquipmentBaseType;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.MonitorUnit;
import com.siteweb.tcs.siteweb.vo.EquipmentTreeVO;
import com.siteweb.tcs.siteweb.vo.EquipmentReferenceVO;
import com.siteweb.tcs.siteweb.dto.EquipTemplateChangeDTO;
import com.siteweb.tcs.siteweb.dto.SwitchTemplateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备控制器
 * 负责设备的增删改查和相关业务操作
 */
@Slf4j
@RestController
@RequestMapping("/equipment")
public class EquipmentController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 根据ID获取设备配置
     *
     * @param id 设备ID
     * @return 设备配置信息
     */
    @GetMapping("/config/{id}")
    public ResponseEntity<ResponseResult> getConfig(@PathVariable("id") Integer id) {
        try {
            Equipment equipment = sitewebPersistentService.getConfigAPI().findByIdForEquipment(id);
            if (equipment != null) {
                return ResponseHelper.successful(equipment);
            } else {
                return ResponseHelper.failed("设备不存在");
            }
        } catch (Exception e) {
            log.error("获取设备配置失败, ID: {}", id, e);
            return ResponseHelper.failed("获取设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备信息
     *
     * @param equipmentId 设备ID
     */
    @GetMapping("/{equipmentId}")
    public ResponseEntity<ResponseResult> findEquipmentDetail(@PathVariable Integer equipmentId) {
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().findEquipmentDetailForEquipment(equipmentId));
    }

    /**
     * 创建设备配置（按照原配置工具的 createEquipmentV3 逻辑，去掉v3字样）
     *
     * @param createEquipmentDto 创建设备DTO
     * @return 创建结果
     */
    @PostMapping("/config")
    public ResponseEntity<ResponseResult> createConfig(@RequestBody CreateEquipmentDto createEquipmentDto) {
        try {
            // 按照原配置工具的逻辑进行重复名称检查
            boolean repeatName = (Boolean) sitewebPersistentService.getConfigAPI().checkEquipmentNameExistsForEquipment(
                null, createEquipmentDto.getMonitorUnitId(), createEquipmentDto.getEquipmentName());
            if (repeatName) {
                return ResponseHelper.failed("设备名称重复");
            }

            // 验证必要字段（去掉局站概念，设置默认值）
            if (createEquipmentDto.getMonitorUnitId() == null) {
                return ResponseHelper.failed("监控单元ID不能为空");
            }

            // 设置默认值（去掉局站和局房概念）
            createEquipmentDto.setStationId(0);
            createEquipmentDto.setHouseId(0);

            Equipment equipment = sitewebPersistentService.getConfigAPI().createForEquipment(createEquipmentDto);

            if (equipment != null) {
                return ResponseHelper.successful(equipment);
            } else {
                return ResponseHelper.failed("设备创建失败");
            }
        } catch (Exception e) {
            log.error("创建设备配置失败", e);
            return ResponseHelper.failed("创建设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新设备配置
     *
     * @param equipmentDetailDTO 设备详情DTO
     * @return 更新结果
     */
    @PutMapping("/config")
    public ResponseEntity<ResponseResult> updateConfig(@RequestBody EquipmentDetailDTO equipmentDetailDTO) {
        try {
            // 确保stationId为0（去掉局站概念）
            if (equipmentDetailDTO.getStationId() == null) {
                equipmentDetailDTO.setStationId(0);
            }
            Equipment equipment = (Equipment) sitewebPersistentService.getConfigAPI().updateForEquipment(equipmentDetailDTO);
            if (equipment != null) {
                return ResponseHelper.successful(equipment);
            } else {
                return ResponseHelper.failed("设备更新失败");
            }
        } catch (Exception e) {
            log.error("更新设备配置失败", e);
            return ResponseHelper.failed("更新设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除设备配置
     *
     * @param id 设备ID
     * @return 删除结果
     */
    @DeleteMapping("/config/{id}")
    public ResponseEntity<ResponseResult> deleteConfig(@PathVariable("id") Integer id) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().deleteForEquipment(id);
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("设备删除失败");
            }
        } catch (Exception e) {
            log.error("删除设备配置失败, ID: {}", id, e);
            return ResponseHelper.failed("删除设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 切换设备模板
     *
     * @param switchTemplateDTO 切换模板DTO
     * @return 切换结果
     */
    @PostMapping("/switchtemplate")
    public ResponseEntity<ResponseResult> switchTemplate(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().switchTemplateForEquipment(switchTemplateDTO);
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("设备模板切换失败");
            }
        } catch (Exception e) {
            log.error("切换设备模板失败", e);
            return ResponseHelper.failed("切换设备模板失败: " + e.getMessage());
        }
    }

    /**
     * 设备实例化
     *
     * @param equipmentId 设备ID
     * @return 实例化结果
     */
    @PostMapping("/instance/{equipmentId}")
    public ResponseEntity<ResponseResult> equipmentInstance(@PathVariable("equipmentId") Integer equipmentId) {
        try {
            Integer templateId = sitewebPersistentService.getConfigAPI().equipmentInstanceForEquipment(equipmentId);
            if (templateId != null) {
                return ResponseHelper.successful(templateId);
            } else {
                return ResponseHelper.failed("设备实例化失败");
            }
        } catch (Exception e) {
            log.error("设备实例化失败, equipmentId: {}", equipmentId, e);
            return ResponseHelper.failed("设备实例化失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备模板详情
     * 注意：这个方法调用的是设备模板相关的API，不是设备API
     *
     * @param templateId 模板ID
     * @return 设备模板详情
     */
    @GetMapping("/template/{templateId}")
    public ResponseEntity<ResponseResult> getEquipmentTemplate(@PathVariable("templateId") Integer templateId) {
        try {
            // 这里应该调用设备模板相关的API
            EquipmentTemplate template = sitewebPersistentService.getConfigAPI().findByIdForEquipmentTemplate(templateId);
            if (template != null) {
                return ResponseHelper.successful(template);
            } else {
                return ResponseHelper.failed("设备模板不存在");
            }
        } catch (Exception e) {
            log.error("获取设备模板详情失败, templateId: {}", templateId, e);
            return ResponseHelper.failed("获取设备模板详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索设备列表
     *
     * @param monitorUnitId 监控单元ID (可选)
     * @param equipmentName 设备名称 (可选，支持模糊查询)
     * @return 设备列表
     */
    @GetMapping("/search")
    public ResponseEntity<ResponseResult> searchEquipment(
            @RequestParam(value = "monitorUnitId", required = false) Integer monitorUnitId,
            @RequestParam(value = "equipmentName", required = false) String equipmentName) {
        try {
            List<Equipment> equipmentList = sitewebPersistentService.getConfigAPI()
                    .searchEquipmentList(monitorUnitId, equipmentName);
            return ResponseHelper.successful(equipmentList);
        } catch (Exception e) {
            log.error("搜索设备列表失败, monitorUnitId: {}, equipmentName: {}", monitorUnitId, equipmentName, e);
            return ResponseHelper.failed("搜索设备列表失败: " + e.getMessage());
        }
    }

    // ==================== 设备管理相关接口 ====================

    /**
     * 获取设备树结构
     * 对应前端: api/config/equipment/tree
     */
    @GetMapping("/tree")
    public ResponseEntity<ResponseResult> getEquipmentTree() {
        try {
            List<EquipmentTreeVO> result = sitewebPersistentService.getConfigAPI()
                    .getEquipmentTreeForDeviceManagement();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get equipment tree", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取设备信息
     * 对应前端: api/config/equipment/info
     */
    @GetMapping("/info")
    public ResponseEntity<ResponseResult> getEquipmentInfo(@RequestParam Integer equipmentId) {
        try {
            Equipment result = sitewebPersistentService.getConfigAPI()
                    .getEquipmentInfoForDeviceManagement(equipmentId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get equipment info", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 修改设备信息
     * 对应前端: api/config/equipment/update
     */
    @PutMapping("/update")
    public ResponseEntity<ResponseResult> updateEquipmentConfig(@RequestBody Equipment equipment) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .updateEquipmentConfigForDeviceManagement(equipment);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to update equipment config", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取设备模板配置
     * 对应前端: api/config/equipment/templateconfig
     */
    @GetMapping("/templateconfig")
    public ResponseEntity<ResponseResult> getEquipmentTemplateConfig(@RequestParam Integer id) {
        try {
            EquipmentTemplate result = sitewebPersistentService.getConfigAPI()
                    .getEquipmentTemplateConfigForDeviceManagement(id);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get equipment template config", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 添加新模板实例
     * 对应前端: api/config/equipment/addinstance
     */
    @PostMapping("/addinstance")
    public ResponseEntity<ResponseResult> addEquipmentInstance(@RequestParam Integer equipmentId) {
        try {
            Integer result = sitewebPersistentService.getConfigAPI()
                    .addEquipmentInstanceForDeviceManagement(equipmentId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to add equipment instance", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取上一级设备列表(拿此设备同局站的设备)
     * 对应前端: api/config/equipment/simplifyequipments
     */
    @GetMapping("/simplifyequipments")
    public ResponseEntity<ResponseResult> getSimplifyEquipments(@RequestParam("equipmentId") Integer equipmentId) {
        try {
            Equipment result = sitewebPersistentService.getConfigAPI()
                    .getSimplifyEquipmentForDeviceManagement(equipmentId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get simplify equipments for equipmentId: {}", equipmentId, e);
            return ResponseHelper.failed("获取设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取顶层设备模板信息
     * 对应前端: api/config/equipment/topparentequipmenttemplate
     */
    @GetMapping("/topparentequipmenttemplate")
    public ResponseEntity<ResponseResult> getTopParentEquipmentTemplate(@RequestParam Integer equipmentTemplateId) {
        try {
            EquipmentTemplate result = sitewebPersistentService.getConfigAPI()
                    .getTopParentEquipmentTemplateForDeviceManagement(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get top parent equipment template", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 修改子模板设备类型
     * 对应前端: api/config/equipment/updatechildrencategory
     */
    @PutMapping("/updatechildrencategory")
    public ResponseEntity<ResponseResult> updateChildrenCategory(
            @RequestParam Integer parentId,
            @RequestParam Integer category) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI()
                    .updateChildrenCategoryForDeviceManagement(parentId, category);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to update children category", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取设备基类列表
     * 对应前端: api/config/equipment/equipmentbasetypelist
     */
    @GetMapping("/equipmentbasetypelist")
    public ResponseEntity<ResponseResult> getEquipmentBaseTypeList() {
        try {
            List<EquipmentBaseType> result = sitewebPersistentService.getConfigAPI()
                    .getEquipmentBaseTypeListForDeviceManagement();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get equipment base type list", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取电池设备类型
     * 对应前端: api/config/equipment/equipmentcategorys
     */
    @GetMapping("/equipmentcategorys")
    public ResponseEntity<ResponseResult> getEquipmentCategorys() {
        try {
            List<DataItem> result = sitewebPersistentService.getConfigAPI()
                    .getEquipmentCategorysForDeviceManagement();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get equipment categorys", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取监控单元信息
     * 对应前端: api/config/equipment/monitorunitconfig
     */
    @GetMapping("/monitorunitconfig")
    public ResponseEntity<ResponseResult> getMonitorUnitConfig(@RequestParam Integer muId) {
        try {
            MonitorUnit result = sitewebPersistentService.getConfigAPI()
                    .getMonitorUnitConfigForDeviceManagement(muId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get monitor unit config", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据设备模板ID查询设备引用信息
     * 对应tcs-config: @GetMapping(value = "/reference", params = "equipmentTemplateId")
     */
    @GetMapping(value = "/reference", params = "equipmentTemplateId")
    public ResponseEntity<ResponseResult> findByEquipmentTemplateId(@RequestParam Integer equipmentTemplateId) {
        try {
            List<EquipmentReferenceVO> result = sitewebPersistentService.getConfigAPI()
                    .findReferenceByEquipmentTemplateIdForDeviceManagement(equipmentTemplateId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find equipment reference by template ID: {}", equipmentTemplateId, e);
            return ResponseHelper.failed("查询设备引用信息失败: " + e.getMessage());
        }
    }

    /**
     * 导出设备引用信息
     * 对应前端: api/config/equipment/reference/export
     */
    @GetMapping("/reference/export")
    public ResponseEntity<byte[]> exportAssociatedDevice(@RequestParam Integer equipmentTemplateId) {
        try {
            byte[] result = sitewebPersistentService.getConfigAPI()
                    .exportReferenceByEquipmentTemplateIdForDeviceManagement(equipmentTemplateId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "equipment_reference.xlsx");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(result);
        } catch (Exception e) {
            log.error("Failed to export equipment reference: {}", equipmentTemplateId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 检查模板切换影响
     * 对应前端: api/config/equipment/switchtemplate/checkchange
     */
    @PostMapping("/switchtemplate/checkchange")
    public ResponseEntity<ResponseResult> getTemplateChangeEffect(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
        try {
            List<EquipTemplateChangeDTO> result = sitewebPersistentService.getConfigAPI()
                    .checkTemplateChangeForDeviceManagement(switchTemplateDTO);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to check template change effect", e);
            return ResponseHelper.failed("检查模板切换影响失败: " + e.getMessage());
        }
    }

    /**
     * 导出模板切换影响
     * 对应前端: api/config/equipment/switchtemplate/checkchange/export
     */
    @PostMapping("/switchtemplate/checkchange/export")
    public ResponseEntity<byte[]> exportTemplateChangeEffect(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
        try {
            byte[] result = sitewebPersistentService.getConfigAPI()
                    .exportTemplateChangeForDeviceManagement(switchTemplateDTO);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "template_change_effect.xlsx");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(result);
        } catch (Exception e) {
            log.error("Failed to export template change effect", e);
            return ResponseEntity.internalServerError().build();
        }
    }

}
