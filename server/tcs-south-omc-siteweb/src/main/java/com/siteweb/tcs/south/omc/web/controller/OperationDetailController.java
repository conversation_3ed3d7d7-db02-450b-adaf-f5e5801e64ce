package com.siteweb.tcs.south.omc.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.IdValueDTO;
import com.siteweb.tcs.siteweb.dto.OperationDetailDTO;
import com.siteweb.tcs.siteweb.entity.OperationDetail;
import com.siteweb.tcs.siteweb.vo.OperationDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作日志Controller
 * 完全按照tcs-config的OperationDetailController实现
 */
@Slf4j
@RestController
@RequestMapping("/operationdetail")
public class OperationDetailController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 分页查询操作日志
     * 对应tcs-config: @PostMapping("/page")
     */
    @PostMapping("/page")
    public ResponseEntity<ResponseResult> findAll(Page<OperationDetail> page, @RequestBody OperationDetailDTO operationDetailDTO) {
        try {
            Page<OperationDetailVO> result = sitewebPersistentService.getConfigAPI()
                    .findPageForOperationDetail(page, operationDetailDTO);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find all operation details", e);
            return ResponseHelper.failed("查询操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 根据对象类型和对象ID查询操作日志
     * 对应tcs-config: @GetMapping(params = {"objectType","objectId"})
     */
    @GetMapping(params = {"objectType", "objectId"})
    public ResponseEntity<ResponseResult> findByObjectTypeAndObjectId(Page<OperationDetail> page, String objectType, String objectId) {
        try {
            Page<OperationDetail> result = sitewebPersistentService.getConfigAPI()
                    .findPageByObjectTypeAndObjectIdForOperationDetail(page, objectType, objectId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find operation details by object type and object id", e);
            return ResponseHelper.failed("查询操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询设备模板操作日志
     * 对应tcs-config: @PostMapping(value = "/page/equipmenttemplate")
     */
    @PostMapping(value = "/page/equipmenttemplate")
    public ResponseEntity<ResponseResult> findPageByEquipmentTemplateId(Page<OperationDetail> page, @RequestBody OperationDetailDTO operationDetailDTO) {
        try {
            Page<OperationDetailVO> result = sitewebPersistentService.getConfigAPI()
                    .findEquipmentTemplateLogPageForOperationDetail(page, operationDetailDTO);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find equipment template log page", e);
            return ResponseHelper.failed("查询设备模板日志失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询设备操作日志
     * 对应tcs-config: @PostMapping(value = "/page/equipment")
     */
    @PostMapping(value = "/page/equipment")
    public ResponseEntity<ResponseResult> findPageByEquipment(Page<OperationDetail> page, @RequestBody OperationDetailDTO operationDetailDTO) {
        try {
            Page<OperationDetailVO> result = sitewebPersistentService.getConfigAPI()
                    .findEquipmentLogPageForOperationDetail(page, operationDetailDTO);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to find equipment log page", e);
            return ResponseHelper.failed("查询设备日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取操作类型
     * 保留原有接口
     */
    @GetMapping("/operationtypes")
    public ResponseEntity<ResponseResult> getOperationTypes() {
        try {
            List<IdValueDTO<Integer, String>> result = sitewebPersistentService.getConfigAPI()
                    .getOperationTypesForDeviceManagement();
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get operation types", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
