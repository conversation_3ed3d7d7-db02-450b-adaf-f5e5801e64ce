package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.Port;
import com.siteweb.tcs.siteweb.vo.SamplerUnitTreeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 端口控制器
 * 负责端口的增删改查和相关业务操作
 */
@Slf4j
@RestController
@RequestMapping("/port")
public class PortController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 根据ID获取端口配置
     *
     * @param id 端口ID
     * @return 端口配置信息
     */
    @GetMapping("/config/{id}")
    public ResponseEntity<ResponseResult> getConfig(@PathVariable("id") Integer id) {
        try {
            Port port = sitewebPersistentService.getConfigAPI().findByPortIdForPort(id);
            if (port != null) {
                return ResponseHelper.successful(port);
            } else {
                return ResponseHelper.failed("端口不存在");
            }
        } catch (Exception e) {
            log.error("获取端口配置失败, ID: {}", id, e);
            return ResponseHelper.failed("获取端口配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建端口配置（按照原配置工具的逻辑）
     *
     * @param port 端口数据
     * @return 创建结果
     */
    @PostMapping("/config")
    public ResponseEntity<ResponseResult> createConfig(@RequestBody Port port) {
        try {
            // 按照原配置工具的逻辑进行验证和创建
            Port createdPort = sitewebPersistentService.getConfigAPI().createForPort(port);
            if (createdPort != null) {
                return ResponseHelper.successful(createdPort);
            } else {
                return ResponseHelper.failed("端口创建失败");
            }
        } catch (Exception e) {
            log.error("创建端口配置失败", e);
            return ResponseHelper.failed("创建端口配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新端口配置
     *
     * @param port 端口数据
     * @return 更新结果
     */
    @PutMapping("/config")
    public ResponseEntity<ResponseResult> updateConfig(@RequestBody Port port) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().updateForPort(port);
            if (success) {
                return ResponseHelper.successful(port);
            } else {
                return ResponseHelper.failed("端口更新失败");
            }
        } catch (Exception e) {
            log.error("更新端口配置失败", e);
            return ResponseHelper.failed("更新端口配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除端口配置
     *
     * @param id 端口ID
     * @return 删除结果
     */
    @DeleteMapping("/config/{id}")
    public ResponseEntity<ResponseResult> deleteConfig(@PathVariable("id") Integer id) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().deleteForPort(id);
            if (success) {
                return ResponseHelper.successful();
            } else {
                return ResponseHelper.failed("端口删除失败");
            }
        } catch (Exception e) {
            log.error("删除端口配置失败, ID: {}", id, e);
            return ResponseHelper.failed("删除端口配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据监控单元ID获取端口列表
     *
     * @param monitorUnitId 监控单元ID
     * @return 端口列表
     */
    @GetMapping("/monitor-unit/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getByMonitorUnitId(@PathVariable("monitorUnitId") Integer monitorUnitId) {
        try {
            List<Port> ports = sitewebPersistentService.getConfigAPI().findByMonitorUnitIdForPort(monitorUnitId);
            return ResponseHelper.successful(ports);
        } catch (Exception e) {
            log.error("根据监控单元ID获取端口失败, monitorUnitId: {}", monitorUnitId, e);
            return ResponseHelper.failed("获取端口列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取端口类型列表
     *
     * @param monitorUnitCategory 监控单元类别（可选）
     * @return 端口类型列表
     */
    @GetMapping("/types")
    public ResponseEntity<ResponseResult> getTypes(@RequestParam(value = "monitorUnitCategory", required = false) Integer monitorUnitCategory) {
        try {
            List<?> types = sitewebPersistentService.getConfigAPI().findTypesForPort(monitorUnitCategory);
            return ResponseHelper.successful(types);
        } catch (Exception e) {
            log.error("获取端口类型失败, monitorUnitCategory: {}", monitorUnitCategory, e);
            return ResponseHelper.failed("获取端口类型失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控单元下的最大端口号
     *
     * @param monitorUnitId 监控单元ID
     * @return 最大端口号
     */
    @GetMapping("/max-port-no/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getMaxPortNo(@PathVariable("monitorUnitId") Integer monitorUnitId) {
        try {
            Integer maxPortNo = sitewebPersistentService.getConfigAPI().getMaxPortNoForPort(monitorUnitId);
            return ResponseHelper.successful(maxPortNo);
        } catch (Exception e) {
            log.error("获取最大端口号失败, monitorUnitId: {}", monitorUnitId, e);
            return ResponseHelper.failed("获取最大端口号失败: " + e.getMessage());
        }
    }

    /**
     * 获取端口下的采集单元列表
     *
     * @param portId 端口ID
     * @return 采集单元列表
     */
    @GetMapping("/sampler-units/{portId}")
    public ResponseEntity<ResponseResult> getSamplerUnits(@PathVariable("portId") Integer portId) {
        try {
            List<SamplerUnitTreeVO> samplerUnits = sitewebPersistentService.getConfigAPI().getSamplerUnitsForPort(portId);
            return ResponseHelper.successful(samplerUnits);
        } catch (Exception e) {
            log.error("获取端口采集单元失败, portId: {}", portId, e);
            return ResponseHelper.failed("获取采集单元列表失败: " + e.getMessage());
        }
    }

    /**
     * 验证端口配置
     * 这是一个辅助方法，用于在创建或更新端口前进行验证
     *
     * @param port 端口数据
     * @return 验证结果
     */
    @PostMapping("/verify")
    public ResponseEntity<ResponseResult> verifyConfig(@RequestBody Port port) {
        try {
            // 这里可以调用端口验证逻辑
            // 暂时返回成功，具体验证逻辑需要根据业务需求实现
            return ResponseHelper.successful("端口配置验证通过");
        } catch (Exception e) {
            log.error("端口配置验证失败", e);
            return ResponseHelper.failed("端口配置验证失败: " + e.getMessage());
        }
    }
}
