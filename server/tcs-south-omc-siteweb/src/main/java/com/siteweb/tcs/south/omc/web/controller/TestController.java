package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.omc.unitweb.EquipmentRuntimeCache;
import com.siteweb.tcs.south.omc.unitweb.UnitWebClientUtil;
import com.siteweb.tcs.south.omc.unitweb.entiry.EquipmentData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/test")
public class TestController {
    @GetMapping(value = "/helloworld")
    public ResponseEntity<String> helloWorld() {
        log.info("测试接口被访问");
        return ResponseEntity.ok("OMC智慧运维插件测试接口运行正常");
    }

    @GetMapping(value = "/unitweb")
    public ResponseEntity<ResponseResult> testUnitWeb(){
        String ip = "*************";
        Integer port = 6790;
        Integer equipmentId = 246;
        UnitWebClientUtil.getSignalList(ip,port,equipmentId);
        UnitWebClientUtil.getEventList(ip,port,equipmentId);
        UnitWebClientUtil.getControlList(ip,port,equipmentId);
        EquipmentData equipmentData = EquipmentRuntimeCache.getEquipmentData(equipmentId);
        return ResponseHelper.successful(equipmentData);
    }
} 