package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.vo.ServerSourceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工作站控制器
 * 通过 SitewebPersistentService.getConfigAPI() 调用相关方法
 */
@Slf4j
@RestController
@RequestMapping("/workstation")
public class WorkstationController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 获取工作站服务器源列表
     * 包含不同类型的工作站信息(RDS、RMU、DS等)
     *
     * @return 服务器源列表
     */
    @GetMapping("/server-source-list")
    public ResponseEntity<ResponseResult> getServerSourceList() {
        try {
            List<ServerSourceVO> serverSourceList = sitewebPersistentService.getConfigAPI()
                    .getServerSourceListForWorkstation();
            
            return ResponseHelper.successful(serverSourceList);
        } catch (Exception e) {
            log.error("获取工作站服务器源列表失败", e);
            return ResponseHelper.failed("获取工作站服务器源列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有工作站列表
     *
     * @return 工作站列表
     */
    @GetMapping("/list")
    public ResponseEntity<ResponseResult> getWorkstationList() {
        try {
            List<?> workstationList = sitewebPersistentService.getConfigAPI()
                    .findAllForWorkstation();
            
            return ResponseHelper.successful(workstationList);
        } catch (Exception e) {
            log.error("获取工作站列表失败", e);
            return ResponseHelper.failed("获取工作站列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据工作站类型获取工作站列表
     *
     * @param workStationType 工作站类型
     * @return 工作站列表
     */
    @GetMapping("/type/{workStationType}")
    public ResponseEntity<ResponseResult> getByWorkStationType(@PathVariable("workStationType") Integer workStationType) {
        try {
            List<?> workstationList = sitewebPersistentService.getConfigAPI()
                    .findByWorkStationTypeForWorkstation(workStationType);
            
            return ResponseHelper.successful(workstationList);
        } catch (Exception e) {
            log.error("根据工作站类型获取工作站列表失败, workStationType: {}", workStationType, e);
            return ResponseHelper.failed("获取工作站列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取工作站详情
     *
     * @param workStationId 工作站ID
     * @return 工作站详情
     */
    @GetMapping("/{workStationId}")
    public ResponseEntity<ResponseResult> getById(@PathVariable("workStationId") Integer workStationId) {
        try {
            Object workstation = sitewebPersistentService.getConfigAPI()
                    .findByIdForWorkstation(workStationId);
            
            if (workstation != null) {
                return ResponseHelper.successful(workstation);
            } else {
                return ResponseHelper.failed("工作站不存在");
            }
        } catch (Exception e) {
            log.error("获取工作站详情失败, workStationId: {}", workStationId, e);
            return ResponseHelper.failed("获取工作站详情失败: " + e.getMessage());
        }
    }
}
