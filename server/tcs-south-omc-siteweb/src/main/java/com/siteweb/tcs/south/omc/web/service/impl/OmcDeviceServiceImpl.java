package com.siteweb.tcs.south.omc.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.omc.dal.dto.OmcDeviceDTO;
import com.siteweb.tcs.south.omc.dal.entity.OmcDevice;
import com.siteweb.tcs.south.omc.dal.mapper.OmcDeviceMapper;
import com.siteweb.tcs.south.omc.web.service.IOmcDeviceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OmcDeviceServiceImpl extends ServiceImpl<OmcDeviceMapper, OmcDevice> implements IOmcDeviceService {
    @Resource
    private OmcDeviceMapper omcDeviceMapper;

    @Override
    public OmcDeviceDTO getDeviceByName(String deviceName) {
        OmcDevice device = omcDeviceMapper.selectByDeviceName(deviceName);
        if (device == null) return null;
        OmcDeviceDTO dto = new OmcDeviceDTO();
        BeanUtils.copyProperties(device, dto);
        return dto;
    }

    @Override
    public List<OmcDeviceDTO> listActiveDevices(LocalDateTime minCreateTime) {
        List<OmcDevice> devices = omcDeviceMapper.findActiveDevices(minCreateTime);
        return devices.stream().map(device -> {
            OmcDeviceDTO dto = new OmcDeviceDTO();
            BeanUtils.copyProperties(device, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDevice(OmcDevice device) {
        return this.save(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDevice(OmcDevice device) {
        return this.updateById(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDevice(Long deviceId) {
        return this.removeById(deviceId);
    }
} 