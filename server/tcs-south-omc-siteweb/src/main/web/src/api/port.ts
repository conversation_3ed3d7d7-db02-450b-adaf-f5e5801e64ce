import { http } from "@/utils/http";

export interface PortInfo {
  portId?: string;
  portName: string;
  portNo: number;
  portType: number;
  phoneNumber?: string;
  setting: string;
  monitorUnitId: string;
  linkSamplerUnitId?: number;
  description?: string;
}

export interface PortType {
  typeId: number;
  typeName: string;
  order: number;
}

export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

/**
 * 获取端口类型列表
 * @param monitorUnitCategory 监控单元类型
 */
export const getPortTypes = (monitorUnitCategory: number) => {
  return http.request<ApiResponse<PortType[]>>(
    "get",
    `/api/thing/south-omc-siteweb/port/types?monitorUnitCategory=${monitorUnitCategory}`
  );
};

/**
 * 新增端口
 * @param portData 端口数据
 */
export const createPort = (portData: PortInfo) => {
  return http.request<ApiResponse<any>>(
    "post",
    "/api/thing/south-omc-siteweb/port/config",
    {
      data: portData
    }
  );
};

/**
 * 编辑端口
 * @param portData 端口数据
 */
export const updatePort = (portData: PortInfo) => {
  return http.request<ApiResponse<any>>(
    "put",
    "/api/thing/south-omc-siteweb/port/config",
    {
      data: portData
    }
  );
};

/**
 * 获取端口详情
 * @param portId 端口ID
 */
export const getPortDetail = (portId: string) => {
  return http.request<ApiResponse<PortInfo>>(
    "get",
    `/api/thing/south-omc-siteweb/port/config/${portId}`
  );
};

/**
 * 删除端口
 * @param portId 端口ID
 */
export const deletePort = (portId: string) => {
  return http.request<ApiResponse<any>>(
    "delete",
    `/api/thing/south-omc-siteweb/port/config/${portId}`
  );
};
