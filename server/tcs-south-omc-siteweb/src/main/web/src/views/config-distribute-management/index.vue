<template>
  <div
    class="config-distribute-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            配置分发管理
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          非RMU监控单元配置分发
        </span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 min-h-0">
      <MuList />
    </div>
  </div>
</template>

<script setup lang="ts">
import MuList from "./components/MuList.vue";

defineOptions({
  name: "ConfigDistributeManagement"
});
</script>

<style scoped>
.config-distribute-management-container {
  height: calc(100vh - 48px);
}
</style>
