<template>
  <div class="device-info-container">
    <el-form
      ref="formRef"
      :model="deviceInfo"
      :rules="rules"
      label-width="120px"
      class="device-form"
    >
      <!-- 基本信息 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="设备编号">
            <el-input v-model="deviceInfo.equipmentId" readonly />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="名称" prop="equipmentName">
            <el-input
              v-model="deviceInfo.equipmentName"
              maxlength="128"
              placeholder="请输入设备名称"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 模板信息 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="设备模板编号">
            <el-input v-model="deviceInfo.equipmentTemplateId" readonly />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="模板名称">
            <el-input v-model="deviceInfo.equipmentTemplateName" readonly />
          </el-form-item>
        </div>
      </div>

      <!-- 层级信息 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="上一级设备">
            <el-select
              v-model="deviceInfo.parentEquipmentId"
              placeholder="请选择上一级设备"
              filterable
              clearable
            >
              <el-option
                v-for="item in parentEquipmentList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="所属服务器">
            <el-input v-model="deviceInfo.workStationName" readonly />
          </el-form-item>
        </div>
      </div>

      <div class="form-row">
        <div class="form-item">
          <el-form-item label="所属局站">
            <el-input v-model="deviceInfo.stationName" readonly />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="所属监控单元">
            <el-input v-model="deviceInfo.monitorUnitName" readonly />
          </el-form-item>
        </div>
      </div>

      <!-- 设备分类 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="类型" prop="equipmentCategory">
            <el-select
              v-model="deviceInfo.equipmentCategory"
              placeholder="请选择设备类型"
              filterable
              @change="handleCategoryChange"
            >
              <el-option
                v-for="item in equipmentCategoryArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="分类">
            <el-select
              v-model="deviceInfo.equipmentType"
              placeholder="请选择设备分类"
              filterable
              disabled
            >
              <el-option
                v-for="item in equipmentTypeArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 电池相关信息（条件显示） -->
      <div v-if="batteryFlag" class="form-row">
        <div class="form-item">
          <el-form-item label="电池类型">
            <el-select
              v-model="deviceInfo.equipmentClass"
              placeholder="请选择电池类型"
              filterable
            >
              <el-option
                v-for="item in equipmentClassArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="电池工作状态" prop="equipmentState">
            <el-select
              v-model="deviceInfo.equipmentState"
              placeholder="请选择电池工作状态"
              filterable
            >
              <el-option
                v-for="item in equipmentStateArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 厂商和单位 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="厂商">
            <el-select
              v-model="deviceInfo.vendor"
              placeholder="请选择厂商"
              filterable
            >
              <el-option
                v-for="item in vendorArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="单位">
            <el-input v-model="deviceInfo.unit" placeholder="请输入单位" />
          </el-form-item>
        </div>
      </div>

      <!-- 设备型号和模块 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="设备型号">
            <el-input
              v-model="deviceInfo.equipmentStyle"
              placeholder="请输入设备型号"
            />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="模块">
            <el-input
              v-model="deviceInfo.equipmentModule"
              placeholder="请输入模块"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 延迟配置 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="开始延迟(秒)">
            <el-input-number
              v-model="deviceInfo.startDelay"
              :min="0"
              placeholder="开始延迟"
              class="full-width"
            />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="结束延迟(秒)">
            <el-input-number
              v-model="deviceInfo.endDelay"
              :min="0"
              placeholder="结束延迟"
              class="full-width"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 属性和设备基类 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="属性">
            <el-select
              v-model="deviceInfo.propertyArr"
              placeholder="请选择属性"
              multiple
              filterable
            >
              <el-option
                v-for="item in propertyArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <!-- <div class="form-item">
          <el-form-item label="设备基类">
            <el-select
              v-model="deviceInfo.equipmentBaseType"
              placeholder="设备基类"
              disabled
            >
              <el-option
                v-for="item in baseTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div> -->
      </div>

      <!-- 告警过滤表达式 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="告警过滤表达式">
            <el-input
              v-model="deviceInfo.eventExpression"
              readonly
              placeholder="点击配置告警过滤表达式"
              style="cursor: pointer"
              @click="openExpression"
            />
          </el-form-item>
        </div>
        <div class="form-item">
          <!-- 空占位 -->
        </div>
      </div>

      <!-- 资产信息 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="资产编号">
            <el-input
              v-model="deviceInfo.equipmentNo"
              placeholder="请输入资产编号"
            />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="资产状态">
            <el-select
              v-model="deviceInfo.assetState"
              placeholder="请选择资产状态"
              filterable
            >
              <el-option
                v-for="item in assetStateArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 日期信息 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="购买日期">
            <el-date-picker
              v-model="deviceInfo.buyDate"
              type="date"
              placeholder="请选择购买日期"
              :format="DATE_FORMAT"
              :value-format="DATE_FORMAT"
            />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="投用日期">
            <el-date-picker
              v-model="deviceInfo.usedDate"
              type="date"
              placeholder="请选择投用日期"
              :format="DATE_FORMAT"
              :value-format="DATE_FORMAT"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 使用年限和价格 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="使用年限(年)">
            <el-input-number
              v-model="deviceInfo.usedLimit"
              :min="0"
              placeholder="使用年限"
              class="full-width"
            />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="价格(元)">
            <el-input-number
              v-model="deviceInfo.price"
              :precision="2"
              placeholder="价格"
              class="full-width"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 容量和模块 -->
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="额定容量">
            <el-input
              v-model="deviceInfo.ratedCapacity"
              placeholder="请输入额定容量"
            />
          </el-form-item>
        </div>
        <div class="form-item">
          <el-form-item label="已安装模块">
            <el-input
              v-model="deviceInfo.installedModule"
              placeholder="请输入已安装模块"
            />
          </el-form-item>
        </div>
      </div>


      <!-- 说明 -->
      <div class="form-row">
        <div class="form-item-full">
          <el-form-item label="说明">
            <el-input
              v-model="deviceInfo.description"
              type="textarea"
              :rows="4"
              placeholder="请输入说明"
              class="full-textarea"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="form-row">
        <div class="form-item-center">
          <el-button type="primary" :loading="saving" @click="handleSave">
            保存设备信息
          </el-button>
        </div>
      </div>
    </el-form>

    <!-- 表达式配置对话框 -->
    <ExpressionConfigDialog
      v-model="expressionDialogVisible"
      :signal-data="{ eventExpression: deviceInfo.eventExpression }"
      :template-id="deviceInfo.equipmentTemplateId"
      expression-field="eventExpression"
      @confirm="confirmExpression"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch, toRefs } from "vue";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules
} from "element-plus";
import {
  getDeviceInfo as getDeviceInfoApi,
  updateDeviceInfo,
  getDataDictionary,
  getSimplifyEquipments,
  getEquipmentBaseTypes
} from "../../../api/device-management";
import ExpressionConfigDialog from "../../device-template/components/ExpressionConfigDialog.vue";
import { DATE_FORMAT, formatDate } from "@/utils/dateTime";

interface Props {
  equipmentId: string;
  active: boolean;
  tabIndex: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  updateSuccess: [];
  deviceNameUpdate: [name: string];
}>();

// 表单引用
const formRef = ref<FormInstance>();
const saving = ref(false);
const batteryFlag = ref(false);

// 表达式对话框
const expressionDialogVisible = ref(false);

// 设备信息数据
const deviceInfo = reactive({
  stationId: 0,
  equipmentId: "",
  equipmentName: "",
  parentEquipmentId: "",
  equipmentTemplateId: "",
  equipmentTemplateName: "",
  workStationId: null,
  workStationName: "",
  stationName: "",
  monitorUnitId: null,
  monitorUnitName: "",
  equipmentCategory: null,
  equipmentType: null,
  equipmentClass: null,
  equipmentState: null,
  vendor: "",
  unit: "",
  equipmentStyle: "",
  equipmentModule: "",
  startDelay: null,
  endDelay: null,
  property: "",
  propertyArr: [],
  equipmentBaseType: null,
  eventExpression: "",
  equipmentNo: "",
  assetState: null,
  buyDate: "",
  usedDate: "",
  usedLimit: null,
  price: null,
  ratedCapacity: "",
  installedModule: "",
  description: ""
});

// 数据字典数组
const equipmentCategoryArr = ref([]);
const equipmentTypeArr = ref([]);
const vendorArr = ref([]);
const propertyArr = ref([]);
const assetStateArr = ref([]);
const equipmentClassArr = ref([]);
const equipmentStateArr = ref([]);
const parentEquipmentList = ref([]);
// const baseTypeList = ref([])
const batteryData = ref<any>({});

// 表单验证规则
const rules: FormRules = {
  equipmentName: [
    { required: true, message: "请输入设备名称", trigger: "blur" },
    { max: 128, message: "名称长度不能超过128个字符", trigger: "blur" },
    { pattern: /^[^<>'\\]+$/, message: "名称格式错误", trigger: "blur" }
  ],
  equipmentCategory: [
    { required: true, message: "请选择设备类型", trigger: "change" }
  ],
  equipmentState: [
    {
      required: true,
      message: "请选择电池工作状态",
      trigger: "change",
      validator: (rule: any, value: any, callback: any) => {
        if (batteryFlag.value && !value) {
          callback(new Error("请选择电池工作状态"));
        } else {
          callback();
        }
      }
    }
  ]
};

// 获取电池相关设备类型配置
const getBatteryDeviceCategory = () => {
  // 根据Angular代码，这里应该调用实际的API
  // 暂时保持模拟数据，等待真实API接口确认
  return Promise.resolve({ data: {} });
};

// 初始化数据
const initData = async () => {
  if (!props.equipmentId) {
    console.warn("设备ID未提供，跳过初始化");
    return;
  }

  if (!props.active) {
    console.warn("组件未激活，跳过初始化");
    return;
  }

  try {
    console.log("开始初始化设备信息数据");
    // 数据字典ID映射（与Angular版本保持一致）
    const dictionaryIds = {
      equipmentCategory: 7, // 设备类别
      equipmentType: 8, // 设备类型
      vendor: 14, // 厂商
      property: 9, // 设备属性
      assetState: 10, // 资产状态
      equipmentClass: 11, // 电池类型
      equipmentState: 12 // 工作状态
    };

    console.log("获取数据字典和设备信息");
    const [
      categoryResponse,
      typeResponse,
      vendorResponse,
      propertyResponse,
      assetStateResponse,
      equipmentClassResponse,
      equipmentStateResponse,
      parentDeviceResponse,
      batteryDataResult
    ] = await Promise.all([
      getDataDictionary(dictionaryIds.equipmentCategory),
      getDataDictionary(dictionaryIds.equipmentType),
      getDataDictionary(dictionaryIds.vendor),
      getDataDictionary(dictionaryIds.property),
      getDataDictionary(dictionaryIds.assetState),
      getDataDictionary(dictionaryIds.equipmentClass),
      getDataDictionary(dictionaryIds.equipmentState),
      getSimplifyEquipments(props.equipmentId),
      getBatteryDeviceCategory()
    ]);

    // 处理数据字典 - 设备类别
    if (categoryResponse.code === 0 && categoryResponse.data) {
      equipmentCategoryArr.value = categoryResponse.data.map((item: any) => ({
        label: item.itemValue,
        value: item.itemId
      }));
    }

    // 处理数据字典 - 设备类型
    if (typeResponse.code === 0 && typeResponse.data) {
      equipmentTypeArr.value = typeResponse.data.map((item: any) => ({
        label: item.itemValue,
        value: item.itemId
      }));
    }

    // 处理数据字典 - 厂商
    if (vendorResponse.code === 0 && vendorResponse.data) {
      vendorArr.value = vendorResponse.data.map((item: any) => ({
        label: item.itemValue,
        value: item.itemValue
      }));
    }

    // 处理数据字典 - 设备属性
    if (propertyResponse.code === 0 && propertyResponse.data) {
      propertyArr.value = propertyResponse.data.map((item: any) => ({
        label: item.itemValue,
        value: item.itemId
      }));
    }

    // 处理数据字典 - 资产状态
    if (assetStateResponse.code === 0 && assetStateResponse.data) {
      assetStateArr.value = assetStateResponse.data.map((item: any) => ({
        label: item.itemValue,
        value: item.itemId
      }));
    }

    // 处理数据字典 - 电池类型
    if (equipmentClassResponse.code === 0 && equipmentClassResponse.data) {
      equipmentClassArr.value = equipmentClassResponse.data.map(
        (item: any) => ({
          label: item.itemValue,
          value: item.itemId
        })
      );
    }

    // 处理数据字典 - 工作状态
    if (equipmentStateResponse.code === 0 && equipmentStateResponse.data) {
      equipmentStateArr.value = equipmentStateResponse.data.map(
        (item: any) => ({
          label: item.itemValue,
          value: item.itemId
        })
      );
    }

    // 处理上级设备列表
    // if (parentDeviceResponse?.code === 0 && parentDeviceResponse?.data) {
    //   parentEquipmentList.value = parentDeviceResponse.data.map((item: any) => ({
    //     label: item.equipmentName,
    //     value: item.equipmentId.toString()
    //   }));
    // }

    // 电池数据
    batteryData.value = batteryDataResult;

    // 获取设备详情
    console.log("获取设备详细信息");
    await getDeviceInfo();

    console.log("设备信息初始化完成");
  } catch (error) {
    console.error("初始化数据失败:", error);
    if (error && typeof error === "object" && "message" in error) {
      ElMessage.error("获取数据失败: " + error.message);
    } else if (typeof error === "string") {
      ElMessage.error("获取数据失败: " + error);
    } else {
      ElMessage.error("获取数据失败，请检查网络连接或联系管理员");
    }
  }
};

// 获取设备信息
const getDeviceInfo = async () => {
  if (!props.equipmentId) {
    console.warn("设备ID未提供，跳过获取设备信息");
    return;
  }

  try {
    console.log("调用获取设备信息API");
    const response = await getDeviceInfoApi(props.equipmentId);
    if (response.state && response.data) {
      // 将API返回的数据赋值给表单
      Object.assign(deviceInfo, response.data);

      // 格式化日期字段
      if (response.data.buyDate) {
        deviceInfo.buyDate = formatDate(response.data.buyDate);
      }
      if (response.data.usedDate) {
        deviceInfo.usedDate = formatDate(response.data.usedDate);
      }

      // 处理属性数组（如果存在property字段）
      if (response.data.property) {
        deviceInfo.propertyArr = response.data.property.split("/").map(Number);
      }

      // 检查是否显示电池相关字段
      if (response.data.equipmentCategory && batteryData.value.data) {
        batteryFlag.value =
          response.data.equipmentCategory in batteryData.value.data;
      }

      // 通知父组件设备名称更新
      if (response.data.equipmentName) {
        emit("deviceNameUpdate", response.data.equipmentName);
      }

      console.log("设备信息加载成功:", response.data);
    } else {
      ElMessage.warning(
        "获取设备信息失败: " + (response.message || "未知错误")
      );
    }
  } catch (error) {
    console.error("获取设备信息失败:", error);
    ElMessage.error("获取设备信息失败");
  }
};

// 监听 props 变化
watch(
  [() => props.equipmentId, () => props.active, () => props.tabIndex],
  (
    [newEquipmentId, newActive, newTabIndex],
    [oldEquipmentId, oldActive, oldTabIndex]
  ) => {
    console.log("DeviceInfo props变化监听:", {
      newEquipmentId,
      newActive,
      newTabIndex,
      oldEquipmentId,
      oldActive,
      oldTabIndex
    });

    if (newEquipmentId && newActive) {
      console.log("DeviceInfo 开始初始化数据");
      initData();
    }
  },
  { immediate: true }
);

// 处理设备类型变化
const handleCategoryChange = (value: any) => {
  // 检查是否为电池类型
  batteryFlag.value = value in batteryData.value.data;

  // 清空电池相关字段
  if (!batteryFlag.value) {
    deviceInfo.equipmentClass = null;
    deviceInfo.equipmentState = null;
  }
};

// 打开表达式配置
const openExpression = () => {
  expressionDialogVisible.value = true;
};

// 确认表达式
const confirmExpression = (expression: string) => {
  deviceInfo.eventExpression = expression;
  expressionDialogVisible.value = false;
  if (expression) {
    ElMessage.success("表达式配置成功");
  }
};

// 保存设备信息
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    // 处理属性数组
    if (deviceInfo.propertyArr?.length) {
      deviceInfo.property = deviceInfo.propertyArr.join("/");
    }

    // 调用真实的更新API
    const response = await updateDeviceInfo(deviceInfo);

    if (response.code === 0) {
      ElMessage.success("保存成功！");
      emit("updateSuccess");
      // 如果设备名称有更新，通知父组件
      if (deviceInfo.equipmentName) {
        emit("deviceNameUpdate", deviceInfo.equipmentName);
      }
    } else {
      ElMessage.error("保存失败: " + (response.message || "未知错误"));
    }
  } catch (error) {
    console.error("保存失败:", error);
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error("保存失败");
    }
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.device-info-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.device-form {
  max-width: 1200px;
}

.form-row {
  display: flex;
  margin-bottom: 8px;
  gap: 40px;
}

.form-item {
  flex: 1;
  min-width: 0;
}

.form-item-full {
  width: 100%;
}

.form-item-center {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}

.form-item :deep(.el-form-item__label) {
  width: 120px;
  text-align: right;
  margin-right: 16px;
}

.form-item :deep(.el-input),
.form-item :deep(.el-select),
.form-item :deep(.el-date-picker) {
  width: 400px;
}

.form-item :deep(.el-input-number) {
  width: 400px;
}

.full-width {
  width: 100% !important;
}

.full-textarea {
  width: 850px !important;
}

.form-item :deep(.el-form-item__error) {
  color: #f56c6c;
  padding-left: 12px;
}

.form-item :deep(.el-form-item.is-error .el-input__wrapper) {
  border-color: #f56c6c;
}

.form-item :deep(.el-form-item.is-error .el-select .el-select__wrapper) {
  border-color: #f56c6c;
}
</style>
