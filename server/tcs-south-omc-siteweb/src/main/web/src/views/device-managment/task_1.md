# 设备模板信号管理功能需求分析

## 功能概述
设备模板信号管理是一个用于配置和管理设备信号模板的功能模块，支持信号的完整生命周期管理。

## 界面布局

### 页面结构
```
顶部工具栏区域 (buttonFlag为true时显示)
├── 设置信号实例按钮 (非跨站设备)
├── 设置跨站信号按钮 (跨站设备 muCategory=24)  
└── 查看所有信号实例按钮 (非跨站设备)

主要内容区域
└── 信号数据表格 (支持筛选、冻结列、多选)
    ├── 右键菜单
    └── 表格列配置
```

### 表格列定义
| 列名 | 宽度 | 类型 | 说明 |
|------|------|------|------|
| 名称 | 150px | 文本 | 信号名称 |
| 信号ID | 100px | 只读 | 系统生成ID |
| 显示顺序 | 70px | 数字 | 排序字段 |
| 种类 | 120px | 下拉 | 信号种类字典 |
| 分类 | 120px | 下拉 | 信号分类字典 |
| 通道号 | 90px | 文本 | 通道编号 |
| 通道类型 | 120px | 下拉 | 通道类型字典 |
| 表达式 | 170px | 只读+点击 | 双击打开表达式配置 |
| 数据类型 | 180px | 下拉 | 数据类型字典 |
| 精度 | 100px | 文本 | 显示精度设置 |
| 单位 | 100px | 文本 | 测量单位 |
| 存储周期(秒) | 120px | 下拉 | 预设存储周期选项 |
| 绝对值阈值 | 100px | 文本 | 阈值设置 |
| 百分比阈值 | 100px | 文本 | 百分比阈值 |
| 统计周期(小时) | 130px | 文本 | 统计时间周期 |
| 有效 | 80px | 复选框 | 启用状态 |
| 可见 | 80px | 复选框 | 可见性控制 |
| 后备状态电池存储周期(秒) | 180px | 数字 | 电池设备专用 |
| 后备状态电池绝对值阀值 | 170px | 数字 | 电池设备专用 |
| 说明 | 80px | 文本 | 描述信息 |
| 信号属性 | 100px | 多选下拉 | 信号属性字典 |
| 状态信号 | 200px | 只读+点击 | 双击配置状态含义 |
| 所属模块 | 100px | 文本 | 模块编号 |
| 基类信号 | 180px | 只读+点击 | 双击选择基类 |

## 核心功能

### 1. 信号数据管理
- **数据加载**: 根据模板ID加载信号列表
- **实时编辑**: 支持单元格直接编辑
- **数据验证**: 
  - 精度字段限制格式：以0开头，小数部分只能是0
  - 跨站信号要求通道号为-2且无表达式
- **搜索过滤**: 支持按信号名称、ID、说明进行搜索

### 2. 信号操作功能
- **新增信号**: 创建新信号记录
- **从模板添加**: 从其他模板复制信号配置
- **删除信号**: 批量删除选中信号
- **复制粘贴**: 复制信号行并粘贴
- **关联事件**: 为信号添加关联事件

### 3. 高级配置功能
- **表达式配置**: 打开专门的表达式配置弹窗
- **状态信号设置**: 仅开关信号可配置状态含义
- **基类信号关联**: 选择并关联基类信号
- **字段批量复制**: 将表达式或状态信号复制到多个信号

### 4. 设备实例管理
- **信号实例化**: 为设备创建具体信号实例
- **跨站信号配置**: 配置跨站点信号连接
- **实例查看**: 查看和管理已创建的信号实例

## 后端API接口

### 数据字典接口
- `GET /api/config/dataitems?entryId={entryId}` - 获取字典数据
  - entryId=17: 信号种类
  - entryId=18: 信号分类  
  - entryId=22: 通道类型
  - entryId=70: 数据类型
  - entryId=21: 信号属性

### 信号管理接口
- `GET /api/signal/list/{templateId}` - 获取信号列表
- `GET /api/signal/listByTempIdEqId/{templateId}/{equipmentId}` - 获取设备信号列表
- `POST /api/signal/add` - 新增信号
- `PUT /api/signal/update` - 更新信号
- `DELETE /api/signal/delete/{templateId}/{signalIds}` - 删除信号
- `POST /api/signal/addRelateEvent/{templateId}/{signalId}` - 添加关联事件
- `POST /api/signal/batchFieldCopy` - 字段批量复制

### 设备实例接口
- `GET /api/device/signalInstances/{equipmentId}` - 获取设备信号实例
- `GET /api/device/template/{equipmentId}` - 根据设备ID获取模板

## 用户操作流程

### 1. 页面初始化
1. 页面加载时获取模板信息和字典数据
2. 根据设备类别决定是否显示电池相关字段
3. 加载信号列表数据并渲染表格
4. 检查信号实例设置状态（绿色背景标识已设置实例）

### 2. 日常编辑操作
**单元格编辑**
- 直接点击可编辑单元格进行修改
- 修改后延迟50ms自动保存（防抖处理）
- 精度字段有格式验证：只能输入以0开头的数字

**特殊列操作（双击触发）**
- 表达式列：打开表达式配置弹窗
- 状态信号列：仅开关信号(signalCategory=2)可编辑，打开状态设置弹窗  
- 基类信号列：打开基类选择器弹窗

### 3. 右键菜单操作
**前提**: 选中一行或多行数据后右键
**菜单选项**:
- 增加信号：创建新信号
- 从模板增加信号：从其他模板复制信号
- 增加关联事件：为信号添加关联事件
- 删除信号：批量删除选中信号
- 行拷贝并粘贴：复制选中行
- 拷贝表达式到...：将当前信号表达式复制到其他信号
- 拷贝状态信号到...：将当前开关信号状态复制到其他开关信号

### 4. 工具栏按钮操作
**设置信号实例按钮** (非跨站设备)
- 前提：选中一行信号
- 功能：为选中信号创建设备实例配置

**设置跨站信号按钮** (跨站设备 muCategory=24)
- 前提：选中一行且通道号为-2且未配置表达式的信号
- 功能：配置跨站信号连接

**查看所有信号实例按钮** (非跨站设备)
- 功能：查看和管理当前设备的所有信号实例

### 5. 模板修改确认机制
**触发条件**: 进行任何修改操作时
**确认流程**:
1. 弹出模板修改确认对话框
2. 用户选择操作方式：
   - "更新原模板"：直接修改当前模板
   - "另存为新模板"：创建新模板后修改
   - "取消"：放弃修改
3. 根据选择执行相应操作

## 数据结构

### 信号数据对象
```javascript
{
  id: null,                    // 记录ID
  equipmentTemplateId: 123,    // 模板ID
  signalId: 456,              // 信号ID
  signalName: "信号名称",      // 信号名称
  displayIndex: 1,            // 显示顺序
  signalCategory: 1,          // 信号种类
  signalType: 1,              // 信号分类
  channelNo: 1,               // 通道号
  channelType: 1,             // 通道类型
  expression: "",             // 表达式
  dataType: "REAL",           // 数据类型
  showPrecision: "0",         // 精度
  unit: "V",                  // 单位
  storeInterval: "300",       // 存储周期
  absValueThreshold: 0.1,     // 绝对值阈值
  percentThreshold: 5,        // 百分比阈值
  staticsPeriod: 24,          // 统计周期
  enable: true,               // 有效状态
  visible: true,              // 可见状态
  chargeStoreInterVal: 3600,  // 后备电池存储周期
  chargeAbsValue: 0.5,        // 后备电池绝对值阈值
  description: "说明",        // 说明
  signalProperty: "1;2",      // 信号属性ID串
  moduleNo: 1,                // 模块号
  baseTypeId: null,           // 基类ID
  baseTypeName: "",           // 基类名称
  signalMeaningsList: [],     // 状态信号列表
  signalPropertyList: [],     // 信号属性列表
  acrossSignal: false,        // 是否跨站信号
  hasInstance: false          // 是否已设置实例
}
```

### 存储周期预设值
86400,28800,14400,3600,1800,600,300... (从大到小的秒数值)

## 业务规则

### 显示控制
- 根模板（isRootTemp=true）：所有字段只读
- 电池设备：显示后备电池相关字段
- 跨站信号：文字显示为蓝色
- 已设置实例：背景显示为绿色

### 操作限制
- 状态信号设置：仅signalCategory=2的开关信号可用
- 跨站信号配置：仅通道号为-2且无表达式的信号可用
- 精度验证：只能输入"0"、"0.0"、"0.00"等格式

### 数据处理
- 搜索过滤：支持按信号名称、ID、说明搜索，500ms防抖
- 批量操作：支持多选进行批量删除、复制等操作
- 自动保存：单元格修改后50ms延迟保存
