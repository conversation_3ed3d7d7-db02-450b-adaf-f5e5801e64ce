<template>
  <div class="device-template-control">
    <!-- 表格区域 -->
    <div class="control-table-container">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="tableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        :row-class="getRowClass"
        :row-event-handlers="rowEventHandlers"
        fixed
      />
    </div>

    <!-- 模板修改确认对话框 -->
    <DeviceTemplateConfirmDialog
      v-model="showConfirmDialog"
      :template-id="templateData?.id"
      :template-data="templateData"
      @confirm="handleConfirmDialogResult"
      @cancel="handleConfirmCancel"
    />

    <!-- 从模板选择控制对话框 -->
    <DeviceTemplateSecSelector
      v-model="showTemplateSelector"
      :type="2"
      :equipment-category="
        templateData?.template?.equipmentCategory ||
        templateData?.equipmentCategory ||
        0
      "
      :origin-template-id="templateData?.template?.id || templateData?.id || 0"
      :origin-signal-list="tableData"
      :protocol-code="
        templateData?.template?.protocolCode || templateData?.protocolCode || ''
      "
      @confirm="handleTemplateSelectorConfirm"
      @cancel="handleTemplateSelectorCancel"
    />

    <!-- 基类配置对话框 -->
    <BaseClassSelectorDialog
      v-model="showBaseClassDialog"
      :signal-data="currentEditingControl"
      :template-id="templateData?.id"
      :type="2"
      @confirm="handleBaseClassConfirm"
      @cancel="handleBaseClassCancel"
    />

    <!-- 参数含义配置对话框 -->
    <ControlMeaningDialog
      v-model="showControlMeaningDialog"
      :control-data="currentEditingControl"
      :disabled="isRootTemplate"
      @confirm="handleControlMeaningConfirm"
      @cancel="handleControlMeaningCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, h, withKeys } from "vue";
import {
  ElMessage,
  ElButton,
  ElSelect,
  ElOption,
  ElIcon,
  ElPopover,
  ElInput,
  ElCheckbox,
  type InputInstance,
  type SelectInstance
} from "element-plus";
import { Filter } from "@element-plus/icons-vue";
import type { FunctionalComponent } from "vue";
import ContextMenu from "@imengyu/vue3-context-menu";
import DeviceTemplateConfirmDialog from "./DeviceTemplateConfirmDialog.vue";
import DeviceTemplateSecSelector from "./DeviceTemplateSecSelector.vue";
import BaseClassSelectorDialog from "./BaseClassSelectorDialog.vue";
import ControlMeaningDialog from "./ControlMeaningDialog.vue";
import {
  getEquipmentTemplateControl,
  getSignalListByTempId,
  getDataitems,
  updateControl,
  createControl,
  deleteEquipmentTemplateControl,
  batchControlFieldCopy,
  getTemplateInfoById,
  getTemplateTreeByCategory,
  getControlSeverityList,
  getControlCategoryList,
  getCommandTypeList,
  getControlTypeList,
  getDataTypeList,
  BaseClassSelectorType,
  deviceTemplateService,
  type DataDictionaryItem,
  type TemplateTreeNode,
  type ControlInfo as BaseControlInfo
} from "@/api/device-template";

// 扩展控制信息接口，添加编辑状态字段
export interface ControlInfo extends BaseControlInfo {
  // 编辑状态字段
  controlName_editing?: boolean;
  displayIndex_editing?: boolean;
  controlCategory_editing?: boolean;
  controlSeverity_editing?: boolean;
  cmdToken_editing?: boolean;
  timeOut_editing?: boolean;
  retry_editing?: boolean;
  commandType_editing?: boolean;
  controlType_editing?: boolean;
  dataType_editing?: boolean;
  maxValue_editing?: boolean;
  minValue_editing?: boolean;
  signalId_editing?: boolean;
  defaultValue_editing?: boolean;
  description_editing?: boolean;
  enable_editing?: boolean;
  visible_editing?: boolean;
  moduleNo_editing?: boolean;
  baseTypeName_editing?: boolean;
  // 添加控制特有字段
  controlSeverity?: number;
  cmdToken?: string;
  timeOut?: number;
  retry?: number;
  commandType?: number;
  maxValue?: number;
  minValue?: number;
  defaultValue?: any;
  baseTypeName?: string;
  baseTypeId?: number | null;
  controlMeaningsList?: any[]; // 参数含义列表
  [key: string]: any; // 允许动态属性
}

interface Props {
  templateData?: any;
  tabIndex?: number;
  muCategory?: number;
  tableSearchText?: string;
  equipmentId?: string | number;
  buttonFlag?: boolean;
  isRootTemplate?: boolean;
}

interface Emits {
  (e: "refresh"): void;
  (e: "selectTab", data: { index: number }): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null,
  tabIndex: 0,
  muCategory: 0,
  tableSearchText: "",
  equipmentId: "",
  buttonFlag: false,
  isRootTemplate: false
});

const emit = defineEmits<Emits>();

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 编辑单元格组件类型定义
type EditCellProps = {
  value: any;
  onChange: (value: any) => void;
  onBlur: () => void;
  onKeydownEnter: () => void;
  forwardRef: (el: any) => void;
  options?: Array<{ label: string; value: any }>;
  type?: "input" | "number" | "select" | "checkbox";
  placeholder?: string;
};

// 输入框编辑组件
const InputCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef,
  placeholder = ""
}) => {
  return h(ElInput, {
    ref: forwardRef,
    modelValue: value,
    placeholder,
    size: "small",
    onInput: onChange,
    onBlur,
    onKeydown: withKeys(onKeydownEnter, ["enter"])
  });
};

// 下拉选择编辑组件
const SelectCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef,
  options = [],
  placeholder = "请选择"
}) => {
  return h(
    ElSelect,
    {
      ref: forwardRef,
      modelValue: value,
      placeholder,
      size: "small",
      clearable: true,
      filterable: true, // 支持搜索
      "onUpdate:modelValue": onChange,
      onBlur,
      onKeydown: withKeys(onKeydownEnter, ["enter"])
    },
    {
      default: () =>
        options.map(option =>
          h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          })
        )
    }
  );
};

// 复选框编辑组件
const CheckboxCell: FunctionalComponent<EditCellProps> = ({
  value,
  onChange,
  onBlur,
  onKeydownEnter,
  forwardRef
}) => {
  return h(ElCheckbox, {
    ref: forwardRef,
    modelValue: !!value,
    "onUpdate:modelValue": onChange,
    onBlur,
    onKeydown: withKeys(onKeydownEnter, ["enter"])
  });
};

// 状态变量
const loading = ref(false);
const tableData = ref<ControlInfo[]>([]);

// 表格选中相关
const selectedRowIndexes = ref<number[]>([]);
const selectedRows = ref<ControlInfo[]>([]);

// 下拉选项数据
const controlSeverityList = ref<DataDictionaryItem[]>([]);
const controlCategoryList = ref<DataDictionaryItem[]>([]);
const commandTypeList = ref<DataDictionaryItem[]>([]);
const controlTypeList = ref<DataDictionaryItem[]>([]);
const dataTypeList = ref<DataDictionaryItem[]>([]);
const signalList = ref<any[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

// 存储原始数据用于对比
const originalData = ref<Map<string, any>>(new Map());

// 处理单元格值变化（编辑过程中）- 只保存值，不提交
const handleCellValueChange = (
  rowData: ControlInfo,
  fieldKey: string,
  value: any
) => {
  // 直接更新值，不触发提交
  rowData[fieldKey] = value;
};

// 处理编辑完成（失去焦点或按回车）- 这时才提交变更
const handleCellEditComplete = (rowData: ControlInfo, fieldKey: string) => {
  // 获取原始值
  const originalKey = `${rowData.controlId}_${fieldKey}`;
  const originalValue = originalData.value.get(originalKey);
  const currentValue = rowData[fieldKey];

  // 检查值是否真的发生了变化
  if (originalValue !== currentValue) {
    // 值确实发生了变化，提交更改
    submitControlChange(rowData, fieldKey);
  }
};

// 开始编辑时保存原始值
const handleCellEditStart = (rowData: ControlInfo, fieldKey: string) => {
  const originalKey = `${rowData.controlId}_${fieldKey}`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData[fieldKey]);
  }
};

// 提交控制变更 - 需要前置声明
const submitControlChange = async (
  rowData: ControlInfo,
  fieldKey: string = ""
) => {
  // 防止重复确认
  if (isConfirming.value) return;

  // 检查是否需要显示确认对话框
  if (!deviceTemplateService.getNotShowState()) {
    // 需要显示确认对话框
    pendingControlData.value = { ...rowData };
    pendingFieldKey.value = fieldKey;
    showConfirmDialog.value = true;
    return;
  }

  // 直接更新
  await performControlUpdate(rowData);
};

// 执行实际的控制更新
const performControlUpdate = async (
  rowData: ControlInfo,
  newTemplateId?: number
) => {
  try {
    loading.value = true;

    // 处理特殊字段
    const submitData = { ...rowData };

    // 如果是新模板，更新模板ID
    if (newTemplateId) {
      submitData.equipmentTemplateId = newTemplateId;
    }

    // 处理数值类型字段
    if (submitData.signalId) {
      submitData.signalId = parseInt(submitData.signalId.toString());
    }
    if (submitData.timeOut) {
      submitData.timeOut = parseInt(submitData.timeOut.toString());
    }
    if (submitData.retry) {
      submitData.retry = parseInt(submitData.retry.toString());
    }
    if (submitData.maxValue) {
      submitData.maxValue = parseFloat(submitData.maxValue.toString());
    }
    if (submitData.minValue) {
      submitData.minValue = parseFloat(submitData.minValue.toString());
    }
    if (submitData.moduleNo) {
      submitData.moduleNo = parseInt(submitData.moduleNo.toString());
    }

    // 处理参数含义列表
    if (
      submitData.controlMeaningsList &&
      Array.isArray(submitData.controlMeaningsList)
    ) {
      submitData.controlMeaningsList.forEach((item: any) => {
        if (item.controlId === undefined || item.controlId === null) {
          item.controlId = submitData.controlId;
        }
        if (
          item.equipmentTemplateId === undefined ||
          item.equipmentTemplateId === null
        ) {
          item.equipmentTemplateId = submitData.equipmentTemplateId;
        }
      });
    }

    const res = await updateControl(submitData);
    if (res.code === 0) {
      ElMessage.success("更新成功！");

      // 不管是否为新模板，都重新加载控制列表以保证数据一致性
      await loadControlList();

      // 只有在新模板的情况下才通知父组件刷新（用于更新模板树）
      if (newTemplateId) {
        emit("refresh");
      }
    } else {
      ElMessage.error(res.msg || "更新失败");
      loadControlList(); // 失败时重新加载数据
    }
  } catch (error) {
    console.error("更新控制失败:", error);
    ElMessage.error("更新失败");
    loadControlList(); // 失败时重新加载数据
  } finally {
    loading.value = false;
  }
};

// 创建可编辑单元格渲染器
const createEditableCell = (column: any, cellType: string = "input") => {
  return ({ rowData }: any) => {
    const fieldKey = column.dataKey;
    const editingKey = `${fieldKey}_editing`;

    // 获取选项数据（需要在前面定义，以便只读状态也能使用）
    const getOptions = () => {
      switch (fieldKey) {
        case "controlCategory":
          return controlCategoryList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "controlSeverity":
          return controlSeverityList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "commandType":
          return commandTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "controlType":
          return controlTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "dataType":
          return dataTypeList.value.map(item => ({
            label: item.itemValue,
            value: item.itemId
          }));
        case "signalId":
          return signalList.value.map(item => ({
            label: item.name,
            value: item.id
          }));
        case "enable":
        case "visible":
          return [
            { label: "是", value: true },
            { label: "否", value: false }
          ];
        default:
          return [];
      }
    };

    // 获取显示值（用于只读和编辑状态）
    const getDisplayValue = () => {
      const value = rowData[fieldKey];
      if (cellType === "select") {
        const options = getOptions();
        // 对于signalId，确保数据类型匹配（将value转换为数字进行比较）
        if (fieldKey === "signalId" && value != null) {
          const numValue = parseInt(value.toString());
          const option = options.find(opt => opt.value === numValue);
          return option ? option.label : value || "";
        } else {
          const option = options.find(opt => opt.value === value);
          return option ? option.label : value || "";
        }
      } else if (cellType === "checkbox") {
        return value ? "是" : "否";
      }
      return value || "";
    };

    // 检查是否为只读模板
    if (props.isRootTemplate) {
      return h("div", { class: "read-only-cell" }, getDisplayValue());
    }

    const onChange = (value: any) => {
      handleCellValueChange(rowData, fieldKey, value);
    };

    const onEnterEditMode = () => {
      // 开始编辑时保存原始值
      handleCellEditStart(rowData, fieldKey);
      rowData[editingKey] = true;
    };

    const onExitEditMode = () => {
      // 退出编辑时检查并提交变更
      handleCellEditComplete(rowData, fieldKey);
      rowData[editingKey] = false;
    };

    const inputRef = ref();
    const setRef = (el: any) => {
      inputRef.value = el;
      if (el) {
        el.focus?.();
      }
    };

    if (rowData[editingKey]) {
      const cellProps = {
        forwardRef: setRef,
        value: rowData[fieldKey],
        onChange,
        onBlur: onExitEditMode,
        onKeydownEnter: onExitEditMode,
        options: getOptions()
      };

      switch (cellType) {
        case "select":
          return h(SelectCell, cellProps);
        case "checkbox":
          return h(CheckboxCell, cellProps);
        default:
          return h(InputCell, cellProps);
      }
    } else {
      const displayValue = getDisplayValue();
      return h(
        "div",
        {
          class: "table-v2-inline-editing-trigger",
          onClick: onEnterEditMode
        },
        displayValue || "\u00A0"
      ); // 使用不间断空格确保有内容可点击
    }
  };
};

// 创建只读单元格渲染器（用于不可编辑的字段）
const createReadOnlyCell = (
  column: any,
  clickHandler?: Function,
  customTitle?: string
) => {
  return ({ rowData }: any) => {
    const fieldKey = column.dataKey;
    const value = rowData[fieldKey];

    // 获取显示值
    const getDisplayValue = () => {
      if (fieldKey === "controlMeaningsList") {
        if (Array.isArray(value) && value.length > 0) {
          // 显示参数含义列表，格式：序号1/序号2/序号3
          const displayValues = value
            .map(item => {
              // 优先使用 parameterValue，如果不存在或为null/undefined则使用 stateValue
              const val =
                item.parameterValue !== null &&
                item.parameterValue !== undefined
                  ? item.parameterValue
                  : item.stateValue;
              return val !== null && val !== undefined ? val.toString() : "";
            })
            .filter(val => val !== "");

          return displayValues.join("/");
        }
        return ""; // 空数组或null时返回空字符串，让点击处理器显示占位符
      }
      return value || "";
    };

    if (clickHandler) {
      // 检查是否为只读模板
      if (props.isRootTemplate) {
        return h(
          "div",
          {
            class: "read-only-cell",
            style:
              fieldKey === "controlMeaningsList"
                ? { color: "rgba(0, 0, 0, 0.3)" }
                : {}
          },
          getDisplayValue()
        );
      }

      // 默认可点击单元格
      const title = customTitle || "点击配置";
      let placeholder = "点击配置";
      if (fieldKey === "baseTypeName") {
        placeholder = "点击配置基类控制";
      } else if (fieldKey === "controlMeaningsList") {
        placeholder = "点击配置";
      }

      const displayValue = getDisplayValue();

      return h(
        "div",
        {
          class: "read-only-clickable-cell",
          onClick: () => clickHandler(rowData),
          title
        },
        [
          h(
            "span",
            {
              class: "cell-content",
              style:
                fieldKey === "controlMeaningsList" && displayValue
                  ? { color: "#333" }
                  : {}
            },
            displayValue || placeholder
          ),
          // 为参数含义和基类控制添加编辑提示图标
          (fieldKey === "controlMeaningsList" || fieldKey === "baseTypeName") &&
          !displayValue
            ? h("span", { class: "edit-hint" }, "")
            : null
        ]
      );
    }

    return h(
      "div",
      {
        class: "read-only-cell"
      },
      getDisplayValue()
    );
  };
};

// 处理基类控制点击
const handleBaseTypeClick = (rowData: ControlInfo) => {
  if (props.isRootTemplate) return;

  // 保存原始值
  const originalKey = `${rowData.controlId}_baseTypeName`;
  if (!originalData.value.has(originalKey)) {
    originalData.value.set(originalKey, rowData.baseTypeName);
  }

  // 打开基类配置对话框
  showBaseClassDialog.value = true;
  currentEditingControl.value = rowData;
};

// 处理参数含义点击
const handleControlMeaningClick = (rowData: ControlInfo) => {
  if (props.isRootTemplate) return;

  // 打开参数含义配置对话框
  showControlMeaningDialog.value = true;
  currentEditingControl.value = rowData;
};

// 模板修改确认对话框相关
const showConfirmDialog = ref(false);
const pendingControlData = ref<ControlInfo | null>(null);
const pendingFieldKey = ref<string>("");
const pendingActionType = ref<string>("");
const isConfirming = ref(false);

// 从模板选择控制对话框相关
const showTemplateSelector = ref(false);

// 基类配置对话框相关
const showBaseClassDialog = ref(false);

// 参数含义配置对话框相关
const showControlMeaningDialog = ref(false);

// 当前编辑的控制
const currentEditingControl = ref<ControlInfo | null>(null);

// 处理确认对话框结果
const handleConfirmDialogResult = async (
  action: "update" | "copy",
  data: any
) => {
  isConfirming.value = true;

  try {
    // 保存"不再提醒"状态
    if (data.notShowAgain) {
      deviceTemplateService.setNotShowState(true);
    }

    if (pendingControlData.value && pendingFieldKey.value) {
      // 处理控制更新操作
      const controlId = pendingControlData.value.controlId;
      const fieldKey = pendingFieldKey.value;

      if (action === "update") {
        // 更新原模板
        await performControlUpdate(pendingControlData.value);
      } else if (action === "copy") {
        // 另存为新模板
        if (data.newTemplateId) {
          await performControlUpdate(
            pendingControlData.value,
            data.newTemplateId
          );
          ElMessage.success("已在新模板中更新成功！");
        }
      }

      // 更新成功后，清理对应的原始数据缓存
      const originalKey = `${controlId}_${fieldKey}`;
      originalData.value.delete(originalKey);
    } else if (pendingActionType.value) {
      // 处理右键菜单操作
      if (action === "update") {
        // 在原模板上执行操作
        await executeAction(pendingActionType.value);
      } else if (action === "copy") {
        // 在新模板上执行操作
        if (data.newTemplateId) {
          await executeAction(pendingActionType.value, data.newTemplateId);
          ElMessage.success("已在新模板中操作成功！");
        }
      }
    }
  } catch (error) {
    console.error("处理确认对话框结果失败:", error);
    ElMessage.error("操作失败");
    // 操作失败时也重新加载数据以确保界面一致性
    loadControlList();
  } finally {
    // 重置状态
    isConfirming.value = false;
    pendingControlData.value = null;
    pendingFieldKey.value = "";
    pendingActionType.value = "";
    showConfirmDialog.value = false;
  }
};

// 处理确认取消
const handleConfirmCancel = () => {
  // 恢复修改前的值（仅对控制更新操作有效）
  if (pendingControlData.value && pendingFieldKey.value) {
    const originalKey = `${pendingControlData.value.controlId}_${pendingFieldKey.value}`;
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== undefined) {
      // 恢复到原始值
      pendingControlData.value[pendingFieldKey.value] = originalValue;

      // 更新对应的表格行数据
      const tableRowIndex = tableData.value.findIndex(
        item => item.controlId === pendingControlData.value!.controlId
      );
      if (tableRowIndex !== -1) {
        tableData.value[tableRowIndex][pendingFieldKey.value] = originalValue;
      }

      // 清理原始数据缓存
      originalData.value.delete(originalKey);
    }
  }

  // 重置状态
  isConfirming.value = false;
  pendingControlData.value = null;
  pendingFieldKey.value = "";
  pendingActionType.value = "";
  showConfirmDialog.value = false;
};

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    controlName: {
      value: [],
      options: []
    },
    controlId: {
      value: [],
      options: []
    },
    displayIndex: {
      value: [],
      options: []
    },
    controlCategory: {
      value: [],
      options: []
    },
    controlSeverity: {
      value: [],
      options: []
    },
    cmdToken: {
      value: [],
      options: []
    },
    timeOut: {
      value: [],
      options: []
    },
    retry: {
      value: [],
      options: []
    },
    commandType: {
      value: [],
      options: []
    },
    controlType: {
      value: [],
      options: []
    },
    dataType: {
      value: [],
      options: []
    },
    maxValue: {
      value: [],
      options: []
    },
    minValue: {
      value: [],
      options: []
    },
    signalId: {
      value: [],
      options: []
    },
    defaultValue: {
      value: [],
      options: []
    },
    description: {
      value: [],
      options: []
    },
    enable: {
      value: [],
      options: []
    },
    visible: {
      value: [],
      options: []
    },
    moduleNo: {
      value: [],
      options: []
    },
    baseTypeName: {
      value: [],
      options: []
    },
    controlMeaningsList: {
      value: [],
      options: []
    }
  };
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  // 更新控制名称选项
  const controlNames = [
    ...new Set(tableData.value.map(item => item.controlName).filter(Boolean))
  ];
  filterState.value.controlName.options = controlNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新控制ID选项
  const controlIds = [
    ...new Set(
      tableData.value
        .map(item => item.controlId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.controlId.options = controlIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新显示顺序选项
  const displayIndexes = [
    ...new Set(
      tableData.value
        .map(item => item.displayIndex)
        .filter(index => index !== null && index !== undefined)
    )
  ];
  filterState.value.displayIndex.options = displayIndexes.map(index => ({
    label: index.toString(),
    value: index
  }));

  // 更新命令字符串选项
  const cmdTokens = [
    ...new Set(tableData.value.map(item => item.cmdToken).filter(Boolean))
  ];
  filterState.value.cmdToken.options = cmdTokens.map(token => ({
    label: token,
    value: token
  }));

  // 更新说明选项
  const descriptions = [
    ...new Set(tableData.value.map(item => item.description).filter(Boolean))
  ];
  filterState.value.description.options = descriptions.map(desc => ({
    label: desc,
    value: desc
  }));

  // 更新基类名称选项
  const baseTypeNames = [
    ...new Set(tableData.value.map(item => item.baseTypeName).filter(Boolean))
  ];
  filterState.value.baseTypeName.options = baseTypeNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新参数含义选项（包含空值选项）
  const controlMeaningsValues = [
    ...new Set(
      tableData.value
        .map(item => {
          if (
            item.controlMeaningsList &&
            Array.isArray(item.controlMeaningsList) &&
            item.controlMeaningsList.length > 0
          ) {
            const values = item.controlMeaningsList
              .map((meaning: any) => {
                const val =
                  meaning.parameterValue !== null &&
                  meaning.parameterValue !== undefined
                    ? meaning.parameterValue
                    : meaning.stateValue;
                return val !== null && val !== undefined ? val : "";
              })
              .filter(val => val !== "");
            return values.length > 0 ? values.join("/") : "(空)";
          }
          return "(空)"; // 空值显示为 "(空)"
        })
        .filter(value => value !== null && value !== undefined)
    )
  ];
  filterState.value.controlMeaningsList.options = controlMeaningsValues.map(
    value => ({
      label: value === "(空)" ? "未配置" : value,
      value: value === "(空)" ? "" : value
    })
  );

  // 从字典更新选项
  if (controlCategoryList.value.length > 0) {
    filterState.value.controlCategory.options = controlCategoryList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (controlSeverityList.value.length > 0) {
    filterState.value.controlSeverity.options = controlSeverityList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (commandTypeList.value.length > 0) {
    filterState.value.commandType.options = commandTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (controlTypeList.value.length > 0) {
    filterState.value.controlType.options = controlTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (dataTypeList.value.length > 0) {
    filterState.value.dataType.options = dataTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (signalList.value.length > 0) {
    filterState.value.signalId.options = signalList.value.map(item => ({
      label: item.name,
      value: item.id
    }));
  }

  // 设置布尔值字段的过滤选项
  filterState.value.enable.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];

  filterState.value.visible.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { class: "flex items-center justify-center" }, [
      h("span", { class: "mr-2 text-xs" }, props.column.title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { class: "filter-wrapper" }, [
              h("div", { class: "filter-group" }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || []).map(
                        (option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                      )
                  }
                )
              ]),
              h("div", { class: "el-table-v2__demo-filter" }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { class: "cursor-pointer" }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (props.tableSearchText && props.tableSearchText.trim() !== "") {
    const searchText = props.tableSearchText.toLowerCase();
    data = data.filter(item => {
      // 基本字段搜索
      const basicMatch =
        item.controlName?.toLowerCase().includes(searchText) ||
        item.controlId?.toString().includes(searchText) ||
        item.description?.toLowerCase().includes(searchText);

      // 参数含义搜索
      let meaningMatch = false;
      if (item.controlMeaningsList && Array.isArray(item.controlMeaningsList)) {
        meaningMatch = item.controlMeaningsList.some((meaning: any) => {
          const meaningText = meaning.meanings
            ?.toLowerCase()
            .includes(searchText);
          const parameterValue =
            meaning.parameterValue !== null &&
            meaning.parameterValue !== undefined
              ? meaning.parameterValue.toString().includes(searchText)
              : false;
          const stateValue =
            meaning.stateValue !== null && meaning.stateValue !== undefined
              ? meaning.stateValue.toString().includes(searchText)
              : false;
          return meaningText || parameterValue || stateValue;
        });
      }

      return basicMatch || meaningMatch;
    });
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        let itemValue = item[key as keyof ControlInfo];

        // 特殊处理参数含义字段
        if (key === "controlMeaningsList") {
          if (Array.isArray(itemValue) && itemValue.length > 0) {
            const values = itemValue
              .map((meaning: any) => {
                const val =
                  meaning.parameterValue !== null &&
                  meaning.parameterValue !== undefined
                    ? meaning.parameterValue
                    : meaning.stateValue;
                return val !== null && val !== undefined ? val : "";
              })
              .filter(val => val !== "");
            itemValue = values.length > 0 ? values.join("/") : "";
          } else {
            itemValue = ""; // 空数组或null时设为空字符串
          }
        }

        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 虚拟表格配置
const tableWidth = ref(1800);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  // 查找 el-tabs__content 容器
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent) {
    const rect = tabsContent.getBoundingClientRect();
    tableWidth.value = rect.width - 32; // 减去内边距
    tableHeight.value = rect.height - 32; // 减去内边距
  }
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 表格列配置
const tableColumns: any[] = [
  {
    key: "controlName",
    title: "名称",
    dataKey: "controlName",
    width: 155,
    cellRenderer: createEditableCell({ dataKey: "controlName" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "controlName" })
  },
  {
    key: "displayIndex",
    title: "显示顺序",
    dataKey: "displayIndex",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "displayIndex" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
  },
  {
    key: "controlId",
    title: "控制ID",
    dataKey: "controlId",
    width: 85,
    cellRenderer: createReadOnlyCell({ dataKey: "controlId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "controlId" })
  },
  {
    key: "controlCategory",
    title: "命令种类",
    dataKey: "controlCategory",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "controlCategory" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "controlCategory" })
  },
  {
    key: "controlSeverity",
    title: "重要度",
    dataKey: "controlSeverity",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "controlSeverity" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "controlSeverity" })
  },
  {
    key: "cmdToken",
    title: "命令字符串",
    dataKey: "cmdToken",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "cmdToken" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "cmdToken" })
  },
  {
    key: "timeOut",
    title: "超时(秒)",
    dataKey: "timeOut",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "timeOut" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "timeOut" })
  },
  {
    key: "retry",
    title: "重试",
    dataKey: "retry",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "retry" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "retry" })
  },
  {
    key: "commandType",
    title: "控制命令类型",
    dataKey: "commandType",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "commandType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "commandType" })
  },
  {
    key: "controlType",
    title: "控件分类",
    dataKey: "controlType",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "controlType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "controlType" })
  },
  {
    key: "dataType",
    title: "数据类型",
    dataKey: "dataType",
    width: 120,
    cellRenderer: createEditableCell({ dataKey: "dataType" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "dataType" })
  },
  {
    key: "maxValue",
    title: "参数上限",
    dataKey: "maxValue",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "maxValue" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "maxValue" })
  },
  {
    key: "minValue",
    title: "参数下限",
    dataKey: "minValue",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "minValue" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "minValue" })
  },
  {
    key: "signalId",
    title: "关联信号",
    dataKey: "signalId",
    width: 200,
    cellRenderer: createEditableCell({ dataKey: "signalId" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
  },
  {
    key: "controlMeaningsList",
    title: "参数含义",
    dataKey: "controlMeaningsList",
    width: 150,
    cellRenderer: createReadOnlyCell(
      { dataKey: "controlMeaningsList" },
      (rowData: ControlInfo) => {
        handleControlMeaningClick(rowData);
      },
      "点击配置"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "controlMeaningsList" })
  },
  {
    key: "defaultValue",
    title: "默认值",
    dataKey: "defaultValue",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "defaultValue" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "defaultValue" })
  },
  {
    key: "description",
    title: "说明",
    dataKey: "description",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "description" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "description" })
  },
  {
    key: "enable",
    title: "有效",
    dataKey: "enable",
    width: 100,
    align: "center",
    cellRenderer: createEditableCell({ dataKey: "enable" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "enable" })
  },
  {
    key: "visible",
    title: "可见",
    dataKey: "visible",
    width: 100,
    align: "center",
    cellRenderer: createEditableCell({ dataKey: "visible" }, "select"),
    headerCellRenderer: createFilterHeader({ dataKey: "visible" })
  },
  {
    key: "moduleNo",
    title: "所属模块",
    dataKey: "moduleNo",
    width: 100,
    cellRenderer: createEditableCell({ dataKey: "moduleNo" }, "input"),
    headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
  },
  {
    key: "baseTypeName",
    title: "基类控制",
    dataKey: "baseTypeName",
    width: 200,
    cellRenderer: createReadOnlyCell(
      { dataKey: "baseTypeName" },
      (rowData: ControlInfo) => {
        handleBaseTypeClick(rowData);
      },
      "点击配置基类控制"
    ),
    headerCellRenderer: createFilterHeader({ dataKey: "baseTypeName" })
  }
];

// 组件初始化
onMounted(() => {
  // 初始化过滤器状态
  initFilterState();

  initializeDictionaryData();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateTableSize();
    });
    resizeObserver.observe(tabsContent);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", updateTableSize);
});

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.template) {
      if (props.tabIndex === 3) {
        // 控制标签页的索引是3
        loadControlList();
      }
    }
  },
  { immediate: true }
);

// 监听标签页变化
watch(
  () => props.tabIndex,
  newIndex => {
    if (newIndex === 3 && props.templateData) {
      // 控制标签页的索引是3
      loadControlList();
    }
  }
);

// 加载控制列表
const loadControlList = async () => {
  if (!props.templateData?.id) return;

  try {
    loading.value = true;

    // 确保信号列表已加载（避免时序问题）
    await loadSignalList();

    const res = await getEquipmentTemplateControl(props.templateData.id);
    if (res.code === 0 && res.data) {
      processControlData(res.data);
    } else {
      ElMessage.error(res.msg || "获取控制列表失败");
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取控制列表失败:", error);
    ElMessage.error("获取控制列表失败");
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadControlSeverityList(),
      loadControlCategoryList(),
      loadCommandTypeList(),
      loadControlTypeList(),
      loadDataTypeList(),
      loadSignalList()
    ]);
  } catch (error) {
    console.error("初始化字典数据失败:", error);
  }
};

// 加载控制重要度列表
const loadControlSeverityList = async () => {
  try {
    const res = await getDataitems(28); // 重要度
    if (res.code === 0) {
      controlSeverityList.value = res.data;
    }
  } catch (error) {
    console.error("获取控制重要度列表失败:", error);
  }
};

// 加载控制命令种类列表
const loadControlCategoryList = async () => {
  try {
    const res = await getDataitems(31); // 命令种类
    if (res.code === 0) {
      controlCategoryList.value = res.data;
    }
  } catch (error) {
    console.error("获取控制命令种类列表失败:", error);
  }
};

// 加载控制命令类型列表
const loadCommandTypeList = async () => {
  try {
    const res = await getDataitems(32); // 控制命令类型
    if (res.code === 0) {
      commandTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取控制命令类型列表失败:", error);
  }
};

// 加载控件分类列表
const loadControlTypeList = async () => {
  try {
    const res = await getDataitems(68); // 控件分类
    if (res.code === 0) {
      controlTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取控件分类列表失败:", error);
  }
};

// 加载数据类型列表
const loadDataTypeList = async () => {
  try {
    const res = await getDataitems(70); // 数据类型
    if (res.code === 0) {
      dataTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取数据类型列表失败:", error);
  }
};

// 加载信号列表
const loadSignalList = async () => {
  if (!props.templateData?.id) return;

  try {
    const res = await getSignalListByTempId(props.templateData.id);
    if (res.code === 0 && res.data) {
      signalList.value = res.data.map((signal: any) => ({
        id: parseInt(signal.signalId), // 确保ID是数字类型
        name: signal.signalName || "" // 确保名称是字符串类型
      }));
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
  }
};

// 处理控制数据
const processControlData = (data: ControlInfo[]) => {
  // 清理之前的原始数据缓存
  originalData.value.clear();

  data.forEach(item => {
    // 初始化编辑状态
    item.controlName_editing = false;
    item.displayIndex_editing = false;
    item.controlCategory_editing = false;
    item.controlSeverity_editing = false;
    item.cmdToken_editing = false;
    item.timeOut_editing = false;
    item.retry_editing = false;
    item.commandType_editing = false;
    item.controlType_editing = false;
    item.dataType_editing = false;
    item.maxValue_editing = false;
    item.minValue_editing = false;
    item.signalId_editing = false;
    item.defaultValue_editing = false;
    item.description_editing = false;
    item.enable_editing = false;
    item.visible_editing = false;
    item.moduleNo_editing = false;
    item.baseTypeName_editing = false;
    // 初始化 controlMeaningsList（如果不存在）
    if (!item.controlMeaningsList) {
      item.controlMeaningsList = [];
    }
  });

  tableData.value = data;

  // 数据加载后更新过滤器选项
  updateDynamicOptions();
};

// 行事件处理器
const rowEventHandlers = {
  onClick: ({ rowData, rowIndex, event }: any) => {
    // 单击时选中行
    const clickedRowIndex = filteredData.value.findIndex(
      item => item.controlId === rowData.controlId
    );
    if (clickedRowIndex !== -1) {
      // 如果按住Ctrl键，支持多选
      if (event.ctrlKey || event.metaKey) {
        const isSelected = selectedRowIndexes.value.includes(clickedRowIndex);
        if (isSelected) {
          // 取消选中
          selectedRowIndexes.value = selectedRowIndexes.value.filter(
            index => index !== clickedRowIndex
          );
        } else {
          // 添加选中
          selectedRowIndexes.value.push(clickedRowIndex);
        }
      } else {
        // 单选
        selectedRowIndexes.value = [clickedRowIndex];
      }

      // 更新选中的行数据
      selectedRows.value = selectedRowIndexes.value.map(
        index => filteredData.value[index]
      );
    }
  },
  onContextmenu: ({ rowData, rowIndex, event }: any) => {
    event.preventDefault();

    // 如果右键的是某一行，确保该行被选中
    const currentIndex = filteredData.value.findIndex(
      item => item.controlId === rowData.controlId
    );
    if (
      currentIndex !== -1 &&
      !selectedRowIndexes.value.includes(currentIndex)
    ) {
      selectedRowIndexes.value = [currentIndex];
      selectedRows.value = [rowData];
    }

    // 只有在非只读模板时才显示右键菜单
    if (props.isRootTemplate) return;

    showContextMenu(event);
  }
};

// 处理行选中
const handleRowSelect = (selectedIndexes: number[]) => {
  selectedRowIndexes.value = selectedIndexes;
  selectedRows.value = selectedIndexes.map(index => filteredData.value[index]);
};

// 显示右键菜单
const showContextMenu = (event: MouseEvent) => {
  const hasSelectedRows = selectedRowIndexes.value.length > 0;
  const hasOneSelectedRow = selectedRowIndexes.value.length === 1;

  ContextMenu.showContextMenu({
    x: event.x,
    y: event.y,
    items: [
      {
        label: "增加控制",
        icon: "h:plus",
        onClick: () => {
          handleRowChangeConfirm("add");
        }
      },
      {
        label: "从模板增加控制",
        icon: "h:document-add",
        onClick: () => {
          handleRowChangeConfirm("addTemp");
        }
      },
      {
        label: "删除控制",
        icon: "h:delete",
        disabled: !hasSelectedRows,
        onClick: () => {
          handleRowChangeConfirm("delete");
        }
      },
      {
        label: "行拷贝并粘贴",
        icon: "h:document-duplicate",
        disabled: !hasSelectedRows,
        onClick: () => {
          handleRowChangeConfirm("copy");
        }
      }
    ]
  });
};

// 处理行变更确认
const handleRowChangeConfirm = async (type: string) => {
  if (!props.templateData?.id) {
    ElMessage.error("模板信息不存在");
    return;
  }

  // 从模板增加控制直接打开选择对话框
  if (type === "addTemp") {
    showTemplateSelector.value = true;
    return;
  }

  // 检查是否需要显示确认对话框
  if (!deviceTemplateService.getNotShowState()) {
    // 需要显示确认对话框
    pendingActionType.value = type;
    showConfirmDialog.value = true;
    return;
  }

  // 直接执行操作
  await executeAction(type);
};

// 执行具体操作
const executeAction = async (type: string, newTemplateId?: number) => {
  try {
    loading.value = true;
    const templateId = newTemplateId || props.templateData.id;

    switch (type) {
      case "add":
        await handleAddNewControl(templateId);
        break;
      case "delete":
        await handleDeleteControl(templateId);
        break;
      case "copy":
        await handleCopyControl(templateId);
        break;
      default:
        ElMessage.warning(`未知操作类型: ${type}`);
        return;
    }

    ElMessage.success("操作成功！");
    // 刷新数据
    await loadControlList();

    // 如果是新模板，通知父组件刷新
    if (newTemplateId) {
      emit("refresh");
    }
  } catch (error) {
    console.error(`操作失败:`, error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

// 增加新控制
const handleAddNewControl = async (templateId: number) => {
  // 生成新的控制ID和显示顺序
  const maxDisplayIndex =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.displayIndex || 0), 0)
      : 0;

  const newControl = {
    id: null,
    equipmentTemplateId: templateId,
    controlId: null,
    enable: true,
    visible: true,
    controlName: `新增控制${Date.now()}`,
    controlCategory: controlCategoryList.value.length
      ? controlCategoryList.value[0].itemId
      : undefined,
    controlSeverity: controlSeverityList.value.length
      ? controlSeverityList.value[0].itemId
      : undefined,
    cmdToken: "",
    timeOut: undefined,
    retry: undefined,
    commandType:
      commandTypeList.value.length > 0
        ? commandTypeList.value[1]?.itemId
        : undefined,
    controlType: controlTypeList.value.length
      ? controlTypeList.value[0].itemId
      : undefined,
    dataType: undefined,
    maxValue: 99,
    minValue: 0,
    defaultValue: undefined,
    displayIndex: maxDisplayIndex + 1,
    moduleNo: 0,
    description: "",
    signalId: null,
    baseTypeId: undefined,
    hasInstance: false
  };

  const res = await createControl(newControl);
  if (res.code !== 0) {
    throw new Error(res.msg || "添加控制失败");
  }
};

// 删除控制
const handleDeleteControl = async (templateId: number) => {
  if (selectedRowIndexes.value.length === 0) {
    ElMessage.warning("请先选择要删除的控制！");
    return;
  }

  const controlIds = selectedRowIndexes.value
    .map(index => filteredData.value[index].controlId)
    .join(",");

  const res = await deleteEquipmentTemplateControl({
    equipmentTemplateId: templateId,
    controlIds: controlIds
  });
  if (res.code !== 0) {
    throw new Error(res.msg || "删除控制失败");
  }

  // 清空选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
};

// 复制控制
const handleCopyControl = async (templateId: number) => {
  if (selectedRowIndexes.value.length === 0) {
    ElMessage.warning("请先选择要复制的控制！");
    return;
  }

  const selectedControls = selectedRowIndexes.value.map(
    index => filteredData.value[index]
  );
  const maxDisplayIndex =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map(item => item.displayIndex || 0), 0)
      : 0;

  for (let i = 0; i < selectedControls.length; i++) {
    const originalControl = selectedControls[i];

    // 生成复制名称（按Angular版本的逻辑）
    let copyIndex = 1;
    let copyName = `${originalControl.controlName}${copyIndex}#`;
    while (tableData.value.some(item => item.controlName === copyName)) {
      copyIndex++;
      copyName = `${originalControl.controlName}${copyIndex}#`;
    }

    const copiedControl = {
      ...originalControl,
      id: null,
      controlId: null, // 后端会自动生成新的controlId
      equipmentTemplateId: templateId,
      controlName: copyName,
      displayIndex: maxDisplayIndex + i + 1,
      hasInstance: false
    };

    // 移除编辑状态字段
    Object.keys(copiedControl).forEach(key => {
      if (key.endsWith("_editing")) {
        delete (copiedControl as any)[key];
      }
    });

    const res = await createControl(copiedControl);
    if (res.code !== 0) {
      throw new Error(res.msg || `复制第${i + 1}个控制失败`);
    }
  }
};

// 处理从模板选择控制确认
const handleTemplateSelectorConfirm = async (data: {
  templateId: number;
  selectedSignal: any;
}) => {
  if (!props.templateData?.id) {
    ElMessage.error("模板信息不存在");
    return;
  }

  try {
    loading.value = true;

    // 准备新控制数据
    const newControl = {
      ...data.selectedSignal,
      id: null,
      controlId: null, // 后端会自动生成新的controlId
      equipmentTemplateId: props.templateData.id,
      hasInstance: false
    };

    // 移除编辑状态字段
    Object.keys(newControl).forEach(key => {
      if (key.endsWith("_editing")) {
        delete (newControl as any)[key];
      }
    });

    const res = await createControl(newControl);
    if (res.code === 0) {
      ElMessage.success("从模板添加控制成功！");
      // 刷新数据
      await loadControlList();
    } else {
      ElMessage.error(res.msg || "从模板添加控制失败");
    }
  } catch (error) {
    console.error("从模板添加控制失败:", error);
    ElMessage.error("从模板添加控制失败");
  } finally {
    loading.value = false;
  }
};

// 处理从模板选择控制取消
const handleTemplateSelectorCancel = () => {
  // 取消操作，无需特殊处理
};

// 处理基类配置确认
const handleBaseClassConfirm = (result: {
  action: string;
  baseTypeId: number | null;
  baseTypeName?: string;
  moduleNumber?: number | null;
}) => {
  if (currentEditingControl.value) {
    // 保存原始值（如果尚未保存）
    const originalKey = `${currentEditingControl.value.controlId}_baseTypeName`;
    if (!originalData.value.has(originalKey)) {
      originalData.value.set(
        originalKey,
        currentEditingControl.value.baseTypeName
      );
    }

    let newBaseTypeName = "";
    if (result.action === "delete") {
      // 取消关联基类
      currentEditingControl.value.baseTypeId = null;
      currentEditingControl.value.baseTypeName = "";
      newBaseTypeName = "";
    } else if (result.action === "confirm") {
      // 设置基类
      currentEditingControl.value.baseTypeId = result.baseTypeId;
      currentEditingControl.value.baseTypeName = result.baseTypeName || "";
      newBaseTypeName = result.baseTypeName || "";
    }

    // 检查是否有变化
    const originalValue = originalData.value.get(originalKey);
    if (originalValue !== newBaseTypeName) {
      submitControlChange(currentEditingControl.value, "baseTypeName");
    }
  }
  showBaseClassDialog.value = false;
  currentEditingControl.value = null;
};

// 处理基类配置取消
const handleBaseClassCancel = () => {
  // 基类对话框取消时不需要特殊处理，因为我们没有修改原始数据
  showBaseClassDialog.value = false;
  currentEditingControl.value = null;
};

// 处理参数含义配置确认
const handleControlMeaningConfirm = (result: {
  action: string;
  data: any[];
}) => {
  if (currentEditingControl.value && result.action === "confirm") {
    // 更新控制的参数含义列表
    currentEditingControl.value.controlMeaningsList = result.data;

    // 提交控制变更
    submitControlChange(currentEditingControl.value, "controlMeaningsList");
  }
  showControlMeaningDialog.value = false;
  currentEditingControl.value = null;
};

// 处理参数含义配置取消
const handleControlMeaningCancel = () => {
  showControlMeaningDialog.value = false;
  currentEditingControl.value = null;
};

// 获取行样式类
const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  const isSelected = selectedRowIndexes.value.includes(rowIndex);
  return isSelected ? "selected-row" : "";
};

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  // 清理原始数据缓存
  originalData.value.clear();
  // 清理选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
  window.removeEventListener("resize", updateTableSize);
});

// 暴露方法给父组件
defineExpose({
  loadControlList
});
</script>

<style scoped>
.device-template-control {
  width: 100%;
  height: 100%;
}

.control-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 可编辑单元格样式 */
:deep(.table-v2-inline-editing-trigger) {
  border: 1px transparent dotted;
  cursor: pointer;
  min-width: 55px; /* 确保占满整个单元格宽度 */
}

:deep(.table-v2-inline-editing-trigger:hover) {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* 只读单元格样式 */
.read-only-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  color: var(--el-text-color-placeholder);
}

/* 可点击的只读单元格样式 */
.read-only-clickable-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid transparent;
}

.read-only-clickable-cell:hover {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.read-only-clickable-cell:hover .edit-hint {
  opacity: 1;
}

.read-only-clickable-cell .edit-hint {
  opacity: 0.6;
  transition: opacity 0.2s;
  font-size: 12px;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

.read-only-clickable-cell .cell-content {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

:deep(.el-input__inner) {
  font-size: 12px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-checkbox) {
  margin: 0;
}

/* 编辑状态下的输入框样式 */
:deep(.el-input--small) {
  font-size: 12px;
}

:deep(.el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-select--small) {
  font-size: 12px;
}

/* 确认对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-light);
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}
</style>
