# 设备模板信号组件集成说明

## 功能概述

DeviceTemplateSignal.vue 组件已成功集成了模板修改确认功能，当用户修改信号时会弹出确认对话框。

## 新增组件

### 1. DeviceTemplateConfirmDialog.vue
- 功能：显示模板修改确认对话框
- 显示受影响的设备列表
- 提供三个操作选项：取消、另存为新模板、更新原模板
- 支持"不再提醒"选项

### 2. DeviceTemplateCopyDialog.vue
- 功能：复制模板对话框
- 输入新模板名称和创建原因
- 调用后端API创建新模板

## 工作流程

1. **用户修改信号** → 触发 `handleCellChange`
2. **检查提醒状态** → 如果未设置"不再提醒"，显示确认对话框
3. **用户选择操作**：
   - **取消**：恢复原数据，不执行更新
   - **更新原模板**：直接在原模板上执行更新
   - **另存为新模板**：先复制模板，再在新模板上执行更新
4. **执行更新** → 调用后端API，更新成功后刷新界面

## API接口

- `getDevicesByTemplateId(templateId)` - 获取受影响的设备列表
- `copyTemplate(params)` - 复制模板
- `updateSignal(signalData)` - 更新信号
- `deviceTemplateService` - 管理"不再提醒"状态

## 使用方法

组件已完全集成到 DeviceTemplateSignal.vue 中，无需额外配置。用户在修改信号时会自动触发确认流程。

## 注意事项

1. 确认对话框只在首次修改时显示，用户选择"不再提醒"后将直接执行更新
2. 新模板创建后，更新将在新模板上执行，原模板保持不变
3. 取消操作会恢复到修改前的状态 