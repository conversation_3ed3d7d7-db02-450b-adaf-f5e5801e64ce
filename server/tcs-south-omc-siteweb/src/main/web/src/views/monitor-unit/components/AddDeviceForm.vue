<template>
  <div class="add-device-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
    >
      <el-form-item label="设备模板文件" prop="equipmentTemplateId">
        <div class="template-select-group">
          <el-input
            v-model="templateName"
            readonly
            placeholder="请选择设备模板"
            style="flex: 1"
          />
          <el-button type="primary" @click="handleSelectTemplate"
            >选择</el-button
          >
        </div>
      </el-form-item>

      <el-form-item label="所属监控单元" prop="monitorUnitId">
        <el-select
          v-model="formData.monitorUnitId"
          placeholder="请选择监控单元"
          @change="handleMonitorUnitChange"
        >
          <el-option
            v-for="item in muList.filter(
              item => item && item.monitorUnitId != null
            )"
            :key="item.monitorUnitId"
            :label="item.monitorUnitName || '未命名监控单元'"
            :value="item.monitorUnitId"
          />
        </el-select>
      </el-form-item>

      <div class="form-row">
        <el-form-item
          label="设备名称"
          prop="equipmentName"
          class="form-item-half"
        >
          <el-input
            v-model="formData.equipmentName"
            placeholder="请输入设备名称"
            maxlength="128"
          />
        </el-form-item>
        <el-form-item
          label="是否实例化"
          prop="instantiated"
          class="form-item-half"
        >
          <el-checkbox v-model="formData.instantiated" />
        </el-form-item>
      </div>


      <el-form-item label="上一级设备" prop="parentEquipmentId">
        <el-select
          v-model="formData.parentEquipmentId"
          placeholder="请选择上一级设备"
          clearable
          :disabled="!protocolCode"
        >
          <el-option
            v-for="item in deviceList.filter(
              item => item && item.equipmentId != null
            )"
            :key="item.equipmentId"
            :label="item.equipmentName || '未命名设备'"
            :value="item.equipmentId"
          />
        </el-select>
      </el-form-item>

      <el-divider />

      <!-- 端口信息 -->
      <div class="form-row">
        <el-form-item label="端口号" prop="portNo" class="form-item-half">
          <el-input
            v-model="formData.portNo"
            placeholder="请输入端口号"
            @change="handlePortChange"
          />
        </el-form-item>
        <el-form-item
          :label="`${protocolCode ? 'COM' + formData.portNo : ''}端口类型`"
          prop="portType"
          class="form-item-half"
        >
          <div class="port-type-group">
            <el-select
              v-model="formData.portType"
              placeholder="请选择端口类型"
              :disabled="hasPort"
              style="width: 120px"
              @change="handlePortTypeChange"
            >
              <el-option
                v-for="item in showTypes"
                :key="item.typeId"
                :label="item.typeName"
                :value="item.typeId"
              />
            </el-select>
            <el-switch
              v-model="isAll"
              inactive-text="所有"
              @change="handleShowAllTypes"
            />
          </div>
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item
          label="父采集单元"
          prop="parentSamplerUnitId"
          class="form-item-half"
        >
          <el-select
            v-model="formData.parentSamplerUnitId"
            placeholder="请选择父采集单元"
            :disabled="!hasPort"
          >
            <el-option
              v-for="item in pSamplerUnitList.filter(
                item => item && item.samplerUnitId != null
              )"
              :key="item.samplerUnitId"
              :label="item.samplerUnitName || '未命名采集单元'"
              :value="item.samplerUnitId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="`${protocolCode ? 'COM' + formData.portNo : ''}电话号码`"
          prop="phoneNumber"
          class="form-item-half"
        >
          <el-input
            v-model="formData.phoneNumber"
            placeholder="请输入电话号码"
            :readonly="hasPort"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="端口" prop="portId" class="form-item-half">
          <el-select
            v-model="formData.portId"
            placeholder="请选择端口"
            disabled
          >
            <el-option
              v-for="item in currentMuPortList"
              :key="item.portId || item.id"
              :label="item.portName || `COM${item.portNo}`"
              :value="item.portId || item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="`${protocolCode ? 'COM' + formData.portNo : ''}端口设置`"
          prop="setting"
          class="form-item-half"
        >
          <el-input
            v-model="formData.setting"
            placeholder="请输入端口设置"
            :readonly="hasPort"
          />
        </el-form-item>
      </div>

      <el-divider />

      <!-- 采集单元信息 -->
      <div class="form-row">
        <el-form-item
          label="采集单元名称"
          prop="samplerUnitId"
          class="form-item-half"
        >
          <el-select
            v-model="formData.samplerUnitId"
            placeholder="请选择采集单元"
            clearable
            @change="handleSamplerUnitChange"
          >
            <el-option
              v-for="item in samplerUnitList"
              :key="item.samplerUnitId"
              :label="item.samplerUnitName || '未命名采集单元'"
              :value="item.samplerUnitId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="采集周期"
          prop="spUnitInterval"
          class="form-item-half"
        >
          <el-input
            v-model="formData.spUnitInterval"
            placeholder="请输入采集周期"
            :readonly="hasSamplerUnit"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item
          label="采集单元地址"
          prop="samplerAddress"
          class="form-item-half"
        >
          <div class="address-group">
            <el-input
              v-model="formData.samplerAddress"
              placeholder="请输入地址"
              :readonly="hasSamplerUnit"
              style="flex: 1"
            />
            <span class="address-hint">0~32767</span>
          </div>
        </el-form-item>
        <el-form-item
          label="采集单元动态库"
          prop="dllPath"
          class="form-item-half"
        >
          <el-input
            v-model="formData.dllPath"
            placeholder="请输入动态库路径"
            :readonly="hasSamplerUnit"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item
          label="采集单元类型"
          prop="samplerType"
          class="form-item-half"
        >
          <el-select
            v-model="formData.samplerType"
            placeholder="请选择采集单元类型"
            disabled
          >
            <el-option
              v-for="item in samplers"
              :key="item.samplerId"
              :label="item.samplerName"
              :value="item.samplerId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="电话号码"
          prop="spPhoneNumber"
          class="form-item-half"
        >
          <el-input
            v-model="formData.spPhoneNumber"
            placeholder="请输入电话号码"
            :readonly="hasSamplerUnit"
          />
        </el-form-item>
      </div>
    </el-form>

    <!-- 选择模板弹框 -->
    <el-dialog
      v-model="showSelectTemplateDialog"
      title="选择模板"
      width="800px"
    >
      <SelectTemplateForm
        ref="selectTemplateFormRef"
        @confirm="handleTemplateConfirm"
      />
      <template #footer>
        <el-button @click="showSelectTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleTemplateDialogConfirm"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import SelectTemplateForm from "./SelectTemplateForm.vue";
import { getMonitorUnitDevices } from "@/api/monitor-unit";
import {
  getPortSamplerUnits,
  getMonitorUnitPorts,
  getSamplerUnitConfig,
  createDevice,
  createSamplerUnit,
  getEquipmentTemplate,
  type DeviceInfo,
  type SamplerUnitInfo
} from "@/api/device";
import { createPort, getPortTypes, type PortInfo } from "@/api/port";
import { getSamplers, getSamplerUnits } from "@/api/station";
import _ from "lodash";

interface Props {
  monitorUnit?: any;
  currentNode?: any;
  muList?: any[];
  currentPort?: any;
  currentMuId?: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  success: [];
}>();

// 表单引用
const formRef = ref<FormInstance>();
const selectTemplateFormRef = ref();

// 控制状态
const showSelectTemplateDialog = ref(false);
const templateName = ref("");
const templateId = ref<number>(0);
const protocolCode = ref("");
const hasPort = ref(false);
const hasSamplerUnit = ref(false);
const isAll = ref(false);
const defaultPortNo = ref(1);

// 数据列表
const muList = ref<any[]>([]);
const deviceList = ref<any[]>([]);
const portTypeList = ref<any[]>([]);
const showTypes = ref<any[]>([]);
const pSamplerUnitList = ref<any[]>([]);
const samplerUnitList = ref<any[]>([]);
const samplers = ref<any[]>([]);
const currentMuPortList = ref<any[]>([]);

// 表单数据
const formData = reactive({
  // 基本信息
  stationId: null,
  equipmentTemplateId: null,
  monitorUnitId: null,
  equipmentName: "",
  instantiated: false,
  parentEquipmentId: null,

  // 端口信息
  portNo: "1",
  portType: null,
  parentSamplerUnitId: null,
  phoneNumber: "",
  portId: null,
  setting: "9600,n,8,1",

  // 采集单元信息
  samplerUnitId: "0",
  spUnitInterval: "2",
  samplerAddress: "",
  dllPath: "",
  samplerType: null,
  spPhoneNumber: ""
});

// 表单验证规则
const formRules = reactive({
  equipmentTemplateId: [
    { required: true, message: "请选择设备模板", trigger: "change" }
  ],
  monitorUnitId: [
    { required: true, message: "请选择监控单元", trigger: "change" }
  ],
  equipmentName: [
    { required: true, message: "请输入设备名称", trigger: "blur" },
    { pattern: /^[^<>'\\]+$/, message: "设备名称格式错误", trigger: "blur" }
  ] as Array<{
    required?: boolean;
    pattern?: RegExp;
    message: string;
    trigger: string;
  }>,
  portNo: [{ required: true, message: "请输入端口号", trigger: "blur" }],
  portType: [{ required: true, message: "请选择端口类型", trigger: "change" }],
  samplerUnitId: [
    { required: true, message: "请选择采集单元", trigger: "change" }
  ],
  spUnitInterval: [
    { required: true, message: "请输入采集周期", trigger: "blur" }
  ],
  samplerAddress: [
    { required: true, message: "请输入采集单元地址", trigger: "blur" }
  ],
  dllPath: [{ required: true, message: "请输入动态库路径", trigger: "blur" }],
  samplerType: [
    { required: true, message: "请选择采集单元类型", trigger: "change" }
  ]
});

// 初始化数据
const initData = async () => {
  if (props.currentNode) {
    formData.stationId = props.currentNode.stationId;
  }

  if (props.muList) {
    muList.value = props.muList;
    if (props.muList.length > 0) {
      formData.monitorUnitId =
        props.currentMuId || props.muList[0].monitorUnitId;
      handleMonitorUnitChange();
    }
  } else if (props.monitorUnit) {
    muList.value = [props.monitorUnit];
    formData.monitorUnitId = props.monitorUnit.monitorUnitId;
    handleMonitorUnitChange();
  }

  if (props.currentPort) {
    defaultPortNo.value = parseInt(
      props.currentPort.portName.replace("COM", "")
    );
    formData.portNo = defaultPortNo.value.toString();
  }


  // 初始化基础数据
  await getSamplersList();
  getPortTypesList();
  getAllMuDevices();

  // 初始化采集单元列表
  initSamplerUnitList();
};

// 获取采集器列表
const getSamplersList = async () => {
  try {
    const res = await getSamplers();
    if (res.code === 0) {
      samplers.value = res.data;
    }
  } catch (error) {
    console.error("获取采集器列表失败:", error);
  }
};

// 获取端口类型列表
const getPortTypesList = async () => {
  if (!muList.value.length) return;

  const currentMu = muList.value.find(
    mu => mu.monitorUnitId === formData.monitorUnitId
  );
  if (!currentMu?.monitorUnitCategory) return;

  try {
    const res = await getPortTypes(currentMu.monitorUnitCategory);
    if (res.state) {
      // 转换后端数据格式为前端期望的格式
      const transformedData = res.data.map((item: any) => ({
        typeId: item.itemId,
        typeName: item.itemValue,
        order: parseInt(item.extendField1) || 0
      }));
      portTypeList.value = transformedData.sort(
        (a: any, b: any) => a.order - b.order
      );
      updateShowTypes();
    }
  } catch (error) {
    console.error("获取端口类型失败:", error);
  }
};

// 更新显示的端口类型
const updateShowTypes = () => {
  if (isAll.value) {
    showTypes.value = _.clone(portTypeList.value).sort(
      (a: any, b: any) => a.order - b.order
    );
  } else {
    showTypes.value = _.clone(portTypeList.value)
      .filter((s: any) => s.order < 50)
      .sort((a: any, b: any) => a.order - b.order);
  }
};

// 获取所有监控单元设备
const getAllMuDevices = async () => {
  if (!muList.value.length) return;

  try {
    const promises = muList.value.map(mu =>
      getMonitorUnitDevices(mu.monitorUnitId)
    );
    const results = await Promise.all(promises);

    deviceList.value = [];
    results.forEach(res => {
      if (res.code === 0 && res.data) {
        deviceList.value.push(...res.data);
      }
    });
  } catch (error) {
    console.error("获取设备列表失败:", error);
  }
};

// 事件处理器
const handleSelectTemplate = () => {
  showSelectTemplateDialog.value = true;
};

const handleTemplateConfirm = (result: { templateId: number }) => {
  showSelectTemplateDialog.value = false;
  templateId.value = result.templateId;
  getEqTemp();
};

const handleTemplateDialogConfirm = () => {
  if (selectTemplateFormRef.value) {
    selectTemplateFormRef.value.confirm();
  }
};

// 获取设备模板详情
const getEqTemp = async () => {
  try {
    const res = await getEquipmentTemplate(templateId.value);
    if (res.code === 0) {
      const template = res.data;
      formData.equipmentTemplateId = template.equipmentTemplateId;
      templateName.value = template.equipmentTemplateName;
      protocolCode.value = template.protocolCode;

      // 设置设备名称后面的编号
      const sameTemps = deviceList.value.filter(
        s =>
          s.equipmentTemplateId === template.equipmentTemplateId &&
          s.monitorUnitId === formData.monitorUnitId
      );
      const newIndex = sameTemps.length + 1;
      formData.equipmentName = `${template.equipmentTemplateName}-${newIndex}#`;

      // 设置采集单元类型
      const selectedSampler = samplers.value.find(
        s => s.protocolCode === template.protocolCode
      );
      if (selectedSampler) {
        setSamplerUnitType(selectedSampler);
      }

      // 设置端口相关
      formData.portNo = defaultPortNo.value.toString();
      const port = currentMuPortList.value.find(
        s => s.portNo.toString() === formData.portNo.toString()
      );

      if (port) {
        // 端口已存在
        Object.assign(formData, _.cloneDeep(port));
        formData.portNo = formData.portNo;

        const cport = portTypeList.value.find(
          (s: any) => s.typeId === formData.portType
        );
        isAll.value = cport && cport.order > 50;
        updateShowTypes();
        hasPort.value = true;

        await getPortSamplerList();
      } else {
        // 新端口
        hasPort.value = false;
        isAll.value = false;
        updateShowTypes();

        formData.setting = "9600,n,8,1";
        formData.portType = 1;
        pSamplerUnitList.value = [];

        // 创建新采集单元
        const selectedSampler = samplers.value.find(
          s => s.protocolCode === protocolCode.value
        );
        if (selectedSampler) {
          const newSU = {
            samplerUnitId: "0",
            samplerUnitName: `1#${selectedSampler.samplerName}`,
            spUnitInterval: "2"
          };
          samplerUnitList.value = [newSU];
          formData.samplerUnitId = "0";
          formData.spUnitInterval = "2";
          formData.samplerAddress = "1";
          setSamplerUnitType(selectedSampler);
        }
      }
    }
  } catch (error) {
    console.error("获取设备模板失败:", error);
    ElMessage.error("获取设备模板失败");
  }
};

const handleMonitorUnitChange = async () => {

  // 重置相关状态
  hasPort.value = false;
  hasSamplerUnit.value = false;
  samplerUnitList.value = [];
  pSamplerUnitList.value = [];
  currentMuPortList.value = [];

  // 重置表单字段
  formData.portId = null;
  formData.samplerUnitId = "0";
  formData.parentSamplerUnitId = null;

  // 重新获取端口类型和监控单元端口列表
  await getPortTypesList();
  await getMuPortList();

  // 重新初始化采集单元列表
  initSamplerUnitList();
};

const handlePortChange = async (portNo: string) => {
  if (!portNo || !protocolCode.value) return;

  const port = currentMuPortList.value.find(
    s => s.portNo.toString() === portNo.toString().trim()
  );

  if (port) {
    // 端口已存在
    Object.assign(formData, {
      ...port,
      portNo: portNo
    });

    const cport = portTypeList.value.find(
      (s: any) => s.typeId === formData.portType
    );
    isAll.value = cport && cport.order > 50;
    updateShowTypes();
    hasPort.value = true;

    await getPortSamplerList();
  } else {
    // 新端口
    hasPort.value = false;
    isAll.value = false;
    updateShowTypes();

    formData.setting = "9600,n,8,1";
    formData.portType = 1;
    pSamplerUnitList.value = [];

    // 创建新采集单元
    const selectedSampler = samplers.value.find(
      s => s.protocolCode === protocolCode.value
    );
    if (selectedSampler) {
      const newSU = {
        samplerUnitId: "0",
        samplerUnitName: `1#${selectedSampler.samplerName}`,
        spUnitInterval: "2"
      };
      samplerUnitList.value = [newSU];
      formData.samplerUnitId = "0";
      formData.spUnitInterval = "2";
      formData.samplerAddress = "1";
      setSamplerUnitType(selectedSampler);
    }
  }
};

const handlePortTypeChange = (typeId: number) => {
  // 根据端口类型设置默认配置
  const settingsMap: Record<number, string> = {
    1: "9600,n,8,1",
    4: "9600,n,8,1",
    7: "9600,n,8,1",
    8: "9600,n,8,1",
    11: "9600,n,8,1",
    12: "9600,n,8,1",
    16: "9600,n,8,1",
    2: "0.0.0.0:0",
    3: "0.0.0.0:0",
    5: "0.0.0.0:0",
    9: "0.0.0.0:0",
    10: "0.0.0.0:0",
    6: "127.0.0.1:7070",
    13: "16002",
    14: "16002",
    15: "16002",
    31: "udp,:8080,",
    32: "127.0.0.1/2003",
    33: "127.0.0.1/public:private",
    34: "comm_host_dev.so",
    35: "comm_io_dev.so"
  };

  formData.setting = settingsMap[typeId] || "";
};

const handleShowAllTypes = (showAll: boolean) => {
  isAll.value = showAll;
  updateShowTypes();
};

const handleSamplerUnitChange = async (samplerUnitId: string) => {
  if (!samplerUnitId) return;

  if (samplerUnitId === "0") {
    // 新采集单元
    hasSamplerUnit.value = false;
    const selectedSampler = samplers.value.find(
      s => s.protocolCode === protocolCode.value
    );
    if (selectedSampler) {
      setSamplerUnitType(selectedSampler);
      // 重新设置采集单元名称
      const selectedUnit = samplerUnitList.value.find(
        unit => unit.samplerUnitId === "0"
      );
      if (selectedUnit) {
        selectedUnit.samplerUnitName = `1#${selectedSampler.samplerName}`;
      }
    }
  } else {
    // 现有采集单元
    hasSamplerUnit.value = true;
    try {
      const res = await getSamplerUnitConfig(samplerUnitId);
      if (res.code === 0) {
        const data = res.data;
        const selectedSampler = samplers.value.find(
          s => s.samplerId === data.samplerId
        );
        if (selectedSampler) {
          setSamplerUnitType(selectedSampler);
        }
        formData.spUnitInterval = data.spUnitInterval;
        formData.samplerAddress = data.address;
        formData.spPhoneNumber = data.phoneNumber || "";
      }
    } catch (error) {
      console.error("获取采集单元配置失败:", error);
    }
  }
};

// 设置采集单元类型
const setSamplerUnitType = (selectedSampler: any) => {
  formData.samplerType = selectedSampler.samplerId;
  formData.dllPath = selectedSampler.dllPath;

  // 根据监控单元类型调整动态库路径
  const currentMu = muList.value.find(
    s => s.monitorUnitId === formData.monitorUnitId
  );
  if (currentMu && formData.dllPath) {
    if (formData.dllPath.includes(".dll")) {
      formData.dllPath = formData.dllPath.split(".")[0] + ".so";
    }
  }
};

// 获取监控单元端口列表
const getMuPortList = async () => {
  if (!formData.monitorUnitId) return;

  try {
    const res = await getMonitorUnitPorts(formData.monitorUnitId);
    if (res.code === 0) {
      currentMuPortList.value = res.data || [];

      // 检查当前端口是否存在
      if (formData.portNo) {
        const port = currentMuPortList.value.find(
          s => s.portNo.toString() === formData.portNo.toString()
        );
        if (port) {
          Object.assign(formData, port);
          hasPort.value = true;
        }
      }
    }
  } catch (error) {
    console.error("获取端口列表失败:", error);
  }
};

// 获取端口采集单元列表
const getPortSamplerList = async () => {
  if (!formData.portId) return;

  try {
    const res = await getPortSamplerUnits(formData.portId);
    if (res.code === 0) {
      pSamplerUnitList.value = res.data || [];

      // 创建新采集单元选项
      const selectedSampler = samplers.value.find(
        s => s.protocolCode === protocolCode.value
      );
      if (selectedSampler) {
        const sus = pSamplerUnitList.value.filter(
          s => s.samplerId === selectedSampler.samplerId
        );
        const newIndex = sus.length + 1;
        const newSU = {
          samplerUnitId: "0",
          samplerUnitName: `${newIndex}#${selectedSampler.samplerName}`,
          spUnitInterval: "2"
        };

        samplerUnitList.value = [newSU, ..._.cloneDeep(pSamplerUnitList.value)];
        formData.samplerUnitId = "0";
        formData.spUnitInterval = "2";
        formData.samplerAddress = (
          pSamplerUnitList.value.length + 1
        ).toString();
        setSamplerUnitType(selectedSampler);
      }
    }
  } catch (error) {
    console.error("获取端口采集单元列表失败:", error);
  }
};

// 提交表单
const submit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 验证必填项
    if (
      !formData.equipmentTemplateId ||
      !formData.portNo ||
      !formData.portType ||
      !formData.equipmentName?.trim() ||
      !formData.samplerUnitId ||
      !formData.spUnitInterval ||
      !formData.samplerAddress ||
      !formData.dllPath?.trim() ||
      !formData.samplerType
    ) {
      ElMessage.warning("请输入必填项");
      return;
    }

    // 实际提交数据流程
    if (!hasPort.value) {
      // 1. 创建端口
      await addPort();
    } else if (!hasSamplerUnit.value) {
      // 2. 创建采集单元
      await addSamplerUnit();
    } else {
      // 3. 直接创建设备
      await addDevice();
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 创建设备
const addDevice = async () => {
  try {
    const deviceData: DeviceInfo = {
      equipmentId: "", // 由后端生成
      equipmentName: formData.equipmentName,
      equipmentTemplateId: formData.equipmentTemplateId!,
      monitorUnitId: formData.monitorUnitId!,
      stationId: formData.stationId!,
      instantiated: formData.instantiated,
      parentEquipmentId: formData.parentEquipmentId || undefined,
      samplerUnitId: formData.samplerUnitId!,
      description: "",
      workStationId: undefined,
      houseId: props.currentNode?.houseId
    };

    const res = await createDevice(deviceData);
    if (res.state) {
      emit("success");
    } else {
      ElMessage.error(res.err_msg || "创建设备失败");
    }
  } catch (error) {
    console.error("创建设备失败:", error);
    ElMessage.error("创建设备失败");
  }
};

// 创建端口
const addPort = async () => {
  try {
    const portData: PortInfo = {
      portNo: parseInt(formData.portNo),
      portName: `COM${formData.portNo}`,
      portType: formData.portType!,
      setting: formData.setting,
      phoneNumber: formData.phoneNumber || undefined,
      monitorUnitId: formData.monitorUnitId!.toString(),
      linkSamplerUnitId: 0,
      description: undefined
    };

    const res = await createPort(portData);
    if (res.state) {
      // 更新端口信息
      Object.assign(formData, res.data);
      hasPort.value = true;

      if (!hasSamplerUnit.value) {
        await addSamplerUnit();
      } else {
        await addDevice();
      }
    } else {
      ElMessage.error(res.err_msg || "创建端口失败");
    }
  } catch (error) {
    console.error("创建端口失败:", error);
    ElMessage.error("创建端口失败");
  }
};

// 创建采集单元
const addSamplerUnit = async () => {
  try {
    // 构建采集单元名称
    let samplerUnitName = "";
    if (!hasPort.value) {
      const selectedSampler = samplers.value.find(
        s => s.protocolCode === protocolCode.value
      );
      samplerUnitName = `1#${selectedSampler?.samplerName}`;
    } else {
      const selectedUnit = samplerUnitList.value.find(
        s => s.samplerUnitId === "0"
      );
      samplerUnitName = selectedUnit?.samplerUnitName || "";
    }

    // 获取选中的采集器信息
    const selectedSampler = samplers.value.find(
      s => s.samplerId === formData.samplerType
    );

    const samplerUnitData: Partial<SamplerUnitInfo> = {
      samplerUnitName,
      samplerId: formData.samplerType!,
      samplerName: selectedSampler?.samplerName || "",
      samplerType: selectedSampler?.samplerType || 0,
      address: formData.samplerAddress,
      spUnitInterval: formData.spUnitInterval,
      phoneNumber: formData.spPhoneNumber || undefined,
      dllPath: formData.dllPath,
      monitorUnitId: formData.monitorUnitId!,
      portId: formData.portId!,
      parentSamplerUnitId: 0,
      connectState: 0,
      description: "",
      updateTime: new Date().toISOString().slice(0, 19).replace("T", " ")
    };

    const res = await createSamplerUnit(samplerUnitData);
    if (res.state) {
      // 更新采集单元信息
      formData.samplerUnitId = res.data.samplerUnitId;
      hasSamplerUnit.value = true;


      await addDevice();
    } else {
      ElMessage.error(res.err_msg || "创建采集单元失败");
    }
  } catch (error) {
    console.error("创建采集单元失败:", error);
    ElMessage.error("创建采集单元失败");
  }
};

// 初始化采集单元列表
const initSamplerUnitList = () => {
  if (samplers.value.length > 0) {
    // 创建默认的新采集单元选项
    const newSU = {
      samplerUnitId: "0",
      samplerUnitName: "请选择设备模板",
      spUnitInterval: "2"
    };
    samplerUnitList.value = [newSU];
    console.log("初始化采集单元列表:", samplerUnitList.value);
  }
};

// 初始化
onMounted(async () => {
  await initData();
});

// 暴露方法
defineExpose({
  submit
});
</script>

<style scoped>
.add-device-form {
  max-height: 70vh;
  overflow-y: auto;
}

.template-select-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-item-half {
  flex: 1;
}

.port-type-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.address-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.address-hint {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  white-space: nowrap;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 14px;
}

:deep(.el-switch) {
  --el-switch-on-color: #007fff;
}

:deep(.el-divider) {
  margin: 20px 0;
}
</style>
