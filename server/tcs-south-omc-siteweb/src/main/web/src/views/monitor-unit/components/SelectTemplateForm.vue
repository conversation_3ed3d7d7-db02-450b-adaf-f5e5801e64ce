<template>
  <div class="select-template-form">
    <div class="search-section">
      <el-input
        v-model="searchValue"
        placeholder="输入关键字查询"
        clearable
        @input="handleSearchChange"
        @keyup.enter="selectNode"
      >
        <template #suffix>
          <el-icon
            v-if="searchValue"
            class="clear-icon"
            @click="searchValue = ''"
          >
            <CircleClose />
          </el-icon>
        </template>
      </el-input>
    </div>

    <div class="tree-section">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="treeProps"
        :default-expanded-keys="expandedKeys"
        :filter-node-method="filterNode"
        :highlight-current="true"
        :current-node-key="currentKey"
        node-key="key"
        style="height: 400px; overflow-y: auto; border: 1px solid #dcdfe6"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span class="tree-node" @dblclick="handleNodeDblClick(data)">
            <el-icon v-if="!data.template" class="folder-icon">
              <Folder />
            </el-icon>
            <el-icon v-else class="template-icon">
              <Document />
            </el-icon>
            <span class="node-label">{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from "vue";
import { ElMessage, type ElTree } from "element-plus";
import { CircleClose, Folder, Document } from "@element-plus/icons-vue";
import { getTemplateTree } from "@/api/device-template";

const emit = defineEmits<{
  confirm: [result: { templateId: number }];
}>();

// 响应式数据
const treeRef = ref<InstanceType<typeof ElTree>>();
const searchValue = ref("");
const searchChanged = ref(false);
const matchNodes = ref<any[]>([]);
const matchIndex = ref(0);
const treeIndex = ref(0);
const treeData = ref<any[]>([]);
const currentKey = ref<string>("");
const expandedKeys = ref<string[]>([]);
const templateId = ref<number>(0);

// 树配置
const treeProps = {
  children: "children",
  label: "title"
};

// 加载模板树
const loadTemplateTree = async () => {
  try {
    const response = await getTemplateTree();
    const nodes = response.data;

    if (nodes && nodes.length > 0) {
      buildTree(nodes);
      nodes[0].expanded = true;
      treeData.value = nodes;

      // 恢复之前的选中状态
      const savedSelectedKeys = sessionStorage.getItem("selEqTmp-selectedKeys");
      if (savedSelectedKeys) {
        const selectedKeys = JSON.parse(savedSelectedKeys);
        if (selectedKeys.length > 0) {
          currentKey.value = selectedKeys[0];
          templateId.value = parseInt(selectedKeys[0]);
        }
      }

      const savedExpandedKeys = sessionStorage.getItem("selEqTmp-expendKeys");
      if (savedExpandedKeys) {
        expandedKeys.value = JSON.parse(savedExpandedKeys);
      }
    }
  } catch (error) {
    console.error("加载模板树失败:", error);
    ElMessage.error("加载模板树失败");
  }
};

// 构建树结构
const buildTree = (nodes: any[]) => {
  for (let i = 0; i < nodes.length; i++) {
    nodes[i].title = nodes[i].name;
    nodes[i].key = nodes[i].id.toString();

    if (nodes[i].children && nodes[i].children.length > 0) {
      buildTree(nodes[i].children);
    } else {
      nodes[i].isLeaf = true;
    }
  }
};

// 搜索相关
const handleSearchChange = (value: string) => {
  searchValue.value = value;
  searchChanged.value = true;

  if (treeRef.value) {
    treeRef.value.filter(value);
  }
};

const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.title.toLowerCase().includes(value.toLowerCase());
};

const selectNode = () => {
  if (searchChanged.value) {
    searchChanged.value = false;
    matchNodes.value = [];
    matchIndex.value = 0;
    findNodesWithTitle(
      searchValue.value.trim(),
      treeData.value,
      matchNodes.value
    );

    if (matchNodes.value.length > 0) {
      const node = matchNodes.value[matchIndex.value];
      currentKey.value = node.key;
      scrollToSelectedNode(node);
    }
  } else {
    matchIndex.value++;
    if (matchIndex.value >= matchNodes.value.length) {
      matchIndex.value = 0;
    }

    if (matchIndex.value < matchNodes.value.length) {
      const node = matchNodes.value[matchIndex.value];
      currentKey.value = node.key;
      scrollToSelectedNode(node);
    }
  }
};

const findNodesWithTitle = (title: string, nodes: any[], result: any[]) => {
  nodes.forEach(node => {
    if (node.title.toLowerCase().includes(title.toLowerCase())) {
      result.push(node);
    }
    if (node.children && node.children.length > 0) {
      findNodesWithTitle(title, node.children, result);
    }
  });
};

const scrollToSelectedNode = (node: any) => {
  // 构建索引
  treeIndex.value = 0;
  buildIndexTree(treeData.value);

  nextTick(() => {
    if (treeRef.value) {
      treeRef.value.setCurrentKey(node.key);
    }
  });
};

const buildIndexTree = (list: any[]) => {
  list.forEach((item: any) => {
    item.index = treeIndex.value;
    treeIndex.value++;
    if (item.expanded && item.children && item.children.length > 0) {
      buildIndexTree(item.children);
    }
  });
};

// 节点操作
const handleNodeClick = (data: any) => {
  if (data.template) {
    templateId.value = parseInt(data.key);
    currentKey.value = data.key;
  } else {
    templateId.value = 0;
  }
};

// 双击节点 - 直接选择并关闭
const handleNodeDblClick = (data: any) => {
  if (data.template) {
    // 保存展开状态
    const expandedNodeKeys = treeRef.value?.store.nodesMap;
    const expandedKeyList: string[] = [];

    if (expandedNodeKeys) {
      Object.keys(expandedNodeKeys).forEach(key => {
        if (expandedNodeKeys[key].expanded) {
          expandedKeyList.push(key);
        }
      });
    }

    sessionStorage.setItem("selEqTmp-selectedKeys", JSON.stringify([data.key]));
    sessionStorage.setItem(
      "selEqTmp-expendKeys",
      JSON.stringify(expandedKeyList)
    );

    templateId.value = parseInt(data.key);
    emit("confirm", { templateId: templateId.value });
  }
};

// 确认选择
const confirm = () => {
  if (!templateId.value) {
    ElMessage.warning("请先选择一个设备模板");
    return false;
  }

  // 保存选中和展开状态
  const expandedNodeKeys = treeRef.value?.store.nodesMap;
  const expandedKeyList: string[] = [];

  if (expandedNodeKeys) {
    Object.keys(expandedNodeKeys).forEach(key => {
      if (expandedNodeKeys[key].expanded) {
        expandedKeyList.push(key);
      }
    });
  }

  sessionStorage.setItem(
    "selEqTmp-selectedKeys",
    JSON.stringify([currentKey.value])
  );
  sessionStorage.setItem(
    "selEqTmp-expendKeys",
    JSON.stringify(expandedKeyList)
  );

  emit("confirm", { templateId: templateId.value });
  return true;
};

// 监听搜索值变化
watch(searchValue, val => {
  if (treeRef.value) {
    treeRef.value.filter(val);
  }
});

// 初始化
onMounted(() => {
  loadTemplateTree();
});

// 暴露方法
defineExpose({
  confirm
});
</script>

<style scoped>
.select-template-form {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.search-section {
  margin-bottom: 16px;
}

.tree-section {
  flex: 1;
  overflow: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  width: 100%;
  cursor: pointer;
}

.folder-icon,
.template-icon {
  margin-right: 6px;
  font-size: 16px;
}

.folder-icon {
  color: var(--el-color-primary);
}

.template-icon {
  color: var(--el-color-success);
}

.node-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clear-icon {
  cursor: pointer;
  color: var(--el-text-color-secondary);
}

.clear-icon:hover {
  color: var(--el-text-color-primary);
}

:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
</style>
