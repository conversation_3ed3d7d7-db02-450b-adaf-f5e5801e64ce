<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="600px"
    destroy-on-close
    :close-on-click-modal="false"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <div class="space-y-6">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="form-container"
      >
        <el-form-item label="条目ID" prop="itemId">
          <el-input
            v-model="formData.itemId"
            :disabled="!!formData.entryItemId"
            placeholder="请输入条目ID"
            size="large"
          />
        </el-form-item>

        <el-form-item label="条目名称" prop="itemValue">
          <el-input
            v-model="formData.itemValue"
            placeholder="请输入条目名称"
            size="large"
          />
        </el-form-item>

        <el-form-item label="条目英文名" prop="itemAlias">
          <el-input
            v-model="formData.itemAlias"
            placeholder="请输入条目英文名"
            size="large"
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            placeholder="请输入描述"
            type="textarea"
            :rows="3"
            size="large"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button size="large" @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          size="large"
          @click="handleConfirm"
        >
          {{ loading ? "处理中..." : "确定" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import type { DictionaryItemData } from "@/api/system-category-management";
import { addItem, updateItem } from "@/api/system-category-management";

// Props
interface Props {
  visible: boolean;
  data: DictionaryItemData | number | null;
  typeId: number;
}

// Emits
const emit = defineEmits<{
  "update:visible": [value: boolean];
  confirm: [];
}>();

const props = defineProps<Props>();

// Form ref
const formRef = ref<FormInstance>();

// Loading state
const loading = ref(false);

// Form data
const formData = ref<DictionaryItemData>({
  entryId: 0,
  itemId: "",
  itemValue: "",
  itemAlias: "",
  description: ""
});

// Form rules
const rules: FormRules = {
  itemId: [{ required: true, message: "条目ID不能为空", trigger: "blur" }],
  itemValue: [{ required: true, message: "条目名称不能为空", trigger: "blur" }]
};

// Computed
const dialogTitle = computed(() => {
  return formData.value.entryItemId ? "修改字典项" : "新增字典项";
});

// Watch props.data to initialize form
watch(
  () => props.data,
  newData => {
    if (newData) {
      if (typeof newData === "number") {
        // 新增模式 - 使用传入的 typeId 作为 entryId
        formData.value = {
          entryId: props.typeId,
          itemId: "",
          itemValue: "",
          itemAlias: "",
          description: ""
        };
      } else {
        // 编辑模式 - 使用传入的 typeId 作为 entryId，确保一致性
        formData.value = {
          entryItemId: newData.entryItemId,
          entryId: props.typeId,
          itemId: newData.itemId,
          itemValue: newData.itemValue,
          itemAlias: newData.itemAlias || "",
          description: newData.description || ""
        };
      }
    }
  },
  { immediate: true }
);

// Methods
const handleClose = () => {
  emit("update:visible", false);
  resetForm();
};

const handleCancel = () => {
  emit("update:visible", false);
  resetForm();
};

const resetForm = () => {
  formRef.value?.resetFields();
  formData.value = {
    entryId: props.typeId,
    itemId: "",
    itemValue: "",
    itemAlias: "",
    description: ""
  };
};

const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;

    if (formData.value.entryItemId) {
      // 编辑模式
      await updateItem(formData.value);
    } else {
      // 新增模式
      await addItem(formData.value);
    }

    emit("update:visible", false);
    emit("confirm");
    resetForm();
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.form-container {
  padding: 20px 0;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  border-radius: 6px;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
  resize: vertical;
}
</style>
