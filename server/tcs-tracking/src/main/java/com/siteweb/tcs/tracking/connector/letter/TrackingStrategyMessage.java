package com.siteweb.tcs.tracking.connector.letter;

import com.siteweb.tcs.common.o11y.WindowLogItem;
import com.siteweb.tcs.tracking.dal.entity.TrackingStrategy;
import lombok.Data;

/**
 * 埋点策略消息
 * 用于在Actor之间传递埋点策略操作
 */
@Data
public class TrackingStrategyMessage implements WindowLogItem {
    
    /**
     * 操作类型
     */
    private OperationType operationType;
    
    /**
     * 策略ID
     */
    private Integer strategyId;
    
    /**
     * 策略对象
     */
    private TrackingStrategy strategy;
    
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE,     // 创建策略
        UPDATE,     // 更新策略
        DELETE,     // 删除策略
        ACTIVATE,   // 激活策略
        DEACTIVATE, // 停用策略
        PAUSE       // 暂停策略
    }
    
    /**
     * 获取日志字符串
     */
    @Override
    public String getWindowLogString() {
        return String.format(
                "TrackingStrategy[operation=%s, id=%d, name=%s]",
                operationType,
                strategyId,
                strategy != null ? strategy.getName() : "null"
        );
    }
}
