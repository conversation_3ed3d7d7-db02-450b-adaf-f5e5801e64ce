package com.siteweb.tcs.tracking.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.tracking.connector.letter.TrackingDataMessage;
import com.siteweb.tcs.tracking.dal.entity.TrackingData;
import com.siteweb.tcs.tracking.dal.entity.TrackingPoint;
import com.siteweb.tcs.tracking.dal.mapper.TrackingDataMapper;
import com.siteweb.tcs.tracking.dal.mapper.TrackingPointMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 埋点数据服务
 * 负责处理埋点数据的收集和查询
 *
 * 本服务实现了数据的缓存机制，提高频繁查询数据时的性能。
 * 缓存包括：
 * - 数据统计缓存
 * - 查询结果缓存
 */
@Slf4j
@Service
@CacheConfig(cacheNames = "trackingData")
public class TrackingDataService {

    @Autowired
    private TrackingDataMapper dataMapper;

    @Autowired
    private TrackingPointMapper pointMapper;

    @Autowired
    private ActorRef trackingGuard;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 本地缓存，用于高频访问的数据统计
    private final Map<String, Map<String, Object>> statisticsCache = new ConcurrentHashMap<>();
    private final Map<String, Long> cacheExpirationTimes = new ConcurrentHashMap<>();

    // 缓存过期时间（毫秒）
    private static final long CACHE_EXPIRATION = 5 * 60 * 1000; // 5分钟

    /**
     * 收集埋点数据
     */
    public void collectData(Integer strategyId, String pointCode, Map<String, Object> data,
                           String sourceId, String userId, String sessionId) {
        try {
            // 查找埋点点位
            TrackingPoint point = pointMapper.selectByStrategyIdAndCode(strategyId, pointCode);
            if (point == null) {
                log.warn("Tracking point not found: strategy={}, code={}", strategyId, pointCode);
                return;
            }

            // 创建埋点数据消息
            TrackingDataMessage message = new TrackingDataMessage();
            message.setStrategyId(strategyId);
            message.setPointId(point.getId());
            message.setPointCode(pointCode);
            message.setTimestamp(LocalDateTime.now());
            message.setSourceId(sourceId);
            message.setUserId(userId);
            message.setSessionId(sessionId);
            message.setData(data);

            // 发送消息
            trackingGuard.tell(message, ActorRef.noSender());

            // 保存到数据库
            saveTrackingData(message);

        } catch (Exception e) {
            log.error("Error collecting tracking data", e);
        }
    }

    /**
     * 保存埋点数据
     */
    private void saveTrackingData(TrackingDataMessage message) throws JsonProcessingException {
        TrackingData trackingData = new TrackingData();
        trackingData.setPointId(message.getPointId());
        trackingData.setStrategyId(message.getStrategyId());
        trackingData.setTimestamp(message.getTimestamp());
        trackingData.setSourceId(message.getSourceId());
        trackingData.setUserId(message.getUserId());
        trackingData.setSessionId(message.getSessionId());
        trackingData.setData(objectMapper.writeValueAsString(message.getData()));
        trackingData.setCreatedAt(LocalDateTime.now());

        dataMapper.insert(trackingData);
    }

    /**
     * 查询埋点数据
     *
     * @param strategyId 策略ID
     * @param pointId 点位ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param sourceId 来源ID
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 埋点数据列表
     */
    @Cacheable(key = "#strategyId + '_' + #pointId + '_' + #startTime + '_' + #endTime + '_' + #sourceId + '_' + #userId + '_' + #limit")
    public List<TrackingData> queryData(Integer strategyId, Integer pointId,
                                       LocalDateTime startTime, LocalDateTime endTime,
                                       String sourceId, String userId, Integer limit) {
        log.debug("Cache miss: Querying tracking data from database");
        return dataMapper.queryData(strategyId, pointId, startTime, endTime, sourceId, userId, limit);
    }

    /**
     * 获取埋点数据统计
     *
     * @param strategyId 策略ID
     * @param pointId 点位ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据
     */
    @Cacheable(key = "'stats_' + #strategyId + '_' + #pointId + '_' + #startTime + '_' + #endTime")
    public Map<String, Object> getDataStatistics(Integer strategyId, Integer pointId,
                                               LocalDateTime startTime, LocalDateTime endTime) {
        // 生成缓存键
        String cacheKey = generateStatisticsCacheKey(strategyId, pointId, startTime, endTime);

        // 检查本地缓存
        Map<String, Object> cachedStatistics = getFromLocalCache(cacheKey);
        if (cachedStatistics != null) {
            log.debug("Local cache hit for statistics: {}", cacheKey);
            return cachedStatistics;
        }

        log.debug("Cache miss: Calculating statistics from database");
        Map<String, Object> statistics = new HashMap<>();

        // 获取数据总量
        long totalCount = dataMapper.countData(strategyId, pointId, startTime, endTime, null, null);
        statistics.put("totalCount", totalCount);

        // 获取来源分布
        List<Map<String, Object>> sourceDistribution = dataMapper.getSourceDistribution(strategyId, pointId, startTime, endTime);
        statistics.put("sourceDistribution", sourceDistribution);

        // 获取用户分布
        List<Map<String, Object>> userDistribution = dataMapper.getUserDistribution(strategyId, pointId, startTime, endTime);
        statistics.put("userDistribution", userDistribution);

        // 获取时间分布
        List<Map<String, Object>> timeDistribution = dataMapper.getTimeDistribution(strategyId, pointId, startTime, endTime);
        statistics.put("timeDistribution", timeDistribution);

        // 更新本地缓存
        updateLocalCache(cacheKey, statistics);

        return statistics;
    }

    /**
     * 获取点位数据统计
     *
     * @param strategyId 策略ID
     * @param pointCode 点位编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 统计数据
     */
    @Cacheable(key = "'stats_code_' + #strategyId + '_' + #pointCode + '_' + #startTime + '_' + #endTime")
    public Map<String, Object> getDataStatisticsByCode(Integer strategyId, String pointCode,
                                                    LocalDateTime startTime, LocalDateTime endTime,
                                                    Integer limit) {
        // 查找点位
        TrackingPoint point = pointMapper.selectByStrategyIdAndCode(strategyId, pointCode);
        if (point == null) {
            log.warn("Tracking point not found: strategy={}, code={}", strategyId, pointCode);
            return new HashMap<>();
        }

        return getDataStatistics(strategyId, point.getId(), startTime, endTime);
    }

    /**
     * 生成统计缓存键
     */
    private String generateStatisticsCacheKey(Integer strategyId, Integer pointId, LocalDateTime startTime, LocalDateTime endTime) {
        return "stats_" +
               (strategyId != null ? strategyId : "all") + "_" +
               (pointId != null ? pointId : "all") + "_" +
               (startTime != null ? startTime.toString() : "0") + "_" +
               (endTime != null ? endTime.toString() : "0");
    }

    /**
     * 从本地缓存获取统计数据
     */
    private Map<String, Object> getFromLocalCache(String cacheKey) {
        Long expirationTime = cacheExpirationTimes.get(cacheKey);
        if (expirationTime == null || System.currentTimeMillis() > expirationTime) {
            // 缓存不存在或已过期
            clearLocalCache(cacheKey);
            return null;
        }

        return statisticsCache.get(cacheKey);
    }

    /**
     * 更新本地缓存
     */
    private void updateLocalCache(String cacheKey, Map<String, Object> statistics) {
        statisticsCache.put(cacheKey, statistics);
        cacheExpirationTimes.put(cacheKey, System.currentTimeMillis() + CACHE_EXPIRATION);
    }

    /**
     * 清除本地缓存
     */
    private void clearLocalCache(String cacheKey) {
        statisticsCache.remove(cacheKey);
        cacheExpirationTimes.remove(cacheKey);
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCaches() {
        log.debug("Clearing all data statistics caches");
        statisticsCache.clear();
        cacheExpirationTimes.clear();
    }
}
