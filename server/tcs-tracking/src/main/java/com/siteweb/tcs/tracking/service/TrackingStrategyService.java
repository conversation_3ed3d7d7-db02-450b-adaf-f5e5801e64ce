package com.siteweb.tcs.tracking.service;

import com.siteweb.tcs.tracking.connector.letter.TrackingStrategyMessage;
import com.siteweb.tcs.tracking.dal.entity.TrackingPoint;
import com.siteweb.tcs.tracking.dal.entity.TrackingStrategy;
import com.siteweb.tcs.tracking.dal.mapper.TrackingPointMapper;
import com.siteweb.tcs.tracking.dal.mapper.TrackingStrategyMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 埋点策略服务
 * 负责管理埋点策略的CRUD操作
 *
 * 本服务实现了策略的缓存机制，提高频繁访问策略时的性能。
 * 缓存包括：
 * - 策略列表缓存
 * - 单个策略缓存
 * - 活跃策略缓存
 */
@Slf4j
@Service
@CacheConfig(cacheNames = "trackingStrategies")
public class TrackingStrategyService {

    @Autowired
    private TrackingStrategyMapper strategyMapper;

    @Autowired
    private TrackingPointMapper pointMapper;

    @Autowired
    private ActorRef trackingGuard;

    // 本地内存缓存，用于高频访问的策略
    private final Map<Integer, TrackingStrategy> strategyCache = new ConcurrentHashMap<>();
    private final Map<String, List<TrackingStrategy>> strategiesByTypeCache = new ConcurrentHashMap<>();
    private final Map<String, List<TrackingStrategy>> strategiesByStatusCache = new ConcurrentHashMap<>();

    // 缓存过期时间（毫秒）
    private static final long CACHE_EXPIRATION = 60 * 1000; // 1分钟
    private Map<Integer, Long> cacheExpirationTimes = new ConcurrentHashMap<>();

    /**
     * 获取所有策略
     *
     * @return 所有策略列表
     */
    @Cacheable(key = "'all'")
    public List<TrackingStrategy> getAllStrategies() {
        log.debug("Cache miss: Loading all strategies from database");
        List<TrackingStrategy> strategies = strategyMapper.selectList(null);

        // 加载关联的埋点点位
        for (TrackingStrategy strategy : strategies) {
            List<TrackingPoint> points = pointMapper.selectByStrategyId(strategy.getId());
            strategy.setTrackingPoints(points);

            // 更新本地缓存
            updateLocalCache(strategy);
        }

        return strategies;
    }

    /**
     * 获取策略详情
     *
     * @param id 策略ID
     * @return 策略详情
     */
    @Cacheable(key = "#id")
    public TrackingStrategy getStrategyById(Integer id) {
        // 先检查本地缓存
        TrackingStrategy cachedStrategy = getFromLocalCache(id);
        if (cachedStrategy != null) {
            log.debug("Local cache hit for strategy ID: {}", id);
            return cachedStrategy;
        }

        log.debug("Cache miss: Loading strategy ID {} from database", id);
        TrackingStrategy strategy = strategyMapper.selectById(id);
        if (strategy != null) {
            List<TrackingPoint> points = pointMapper.selectByStrategyId(id);
            strategy.setTrackingPoints(points);

            // 更新本地缓存
            updateLocalCache(strategy);
        }
        return strategy;
    }

    /**
     * 创建策略
     *
     * @param strategy 要创建的策略
     * @return 创建后的策略
     */
    @Transactional
    @CacheEvict(allEntries = true)
    public TrackingStrategy createStrategy(TrackingStrategy strategy) {
        // 设置初始状态
        strategy.setStatus(TrackingStrategy.StrategyStatus.INACTIVE.name());
        strategy.setCreatedAt(LocalDateTime.now());
        strategy.setUpdatedAt(LocalDateTime.now());

        // 保存策略
        strategyMapper.insert(strategy);

        // 保存关联的埋点点位
        if (strategy.getTrackingPoints() != null) {
            for (TrackingPoint point : strategy.getTrackingPoints()) {
                point.setStrategyId(strategy.getId());
                point.setCreatedAt(LocalDateTime.now());
                point.setUpdatedAt(LocalDateTime.now());
                pointMapper.insert(point);
            }
        }

        // 发送策略创建消息
        sendStrategyMessage(TrackingStrategyMessage.OperationType.CREATE, strategy);

        return strategy;
    }

    /**
     * 更新策略
     *
     * @param strategy 要更新的策略
     * @return 更新后的策略
     */
    @Transactional
    @CacheEvict(allEntries = true)
    public TrackingStrategy updateStrategy(TrackingStrategy strategy) {
        // 检查策略是否存在
        TrackingStrategy existingStrategy = strategyMapper.selectById(strategy.getId());
        if (existingStrategy == null) {
            throw new RuntimeException("Strategy not found: " + strategy.getId());
        }

        // 更新时间
        strategy.setUpdatedAt(LocalDateTime.now());

        // 更新策略
        strategyMapper.updateById(strategy);

        // 更新关联的埋点点位
        if (strategy.getTrackingPoints() != null) {
            // 删除旧的点位
            pointMapper.deleteByStrategyId(strategy.getId());

            // 添加新的点位
            for (TrackingPoint point : strategy.getTrackingPoints()) {
                point.setStrategyId(strategy.getId());
                point.setCreatedAt(LocalDateTime.now());
                point.setUpdatedAt(LocalDateTime.now());
                pointMapper.insert(point);
            }
        }

        // 发送策略更新消息
        sendStrategyMessage(TrackingStrategyMessage.OperationType.UPDATE, strategy);

        return strategy;
    }

    /**
     * 删除策略
     *
     * @param id 要删除的策略ID
     */
    @Transactional
    @CacheEvict(allEntries = true)
    public void deleteStrategy(Integer id) {
        // 检查策略是否存在
        TrackingStrategy existingStrategy = strategyMapper.selectById(id);
        if (existingStrategy == null) {
            throw new RuntimeException("Strategy not found: " + id);
        }

        // 删除关联的埋点点位
        pointMapper.deleteByStrategyId(id);

        // 删除策略
        strategyMapper.deleteById(id);

        // 发送策略删除消息
        sendStrategyMessage(TrackingStrategyMessage.OperationType.DELETE, existingStrategy);
    }

    /**
     * 激活策略
     *
     * @param id 要激活的策略ID
     */
    @CacheEvict(allEntries = true)
    public void activateStrategy(Integer id) {
        // 检查策略是否存在
        TrackingStrategy strategy = strategyMapper.selectById(id);
        if (strategy == null) {
            throw new RuntimeException("Strategy not found: " + id);
        }

        // 更新状态
        strategy.setStatus(TrackingStrategy.StrategyStatus.ACTIVE.name());
        strategy.setUpdatedAt(LocalDateTime.now());
        strategyMapper.updateById(strategy);

        // 发送策略激活消息
        sendStrategyMessage(TrackingStrategyMessage.OperationType.ACTIVATE, strategy);
    }

    /**
     * 停用策略
     *
     * @param id 要停用的策略ID
     */
    @CacheEvict(allEntries = true)
    public void deactivateStrategy(Integer id) {
        // 检查策略是否存在
        TrackingStrategy strategy = strategyMapper.selectById(id);
        if (strategy == null) {
            throw new RuntimeException("Strategy not found: " + id);
        }

        // 更新状态
        strategy.setStatus(TrackingStrategy.StrategyStatus.INACTIVE.name());
        strategy.setUpdatedAt(LocalDateTime.now());
        strategyMapper.updateById(strategy);

        // 发送策略停用消息
        sendStrategyMessage(TrackingStrategyMessage.OperationType.DEACTIVATE, strategy);
    }

    /**
     * 暂停策略
     *
     * @param id 要暂停的策略ID
     */
    @CacheEvict(allEntries = true)
    public void pauseStrategy(Integer id) {
        // 检查策略是否存在
        TrackingStrategy strategy = strategyMapper.selectById(id);
        if (strategy == null) {
            throw new RuntimeException("Strategy not found: " + id);
        }

        // 更新状态
        strategy.setStatus(TrackingStrategy.StrategyStatus.PAUSED.name());
        strategy.setUpdatedAt(LocalDateTime.now());
        strategyMapper.updateById(strategy);

        // 发送策略暂停消息
        sendStrategyMessage(TrackingStrategyMessage.OperationType.PAUSE, strategy);
    }

    /**
     * 发送策略消息
     *
     * @param operationType 操作类型
     * @param strategy 策略对象
     */
    private void sendStrategyMessage(TrackingStrategyMessage.OperationType operationType, TrackingStrategy strategy) {
        TrackingStrategyMessage message = new TrackingStrategyMessage();
        message.setOperationType(operationType);
        message.setStrategyId(strategy.getId());
        message.setStrategy(strategy);

        // 发送消息
        trackingGuard.tell(message, ActorRef.noSender());

        // 清除本地缓存
        clearLocalCache(strategy.getId());
    }

    /**
     * 获取活跃策略列表
     *
     * @return 活跃策略列表
     */
    @Cacheable(key = "'active'")
    public List<TrackingStrategy> getActiveStrategies() {
        log.debug("Cache miss: Loading active strategies from database");
        List<TrackingStrategy> strategies = strategyMapper.selectByStatus(TrackingStrategy.StrategyStatus.ACTIVE.name());

        // 加载关联的埋点点位
        for (TrackingStrategy strategy : strategies) {
            List<TrackingPoint> points = pointMapper.selectByStrategyId(strategy.getId());
            strategy.setTrackingPoints(points);

            // 更新本地缓存
            updateLocalCache(strategy);
        }

        return strategies;
    }

    /**
     * 获取特定类型的策略列表
     *
     * @param type 策略类型
     * @return 策略列表
     */
    @Cacheable(key = "'type_' + #type")
    public List<TrackingStrategy> getStrategiesByType(String type) {
        log.debug("Cache miss: Loading strategies of type {} from database", type);
        List<TrackingStrategy> strategies = strategyMapper.selectByType(type);

        // 加载关联的埋点点位
        for (TrackingStrategy strategy : strategies) {
            List<TrackingPoint> points = pointMapper.selectByStrategyId(strategy.getId());
            strategy.setTrackingPoints(points);

            // 更新本地缓存
            updateLocalCache(strategy);
        }

        return strategies;
    }

    /**
     * 刷新策略缓存
     *
     * @param id 策略ID
     */
    @CacheEvict(key = "#id")
    public void refreshStrategy(Integer id) {
        log.debug("Refreshing cache for strategy ID: {}", id);
        clearLocalCache(id);
    }

    /**
     * 清除所有缓存
     */
    @CacheEvict(allEntries = true)
    public void clearAllCaches() {
        log.debug("Clearing all strategy caches");
        strategyCache.clear();
        strategiesByTypeCache.clear();
        strategiesByStatusCache.clear();
        cacheExpirationTimes.clear();
    }

    /**
     * 从本地缓存获取策略
     *
     * @param id 策略ID
     * @return 缓存的策略，如果不存在或已过期则返回null
     */
    private TrackingStrategy getFromLocalCache(Integer id) {
        Long expirationTime = cacheExpirationTimes.get(id);
        if (expirationTime == null || System.currentTimeMillis() > expirationTime) {
            // 缓存不存在或已过期
            clearLocalCache(id);
            return null;
        }

        return strategyCache.get(id);
    }

    /**
     * 更新本地缓存
     *
     * @param strategy 要缓存的策略
     */
    private void updateLocalCache(TrackingStrategy strategy) {
        if (strategy == null || strategy.getId() == null) {
            return;
        }

        Integer id = strategy.getId();
        strategyCache.put(id, strategy);
        cacheExpirationTimes.put(id, System.currentTimeMillis() + CACHE_EXPIRATION);

        // 更新类型缓存
        String type = strategy.getType();
        if (type != null) {
            List<TrackingStrategy> typeStrategies = strategiesByTypeCache.computeIfAbsent(type, k -> new ArrayList<>());
            typeStrategies.removeIf(s -> s.getId().equals(id));
            typeStrategies.add(strategy);
        }

        // 更新状态缓存
        String status = strategy.getStatus();
        if (status != null) {
            List<TrackingStrategy> statusStrategies = strategiesByStatusCache.computeIfAbsent(status, k -> new ArrayList<>());
            statusStrategies.removeIf(s -> s.getId().equals(id));
            statusStrategies.add(strategy);
        }
    }

    /**
     * 清除本地缓存中的策略
     *
     * @param id 策略ID
     */
    private void clearLocalCache(Integer id) {
        TrackingStrategy strategy = strategyCache.remove(id);
        cacheExpirationTimes.remove(id);

        if (strategy != null) {
            // 清除类型缓存
            String type = strategy.getType();
            if (type != null && strategiesByTypeCache.containsKey(type)) {
                strategiesByTypeCache.get(type).removeIf(s -> s.getId().equals(id));
            }

            // 清除状态缓存
            String status = strategy.getStatus();
            if (status != null && strategiesByStatusCache.containsKey(status)) {
                strategiesByStatusCache.get(status).removeIf(s -> s.getId().equals(id));
            }
        }
    }
}
