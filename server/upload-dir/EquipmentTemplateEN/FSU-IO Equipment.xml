<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="Template List">
  <EquipmentTemplate EquipmentTemplateId="755000112" ParentTemplateId="0" EquipmentTemplateName="FSU-IO" ProtocolCode="FSU-IO6-00" EquipmentCategory="51" EquipmentType="1" Memo="1" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1004" StationCategory="0">
    <Signals Name="Signal">
      <Signal SignalId="510000390" SignalName="12V1 power supply(FSU2809)" SignalCategory="2" SignalType="1" ChannelNo="79" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Disconnect;1:Connect" />
      <Signal SignalId="510000400" SignalName="12V2 power supply(FSU2809)" SignalCategory="2" SignalType="1" ChannelNo="80" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="15" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Disconnect;1:Connect" />
      <Signal SignalId="510000751" SignalName="DI13(FSU2809)" SignalCategory="2" SignalType="1" ChannelNo="81" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="36" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000350" SignalName="DO2" SignalCategory="2" SignalType="1" ChannelNo="25" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="11" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Disconnect;1:Connect" />
      <Signal SignalId="510000370" SignalName="DO4" SignalCategory="2" SignalType="1" ChannelNo="27" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="13" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Disconnect;1:Connect" />
      <Signal SignalId="510000750" SignalName="SD status" SignalCategory="2" SignalType="1" ChannelNo="71" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="41" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:No;1:Yes" />
      <Signal SignalId="510000771" SignalName="SD surplus capacity" SignalCategory="1" SignalType="1" ChannelNo="73" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="KB" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="43" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000761" SignalName="SD capacity" SignalCategory="1" SignalType="1" ChannelNo="72" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="KB" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="42" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000340" SignalName="Lighting control-DO1" SignalCategory="2" SignalType="1" ChannelNo="24" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="10" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Disconnect;1:Connect" />
      <Signal SignalId="510000671" SignalName="Infrared#1-DI9" SignalCategory="2" SignalType="1" ChannelNo="37" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004008001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="32" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000681" SignalName="Infrared#2-DI10" SignalCategory="2" SignalType="1" ChannelNo="38" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004008002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="33" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000690" SignalName="Infrared#3-DI11" SignalCategory="2" SignalType="1" ChannelNo="39" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004008003" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="34" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000700" SignalName="Infrared#4-DI12" SignalCategory="2" SignalType="1" ChannelNo="40" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004008004" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="35" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000631" SignalName="Door magnet#1-DI5" SignalCategory="2" SignalType="1" ChannelNo="33" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004007001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="28" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000651" SignalName="Door magnet#2-DI7" SignalCategory="2" SignalType="1" ChannelNo="35" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004007002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="30" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000641" SignalName="Door lock#1-DI6" SignalCategory="2" SignalType="1" ChannelNo="34" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004305001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="29" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000661" SignalName="Door lock#2-DI8" SignalCategory="2" SignalType="1" ChannelNo="36" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004305002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="31" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="-3" SignalName="IO status" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:Normal" />
      <Signal SignalId="510000021" SignalName="Humidity#1-CH2" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="RH" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004003001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000041" SignalName="Humidity#2-CH4" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="RH" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004003002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000061" SignalName="Humidity#3-CH6" SignalCategory="1" SignalType="1" ChannelNo="5" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="RH" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004003003" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000081" SignalName="Humidity#4-CH8" SignalCategory="1" SignalType="1" ChannelNo="7" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="RH" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004003004" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000711" SignalName="Water#1" SignalCategory="2" SignalType="1" ChannelNo="41" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004005001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="37" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000721" SignalName="Water#2" SignalCategory="2" SignalType="1" ChannelNo="42" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004005002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="38" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000731" SignalName="Water#3" SignalCategory="2" SignalType="1" ChannelNo="43" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004005003" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="39" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000741" SignalName="Water#4(FSU2808)" SignalCategory="2" SignalType="1" ChannelNo="44" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004005004" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="40" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000011" SignalName="Templerature#1-CH1" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004001001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000031" SignalName="Templerature#2-CH3" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004001002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000051" SignalName="Templerature#3-CH5" SignalCategory="1" SignalType="1" ChannelNo="4" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004001003" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000071" SignalName="Templerature#4-CH7" SignalCategory="1" SignalType="1" ChannelNo="6" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004001004" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000521" SignalName="Battery#1-voltage(group#2)" SignalCategory="1" SignalType="1" ChannelNo="63" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101321001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="18" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000511" SignalName="Battery#1-voltage(group#1)" SignalCategory="1" SignalType="1" ChannelNo="9" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101320001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="17" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000551" SignalName="Battery#1-voltage(group#1-group#2)" SignalCategory="1" SignalType="1" ChannelNo="75" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="19" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
	  <Signal SignalId="510000772" SignalName="Battery#1-voltage" SignalCategory="1" SignalType="1" ChannelNo="8" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101170001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="16" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="" />
      <Signal SignalId="510000541" SignalName="Battery#2-voltage(group#2)" SignalCategory="1" SignalType="1" ChannelNo="65" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101321002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="22" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000531" SignalName="Battery#2-voltage(group#1)" SignalCategory="1" SignalType="1" ChannelNo="11" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101320002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="21" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000561" SignalName="Battery#2-voltage(group#1-group#2)" SignalCategory="1" SignalType="1" ChannelNo="76" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="23" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
	  <Signal SignalId="510000773" SignalName="Battery#2-voltage" SignalCategory="1" SignalType="1" ChannelNo="10" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101170002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="20" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="" />
      <Signal SignalId="510000591" SignalName="Smoke#1-DI1" SignalCategory="2" SignalType="1" ChannelNo="29" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004006001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="24" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000601" SignalName="Smoke#2-DI2" SignalCategory="2" SignalType="1" ChannelNo="30" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004006002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="25" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000611" SignalName="Smoke#3-DI3" SignalCategory="2" SignalType="1" ChannelNo="31" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004006003" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="26" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000621" SignalName="Smoke#4-DI4" SignalCategory="2" SignalType="1" ChannelNo="32" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004006004" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="27" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Normal;1:Alarm" />
      <Signal SignalId="510000360" SignalName="Door remote control-DO3" SignalCategory="2" SignalType="1" ChannelNo="26" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="12" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Disconnect;1:Connect" />
    </Signals>
    <Events Name="Event">
      <Event EventId="-3" EventName="IO status" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000591" EventName="Smoke#1-DI1" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000591]" SuppressExpression="" SignalId="510000591" Enable="True" Visible="True" Description="" DisplayIndex="24" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004006001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000601" EventName="Smoke#2-DI2" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000601]" SuppressExpression="" SignalId="510000601" Enable="True" Visible="True" Description="" DisplayIndex="25" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004006002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000611" EventName="Smoke#3-DI3" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000611]" SuppressExpression="" SignalId="510000611" Enable="True" Visible="True" Description="" DisplayIndex="26" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004006003" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000621" EventName="Smoke#4-DI4" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000621]" SuppressExpression="" SignalId="510000621" Enable="True" Visible="True" Description="" DisplayIndex="27" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004006004" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000631" EventName="Door magnet#-DI5" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000631]" SuppressExpression="" SignalId="510000631" Enable="True" Visible="True" Description="" DisplayIndex="28" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004007001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000641" EventName="Door lock#1-DI6" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000641]" SuppressExpression="" SignalId="510000641" Enable="True" Visible="True" Description="" DisplayIndex="29" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004308001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000651" EventName="Door magnet#-DI7" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000651]" SuppressExpression="" SignalId="510000651" Enable="True" Visible="True" Description="" DisplayIndex="30" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004007002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000661" EventName="Door lock#2-DI8" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000661]" SuppressExpression="" SignalId="510000661" Enable="True" Visible="True" Description="" DisplayIndex="31" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004308002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000671" EventName="Infrared#1-DI9" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000671]" SuppressExpression="" SignalId="510000671" Enable="True" Visible="True" Description="" DisplayIndex="32" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004008001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000681" EventName="Infrared#2-DI10" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000681]" SuppressExpression="" SignalId="510000681" Enable="True" Visible="True" Description="" DisplayIndex="33" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004008002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000690" EventName="Infrared#3-DI11" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000690]" SuppressExpression="" SignalId="510000690" Enable="True" Visible="True" Description="" DisplayIndex="34" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004008003" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000700" EventName="Infrared#4-DI12" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000700]" SuppressExpression="" SignalId="510000700" Enable="True" Visible="True" Description="" DisplayIndex="35" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004008004" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000711" EventName="Water#1" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000711]" SuppressExpression="" SignalId="510000711" Enable="True" Visible="True" Description="" DisplayIndex="37" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004005001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000721" EventName="Water#2" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000721]" SuppressExpression="" SignalId="510000721" Enable="True" Visible="True" Description="" DisplayIndex="38" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004005002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000731" EventName="Water#3" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000731]" SuppressExpression="" SignalId="510000731" Enable="True" Visible="True" Description="" DisplayIndex="39" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004005003" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000741" EventName="Water#4(FSU2808)" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000741]" SuppressExpression="" SignalId="510000741" Enable="True" Visible="True" Description="" DisplayIndex="40" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004005004" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000750" EventName="SD status" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000750]" SuppressExpression="" SignalId="510000750" Enable="True" Visible="True" Description="" DisplayIndex="41" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="No SD Card" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000751" EventName="DI13(FSU2809)" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000751]" SuppressExpression="" SignalId="510000751" Enable="True" Visible="True" Description="" DisplayIndex="36" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000761" EventName="Battery#1 voltage imbalance" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000551]" SuppressExpression="" SignalId="510000551" Enable="True" Visible="True" Description="" DisplayIndex="19" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="" BaseTypeId="1101330001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000762" EventName="Battery#2 voltage imbalance" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000561]" SuppressExpression="" SignalId="510000561" Enable="True" Visible="True" Description="" DisplayIndex="23" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="" BaseTypeId="1101330002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000763" EventName="Battery#1-voltage" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000772]" SuppressExpression="" SignalId="510000772" Enable="True" Visible="True" Description="" DisplayIndex="16" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="60" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Too high" EquipmentState="" BaseTypeId="1101370001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="46" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Too Low" EquipmentState="" BaseTypeId="1101170001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000764" EventName="Battery#2-voltage" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000773]" SuppressExpression="" SignalId="510000773" Enable="True" Visible="True" Description="" DisplayIndex="20" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="60" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1101370002" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="46" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1101170002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000765" EventName="Temperature#1-CH1" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000011]" SuppressExpression="" SignalId="510000011" Enable="True" Visible="True" Description="" DisplayIndex="2" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="&gt;" StartCompareValue="45" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Super high" EquipmentState="" BaseTypeId="1004307001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&gt;" StartCompareValue="40" StartDelay="120" EndOperation="&gt;" EndCompareValue="45" EndDelay="20" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004001001" StandardName="" />
          <EventCondition EventConditionId="2" EventSeverity="0" StartOperation="&lt;" StartCompareValue="-10" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004002001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000766" EventName="Humidity#1-CH2" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000021]" SuppressExpression="" SignalId="510000021" Enable="True" Visible="True" Description="" DisplayIndex="3" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="95" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004003001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="10" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004004001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000767" EventName="Temperature#2-CH3" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000031]" SuppressExpression="" SignalId="510000031" Enable="True" Visible="True" Description="" DisplayIndex="4" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="&gt;" StartCompareValue="45" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Super high" EquipmentState="" BaseTypeId="1004307002" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&gt;" StartCompareValue="40" StartDelay="120" EndOperation="&gt;" EndCompareValue="45" EndDelay="20" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004001002" StandardName="" />
          <EventCondition EventConditionId="2" EventSeverity="0" StartOperation="&lt;" StartCompareValue="-10" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004002002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000768" EventName="Humidity#2-CH4" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000041]" SuppressExpression="" SignalId="510000041" Enable="True" Visible="True" Description="" DisplayIndex="5" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="95" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004003002" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="10" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004004002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000769" EventName="Temperature#3-CH5" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000051]" SuppressExpression="" SignalId="510000051" Enable="True" Visible="True" Description="" DisplayIndex="6" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="&gt;" StartCompareValue="45" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Super high" EquipmentState="" BaseTypeId="1004307003" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&gt;" StartCompareValue="40" StartDelay="120" EndOperation="&gt;" EndCompareValue="45" EndDelay="20" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004001003" StandardName="" />
          <EventCondition EventConditionId="2" EventSeverity="0" StartOperation="&lt;" StartCompareValue="-10" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004002003" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000770" EventName="Humidity#3-CH6" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000061]" SuppressExpression="" SignalId="510000061" Enable="True" Visible="True" Description="" DisplayIndex="7" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="95" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004003003" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="10" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004004003" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000771" EventName="Temperature#4-CH7" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000071]" SuppressExpression="" SignalId="510000071" Enable="True" Visible="True" Description="" DisplayIndex="8" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="&gt;" StartCompareValue="45" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Super high" EquipmentState="" BaseTypeId="1004307004" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&gt;" StartCompareValue="40" StartDelay="120" EndOperation="&gt;" EndCompareValue="45" EndDelay="20" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004001004" StandardName="" />
          <EventCondition EventConditionId="2" EventSeverity="0" StartOperation="&lt;" StartCompareValue="-10" StartDelay="120" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004002004" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000772" EventName="Humidity#4-CH8" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000081]" SuppressExpression="" SignalId="510000081" Enable="True" Visible="True" Description="" DisplayIndex="9" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="95" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="High" EquipmentState="" BaseTypeId="1004003004" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="10" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Low" EquipmentState="" BaseTypeId="1004004004" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="Control">
      <Control ControlId="510000340" ControlName="Lighting control-DO1" ControlCategory="1" CmdToken="10,0" BaseTypeId="1004303001" ControlSeverity="1" SignalId="510000340" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="1" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Close;1:Open" />
      <Control ControlId="510000350" ControlName="DO2" ControlCategory="1" CmdToken="11,0" BaseTypeId="" ControlSeverity="1" SignalId="510000350" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="2" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Disconnect;1:Connect" />
      <Control ControlId="510000360" ControlName="Door remote control-DO3" ControlCategory="1" CmdToken="12,250" BaseTypeId="1004010001" ControlSeverity="1" SignalId="510000360" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="3" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Close;1:Open" />
      <Control ControlId="510000370" ControlName="DO4" ControlCategory="1" CmdToken="13,0" BaseTypeId="" ControlSeverity="1" SignalId="510000370" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="4" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Disconnect;1:Connect" />
      <Control ControlId="510000390" ControlName="12V1 power supply(FSU2809)" ControlCategory="1" CmdToken="15,0" BaseTypeId="" ControlSeverity="1" SignalId="510000390" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="5" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Disconnect;1:Connect" />
      <Control ControlId="510000400" ControlName="12V2 power supply(FSU2809)" ControlCategory="1" CmdToken="16,0" BaseTypeId="" ControlSeverity="1" SignalId="510000400" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="6" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Disconnect;1:Connect" />
    </Controls>
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="755000070" SamplerName="FSUIO" SamplerType="18" ProtocolCode="FSU-IO6-00" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="FSUIO.DLL" Setting="9600,N,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>