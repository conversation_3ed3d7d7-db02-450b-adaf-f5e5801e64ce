<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="100011176" ParentTemplateId="0" EquipmentTemplateName="雅达YD878Y-N2温湿度传感器" ProtocolCode="OT_13F4F3B3-73B7-4817-8FE5-EB3B2" EquipmentCategory="12" EquipmentType="1" Memo="2024-02-28 16:17:03  协议自动工具导入" Property="" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1006" StationCategory="">
    <Signals Name="模板信号">
      <Signal SignalId="-3" SignalName="设备通讯状态" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006999001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="" ModuleNo="0" SignalProperty="" SignalMeanings="0:通讯异常:1;1:通讯正常:0" />
      <Signal SignalId="120000001" SignalName="1路温度修正值" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="0" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000011" SignalName="1路湿度修正值" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="%RH" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000021" SignalName="温度告警阀值" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000031" SignalName="湿度告警阀值" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="%RH" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000040" SignalName="摄氏度/华氏度显示方式" SignalCategory="2" SignalType="1" ChannelNo="4" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:摄氏度;1:华氏度" />
      <Signal SignalId="120000050" SignalName="1路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="5" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000060" SignalName="2路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="6" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000070" SignalName="3路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="7" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000080" SignalName="4路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="8" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000090" SignalName="5路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="9" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000100" SignalName="6路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="10" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="10" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000110" SignalName="7路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="11" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="11" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000120" SignalName="8路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="12" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="12" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000130" SignalName="9路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="13" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="13" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000140" SignalName="10路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="14" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000150" SignalName="11路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="15" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="15" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000160" SignalName="12路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="16" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="16" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000170" SignalName="13路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="17" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="17" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000180" SignalName="14路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="18" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="18" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000190" SignalName="15路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="19" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="19" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000200" SignalName="16路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="20" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="20" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000210" SignalName="17路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="21" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="21" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000220" SignalName="18路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="22" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="22" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000230" SignalName="19路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="23" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="23" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000240" SignalName="20路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="24" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="24" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000250" SignalName="21路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="25" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="25" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000260" SignalName="22路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="26" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="26" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000270" SignalName="23路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="27" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="27" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000280" SignalName="24路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="28" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="28" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000290" SignalName="25路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="29" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="29" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000300" SignalName="26路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="30" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="30" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000310" SignalName="27路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="31" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="31" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000320" SignalName="28路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="32" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="32" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000330" SignalName="29路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="33" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="33" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000340" SignalName="30路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="34" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="34" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000350" SignalName="31路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="35" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="35" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000360" SignalName="32路温度告警状态" SignalCategory="2" SignalType="1" ChannelNo="36" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="36" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000370" SignalName="1路湿度告警状态" SignalCategory="2" SignalType="1" ChannelNo="37" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="37" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="120000381" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="38" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="38" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000391" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="39" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="39" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000401" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="40" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="40" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000411" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="41" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="41" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000421" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="42" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="42" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000431" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="43" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="43" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000441" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="44" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="44" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000451" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="45" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="45" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000461" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="46" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="46" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000471" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="47" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="47" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000481" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="48" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="48" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000491" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="49" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="49" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000501" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="50" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="50" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000511" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="51" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="51" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000521" SignalName="保留" SignalCategory="1" SignalType="1" ChannelNo="52" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="52" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000531" SignalName="1路温度值" SignalCategory="1" SignalType="1" ChannelNo="53" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="53" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000541" SignalName="2路温度值" SignalCategory="1" SignalType="1" ChannelNo="54" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="54" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000551" SignalName="3路温度值" SignalCategory="1" SignalType="1" ChannelNo="55" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001003" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="55" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000561" SignalName="4路温度值" SignalCategory="1" SignalType="1" ChannelNo="56" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001004" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="56" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000571" SignalName="5路温度值" SignalCategory="1" SignalType="1" ChannelNo="57" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001005" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="57" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000581" SignalName="6路温度值" SignalCategory="1" SignalType="1" ChannelNo="58" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001006" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="58" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000591" SignalName="7路温度值" SignalCategory="1" SignalType="1" ChannelNo="59" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001007" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="59" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000601" SignalName="8路温度值" SignalCategory="1" SignalType="1" ChannelNo="60" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001008" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="60" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000611" SignalName="9路温度值" SignalCategory="1" SignalType="1" ChannelNo="61" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001009" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="61" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000621" SignalName="10路温度值" SignalCategory="1" SignalType="1" ChannelNo="62" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001010" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="62" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000631" SignalName="11路温度值" SignalCategory="1" SignalType="1" ChannelNo="63" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001011" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="63" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000641" SignalName="12路温度值" SignalCategory="1" SignalType="1" ChannelNo="64" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001012" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="64" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000651" SignalName="13路温度值" SignalCategory="1" SignalType="1" ChannelNo="65" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001013" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="65" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000661" SignalName="14路温度值" SignalCategory="1" SignalType="1" ChannelNo="66" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001014" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="66" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000671" SignalName="15路温度值" SignalCategory="1" SignalType="1" ChannelNo="67" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001015" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="67" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000681" SignalName="16路温度值" SignalCategory="1" SignalType="1" ChannelNo="68" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001016" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="68" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000691" SignalName="17路温度值" SignalCategory="1" SignalType="1" ChannelNo="69" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001017" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="69" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000701" SignalName="18路温度值" SignalCategory="1" SignalType="1" ChannelNo="70" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001018" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="70" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000711" SignalName="19路温度值" SignalCategory="1" SignalType="1" ChannelNo="71" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001019" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="71" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000721" SignalName="20路温度值" SignalCategory="1" SignalType="1" ChannelNo="72" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001020" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="72" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000731" SignalName="21路温度值" SignalCategory="1" SignalType="1" ChannelNo="73" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001021" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="73" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000741" SignalName="22路温度值" SignalCategory="1" SignalType="1" ChannelNo="74" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001022" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="74" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000751" SignalName="23路温度值" SignalCategory="1" SignalType="1" ChannelNo="75" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001023" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="75" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000761" SignalName="24路温度值" SignalCategory="1" SignalType="1" ChannelNo="76" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001024" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="76" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000771" SignalName="25路温度值" SignalCategory="1" SignalType="1" ChannelNo="77" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001025" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="77" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000781" SignalName="26路温度值" SignalCategory="1" SignalType="1" ChannelNo="78" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001026" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="78" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000791" SignalName="27路温度值" SignalCategory="1" SignalType="1" ChannelNo="79" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001027" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="79" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000801" SignalName="28路温度值" SignalCategory="1" SignalType="1" ChannelNo="80" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001028" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="80" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000811" SignalName="29路温度值" SignalCategory="1" SignalType="1" ChannelNo="81" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001029" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="81" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000821" SignalName="30路温度值" SignalCategory="1" SignalType="1" ChannelNo="82" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001030" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="82" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000831" SignalName="31路温度值" SignalCategory="1" SignalType="1" ChannelNo="83" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001031" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="83" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000841" SignalName="32路温度值" SignalCategory="1" SignalType="1" ChannelNo="84" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="℃/℉" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006001032" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="84" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="120000851" SignalName="第1路湿度值" SignalCategory="1" SignalType="1" ChannelNo="85" ChannelType="1" Expression="" DataType="" ShowPrecision="0.0" Unit="%RH" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1006003001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="85" MDBSignalId="" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
    </Signals>
    <Events Name="模板事件">
      <Event EventId="-3" EventName="设备通讯状态" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="通讯异常" EquipmentState="" BaseTypeId="1001999001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000000" EventName="1路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000050]" SuppressExpression="" SignalId="120000050" Enable="True" Visible="True" Description="" DisplayIndex="0" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000010" EventName="2路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000060]" SuppressExpression="" SignalId="120000060" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000020" EventName="3路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000070]" SuppressExpression="" SignalId="120000070" Enable="True" Visible="True" Description="" DisplayIndex="2" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000030" EventName="4路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000080]" SuppressExpression="" SignalId="120000080" Enable="True" Visible="True" Description="" DisplayIndex="3" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000040" EventName="5路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000090]" SuppressExpression="" SignalId="120000090" Enable="True" Visible="True" Description="" DisplayIndex="4" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000050" EventName="6路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000100]" SuppressExpression="" SignalId="120000100" Enable="True" Visible="True" Description="" DisplayIndex="5" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000060" EventName="7路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000110]" SuppressExpression="" SignalId="120000110" Enable="True" Visible="True" Description="" DisplayIndex="6" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000070" EventName="8路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000120]" SuppressExpression="" SignalId="120000120" Enable="True" Visible="True" Description="" DisplayIndex="7" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000080" EventName="9路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000130]" SuppressExpression="" SignalId="120000130" Enable="True" Visible="True" Description="" DisplayIndex="8" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000090" EventName="10路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000140]" SuppressExpression="" SignalId="120000140" Enable="True" Visible="True" Description="" DisplayIndex="9" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000100" EventName="11路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000150]" SuppressExpression="" SignalId="120000150" Enable="True" Visible="True" Description="" DisplayIndex="10" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000110" EventName="12路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000160]" SuppressExpression="" SignalId="120000160" Enable="True" Visible="True" Description="" DisplayIndex="11" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000120" EventName="13路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000170]" SuppressExpression="" SignalId="120000170" Enable="True" Visible="True" Description="" DisplayIndex="12" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000130" EventName="14路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000180]" SuppressExpression="" SignalId="120000180" Enable="True" Visible="True" Description="" DisplayIndex="13" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000140" EventName="15路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000190]" SuppressExpression="" SignalId="120000190" Enable="True" Visible="True" Description="" DisplayIndex="14" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000150" EventName="16路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000200]" SuppressExpression="" SignalId="120000200" Enable="True" Visible="True" Description="" DisplayIndex="15" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000160" EventName="17路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000210]" SuppressExpression="" SignalId="120000210" Enable="True" Visible="True" Description="" DisplayIndex="16" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000170" EventName="18路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000220]" SuppressExpression="" SignalId="120000220" Enable="True" Visible="True" Description="" DisplayIndex="17" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000180" EventName="19路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000230]" SuppressExpression="" SignalId="120000230" Enable="True" Visible="True" Description="" DisplayIndex="18" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000190" EventName="20路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000240]" SuppressExpression="" SignalId="120000240" Enable="True" Visible="True" Description="" DisplayIndex="19" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000200" EventName="21路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000250]" SuppressExpression="" SignalId="120000250" Enable="True" Visible="True" Description="" DisplayIndex="20" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000210" EventName="22路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000260]" SuppressExpression="" SignalId="120000260" Enable="True" Visible="True" Description="" DisplayIndex="21" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000220" EventName="23路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000270]" SuppressExpression="" SignalId="120000270" Enable="True" Visible="True" Description="" DisplayIndex="22" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000230" EventName="24路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000280]" SuppressExpression="" SignalId="120000280" Enable="True" Visible="True" Description="" DisplayIndex="23" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000240" EventName="25路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000290]" SuppressExpression="" SignalId="120000290" Enable="True" Visible="True" Description="" DisplayIndex="24" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000250" EventName="26路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000300]" SuppressExpression="" SignalId="120000300" Enable="True" Visible="True" Description="" DisplayIndex="25" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000260" EventName="27路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000310]" SuppressExpression="" SignalId="120000310" Enable="True" Visible="True" Description="" DisplayIndex="26" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000270" EventName="28路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000320]" SuppressExpression="" SignalId="120000320" Enable="True" Visible="True" Description="" DisplayIndex="27" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000280" EventName="29路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000330]" SuppressExpression="" SignalId="120000330" Enable="True" Visible="True" Description="" DisplayIndex="28" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000290" EventName="30路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000340]" SuppressExpression="" SignalId="120000340" Enable="True" Visible="True" Description="" DisplayIndex="29" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000300" EventName="31路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000350]" SuppressExpression="" SignalId="120000350" Enable="True" Visible="True" Description="" DisplayIndex="30" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000310" EventName="32路温度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000360]" SuppressExpression="" SignalId="120000360" Enable="True" Visible="True" Description="" DisplayIndex="31" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="120000320" EventName="1路湿度告警状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,120000370]" SuppressExpression="" SignalId="120000370" Enable="True" Visible="True" Description="" DisplayIndex="32" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="模板控制" />
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="100015258" SamplerName="雅达YD878Y-N2温湿度传感器" SamplerType="18" ProtocolCode="OT_13F4F3B3-73B7-4817-8FE5-EB3B2" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="OYDYD878YN2AT.dll" Setting="9600,n,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>