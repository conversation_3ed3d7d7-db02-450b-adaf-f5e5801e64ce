<?xml version="1.0" encoding="UTF-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="1" ParentTemplateId="0" EquipmentTemplateName="衍生告警设备" ProtocolCode="衍生告警设备6-00" EquipmentCategory="99" EquipmentType="2" Memo="" Property="" Decription=" " EquipmentStyle=" " Unit=" " Vender=" ">
    <Signals Name="模板信号">
    </Signals>
    <Events Name="模板事件">
	  <Event EventId="100" EventName="大规模断站告警" EventCategory="6" StartType="1" EndType="3" StartExpression="" SuppressExpression=" " SignalId="" Enable="True" Visible="True" Description=" " DisplayIndex="100">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="大规模断站告警" EquipmentState="" BaseTypeId="1302302001" StandardName="0" />
        </Conditions>
      </Event>
	   <Event EventId="101" EventName="大规模停电告警" EventCategory="6" StartType="1" EndType="3" StartExpression="" SuppressExpression=" " SignalId="" Enable="True" Visible="True" Description=" " DisplayIndex="101">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="大规模停电告警" EquipmentState="" BaseTypeId="1302323001" StandardName="0" />
        </Conditions>
      </Event>
    </Events>
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="1" SamplerName="衍生告警采集器采集器" SamplerType="30" ProtocolCode="衍生告警设备6-00" DllCode="" DLLVersion=" " ProtocolFilePath=" " DLLFilePath=" " DllPath="KoloBusinessServer.exe" Setting="" Description=" " />
  </Samplers>
</EquipmentTemplates>