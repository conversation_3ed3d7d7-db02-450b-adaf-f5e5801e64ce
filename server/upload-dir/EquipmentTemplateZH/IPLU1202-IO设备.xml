<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="16" ParentTemplateId="0" EquipmentTemplateName="IPLU1202-IO设备" ProtocolCode="3E2612C4F399F089049BF28086ECF864" EquipmentCategory="51" EquipmentType="1" Memo="2024-06-07 08:39:57:首次导入模板" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1004" StationCategory="0">
    <Signals Name="模板信号">
      <Signal SignalId="810000250" SignalName="DI1状态" SignalCategory="2" SignalType="1" ChannelNo="16" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="10" MDBSignalId="810000250" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
      <Signal SignalId="810000260" SignalName="DI2状态" SignalCategory="2" SignalType="1" ChannelNo="17" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="11" MDBSignalId="810000260" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
      <Signal SignalId="810000270" SignalName="DI3状态" SignalCategory="2" SignalType="1" ChannelNo="18" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="12" MDBSignalId="810000270" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
      <Signal SignalId="810000280" SignalName="DI4状态" SignalCategory="2" SignalType="1" ChannelNo="19" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="13" MDBSignalId="810000280" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
      <Signal SignalId="810000290" SignalName="DI5状态" SignalCategory="2" SignalType="1" ChannelNo="20" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="810000290" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
      <Signal SignalId="810000300" SignalName="DI6状态" SignalCategory="2" SignalType="1" ChannelNo="21" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="15" MDBSignalId="810000300" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
      <Signal SignalId="810000361" SignalName="I2C温度1" SignalCategory="1" SignalType="1" ChannelNo="8" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="19" MDBSignalId="810000361" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000371" SignalName="I2C温度2" SignalCategory="1" SignalType="1" ChannelNo="9" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="20" MDBSignalId="810000371" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000381" SignalName="I2C温度3" SignalCategory="1" SignalType="1" ChannelNo="10" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="21" MDBSignalId="810000381" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000391" SignalName="I2C温度4" SignalCategory="1" SignalType="1" ChannelNo="11" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="22" MDBSignalId="810000391" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000401" SignalName="I2C温度5" SignalCategory="1" SignalType="1" ChannelNo="12" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="23" MDBSignalId="810000401" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000411" SignalName="I2C温度6" SignalCategory="1" SignalType="1" ChannelNo="13" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="24" MDBSignalId="810000411" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000421" SignalName="I2C温度7" SignalCategory="1" SignalType="1" ChannelNo="35" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="25" MDBSignalId="810000421" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000431" SignalName="I2C温度8" SignalCategory="1" SignalType="1" ChannelNo="36" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="26" MDBSignalId="810000431" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000011" SignalName="模拟输入01值" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="?" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" MDBSignalId="810000011" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000021" SignalName="模拟输入02值" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="?" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="810000021" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000031" SignalName="模拟输入03值" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="?" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" MDBSignalId="810000031" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000041" SignalName="模拟输入04值" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="?" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" MDBSignalId="810000041" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000051" SignalName="模拟输入05值" SignalCategory="1" SignalType="1" ChannelNo="4" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="?" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="810000051" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000310" SignalName="水浸DI通道" SignalCategory="2" SignalType="1" ChannelNo="22" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="16" MDBSignalId="810000310" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
      <Signal SignalId="810000071" SignalName="温度" SignalCategory="1" SignalType="1" ChannelNo="6" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" MDBSignalId="810000071" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000081" SignalName="湿度" SignalCategory="1" SignalType="1" ChannelNo="7" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="%RH" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" MDBSignalId="810000081" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000061" SignalName="电池电压" SignalCategory="1" SignalType="1" ChannelNo="5" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="810000061" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="810000340" SignalName="继电器1状态" SignalCategory="2" SignalType="1" ChannelNo="24" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="17" MDBSignalId="810000340" ModuleNo="0" SignalProperty="27" SignalMeanings="0:失电;1:得电" />
      <Signal SignalId="810000350" SignalName="继电器2状态" SignalCategory="2" SignalType="1" ChannelNo="25" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="18" MDBSignalId="810000350" ModuleNo="0" SignalProperty="27" SignalMeanings="0:失电;1:得电" />
      <Signal SignalId="-3" SignalName="设备通讯状态" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:通讯异常;1:通讯正常" />
    </Signals>
    <Events Name="模板事件">
      <Event EventId="-3" EventName="设备通讯状态" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="通讯异常" EquipmentState="" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="810000250" EventName="DI1状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,810000250]" SuppressExpression="" SignalId="810000250" Enable="True" Visible="True" Description="" DisplayIndex="10" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="810000260" EventName="DI2状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,810000260]" SuppressExpression="" SignalId="810000260" Enable="True" Visible="True" Description="" DisplayIndex="11" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="810000270" EventName="DI3状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,810000270]" SuppressExpression="" SignalId="810000270" Enable="True" Visible="True" Description="" DisplayIndex="12" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="810000280" EventName="DI4状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,810000280]" SuppressExpression="" SignalId="810000280" Enable="True" Visible="True" Description="" DisplayIndex="13" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="810000290" EventName="DI5状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,810000290]" SuppressExpression="" SignalId="810000290" Enable="True" Visible="True" Description="" DisplayIndex="14" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="810000300" EventName="DI6状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,810000300]" SuppressExpression="" SignalId="810000300" Enable="True" Visible="True" Description="" DisplayIndex="15" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="810000310" EventName="水浸DI通道" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,810000310]" SuppressExpression="" SignalId="810000310" Enable="True" Visible="True" Description="" DisplayIndex="16" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="模板控制">
      <Control ControlId="810000340" ControlName="继电器1状态" ControlCategory="1" CmdToken="10,250" BaseTypeId="" ControlSeverity="1" SignalId="810000340" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="1" CommandType="2" ControlType="1" DataType="0" MaxValue="1" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:失电;1:得电" />
      <Control ControlId="810000350" ControlName="继电器2状态" ControlCategory="1" CmdToken="11,250" BaseTypeId="" ControlSeverity="1" SignalId="810000350" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="2" CommandType="2" ControlType="1" DataType="0" MaxValue="1" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:失电;1:得电" />
    </Controls>
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="12" SamplerName="IPLU1202-IO设备" SamplerType="18" ProtocolCode="3E2612C4F399F089049BF28086ECF864" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="IDUIO.so" Setting="9600,N,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>