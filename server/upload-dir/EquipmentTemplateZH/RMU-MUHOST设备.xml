<?xml version="1.0" encoding="UTF-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="755000005" ParentTemplateId="0" EquipmentTemplateName="RMU-MUHOST设备" ProtocolCode="RMU-MUHOST设备6-00" EquipmentCategory="99" EquipmentType="2" Memo="从模板导入" Property="1/3" Decription=" " EquipmentStyle=" " Unit=" " EquipmentBaseType="1301" Vender=" ">
    <Signals Name="模板信号">
        <Signal SignalId="755001004" SignalName="监控单元通讯状态" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" SignalProperty="" SignalMeanings="0:通讯异常;1:通讯正常" />
    </Signals>
    <Events Name="模板事件">
      <Event EventId="755001004" EventName="监控单元通讯状态" EventCategory="7" StartType="2" EndType="3" StartExpression="" SuppressExpression=" " SignalId="0" Enable="True" Visible="True" Description=" " DisplayIndex="1">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="通讯异常" EquipmentState="" BaseTypeId="1301308001" StandardName="0" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="模板控制" />
  </EquipmentTemplate>
</EquipmentTemplates>