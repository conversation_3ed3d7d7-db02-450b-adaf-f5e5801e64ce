import { http } from "@/utils/http";
import type { ApiResponse } from "./account";

// 角色数据模型
export interface RoleInfo {
  roleId: number;
  roleName: string;
  description?: string;
  roleCode?: string;
  status: number; // 启用状态：1=启用，0=停用
  sort: number; // 排序值
  createTime: string; // 创建时间
  updateTime?: string; // 更新时间
}

// 创建角色请求参数
export interface CreateRoleRequest {
  roleName: string;
  description?: string;
  roleCode?: string;
  status?: number;
  sort?: number;
}

// 更新角色请求参数
export interface UpdateRoleRequest {
  roleId: number;
  roleName: string;
  description?: string;
  roleCode?: string;
  status?: number;
  sort?: number;
}

// 角色查询参数
export interface RoleQueryRequest {
  roleName?: string;
  roleCode?: string;
  status?: number;
  page?: number;
  size?: number;
  sortField?: string;
  sortOrder?: string;
}

// 分页响应数据
export interface PageResponse<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 批量删除请求参数
export interface BatchDeleteRequest {
  ids: number[];
}

// 角色状态切换请求参数
export interface RoleStatusRequest {
  roleId: number;
  status: number;
}

// 获取所有角色列表
export const getAllRoles = () => {
  return http.request<ApiResponse<RoleInfo[]>>("get", "/api/thing/role/all");
};

// 分页查询角色列表
export const getRolesPage = (query: RoleQueryRequest) => {
  return http.request<ApiResponse<PageResponse<RoleInfo>>>("post", "/api/thing/role/page", {
    data: query
  });
};

// 获取角色信息
export const getRoleInfo = (roleId: number) => {
  return http.request<ApiResponse<RoleInfo>>("get", `/api/thing/role/${roleId}`);
};

// 获取用户角色
export const getUserRoles = (userId: number) => {
  return http.request<ApiResponse<RoleInfo[]>>("get", `/api/thing/role/user/${userId}`);
};

// 创建角色
export const createRole = (data: CreateRoleRequest) => {
  return http.request<ApiResponse<RoleInfo>>("post", "/api/thing/role/create", {
    data
  });
};

// 更新角色
export const updateRole = (data: UpdateRoleRequest) => {
  return http.request<ApiResponse<RoleInfo>>("put", "/api/thing/role/update", {
    data
  });
};

// 删除角色
export const deleteRole = (roleId: number) => {
  return http.request<ApiResponse<boolean>>("delete", `/api/thing/role/${roleId}`);
};

// 切换角色状态
export const updateRoleStatus = (data: RoleStatusRequest) => {
  return http.request<ApiResponse<boolean>>("put", "/api/thing/role/status", {
    data
  });
};

// 批量删除角色
export const batchDeleteRoles = (data: BatchDeleteRequest) => {
  return http.request<ApiResponse<number>>("delete", "/api/thing/role/batch", {
    data
  });
};

// 检查角色名称是否已存在
export const checkRoleName = (roleName: string, excludeId?: number) => {
  return http.request<ApiResponse<boolean>>("get", "/api/thing/role/check-name", {
    params: { roleName, excludeId }
  });
};

// 检查角色代码是否已存在
export const checkRoleCode = (roleCode: string, excludeId?: number) => {
  return http.request<ApiResponse<boolean>>("get", "/api/thing/role/check-code", {
    params: { roleCode, excludeId }
  });
};