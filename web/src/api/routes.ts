import { http } from "@/utils/http";

type Result = {
  state: boolean;
  data: Array<any>;
  code: number;
  err_msg: string | null;
  err_code: string | null;
};

interface MenuTreeItem {
  menuItemId: number;
  pluginId: string;
  menuItemName: string;
  name: string;
  parentMenuItemId: number;
  path: string;
  icon: string | null;
  component: string | null;
  showLink: boolean;
  showParent: boolean;
  activePath: string | null;
  redirect: string | null;
  rank: number;
  auths: string | null;
  roles: any[];
  children: MenuTreeItem[];
}

// 将菜单数据转换为路由数据
function transformMenuToRoute(menu: MenuTreeItem, isChild: boolean = false): any {
  const route: any = {
    path: menu.path,
    name: menu.name,
    meta: {
      title: menu.menuItemName,
      icon: menu.icon,
      showLink: menu.showLink,
      showParent: menu.showParent,
      activePath: menu.activePath,
      roles: menu.roles,
      auths: menu.auths
    }
  };

  // 只有顶级菜单才添加rank
  if (!isChild) {
    route.meta.rank = menu.rank;
  }

  // 只有当component存在时才添加component字段
  if (menu.component) {
    route.component = menu.component;
  }

  // 处理children
  if (menu.children && menu.children.length > 0) {
    route.children = menu.children.map(child => transformMenuToRoute(child, true));
    
    // 如果是父级菜单且没有component，设置redirect为第一个showLink为true的子菜单
    if (!menu.component) {
      const firstVisibleChild = menu.children.find(child => child.showLink);
      if (firstVisibleChild) {
        route.redirect = firstVisibleChild.path;
      }
    }
  }

  return route;
}

export const getAsyncRoutes = () => {
  return http.request<Result>("get", "/api/thing/permission/current-user-menutree").then(response => {
    if (response.state && response.data) {
      // 转换菜单数据为路由数据
      const routeData = response.data.map(menu => transformMenuToRoute(menu));
      console.log(routeData);
      return {
        success: true,
        data: routeData
      };
    }
    return {
      success: false,
      data: []
    };
  });
};
