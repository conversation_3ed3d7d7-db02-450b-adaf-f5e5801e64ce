import { http } from "@/utils/http";

export interface StreamLibraryInfo {
  libraryId: string;
  libraryName: string;
  libraryVersion: string;
  libraryPackage: string;
  libraryProvider: string;
  buildTime: string;
  enable: boolean;
  createTime: string;
  updateTime: string;
  shapes: ShapeNode[];
}

export interface ShapeNode {
  pluginId: number;
  shapeType: string; // 节点类型ID
  shapeName: string; // 节点名称
  shapeAlias: string; // 节点别名
  defaultIcon: string;
  defaultIconColor: string;
  defaultBkColor: string;
  version: string;
  author: string;
  groups: string[];
  tags: string[];
  tooltip: string;
  defaultOptions: any;
  optionSchema: any;
  deprecated: boolean;
  document: string;
  inlets: PortConfig[]; // 输入端口
  outlets: PortConfig[]; // 输出端口
}

export interface PortConfig {
  letId: number;
  letName: string; // 端口名称
  dataType: string[]; // 支持的数据类型
  maxFan: number; // 最大连接数
  dynamic: boolean; // 是否动态端口
}

export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

/**
 * 获取图元库列表
 */
export const getStreamLibraryList = () => {
  return http.request<ApiResponse<StreamLibraryInfo[]>>(
    "get",
    "api/thing/streams/library/list"
  );
};

/**
 * 根据libraryId获取图元库详情
 * @param libraryId 图元库ID
 */
export const getStreamLibraryById = (libraryId: string) => {
  return http.request<ApiResponse<StreamLibraryInfo>>(
    "get",
    `api/thing/streams/library/${libraryId}`
  );
};

/**
 * 上传图元库
 * @param file 图元库文件(.jar格式)
 */
export const uploadStreamLibrary = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);
  return http.request<ApiResponse<any>>(
    "post",
    "api/thing/streams/library/upload",
    {
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/**
 * 卸载图元库
 * @param libraryId 图元库ID
 */
export const unloadStreamLibrary = (libraryId: string) => {
  return http.request<ApiResponse<boolean>>(
    "put",
    "api/thing/streams/library/unload",
    {
      params: {
        libraryId
      }
    }
  );
};

/**
 * 启用图元库
 * @param libraryId 图元库ID
 */
export const enableStreamLibrary = (libraryId: string) => {
  return http.request<ApiResponse<boolean>>(
    "put",
    "api/thing/streams/library/enable",
    {
      params: {
        libraryId
      }
    }
  );
};

/**
 * 禁用图元库
 * @param libraryId 图元库ID
 */
export const disableStreamLibrary = (libraryId: string) => {
  return http.request<ApiResponse<boolean>>(
    "put",
    "api/thing/streams/library/disable",
    {
      params: {
        libraryId
      }
    }
  );
};
