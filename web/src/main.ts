import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
import { useI18n } from "@/plugins/i18n";
import { getPlatformConfig } from "./config";
import { MotionPlugin } from "@vueuse/motion";
// import { useEcharts } from "@/plugins/echarts";
import { createApp, type Directive } from "vue";
import { useElementPlus } from "@/plugins/elementPlus";
import { injectResponsiveStorage } from "@/utils/responsive";
import * as Vue from "vue";
import Table from "@pureadmin/table";
import appConfig from "./app-config";
import { initPluginSystem } from "./plugin-load";
import { install as VueMonacoEditorPlugin } from "@guolao/vue-monaco-editor";
import formCreate from '@form-create/element-ui';
import * as VueRouter from 'vue-router'
import ElementPlus, { ElMessage, ElMessageBox, ElLoading, ElInfiniteScroll, ElPopoverDirective, ElNotification } from 'element-plus'

// 引入重置样式
import "./style/reset.scss";
// 导入公共样式
import "./style/index.scss";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css";
import "element-plus/dist/index.css";
// 导入字体图标
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import ContextMenu from '@imengyu/vue3-context-menu'

(window as any).Vue = Vue; // 暴露整个 Vue 模块
(window as any).VueRouter = VueRouter; // 暴露整个 VueRouter 模块
(window as any).ElementPlus = ElementPlus; // 暴露整个 ElementPlus 模块
// (window as any).VueRouter = VueRouter;
// (window as any).Pinia = Pinia;
// (window as any).ElementPlus = ElementPlus;
// (window as any).VueUse = VueUse;
// (window as any).dayjs = dayjs;

const app = createApp(App);

// 自定义指令
import * as directives from "@/directives";
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

// 全局注册@iconify/vue图标库
import {
  IconifyIconOffline,
  IconifyIconOnline,
  FontIcon
} from "./components/ReIcon";
app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("FontIcon", FontIcon);

// 全局注册按钮级别权限组件
import { Auth } from "@/components/ReAuth";
import { Perms } from "@/components/RePerms";
app.component("Auth", Auth);
app.component("Perms", Perms);

// 全局注册vue-tippy
import "tippy.js/dist/tippy.css";
import "tippy.js/themes/light.css";
import VueTippy from "vue-tippy";
app.use(VueTippy);
try {
  // 等待插件加载完成
  await initPluginSystem(appConfig);
} catch (error) {
  console.error(error);
}
getPlatformConfig(app).then(async config => {
  setupStore(app);
  app.use(router);
  
  await router.isReady();
  injectResponsiveStorage(app, config);
  app.use(MotionPlugin).use(useI18n).use(useElementPlus);
  app.use(VueMonacoEditorPlugin);
  app.use(formCreate)
  app.use(ContextMenu)
  // .use(PureDescriptions)
  // .use(useEcharts);
  app.mount("#app");
});
