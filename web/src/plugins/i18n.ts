// 多组件库的国际化和本地项目国际化兼容
import { type I18n, createI18n } from "vue-i18n";
import type { App, WritableComputedRef } from "vue";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal, isObject } from "@pureadmin/utils";

// element-plus国际化
import enLocale from "element-plus/es/locale/lang/en";
import zhLocale from "element-plus/es/locale/lang/zh-cn";

const siphonI18n = (function () {
  const allFiles = import.meta.glob("../../locales/**/*.y(a)?ml", {
    eager: true
  });

  console.log("找到的翻译文件:", Object.keys(allFiles));

  const langMap: Record<string, any> = {};

  Object.entries(allFiles).forEach(([path, mod]: any) => {
    console.log("处理文件:", path);
    const fileName = path.split("/").pop(); // 文件名：zh-CN.yaml
    const match = fileName?.match(/^([a-zA-Z-]+)\.ya?ml$/); // zh-CN.yaml -> zh-CN
    if (!match) {
      console.log("文件名不匹配:", fileName);
      return;
    }

    const lang = match[1];
    if (!langMap[lang]) langMap[lang] = {};

    // 获取文件路径中的模块名
    const pathSegments = path.split("/locales/")[1].split("/");
    const moduleName = pathSegments[0]; // 获取第一个目录名作为模块名

    console.log("语言:", lang, "模块名:", moduleName, "路径段:", pathSegments);

    if (pathSegments.length > 1) {
      // 是子目录下的文件
      if (!langMap[lang][moduleName]) {
        langMap[lang][moduleName] = {};
      }
      // 合并子目录下的翻译
      const translations = mod.default;
      if (translations && typeof translations === "object") {
        Object.assign(langMap[lang][moduleName], translations);
        console.log("合并子目录翻译:", lang, moduleName, langMap[lang][moduleName]);
      }
    } else {
      // 是根目录下的文件
      const translations = mod.default;
      if (translations && typeof translations === "object") {
        Object.assign(langMap[lang], translations);
        console.log("合并根目录翻译:", lang, langMap[lang]);
      }
    }
  });

  console.log("最终的翻译映射:", langMap);
  return (lang = "zh-CN") => langMap[lang] || {};
})();

// 合并翻译配置
const mergeTranslations = (lang: string) => {
  const translations = siphonI18n(lang);
  const middlewareTranslations = translations.middleware || {};
  const rootTranslations = { ...translations };
  delete rootTranslations.middleware;

  return {
    ...rootTranslations,
    ...middlewareTranslations
  };
};

export const localesConfigs = {
  zh: {
    ...mergeTranslations("zh-CN"),
    ...zhLocale
  },
  en: {
    ...mergeTranslations("en-US"),
    ...enLocale
  }
};

/** 获取对象中所有嵌套对象的key键，并将它们用点号分割组成字符串 */
function getObjectKeys(obj) {
  const stack = [];
  const keys: Set<string> = new Set();

  stack.push({ obj, key: "" });

  while (stack.length > 0) {
    const { obj, key } = stack.pop();

    for (const k in obj) {
      const newKey = key ? `${key}.${k}` : k;

      if (obj[k] && isObject(obj[k])) {
        stack.push({ obj: obj[k], key: newKey });
      } else {
        keys.add(key);
      }
    }
  }

  return keys;
}

/** 将展开的key缓存 */
const keysCache: Map<string, Set<string>> = new Map();
const flatI18n = (prefix = "zh-CN") => {
  let cache = keysCache.get(prefix);
  if (!cache) {
    cache = getObjectKeys(siphonI18n(prefix));
    keysCache.set(prefix, cache);
  }
  return cache;
};

/**
 * 国际化转换工具函数（自动读取根目录locales文件夹下文件进行国际化匹配）
 * @param message message
 * @returns 转化后的message
 */
export function transformI18n(message: any = "") {
  if (!message) {
    return "";
  }

  // 处理存储动态路由的title,格式 {zh:"",en:""}
  if (typeof message === "object") {
    const locale: string | WritableComputedRef<string> | any =
      i18n.global.locale;
    return message[locale?.value];
  }

  const key = message.match(/(\S*)\./)?.input;

  if (key && flatI18n("zh-CN").has(key)) {
    return i18n.global.t.call(i18n.global.locale, message);
  } else if (!key && Object.hasOwn(siphonI18n("zh-CN"), message)) {
    // 兼容非嵌套形式的国际化写法
    return i18n.global.t.call(i18n.global.locale, message);
  } else {
    return message;
  }
}

/** 此函数只是配合i18n Ally插件来进行国际化智能提示，并无实际意义（只对提示起作用），如果不需要国际化可删除 */
export const $t = (key: string) => key;

export const i18n: I18n = createI18n({
  legacy: false,
  locale:
    storageLocal().getItem<StorageConfigs>(
      `${responsiveStorageNameSpace()}locale`
    )?.locale ?? "zh",
  fallbackLocale: "en",
  messages: localesConfigs
});

export function useI18n(app: App) {
  app.use(i18n);
}
