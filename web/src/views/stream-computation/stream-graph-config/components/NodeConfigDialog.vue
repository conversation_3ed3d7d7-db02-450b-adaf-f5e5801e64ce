<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
    align-center
  >
    <div class="node-config-container">
      <!-- 节点基本信息 -->
      <div class="node-info mb-4">
        <div class="flex items-center mb-2">
          <div
            class="w-8 h-8 rounded-md flex items-center justify-center mr-3"
            :style="{
              backgroundColor: nodeData?.color + '20',
              color: nodeData?.color
            }"
          >
            <el-icon size="16">
              <Setting />
            </el-icon>
          </div>
          <div>
            <h3
              class="text-lg font-semibold text-[var(--el-text-color-primary)]"
            >
              {{ nodeData?.label }}
            </h3>
            <p class="text-sm text-[var(--el-text-color-secondary)]">
              {{ nodeSchema?.description || nodeData?.description || "配置该节点的参数" }}
            </p>
          </div>
        </div>
      </div>

      <!-- 配置表单 -->
      <div class="config-form">
        <form-create
          v-if="formRule.length > 0"
          ref="formRef"
          v-model:api="fApi"
          v-model="formData"
          :rule="formRule"
          :option="formOption"
        />
        <div v-else class="text-center py-8">
          <el-icon
            size="48"
            class="text-[var(--el-text-color-placeholder)] mb-4"
          >
            <InfoFilled />
          </el-icon>
          <p class="text-[var(--el-text-color-secondary)]">
            该节点暂无可配置参数
          </p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { Setting, InfoFilled } from "@element-plus/icons-vue";

// 节点数据接口
interface NodeData {
  id: string;
  label: string;
  color?: string;
  type?: string;
  description?: string;
  originalElement?: any; // 原始图元数据
  [key: string]: any;
}

// 节点配置 Schema 接口
interface NodeSchema {
  id: string;
  name: string;
  description?: string;
  config?: {
    properties: Array<{
      field: string;
      title: string;
      type: string;
      props?: any;
      options?: any[];
      required?: boolean;
      defaultValue?: any;
      description?: string;
    }>;
  };
}

// 属性定义
interface Props {
  visible: boolean;
  nodeData: NodeData | null;
}

const props = defineProps<Props>();

// 事件定义
const emit = defineEmits<{
  "update:visible": [value: boolean];
  save: [nodeData: NodeData, config: any];
}>();

// 响应式数据
const formRef = ref();
const fApi = ref({});
const formData = ref({});
const saving = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

const dialogTitle = computed(() => {
  return props.nodeData ? `配置 - ${props.nodeData.label}` : "节点配置";
});

// 根据节点的optionSchema生成配置
const nodeSchema = computed((): NodeSchema | null => {
  if (!props.nodeData || !props.nodeData.originalElement) {
    console.log("NodeConfigDialog: 缺少节点数据或原始图元数据");
    return null;
  }

  const originalElement = props.nodeData.originalElement;
  const optionSchema = originalElement.optionSchema;

  console.log("NodeConfigDialog: 原始图元数据:", originalElement);
  console.log("NodeConfigDialog: optionSchema:", optionSchema);

  // 如果没有optionSchema，返回null表示无配置
  if (!optionSchema || !optionSchema.properties) {
    console.log("NodeConfigDialog: optionSchema无效或缺少properties");
    return null;
  }

  // 转换optionSchema为NodeSchema格式
  const properties = Object.entries(optionSchema.properties).map(([field, property]: [string, any]) => {
    // 根据JSON Schema类型映射为form-create类型
    let formType = "input";
    let formProps: any = {};
    let options: any[] = [];

    switch (property.type) {
      case "string":
        formType = "input";
        if (property.enum) {
          formType = "select";
          options = property.enum.map((value: any) => ({
            value: value,
            label: value
          }));
        }
        if (property.format === "textarea") {
          formProps.type = "textarea";
          formProps.rows = 4;
        }
        break;

      case "number":
      case "integer":
        formType = "inputNumber";
        if (property.minimum !== undefined) formProps.min = property.minimum;
        if (property.maximum !== undefined) formProps.max = property.maximum;
        if (property.multipleOf !== undefined) formProps.step = property.multipleOf;
        break;

      case "boolean":
        formType = "switch";
        break;

      case "array":
        if (property.items?.enum) {
          formType = "select";
          formProps.multiple = true;
          options = property.items.enum.map((value: any) => ({
            value: value,
            label: value
          }));
        }
        break;

      default:
        formType = "input";
    }

    return {
      field: field,
      title: property.title || field,
      type: formType,
      props: formProps,
      options: options.length > 0 ? options : undefined,
      required: optionSchema.required?.includes(field) || false,
      defaultValue: property.default,
      description: property.description
    };
  });

  const result = {
    id: originalElement.shapeType || "unknown",
    name: originalElement.name || props.nodeData.label,
    description: originalElement.description || "配置该节点的参数",
    config: {
      properties: properties
    }
  };

  console.log("NodeConfigDialog: 转换后的schema:", result);
  return result;
});

// form-create 规则
const formRule = computed(() => {
  if (!nodeSchema.value?.config?.properties) return [];

  return nodeSchema.value.config.properties.map(property => {
    const rule: any = {
      type: property.type,
      field: property.field,
      title: property.title,
      value: property.defaultValue,
      props: property.props || {},
      validate: []
    };

    // 添加必填验证
    if (property.required) {
      rule.validate.push({
        required: true,
        message: `请输入${property.title}`,
        trigger: ["blur", "change"]
      });
    }

    // 添加选项
    if (property.options) {
      rule.options = property.options;
    }

    // 添加描述信息
    if (property.description) {
      rule.info = property.description;
    }

    return rule;
  });
});

// form-create 配置
const formOption = ref({
  submitBtn: false,
  resetBtn: false,
  form: {
    labelWidth: "120px",
    labelPosition: "right",
    size: "default"
  }
});

// 监听弹框显示状态
watch(dialogVisible, async visible => {
  if (visible && props.nodeData) {
    await nextTick();
    // 初始化表单数据
    initFormData();
  }
});

// 初始化表单数据
const initFormData = () => {
  if (!nodeSchema.value?.config?.properties) return;

  const initialData: any = {};
  nodeSchema.value.config.properties.forEach(property => {
    initialData[property.field] = property.defaultValue;
  });

  formData.value = { ...initialData };
};

// 关闭弹框
const handleClose = () => {
  dialogVisible.value = false;
  formData.value = {};
};

// 保存配置
const handleSave = async () => {
  if (!props.nodeData) return;

  try {
    saving.value = true;

    // 如果有表单，进行验证
    if (formRef.value && formRule.value.length > 0 && fApi.value && typeof fApi.value.validate === 'function') {
      try {
        const valid = await fApi.value.validate();
        if (!valid) {
          ElMessage.error("请检查表单输入");
          return;
        }
      } catch (validationError) {
        console.warn("表单验证失败:", validationError);
        ElMessage.error("表单验证失败，请检查输入");
        return;
      }
    }

    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 触发保存事件
    emit("save", props.nodeData, formData.value);

    ElMessage.success("配置保存成功");
    handleClose();
  } catch (error) {
    console.error("保存配置失败:", error);
    ElMessage.error("保存配置失败");
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.node-config-container {
  min-height: 300px;
}

.node-info {
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.config-form {
  padding: 16px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 深度定制 form-create 样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__content) {
  flex: 1;
}

:deep(.form-create .el-form-item__error) {
  position: static;
  margin-top: 4px;
}

/* 调整表单间距 */
:deep(.el-form-item) {
  margin-bottom: 22px;
}

/* 调整文本域样式 */
:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
