// 添加自定义背景
export function addCustomBackground(area: any) {
  const container = area.container;
  if (container) {
    container.style.background = 'var(--el-bg-color)';
    
    // 添加网格背景
    const style = document.createElement('style');
    style.textContent = `
      .rete-area {
        background-color: var(--el-bg-color);
        background-image: 
          radial-gradient(circle, var(--el-border-color-lighter) 1px, transparent 1px);
        background-size: 20px 20px;
        background-position: 0 0, 10px 10px;
      }
      
      .rete-area .rete-connection path {
        stroke: var(--el-color-primary) !important;
        stroke-width: 3px !important;
        fill: none !important;
        transition: none !important;
      }
    `;
    document.head.appendChild(style);
  }
} 