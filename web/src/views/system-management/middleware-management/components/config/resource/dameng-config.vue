<template>
  <div class="dameng-config">
    <el-form :model="configData" label-width="120px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.host')" required>
              <el-input
                v-model="configData.host"
                placeholder="localhost"
                :readonly="isDetail"
                      @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.port')" required>
              <el-input-number
                v-model="configData.port"
                :min="1"
                :max="65535"
                placeholder="5236"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="Schema" required>
          <el-input
            v-model="configData.schema"
            placeholder="SYSDBA"
            :readonly="isDetail"
            @input="handleConfigChange"
          />
          <div class="form-tip">Dameng使用Schema来组织数据</div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.username')" required>
              <el-input
                v-model="configData.username"
                placeholder="SYSDBA"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.password')" required>
              <el-input
                v-model="configData.password"
                type="password"
                placeholder="请输入密码"
                :readonly="isDetail"
                show-password
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 高级配置 -->
      <el-card shadow="never" class="config-section advanced-section">
        <template #header>
          <div class="advanced-header" @click="showAdvanced = !showAdvanced">
            <span class="advanced-title">高级配置</span>
            <span class="advanced-desc" v-if="!isDetail">（展开进行配置）</span>
            <span class="advanced-desc" v-if="isDetail">（展开查看配置）</span>
            <el-icon class="advanced-arrow" :class="{ 'is-active': showAdvanced }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="showAdvanced">
            <!-- 连接池配置 -->
            <div class="sub-section">
              <h4 class="sub-title">连接池配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小连接数">
                    <el-input-number
                      v-model="configData.minPoolSize"
                      :min="0"
                      :max="100"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大连接数">
                    <el-input-number
                      v-model="configData.maxPoolSize"
                      :min="1"
                      :max="200"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="连接超时(ms)">
                    <el-input-number
                      v-model="configData.connectionTimeout"
                      :min="1000"
                      :max="300000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="空闲超时(ms)">
                    <el-input-number
                      v-model="configData.idleTimeout"
                      :min="10000"
                      :max="3600000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最大生存时间(ms)">
                    <el-input-number
                      v-model="configData.maxLifetime"
                      :min="30000"
                      :max="7200000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="泄漏检测阈值(ms)">
                    <el-input-number
                      v-model="configData.leakDetectionThreshold"
                      :min="0"
                      :max="300000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 连接验证配置 -->
            <div class="sub-section">
              <h4 class="sub-title">连接验证配置</h4>
              <el-form-item label="验证查询">
                <el-input
                  v-model="configData.validationQuery"
                  placeholder="SELECT 1 FROM DUAL"
                  :readonly="isDetail"
                  @input="handleConfigChange"
                />
              </el-form-item>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="验证超时(秒)">
                    <el-input-number
                      v-model="configData.validationTimeout"
                      :min="1"
                      :max="60"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="验证间隔(ms)">
                    <el-input-number
                      v-model="configData.timeBetweenEvictionRunsMillis"
                      :min="10000"
                      :max="300000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="获取时验证">
                    <el-switch
                      v-model="configData.testOnBorrow"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="归还时验证">
                    <el-switch
                      v-model="configData.testOnReturn"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="空闲时验证">
                    <el-switch
                      v-model="configData.testWhileIdle"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- Dameng特有配置 -->
            <div class="sub-section">
              <h4 class="sub-title">Dameng特有配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="字符编码">
                    <el-select
                      v-model="configData.characterEncoding"
                      style="width: 100%"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    >
                      <el-option label="UTF-8" value="UTF-8" />
                      <el-option label="GBK" value="GBK" />
                      <el-option label="GB2312" value="GB2312" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="应用程序名称">
                    <el-input
                      v-model="configData.applicationName"
                      placeholder="TCS-Middleware"
                      :readonly="isDetail"
                      @input="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="启用SSL">
                    <el-switch
                      v-model="configData.sslEnabled"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="自动提交">
                    <el-switch
                      v-model="configData.autoCommit"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="JMX监控">
                    <el-switch
                      v-model="configData.jmxEnabled"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="启用批处理">
                    <el-switch
                      v-model="configData.batchEnabled"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="批处理大小">
                    <el-input-number
                      v-model="configData.batchSize"
                      :min="100"
                      :max="10000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="预编译缓存">
                    <el-switch
                      v-model="configData.prepStmtCacheEnabled"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="预编译缓存大小">
                    <el-input-number
                      v-model="configData.prepStmtCacheSize"
                      :min="50"
                      :max="1000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="SQL限制长度">
                    <el-input-number
                      v-model="configData.prepStmtCacheSqlLimit"
                      :min="256"
                      :max="8192"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="事务隔离级别">
                <el-select
                  v-model="configData.transactionIsolation"
                  style="width: 100%"
                  :disabled="isDetail"
                  @change="handleConfigChange"
                >
                  <el-option label="READ_UNCOMMITTED" value="TRANSACTION_READ_UNCOMMITTED" />
                  <el-option label="READ_COMMITTED" value="TRANSACTION_READ_COMMITTED" />
                  <el-option label="REPEATABLE_READ" value="TRANSACTION_REPEATABLE_READ" />
                  <el-option label="SERIALIZABLE" value="TRANSACTION_SERIALIZABLE" />
                </el-select>
              </el-form-item>

              <el-form-item label="连接初始化SQL">
                <el-input
                  v-model="configData.connectionInitSql"
                  type="textarea"
                  :rows="2"
                  placeholder="连接建立后执行的SQL语句"
                  :readonly="isDetail"
                      @input="handleConfigChange"
                />
              </el-form-item>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, watch, ref } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  defaultConfig: {
    type: Object,
    default: () => ({})
  },
  isDetail: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'test-connection']);

// 显示高级配置
const showAdvanced = ref(false);

// 配置数据 - 与后端DamengConfig的属性保持一致
const configData = reactive({
  host: 'localhost',
  port: 5236,
  username: 'SYSDBA',
  password: '',
  schema: 'SYSDBA',
  minPoolSize: 5,
  maxPoolSize: 20,
  connectionTimeout: 30000,
  idleTimeout: 600000,
  maxLifetime: 1800000,
  leakDetectionThreshold: 60000,
  sslEnabled: false,
  applicationName: 'TCS-Middleware',
  connectionInitSql: '',
  autoCommit: true,
  transactionIsolation: 'TRANSACTION_READ_COMMITTED',
  validationQuery: 'SELECT 1 FROM DUAL',
  validationTimeout: 5,
  testOnBorrow: true,
  testOnReturn: false,
  testWhileIdle: true,
  timeBetweenEvictionRunsMillis: 60000,
  poolName: 'DamengPool',
  jmxEnabled: false,
  characterEncoding: 'UTF-8',
  batchEnabled: true,
  batchSize: 1000,
  prepStmtCacheEnabled: true,
  prepStmtCacheSize: 250,
  prepStmtCacheSqlLimit: 2048
});

// 初始化配置数据
const initConfigData = () => {
  // 合并默认配置
  Object.assign(configData, props.defaultConfig);

  // 合并传入的配置
  Object.assign(configData, props.modelValue);
};

// 处理配置变化
const handleConfigChange = () => {
  emit('update:modelValue', { ...configData });
};

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(configData, newValue);
}, { deep: true });

watch(() => props.defaultConfig, (newValue) => {
  Object.assign(configData, newValue);
}, { deep: true });

// 初始化
initConfigData();
</script>

<style scoped>
@import './common-config.scss';

.dameng-config {
  padding: 0;
}

.config-section {
  margin-bottom: 20px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-header {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.sub-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.sub-section:last-child {
  margin-bottom: 0;
}

.sub-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.test-connection-section {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.advanced-section .el-card__body {
  padding-top: 0;
}

.base-section {
  border: 1px solid #e4e7ed;
}

.advanced-section {
  border: 1px solid #e4e7ed;
}

/* 表单项样式调整 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 输入框样式 */
.el-input, .el-input-number, .el-select {
  width: 100%;
}

/* 开关样式 */
.el-switch {
  display: flex;
  align-items: center;
}

/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 高级配置头部样式 */
.advanced-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.advanced-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.advanced-desc {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.advanced-arrow {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.advanced-arrow.is-active {
  transform: rotate(180deg);
}
</style>
