<template>
  <div class="kafka-config">
    <el-form :model="configData" label-width="120px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>
        <el-form-item :label="$t('middleware.config.brokers')" required>
          <el-input
            v-model="configData.bootstrapServers"
            placeholder="localhost:9092"
            :readonly="isDetail"
            @input="handleConfigChange"
          />
          <div class="form-tip">
            多个Broker用逗号分隔，如：localhost:9092,localhost:9093
          </div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户端ID">
              <el-input
                v-model="configData.clientId"
                placeholder="kafka-client"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.consumerGroup')">
              <el-input
                v-model="configData.groupId"
                placeholder="my-consumer-group"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 高级配置 -->
      <el-card shadow="never" class="config-section advanced-section">
        <template #header>
          <div class="advanced-header" @click="showAdvanced = !showAdvanced">
            <span class="advanced-title">高级配置</span>
            <span class="advanced-desc" v-if="!isDetail">（展开进行配置）</span>
            <span class="advanced-desc" v-if="isDetail">（展开查看配置）</span>
            <el-icon class="advanced-arrow" :class="{ 'is-active': showAdvanced }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="showAdvanced">
          <el-divider content-position="left">生产者配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="确认模式">
            <el-select
              v-model="configData.acks"
              placeholder="请选择确认模式"
              style="width: 100%"
              :disabled="isDetail"
              @change="handleConfigChange"
            >
              <el-option label="0 - 不等待确认" value="0" />
              <el-option label="1 - 等待Leader确认" value="1" />
              <el-option label="all - 等待所有副本确认" value="all" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重试次数">
            <el-input-number
              v-model="configData.retries"
              :min="0"
              :max="100"
              placeholder="3"
              :readonly="isDetail"
              style="width: 100%"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="批次大小(字节)">
            <el-input-number
              v-model="configData.batchSize"
              :min="1"
              :max="1048576"
              placeholder="16384"
              style="width: 100%"
              :readonly="isDetail"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="延迟时间(ms)">
            <el-input-number
              v-model="configData.lingerMs"
              :min="0"
              :max="10000"
              placeholder="0"
              style="width: 100%"
              :readonly="isDetail"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="缓冲区大小(字节)">
        <el-input-number
          v-model="configData.bufferMemory"
          :min="1024"
          :readonly="isDetail"
          :max="134217728"
          placeholder="33554432"
          style="width: 200px"
          @change="handleConfigChange"
        />
      </el-form-item>

      <el-divider content-position="left">消费者配置</el-divider>

      <el-form-item :label="$t('middleware.config.consumerGroup')">
        <el-input
          v-model="configData.groupId"
          placeholder="my-consumer-group"
          :readonly="isDetail"
          @input="handleConfigChange"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="自动提交">
            <el-switch
              v-model="configData.enableAutoCommit"
              :disabled="isDetail"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="自动提交间隔(ms)" v-if="configData.enableAutoCommit">
            <el-input-number
              v-model="configData.autoCommitIntervalMs"
              :min="100"
              :max="60000"
              placeholder="5000"
              style="width: 100%"
              :readonly="isDetail"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="偏移量重置策略">
            <el-select
              v-model="configData.autoOffsetReset"
              placeholder="请选择重置策略"
              style="width: 100%"
              :disabled="isDetail"
              @change="handleConfigChange"
            >
              <el-option label="earliest - 从最早开始" value="earliest" />
              <el-option label="latest - 从最新开始" value="latest" />
              <el-option label="none - 抛出异常" value="none" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="会话超时(ms)">
            <el-input-number
              v-model="configData.sessionTimeoutMs"
              :min="1000"
              :max="300000"
              placeholder="30000"
              style="width: 100%"
              :readonly="isDetail"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="心跳间隔(ms)">
            <el-input-number
              v-model="configData.heartbeatIntervalMs"
              :min="1000"
              :max="30000"
              placeholder="3000"
              style="width: 100%"
              :readonly="isDetail"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大拉取记录数">
            <el-input-number
              v-model="configData.maxPollRecords"
              :min="1"
              :max="10000"
              placeholder="500"
              style="width: 100%"
              :readonly="isDetail"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">序列化配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Key序列化器">
            <el-select
              v-model="configData.keySerializer"
              placeholder="请选择Key序列化器"
              style="width: 100%"
              :disabled="isDetail"
              @change="handleConfigChange"
            >
              <el-option
                label="StringSerializer"
                value="org.apache.kafka.common.serialization.StringSerializer"
              />
              <el-option
                label="ByteArraySerializer"
                value="org.apache.kafka.common.serialization.ByteArraySerializer"
              />
              <el-option
                label="IntegerSerializer"
                value="org.apache.kafka.common.serialization.IntegerSerializer"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Value序列化器">
            <el-select
              v-model="configData.valueSerializer"
              placeholder="请选择Value序列化器"
              style="width: 100%"
              :disabled="isDetail"
              @change="handleConfigChange"
            >
              <el-option
                label="StringSerializer"
                value="org.apache.kafka.common.serialization.StringSerializer"
              />
              <el-option
                label="ByteArraySerializer"
                value="org.apache.kafka.common.serialization.ByteArraySerializer"
              />
              <el-option
                label="JsonSerializer"
                value="org.springframework.kafka.support.serializer.JsonSerializer"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Key反序列化器">
            <el-select
              v-model="configData.keyDeserializer"
              placeholder="请选择Key反序列化器"
              style="width: 100%"
              :disabled="isDetail"
              @change="handleConfigChange"
            >
              <el-option
                label="StringDeserializer"
                value="org.apache.kafka.common.serialization.StringDeserializer"
              />
              <el-option
                label="ByteArrayDeserializer"
                value="org.apache.kafka.common.serialization.ByteArrayDeserializer"
              />
              <el-option
                label="IntegerDeserializer"
                value="org.apache.kafka.common.serialization.IntegerDeserializer"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Value反序列化器">
            <el-select
              v-model="configData.valueDeserializer"
              placeholder="请选择Value反序列化器"
              style="width: 100%"
              :disabled="isDetail"
              @change="handleConfigChange"
            >
              <el-option
                label="StringDeserializer"
                value="org.apache.kafka.common.serialization.StringDeserializer"
              />
              <el-option
                label="ByteArrayDeserializer"
                value="org.apache.kafka.common.serialization.ByteArrayDeserializer"
              />
              <el-option
                label="JsonDeserializer"
                value="org.springframework.kafka.support.serializer.JsonDeserializer"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">安全配置</el-divider>

      <el-form-item label="安全协议">
        <el-select
          v-model="configData.securityProtocol"
          placeholder="请选择安全协议"
          style="width: 200px"
          :disabled="isDetail"
          @change="handleConfigChange"
        >
          <el-option label="PLAINTEXT" value="PLAINTEXT" />
          <el-option label="SSL" value="SSL" />
          <el-option label="SASL_PLAINTEXT" value="SASL_PLAINTEXT" />
          <el-option label="SASL_SSL" value="SASL_SSL" />
        </el-select>
      </el-form-item>

      <template v-if="configData.securityProtocol && configData.securityProtocol !== 'PLAINTEXT'">
        <el-form-item label="SASL机制" v-if="configData.securityProtocol.includes('SASL')">
          <el-select
            v-model="configData.saslMechanism"
            placeholder="请选择SASL机制"
            style="width: 200px"
            @change="handleConfigChange"
          >
            <el-option label="PLAIN" value="PLAIN" />
            <el-option label="SCRAM-SHA-256" value="SCRAM-SHA-256" />
            <el-option label="SCRAM-SHA-512" value="SCRAM-SHA-512" />
          </el-select>
        </el-form-item>

        <el-form-item label="JAAS配置" v-if="configData.securityProtocol.includes('SASL')">
          <el-input
            v-model="configData.saslJaasConfig"
            type="textarea"
            :rows="3"
            placeholder="org.apache.kafka.common.security.plain.PlainLoginModule required username='user' password='password';"
            @input="handleConfigChange"
          />
        </el-form-item>
      </template>

      <el-divider content-position="left">其他配置</el-divider>

      <el-form-item label="请求超时(ms)">
        <el-input-number
          v-model="configData.requestTimeoutMs"
          :min="1000"
          :max="300000"
          placeholder="30000"
          style="width: 200px"
          :readonly="isDetail"
          @change="handleConfigChange"
        />
      </el-form-item>

          <el-form-item label="额外配置">
            <el-input
              v-model="configData.additionalProperties"
              type="textarea"
              :rows="4"
              placeholder="额外的Kafka配置，每行一个，格式：key=value"
              :readonly="isDetail"
              @input="handleConfigChange"
            />
            <div class="form-tip">
              示例：<br>
              compression.type=gzip<br>
              max.request.size=1048576
            </div>
          </el-form-item>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown } from '@element-plus/icons-vue';

defineOptions({
  name: "KafkaConfig"
});

const { t } = useI18n();
const showAdvanced = ref(false);

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端KafkaConfig.java的25个字段保持一致
const configData = reactive({
  bootstrapServers: 'localhost:9092',
  clientId: '',
  groupId: 'my-consumer-group',
  keySerializer: 'org.apache.kafka.common.serialization.StringSerializer',
  valueSerializer: 'org.apache.kafka.common.serialization.StringSerializer',
  keyDeserializer: 'org.apache.kafka.common.serialization.StringDeserializer',
  valueDeserializer: 'org.apache.kafka.common.serialization.StringDeserializer',
  acks: 'all',
  retries: 3,
  batchSize: 16384,
  lingerMs: 1,
  bufferMemory: 33554432,
  autoOffsetReset: 'earliest',
  enableAutoCommit: true,
  autoCommitIntervalMs: 5000,
  sessionTimeoutMs: 30000,
  maxPollRecords: 500,
  fetchMaxBytes: 52428800,
  maxPartitionFetchBytes: 1048576,
  securityProtocol: 'PLAINTEXT',
  saslMechanism: 'PLAIN',
  saslUsername: '',
  saslPassword: '',
  additionalProducerConfig: {},
  additionalConsumerConfig: {}
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);

    // 处理额外配置显示
    if (newValue.additional && typeof newValue.additional === 'object') {
      configData.additionalProperties = Object.entries(newValue.additional)
        .map(([key, value]) => `${key}=${value}`)
        .join('\n');
    }
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
@import './common-config.scss';

.kafka-config {
  padding: 24px 32px;
  background-color: #fafafa;
  border-radius: 6px;
}

.config-section {
  margin-bottom: 24px;
}

.base-section .section-header {
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 1px;
}

.adv-title-row {
  display: flex;
  align-items: baseline;
  gap: 16px;
  margin-bottom: 4px;
  margin-top: 24px;
}
.adv-title {
  font-size: 16px;
  font-weight: normal;
  letter-spacing: 1px;
}
.adv-desc {
  font-size: 13px;
  color: #909399;
}

.adv-section .section-header {
  display: none;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-form-item__content),
:deep(.el-input__inner),
:deep(.el-select__inner),
:deep(.el-input-number__inner) {
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #303133;
}
</style>
