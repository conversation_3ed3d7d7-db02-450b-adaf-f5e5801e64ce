<template>
  <div class="postgresql-config">
    <el-form :model="configData" label-width="120px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.host')" required>
              <el-input
                v-model="configData.host"
                placeholder="localhost"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.port')" required>
              <el-input-number
                v-model="configData.port"
                :min="1"
                :max="65535"
                placeholder="5432"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item :label="$t('middleware.config.database')" required>
          <el-input
            v-model="configData.database"
            placeholder="数据库名称"
            :readonly="isDetail"
            @input="handleConfigChange"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.username')" required>
              <el-input
                v-model="configData.username"
                placeholder="postgres"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.password')" required>
              <el-input
                v-model="configData.password"
                type="password"
                placeholder="请输入密码"
                show-password
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="Schema">
          <el-input
            v-model="configData.schema"
            placeholder="public"
            :readonly="isDetail"
            @input="handleConfigChange"
          />
        </el-form-item>
      </el-card>

      <!-- 高级配置 -->
      <el-card shadow="never" class="config-section advanced-section">
        <template #header>
          <div class="advanced-header" @click="showAdvanced = !showAdvanced">
            <span class="advanced-title">高级配置</span>
            <span class="advanced-desc" v-if="!isDetail">（展开进行配置）</span>
            <span class="advanced-desc" v-if="isDetail">（展开查看配置）</span>
            <el-icon class="advanced-arrow" :class="{ 'is-active': showAdvanced }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="showAdvanced">

              <el-divider content-position="left">连接池配置</el-divider>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小空闲连接数">
                    <el-input-number
                      v-model="configData.minPoolSize"
                      :min="1"
                      :max="100"
                      placeholder="5"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大连接数">
                    <el-input-number
                      v-model="configData.maxPoolSize"
                      :min="1"
                      :max="1000"
                      placeholder="20"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="连接超时(ms)">
                    <el-input-number
                      v-model="configData.connectionTimeout"
                      :min="1000"
                      :max="300000"
                      placeholder="30000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="空闲超时(ms)">
                    <el-input-number
                      v-model="configData.idleTimeout"
                      :min="10000"
                      :max="600000"
                      placeholder="600000"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="连接最大生命周期(ms)">
                <el-input-number
                  v-model="configData.maxLifetime"
                  :min="60000"
                  :max="7200000"
                  placeholder="1800000"
                  style="width: 200px"
                  :readonly="isDetail"
                  @change="handleConfigChange"
                />
                <div class="form-tip">连接在池中的最大生命周期，默认30分钟</div>
              </el-form-item>

              <el-divider content-position="left">SSL配置</el-divider>

              <el-form-item label="SSL启用">
                <el-switch
                  v-model="configData.sslEnabled"
                  :disabled="isDetail"
                  @change="handleConfigChange"
                />
              </el-form-item>

              <template v-if="configData.sslEnabled">
                <el-form-item label="SSL模式">
                  <el-select
                    v-model="configData.sslMode"
                    placeholder="请选择SSL模式"
                    style="width: 200px"
                    :disabled="isDetail"
                    @change="handleConfigChange"
                  >
                    <el-option label="disable" value="disable" />
                    <el-option label="allow" value="allow" />
                    <el-option label="prefer" value="prefer" />
                    <el-option label="require" value="require" />
                    <el-option label="verify-ca" value="verify-ca" />
                    <el-option label="verify-full" value="verify-full" />
                  </el-select>
                </el-form-item>
              </template>

              <el-divider content-position="left">其他配置</el-divider>

              <el-form-item label="应用程序名称">
                <el-input
                  v-model="configData.applicationName"
                  placeholder="TCS-Middleware"
                  :readonly="isDetail"
                  @input="handleConfigChange"
                />
              </el-form-item>

              <el-form-item label="连接验证查询">
                <el-input
                  v-model="configData.validationQuery"
                  placeholder="SELECT 1"
                  :readonly="isDetail"
                  @input="handleConfigChange"
                />
              </el-form-item>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="自动提交">
                    <el-switch
                      v-model="configData.autoCommit"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="获取时验证">
                    <el-switch
                      v-model="configData.testOnBorrow"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="空闲时验证">
                    <el-switch
                      v-model="configData.testWhileIdle"
                      :disabled="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="事务隔离级别">
                <el-select
                  v-model="configData.transactionIsolation"
                  placeholder="请选择事务隔离级别"
                  style="width: 300px"
                  :disabled="isDetail"
                  @change="handleConfigChange"
                >
                  <el-option label="READ_UNCOMMITTED" value="TRANSACTION_READ_UNCOMMITTED" />
                  <el-option label="READ_COMMITTED" value="TRANSACTION_READ_COMMITTED" />
                  <el-option label="REPEATABLE_READ" value="TRANSACTION_REPEATABLE_READ" />
                  <el-option label="SERIALIZABLE" value="TRANSACTION_SERIALIZABLE" />
                </el-select>
              </el-form-item>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown } from '@element-plus/icons-vue';

defineOptions({
  name: "PostgreSQLConfig"
});

const { t } = useI18n();
const showAdvanced = ref(false);

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端PostgreSQLConfig.java的26个字段保持一致
const configData = reactive({
  host: 'localhost',
  port: 5432,
  database: 'tcs_middleware',
  username: 'postgres',
  password: '',
  schema: 'public',
  minPoolSize: 1,
  maxPoolSize: 10,
  connectionTimeout: 30000,
  idleTimeout: 600000,
  maxLifetime: 1800000,
  leakDetectionThreshold: 0,
  sslEnabled: false,
  sslMode: 'disable',
  applicationName: 'TCS-Middleware',
  connectionInitSql: '',
  autoCommit: true,
  transactionIsolation: 'TRANSACTION_READ_COMMITTED',
  validationQuery: 'SELECT 1',
  validationTimeout: 5000,
  testOnBorrow: false,
  testOnReturn: false,
  testWhileIdle: true,
  timeBetweenEvictionRunsMillis: 30000,
  poolName: 'PostgreSQLPool',
  jmxEnabled: false
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };

  // 构建连接URL
  let url = `jdbc:postgresql://${config.host}:${config.port}/${config.database}`;

  // 添加参数
  const params = [];

  if (config.schema && config.schema !== 'public') {
    params.push(`currentSchema=${config.schema}`);
  }

  if (config.sslMode && config.sslMode !== 'disable') {
    params.push(`ssl=true&sslmode=${config.sslMode}`);

    if (config.sslCert) {
      params.push(`sslcert=${config.sslCert}`);
    }
    if (config.sslKey) {
      params.push(`sslkey=${config.sslKey}`);
    }
    if (config.sslRootCert) {
      params.push(`sslrootcert=${config.sslRootCert}`);
    }
  }

  if (config.applicationName) {
    params.push(`ApplicationName=${config.applicationName}`);
  }

  if (config.characterEncoding) {
    params.push(`charSet=${config.characterEncoding}`);
  }

  if (config.tcpKeepAlive) {
    params.push(`tcpKeepAlive=true`);
  }

  if (config.connectionProperties) {
    params.push(config.connectionProperties);
  }

  if (params.length > 0) {
    url += '?' + params.join('&');
  }

  config.url = url;

  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
@import './common-config.scss';

.postgresql-config {
  padding: 24px 32px;
  background-color: #fafafa;
  border-radius: 6px;
}

.config-section {
  margin-bottom: 24px;
}

.base-section .section-header {
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 1px;
}

.adv-title-row {
  display: flex;
  align-items: baseline;
  gap: 16px;
  margin-bottom: 4px;
  margin-top: 24px;
}
.adv-title {
  font-size: 16px;
  font-weight: normal;
  letter-spacing: 1px;
}
.adv-desc {
  font-size: 13px;
  color: #909399;
}

.adv-section .section-header {
  display: none;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-form-item__content),
:deep(.el-input__inner),
:deep(.el-select__inner),
:deep(.el-input-number__inner) {
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #303133;
}
</style>
