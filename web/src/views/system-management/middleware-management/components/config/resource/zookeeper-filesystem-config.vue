<template>
  <div class="zookeeper-filesystem-config">
    <el-form :model="configData" label-width="140px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>
        
        <el-form-item label="连接字符串" required>
          <el-input
            v-model="configData.connectString"
            placeholder="localhost:2181"
            :readonly="isDetail"
            @input="handleConfigChange"
          />
          <div class="form-tip">Zookeeper服务器地址，多个用逗号分隔</div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="会话超时(毫秒)">
              <el-input-number
                v-model="configData.sessionTimeoutMs"
                :min="1000"
                :max="300000"
                placeholder="60000"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="连接超时(毫秒)">
              <el-input-number
                v-model="configData.connectionTimeoutMs"
                :min="1000"
                :max="300000"
                placeholder="15000"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="根路径">
              <el-input
                v-model="configData.rootPath"
                placeholder="/filesystem"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="命名空间">
              <el-input
                v-model="configData.namespace"
                placeholder="可选的命名空间"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="启用ACL">
          <el-switch
            v-model="configData.enableAcl"
            :disabled="isDetail"
            @change="handleConfigChange"
          />
          <div class="form-tip">是否启用Zookeeper访问控制列表</div>
        </el-form-item>

        <!-- ACL配置 - 仅在启用时显示 -->
        <template v-if="configData.enableAcl">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="ACL用户名" required>
                <el-input
                  v-model="configData.aclUsername"
                  placeholder="请输入用户名"
                  :readonly="isDetail"
                  @input="handleConfigChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="ACL密码" required>
                <el-input
                  v-model="configData.aclPassword"
                  type="password"
                  placeholder="请输入密码"
                  show-password
                  :readonly="isDetail"
                  @input="handleConfigChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-card>

      <!-- 高级配置 -->
      <el-card shadow="never" class="config-section advanced-section">
        <template #header>
          <div class="advanced-header" @click="showAdvanced = !showAdvanced">
            <span class="advanced-title">高级配置</span>
            <span class="advanced-desc" v-if="!isDetail">（展开进行配置）</span>
            <span class="advanced-desc" v-if="isDetail">（展开查看配置）</span>
            <el-icon class="advanced-arrow" :class="{ 'is-active': showAdvanced }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="showAdvanced">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="重试基础间隔(毫秒)">
                  <el-input-number
                    v-model="configData.baseSleepTimeMs"
                    :min="100"
                    :max="10000"
                    placeholder="1000"
                    style="width: 100%"
                    :readonly="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大重试次数">
                  <el-input-number
                    v-model="configData.maxRetries"
                    :min="0"
                    :max="10"
                    placeholder="3"
                    style="width: 100%"
                    :readonly="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="最大数据大小(字节)">
              <el-input-number
                v-model="configData.maxDataSize"
                :min="1024"
                :max="10485760"
                placeholder="1048576"
                style="width: 200px"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">单个节点的最大数据大小</div>
            </el-form-item>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown } from '@element-plus/icons-vue';

defineOptions({
  name: "ZookeeperFilesystemConfig"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// 显示高级配置
const showAdvanced = ref(false);

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端default_config保持一致
const configData = reactive({
  connectString: 'localhost:2181',
  sessionTimeoutMs: 60000,
  connectionTimeoutMs: 15000,
  baseSleepTimeMs: 1000,
  maxRetries: 3,
  rootPath: '/filesystem',
  namespace: '',
  enableAcl: false,
  aclUsername: '',
  aclPassword: '',
  maxDataSize: 1048576
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
@import './common-config.scss';

.zookeeper-filesystem-config {
  padding: 0;
}
</style>
