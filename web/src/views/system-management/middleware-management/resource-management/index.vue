<template>
  <div
    class="resource-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            {{ $t("middleware.resourceManagement") }}
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          共 {{ pagination.total }} 个资源配置
        </span>
      </div>

      <!-- 工具栏 -->
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
      >
        <!-- 搜索和操作按钮 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索资源ID或名称..."
              clearable
              :prefix-icon="Search"
              style="width: 512px"
              @input="handleSearch"
            />
            <el-select
              v-model="searchForm.resourceTypeId"
              placeholder="选择资源类型"
              clearable
              style="width: 288px"
              @change="handleSearch"
            >
              <el-option :label="$t('middleware.search.all')" value="" />
              <el-option
                v-for="type in resourceTypes"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </el-select>
            <el-button @click="handleReset">
              <el-icon size="14" class="mr-1"><Refresh /></el-icon>
              重置
            </el-button>
          </div>
          <Auth value="system:middleware:resource:add">
            <el-button
              type="primary"
              size="small"
              @click="handleAdd"
              :disabled="loading"
            >
              <el-icon size="14" class="mr-1"><Plus /></el-icon>
              {{ $t("middleware.button.addResource") }}
            </el-button>
          </Auth>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 flex flex-col min-h-0">
      <!-- 表格容器 -->
      <div class="flex-1 min-h-0">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table
              :data="tableData"
              v-loading="loading"
              stripe
              :height="height - 40"
              :width="width"
              class="rounded-lg"
              @sort-change="handleSortChange"
            >
              <el-table-column prop="id" label="资源ID" width="200" >
                <template #default="{ row }">
                  <Auth value="system:middleware:resource:detail">
                    <el-button link type="primary" @click="handleDetail(row)">{{ row.id }}</el-button>
                  </Auth>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="资源名称" min-width="180" >
                <template #default="{ row }">
                  <div>
                    <div class="font-medium text-gray-900 dark:text-white">{{ row.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.description || '暂无描述' }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="resourceId" label="资源类型" width="140" >
                <template #default="{ row }">
                  <el-tag type="info" effect="light">
                    {{ getResourceTypeName(row.resourceId) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center" >
                <template #default="{ row }">
                  <el-tag :type="row.status === 'ENABLED' ? 'success' : 'info'">
                    {{ row.status === 'ENABLED' ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="实例化" width="120" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.instantiated ? 'success' : 'warning'">
                    {{ row.instantiated ? '已实例化' : '未实例化' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="160">
                <template #default="{ row }">
                  {{ new Date(row.createTime).toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="createdBy" label="创建者" width="120"  />
              <el-table-column label="操作" width="240" fixed="right">
                <template #default="{ row }">
                  <Auth value="system:middleware:resource:edit">
                    <el-button
                      size="small"
                      type="primary"
                      text
                      @click="handleEdit(row)"
                      :disabled="row.instantiated"
                    >
                      编辑
                    </el-button>
                  </Auth>
                  <Auth value="system:middleware:resource:edit">
                    <el-button
                      size="small"
                      :type="row.status === 'ENABLED' ? 'warning' : 'success'"
                      text
                      @click="handleToggleStatus(row)"
                      :disabled="row.instantiated"
                    >
                      {{ row.status === 'ENABLED' ? '禁用' : '启用' }}
                    </el-button>
                  </Auth>
                  <Auth value="system:middleware:resource:delete">
                    <el-button
                      size="small"
                      type="danger"
                      text
                      @click="handleDelete(row)"
                      :disabled="row.instantiated"
                    >
                      删除
                    </el-button>
                  </Auth>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-auto-resizer>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center mt-4 flex-shrink-0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Refresh } from "@element-plus/icons-vue";
import {
  getResourceConfigPage,
  getResourceTypeList,
  deleteResourceConfig,
  enableResourceConfig,
  disableResourceConfig,
  instantiateResource,
  destroyResource,
  isResourceInstantiated,
  batchCheckResourceInstantiated,
  type ResourceConfiguration,
  type ResourceType
} from "@/api/system/middleware";

defineOptions({
  name: "ResourceManagement"
});

const { t } = useI18n();
const router = useRouter();

// 搜索表单
const searchForm = reactive({
  keyword: "",
  resourceTypeId: ""
});

// 分页参数
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 表格数据
const tableData = ref<(ResourceConfiguration & { instantiated?: boolean; healthStatus?: string })[]>([]);
const loading = ref(false);
const resourceTypes = ref<ResourceType[]>([]);

// 获取资源类型名称
const getResourceTypeName = (resourceId: string) => {
  const type = resourceTypes.value.find(t => t.id === resourceId);
  return type ? type.name : resourceId;
};

// 获取资源配置列表
const fetchResourceConfigs = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      resourceTypeId: searchForm.resourceTypeId || undefined
    };

    const res = await getResourceConfigPage(params);
    console.log("API响应:", res); // 调试信息

    if (res.state) {
      // 处理不同的响应结构
      if (res.data && Array.isArray(res.data.records)) {
        // 标准分页响应结构
        tableData.value = res.data.records;
        pagination.total = res.data.total || 0;
      } else if (res.data && Array.isArray(res.data)) {
        // 直接数组响应
        tableData.value = res.data;
        pagination.total = res.data.length;
      } else if (Array.isArray(res.data)) {
        // 其他数组响应
        tableData.value = res.data;
        pagination.total = res.data.length;
      } else {
        console.warn("未知的响应结构:", res.data);
        tableData.value = [];
        pagination.total = 0;
      }

      console.log("处理后的表格数据:", tableData.value); // 调试信息

      // 批量检查实例化状态
      if (tableData.value.length > 0) {
        try {
          const instantiatedRes = await batchCheckResourceInstantiated(
            tableData.value.map(item => item.id)
          );
          if (instantiatedRes.state) {
            // 更新每个资源的实例化状态
            tableData.value.forEach(item => {
              item.instantiated = instantiatedRes.data[item.id] || false;
              // 模拟健康检查状态
              if (item.instantiated) {
                // 随机分配健康状态用于演示
                const healthStatuses = ['HEALTHY', 'UNHEALTHY', 'UNKNOWN'];
                item.healthStatus = healthStatuses[Math.floor(Math.random() * healthStatuses.length)];
              } else {
                item.healthStatus = null;
              }
            });
          }
        } catch (error) {
          console.warn("批量检查资源实例化状态失败:", error);
          // 如果批量检查失败，将所有资源的实例化状态设为 false
          tableData.value.forEach(item => {
            item.instantiated = false;
            item.healthStatus = null;
          });
        }
      }
    } else {
      console.error("API调用失败:", res);
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取资源配置列表失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  } finally {
    loading.value = false;
  }
};

// 获取资源类型列表
const fetchResourceTypes = async () => {
  try {
    const res = await getResourceTypeList();
    console.log("资源类型API响应:", res); // 调试信息

    if (res.state) { // 使用 state 而不是 success
      if (Array.isArray(res.data)) {
        resourceTypes.value = res.data;
      } else {
        console.warn("资源类型响应不是数组:", res.data);
        resourceTypes.value = [];
      }
    } else {
      console.error("获取资源类型失败:", res);
      resourceTypes.value = [];
    }
  } catch (error) {
    console.error("获取资源类型列表失败:", error);
    resourceTypes.value = [];
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchResourceConfigs();
};

// 重置
const handleReset = () => {
  searchForm.keyword = "";
  searchForm.resourceTypeId = "";
  pagination.current = 1;
  fetchResourceConfigs();
};

// 新增
const handleAdd = () => {
  router.push("/system-management/middleware-management/resource-management/new");
};

// 编辑
const handleEdit = (row: ResourceConfiguration) => {
  router.push(`/system-management/middleware-management/resource-management/${row.id}/edit`);
};

// 详情
const handleDetail = (row: ResourceConfiguration) => {
  router.push(`/system-management/middleware-management/resource-management/${row.id}/detail`);
};

// 删除
const handleDelete = async (row: ResourceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDelete"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await deleteResourceConfig(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.deleteSuccess"));
      fetchResourceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("删除资源配置失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 实例化
const handleInstantiate = async (row: ResourceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmInstantiate"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await instantiateResource(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.instantiateSuccess"));
      fetchResourceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("实例化资源失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 销毁
const handleDestroy = async (row: ResourceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDestroy"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await destroyResource(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.destroySuccess"));
      fetchResourceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("销毁资源失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchResourceConfigs();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  fetchResourceConfigs();
};

// 切换状态
const handleToggleStatus = async (row: ResourceConfiguration) => {
  try {
    if (row.status === 'ENABLED') {
      await ElMessageBox.confirm(
        t("middleware.message.confirmDisable"),
        t("middleware.button.confirm"),
        { type: "warning" }
      );
      const res = await disableResourceConfig(row.id);
      if (res.state) {
        ElMessage.success(t("middleware.message.disableSuccess"));
        fetchResourceConfigs();
      } else {
        ElMessage.error(t("middleware.message.operationFailed"));
      }
    } else {
      await ElMessageBox.confirm(
        t("middleware.message.confirmEnable"),
        t("middleware.button.confirm"),
        { type: "warning" }
      );
      const res = await enableResourceConfig(row.id);
      if (res.state) {
        ElMessage.success(t("middleware.message.enableSuccess"));
        fetchResourceConfigs();
      } else {
        ElMessage.error(t("middleware.message.operationFailed"));
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("切换状态失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

onMounted(() => {
  fetchResourceTypes();
  fetchResourceConfigs();
});
</script>

<style scoped>
.resource-management-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
