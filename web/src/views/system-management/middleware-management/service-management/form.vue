<template>
  <div class="service-form-container">
    <div class="header">
      <h2>{{ isEdit ? $t("middleware.button.edit") : $t("middleware.button.add") }}{{ $t("middleware.serviceManagement") }}</h2>
      <el-button @click="handleBack">
        <el-icon><ArrowLeft /></el-icon>
        {{ $t("middleware.button.back") }}
      </el-button>
    </div>

    <el-card shadow="never">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="loading"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <h3>{{ $t("middleware.form.basicInfo") }}</h3>

          <el-form-item :label="$t('middleware.form.id')" prop="id">
            <el-input
              v-model="formData.id"
              :disabled="isEdit"
              :placeholder="isEdit ? '' : $t('middleware.form.placeholder.id') || '请输入服务ID'"
            />
            <div class="form-tip" v-if="!isEdit">服务的唯一标识符，创建后不可修改</div>
          </el-form-item>

          <el-form-item :label="$t('middleware.form.name')" prop="name">
            <el-input
              v-model="formData.name"
              :placeholder="$t('middleware.form.placeholder.name')"
            />
          </el-form-item>

          <el-form-item :label="$t('middleware.form.description')" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              :placeholder="$t('middleware.form.placeholder.description')"
            />
          </el-form-item>

          <el-form-item :label="$t('middleware.form.serviceType')" prop="serviceId">
            <el-select
              v-model="formData.serviceId"
              :placeholder="$t('middleware.form.placeholder.selectServiceType')"
              style="width: 100%"
              @change="handleServiceTypeChange"
            >
              <el-option
                v-for="type in serviceTypes"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            :label="$t('middleware.form.resourceConfig')"
            prop="resourceConfigurationId"
            v-if="selectedServiceType && selectedServiceType.supportedResourceCategory !== 'NONE'"
          >
            <el-select
              v-model="formData.resourceConfigurationId"
              :placeholder="$t('middleware.form.placeholder.selectResourceConfig')"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="config in filteredResourceConfigs"
                :key="config.id"
                :label="config.name + ' - (' + config.id + ')'"
                :value="config.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('middleware.table.status')" prop="status">
            <el-switch
              v-model="formData.status"
              :active-value="'ENABLED'"
              :inactive-value="'DISABLED'"
              :active-text="$t('middleware.button.enable')"
              :inactive-text="$t('middleware.button.disable')"
            />
          </el-form-item>
        </div>

        <!-- 配置区域 -->
        <div class="form-section" v-if="formData.serviceId">
          <h3>{{ $t("middleware.form.configArea") }}</h3>

          <!-- 动态配置组件 -->
          <component
            :is="configComponent"
            v-if="configComponent"
            v-model="formData.config"
            :default-config="defaultConfig"
            :resource-configs="filteredResourceConfigs"
            @test-connection="handleTestConnection"
          />

          <!-- 通用JSON配置编辑器 -->
          <el-form-item v-else :label="$t('middleware.form.config')" prop="config">
            <el-input
              v-model="configJson"
              type="textarea"
              :rows="10"
              :placeholder="$t('middleware.form.placeholder.jsonConfig')"
              @blur="handleConfigJsonChange"
            />
          </el-form-item>

        </div>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            {{ $t("middleware.button.save") }}
          </el-button>
          <el-button @click="handleBack">
            <el-icon><Close /></el-icon>
            {{ $t("middleware.button.cancel") }}
          </el-button>
          <el-button
            type="success"
            @click="handleTestConnection"
            :loading="testingConnection"
            :disabled="!formData.serviceId || !formData.config"
          >
            <el-icon><Connection /></el-icon>
            {{ $t("middleware.button.testConnection") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, defineAsyncComponent } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { ArrowLeft, Check, Close, Connection } from "@element-plus/icons-vue";
import {
  getServiceTypeList,
  getResourceConfigList,
  getResourceTypesByCategory,
  getServiceConfigById,
  createServiceConfig,
  updateServiceConfig,
  testServiceConnection,
  getResourceTypeList,
  type ServiceConfiguration,
  type ServiceType,
  type ResourceConfiguration,
  type ResourceType
} from "@/api/system/middleware";

defineOptions({
  name: "ServiceForm"
});

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 表单引用
const formRef = ref<FormInstance>();

// 是否编辑模式
const isEdit = computed(() => route.name === "ServiceManagementEdit");

// 表单数据
const formData = reactive<Partial<ServiceConfiguration>>({
  id: "",
  name: "",
  description: "",
  serviceId: "",
  resourceConfigurationId: "",
  config: {},
  status: "ENABLED"
});

// 表单验证规则
const formRules: FormRules = {
  id: [
    { required: true, message: t("middleware.form.rules.idRequired") || "服务ID不能为空", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: t("middleware.form.rules.idFormat") || "服务ID只能包含字母、数字、下划线和短横线",
      trigger: "blur"
    },
    {
      min: 2,
      max: 50,
      message: t("middleware.form.rules.idLength") || "服务ID长度应在2-50个字符之间",
      trigger: "blur"
    }
  ],
  name: [
    { required: true, message: t("middleware.form.rules.nameRequired"), trigger: "blur" }
  ],
  serviceId: [
    { required: true, message: t("middleware.form.rules.serviceTypeRequired"), trigger: "change" }
  ],
  resourceConfigurationId: [
    {
      required: true,
      message: t("middleware.form.rules.resourceConfigRequired"),
      trigger: "change",
      validator: (rule, value, callback) => {
        if (selectedServiceType.value?.supportedResourceCategory === "NONE") {
          callback();
        } else if (!value) {
          callback(new Error(t("middleware.form.rules.resourceConfigRequired")));
        } else {
          callback();
        }
      }
    }
  ]
};

// 状态
const loading = ref(false);
const submitting = ref(false);
const testingConnection = ref(false);
const serviceTypes = ref<ServiceType[]>([]);
const resourceConfigs = ref<ResourceConfiguration[]>([]);
const resourceTypes = ref<ResourceType[]>([]);
const selectedServiceType = ref<ServiceType | null>(null);
const defaultConfig = ref<Record<string, any>>({});

// 配置JSON字符串（用于通用编辑器）
const configJson = ref("");

// 过滤后的资源配置（根据服务类型支持的资源类别）
const filteredResourceConfigs = computed(() => {
  if (!selectedServiceType.value?.supportedResourceCategory) {
    return resourceConfigs.value;
  }

  // 获取支持的资源类型ID列表
  const supportedResourceTypeIds = resourceTypes.value
    .filter(rt => rt.category === selectedServiceType.value!.supportedResourceCategory)
    .map(rt => rt.id);

  // 过滤资源配置
  return resourceConfigs.value.filter(rc =>
    supportedResourceTypeIds.includes(rc.resourceId)
  );
});

// 动态配置组件
const configComponent = computed(() => {
  if (!selectedServiceType.value?.uiComponent) return null;

  try {
    // 根据uiComponent动态加载组件
    const componentName = selectedServiceType.value.uiComponent;
    return defineAsyncComponent(() =>
      import(`../components/config/service/${componentName}.vue`).catch(() => null)
    );
  } catch (error) {
    console.warn("加载配置组件失败:", error);
    return null;
  }
});

// 获取服务类型列表
const fetchServiceTypes = async () => {
  try {
    const res = await getServiceTypeList();
    if (res.state) {
      serviceTypes.value = res.data;
    }
  } catch (error) {
    console.error("获取服务类型列表失败:", error);
  }
};

// 获取资源配置列表
const fetchResourceConfigs = async () => {
  try {
    const res = await getResourceConfigList();
    if (res.state) {
      resourceConfigs.value = res.data;
    }
  } catch (error) {
    console.error("获取资源配置列表失败:", error);
  }
};

// 获取资源类型列表
const fetchResourceTypes = async () => {
  try {
    const res = await getResourceTypeList();
    if (res.state) {
      resourceTypes.value = res.data;
      console.log("获取到的资源类型列表:", resourceTypes.value);
    }
  } catch (error) {
    console.error("获取资源类型列表失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  }
};

// 获取服务配置详情（编辑模式）
const fetchServiceConfig = async (id: string) => {
  loading.value = true;
  try {
    const res = await getServiceConfigById(id);
    if (res.state) {
      Object.assign(formData, res.data);
      configJson.value = JSON.stringify(formData.config, null, 2);

      // 设置选中的服务类型
      selectedServiceType.value = serviceTypes.value.find(t => t.id === formData.serviceId) || null;
      if (selectedServiceType.value) {
        defaultConfig.value = selectedServiceType.value.defaultConfig || {};
      }
    }
  } catch (error) {
    console.error("获取服务配置详情失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  } finally {
    loading.value = false;
  }
};

// 服务类型改变
const handleServiceTypeChange = (serviceId: string) => {
  selectedServiceType.value = serviceTypes.value.find(t => t.id === serviceId) || null;
  if (selectedServiceType.value) {
    defaultConfig.value = selectedServiceType.value.defaultConfig || {};
    // 如果是新增模式，使用默认配置
    if (!isEdit.value) {
      formData.config = { ...defaultConfig.value };
      configJson.value = JSON.stringify(formData.config, null, 2);
    }

    // 清空资源配置选择（如果新的服务类型不支持当前选择的资源）
    if (formData.resourceConfigurationId && !filteredResourceConfigs.value.find(rc => rc.id === formData.resourceConfigurationId)) {
      formData.resourceConfigurationId = "";
    }
  }
};

// 配置JSON改变
const handleConfigJsonChange = () => {
  try {
    formData.config = JSON.parse(configJson.value);
  } catch (error) {
    console.warn("JSON格式错误:", error);
  }
};

// 测试连接
const handleTestConnection = async () => {
  if (!formData.serviceId || !formData.config) {
    ElMessage.warning(t("middleware.message.configRequired"));
    return;
  }

  testingConnection.value = true;
  try {
    const res = await testServiceConnection(formData.serviceId, formData.config);
    if (res.state && res.data.success) {
      ElMessage.success(t("middleware.message.testConnectionSuccess"));
    } else {
      ElMessage.error(res.data.message || t("middleware.message.testConnectionFailed"));
    }
  } catch (error) {
    console.error("测试连接失败:", error);
    ElMessage.error(t("middleware.message.testConnectionFailed"));
  } finally {
    testingConnection.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitting.value = true;

    // 确保config是对象
    if (typeof formData.config === 'string') {
      try {
        formData.config = JSON.parse(formData.config);
      } catch (error) {
        ElMessage.error(t("middleware.message.invalidConfig"));
        return;
      }
    }

    let res;
    if (formData.resourceConfigurationId === "") {
      formData.resourceConfigurationId = null;
    }
    if (isEdit.value) {
      res = await updateServiceConfig(formData.id!, formData);
    } else {
      // 新增时确保包含ID字段
      const createData = {
        id: formData.id,
        name: formData.name,
        description: formData.description,
        serviceId: formData.serviceId,
        resourceConfigurationId: formData.resourceConfigurationId,
        config: formData.config,
        status: formData.status,
        createdBy: formData.createdBy || 'system',
        updatedBy: formData.updatedBy || 'system'
      };
      res = await createServiceConfig(createData);
    }

    if (res.state) {
      ElMessage.success(t(isEdit.value ? "middleware.message.editSuccess" : "middleware.message.addSuccess"));
      handleBack();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    console.error("提交表单失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  } finally {
    submitting.value = false;
  }
};

// 返回
const handleBack = () => {
  router.push({ name: "ServiceManagement" });
};

onMounted(async () => {
  await Promise.all([
    fetchServiceTypes(),
    fetchResourceConfigs(),
    fetchResourceTypes()
  ]);

  if (isEdit.value && route.params.id) {
    await fetchServiceConfig(route.params.id as string);
  }
});
</script>

<style scoped>
.service-form-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  position: relative;
  padding-left: 12px;
}

.form-section h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: var(--el-color-primary);
  border-radius: 2px;
}
</style>
