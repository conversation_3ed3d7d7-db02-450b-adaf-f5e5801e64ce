<template>
  <div
    class="service-management-container bg-gray-50 dark:bg-gray-900 p-4 flex flex-col"
    style="height: calc(100vh - 48px)"
  >
    <!-- 页面头部 -->
    <div class="mb-4 flex-shrink-0">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            {{ $t("middleware.serviceManagement") }}
          </h1>
        </div>
        <span class="text-sm text-gray-600 dark:text-gray-400">
          共 {{ pagination.total }} 个服务配置
        </span>
      </div>
      <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
      >
        <!-- 搜索和操作按钮 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索服务ID或名称..."
              clearable
              :prefix-icon="Search"
              style="width: 512px"
              @input="handleSearch"
            />
            <el-select
              v-model="searchForm.serviceId"
              placeholder="选择服务类型"
              clearable
              style="width: 240px"
              @change="handleSearch"
            >
              <el-option
                v-for="type in serviceTypes"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </el-select>
            <el-button @click="handleReset">
              <el-icon size="14" class="mr-1"><Refresh /></el-icon>
              重置
            </el-button>
          </div>
          <Auth value="system:middleware:service:add">
            <el-button
              type="primary"
              size="small"
              @click="handleAdd"
              :disabled="loading"
            >
              <el-icon size="14" class="mr-1"><Plus /></el-icon>
              {{ $t("middleware.button.addService") }}
            </el-button>
          </Auth>
        </div>
      </div>

      <!-- 搜索区域 -->
      <!-- <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4">
        <el-form :model="searchForm" inline>
          <el-form-item>
            <el-select
              v-model="searchForm.serviceId"
              placeholder="选择服务类型"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="type in serviceTypes"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="searchForm.status"
              placeholder="选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" value="ENABLED" />
              <el-option label="禁用" value="DISABLED" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <Auth value="system:middleware:service:add">
              <el-button type="primary" @click="handleAdd" :disabled="loading">
                <el-icon><Plus /></el-icon>
                {{ $t("middleware.button.addService") }}
              </el-button>
            </Auth>
          </el-form-item>
        </el-form>
      </div> -->
    </div>



    <!-- 内容区域 -->
    <div class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 flex flex-col min-h-0">
      <!-- 表格容器 -->
      <div class="flex-1 min-h-0">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table
              :data="tableData"
              v-loading="loading"
              stripe
              :height="height"
              :width="width"
              class="rounded-lg"
            >
              <el-table-column prop="id" label="服务ID" width="200" >
                <template #default="{ row }">
                  <Auth value="system:middleware:service:detail">
                    <el-button link type="primary" @click="handleDetail(row)">{{ row.id }}</el-button>
                  </Auth>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="服务名称" min-width="180" >
                <template #default="{ row }">
                  <div>
                    <div class="font-medium">{{ row.name }}</div>
                    <div class="text-xs text-gray-500 mt-1">{{ row.description || '-' }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="serviceId" :label="$t('middleware.table.serviceType')" width="150">
                <template #default="{ row }">
                  {{ getServiceTypeName(row.serviceId) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" :label="$t('middleware.table.status')" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'ENABLED' ? 'success' : 'info'" effect="light">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'w-2 h-2 rounded-full mr-2',
                          row.status === 'ENABLED'
                            ? 'bg-green-500 animate-pulse'
                            : 'bg-gray-400'
                        ]"
                      />
                      {{ row.status === 'ENABLED' ? $t('middleware.status.enabled') : $t('middleware.status.disabled') }}
                    </div>
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="$t('middleware.table.instantiated')" width="120" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.instantiated ? 'success' : 'warning'" effect="light">
                    {{ row.instantiated ? $t('middleware.status.instantiated') : $t('middleware.status.notInstantiated') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="resourceConfigurationId" :label="$t('middleware.table.resourceConfig')" width="180">
                <template #default="{ row }">
                  <div>
                    <div class="font-medium">{{ getResourceConfigName(row.resourceConfigurationId) }}</div>
                    <div class="text-xs text-gray-500 mt-1">{{ row.resourceConfigurationId }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" :label="$t('middleware.table.createTime')" width="160">
                <template #default="{ row }">
                  {{ new Date(row.createTime).toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="createdBy" :label="$t('middleware.table.createdBy')" width="100" />
              <el-table-column
                label="操作"
                width="200"
                fixed="right"
              >
                <template #default="{ row }">
                  <Auth value="system:middleware:service:edit">
                    <el-button
                      size="small"
                      type="primary"
                      text
                      @click="handleEdit(row)"
                    >
                      编辑
                    </el-button>
                  </Auth>
                  <el-dropdown
                    trigger="click"
                    @command="(command: string) => handleCommand(command, row)"
                  >
                    <el-button size="small" type="primary" text class="ml-2">
                      更多
                      <el-icon size="12" class="ml-1"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          v-if="row.status === 'ENABLED'"
                          command="disable"
                        >
                          <el-icon size="14" class="mr-2 text-orange-500">
                            <VideoPause />
                          </el-icon>
                          {{ $t("middleware.button.disable") }}
                        </el-dropdown-item>
                        <el-dropdown-item v-else command="enable">
                          <el-icon size="14" class="mr-2 text-green-500">
                            <VideoPlay />
                          </el-icon>
                          {{ $t("middleware.button.enable") }}
                        </el-dropdown-item>
                        <el-dropdown-item command="delete" class="text-red-500">
                          <el-icon size="14" class="mr-2"><Delete /></el-icon>
                          {{ $t("middleware.button.delete") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-auto-resizer>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center mt-4 flex-shrink-0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Refresh, Setting, ArrowDown, VideoPause, VideoPlay, Delete } from "@element-plus/icons-vue";
import {
  getServiceConfigPage,
  getServiceTypeList,
  getResourceConfigList,
  deleteServiceConfig,
  enableServiceConfig,
  disableServiceConfig,
  instantiateService,
  destroyService,
  isServiceInstantiated,
  batchCheckServiceInstantiated,
  type ServiceConfiguration,
  type ServiceType,
  type ResourceConfiguration
} from "@/api/system/middleware";

defineOptions({
  name: "ServiceManagement"
});

const { t } = useI18n();
const router = useRouter();

// 搜索表单
const searchForm = reactive({
  keyword: "",
  serviceId: "",
  status: ""
});

// 分页参数
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 表格数据
const tableData = ref<(ServiceConfiguration & { instantiated?: boolean })[]>([]);
const loading = ref(false);
const serviceTypes = ref<ServiceType[]>([]);
const resourceConfigs = ref<ResourceConfiguration[]>([]);

// 获取服务类型名称
const getServiceTypeName = (serviceId: string) => {
  const type = serviceTypes.value.find(t => t.id === serviceId);
  return type ? type.name : serviceId;
};

// 获取资源配置名称
const getResourceConfigName = (resourceConfigId: string) => {
  if (!resourceConfigId) return '-';
  const config = resourceConfigs.value.find(c => c.id === resourceConfigId);
  return config ? config.name : resourceConfigId;
};

// 获取服务配置列表
const fetchServiceConfigs = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      serviceId: searchForm.serviceId || undefined,
      status: searchForm.status || undefined
    };

    const res = await getServiceConfigPage(params);
    console.log("服务配置API响应:", res); // 调试信息

    if (res.state) { // 使用 state 而不是 success
      // 处理不同的响应结构
      if (res.data && Array.isArray(res.data.records)) {
        tableData.value = res.data.records;
        pagination.total = res.data.total || 0;
      } else if (Array.isArray(res.data)) {
        tableData.value = res.data;
        pagination.total = res.data.length;
      } else {
        console.warn("未知的服务配置响应结构:", res.data);
        tableData.value = [];
        pagination.total = 0;
      }

      // 检查每个服务的实例化状态
      if (tableData.value.length > 0) {
        try {
          const instantiatedRes = await batchCheckServiceInstantiated(
            tableData.value.map(item => item.id)
          );
          if (instantiatedRes.state) {
            // 更新每个服务的实例化状态
            tableData.value.forEach(item => {
              item.instantiated = instantiatedRes.data[item.id] || false;
            });
          }
        } catch (error) {
          console.warn("批量检查服务实例化状态失败:", error);
          // 如果批量检查失败，将所有服务的实例化状态设为 false
          tableData.value.forEach(item => {
            item.instantiated = false;
          });
        }
      }
    } else {
      console.error("获取服务配置失败:", res);
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取服务配置列表失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  } finally {
    loading.value = false;
  }
};

// 获取服务类型列表
const fetchServiceTypes = async () => {
  try {
    const res = await getServiceTypeList();
    if (res.state) { // 使用 state 而不是 success
      serviceTypes.value = res.data;
    }
  } catch (error) {
    console.error("获取服务类型列表失败:", error);
  }
};

// 获取资源配置列表
const fetchResourceConfigs = async () => {
  try {
    const res = await getResourceConfigList();
    if (res.state) { // 使用 state 而不是 success
      resourceConfigs.value = res.data;
    }
  } catch (error) {
    console.error("获取资源配置列表失败:", error);
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchServiceConfigs();
};

// 重置
const handleReset = () => {
  searchForm.keyword = "";
  searchForm.serviceId = "";
  searchForm.status = "";
  pagination.current = 1;
  fetchServiceConfigs();
};

// 新增
const handleAdd = () => {
  router.push("/system-management/middleware-management/service-management/new");
};

// 编辑
const handleEdit = (row: ServiceConfiguration) => {
  router.push(`/system-management/middleware-management/service-management/${row.id}/edit`);
};

// 详情
const handleDetail = (row: ServiceConfiguration) => {
  router.push(`/system-management/middleware-management/service-management/${row.id}/detail`);
};

// 切换状态
const handleToggleStatus = async (row: ServiceConfiguration) => {
  const isEnable = row.status !== 'ENABLED';
  const action = isEnable ? 'enable' : 'disable';
  const confirmMessage = isEnable ? 'confirmEnable' : 'confirmDisable';

  try {
    await ElMessageBox.confirm(
      t(`middleware.message.${confirmMessage}`),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = isEnable ? await enableServiceConfig(row.id) : await disableServiceConfig(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t(`middleware.message.${action}Success`));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}服务配置失败:`, error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 删除
const handleDelete = async (row: ServiceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDelete"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await deleteServiceConfig(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.deleteSuccess"));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("删除服务配置失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 实例化
const handleInstantiate = async (row: ServiceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmInstantiate"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await instantiateService(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.instantiateSuccess"));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("实例化服务失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 销毁
const handleDestroy = async (row: ServiceConfiguration) => {
  try {
    await ElMessageBox.confirm(
      t("middleware.message.confirmDestroy"),
      t("middleware.button.confirm"),
      { type: "warning" }
    );

    const res = await destroyService(row.id);
    if (res.state) { // 使用 state 而不是 success
      ElMessage.success(t("middleware.message.destroySuccess"));
      fetchServiceConfigs();
    } else {
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error("销毁服务失败:", error);
      ElMessage.error(t("middleware.message.operationFailed"));
    }
  }
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchServiceConfigs();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  fetchServiceConfigs();
};



// 处理命令
const handleCommand = async (command: string, row: ServiceConfiguration) => {
  switch (command) {
    case "enable":
    case "disable":
      await handleToggleStatus(row);
      break;
    case "delete":
      await handleDelete(row);
      break;
  }
};

onMounted(() => {
  fetchServiceTypes();
  fetchResourceConfigs();
  fetchServiceConfigs();
});
</script>

<style scoped>
.service-management-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
