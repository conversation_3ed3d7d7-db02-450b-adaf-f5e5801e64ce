<template>
  <div class="tree-node-item">
    <div 
      v-if="node.type !== 'shard-heatmap' || node.label"
      class="flex items-center py-2 px-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
      :style="{ paddingLeft: `${level * 20 + 12}px` }"
      @click="handleClick"
    >
    <!-- 展开/折叠图标 -->
    <div class="w-4 h-4 mr-2 flex items-center justify-center">
      <el-icon 
        v-if="!node.isLeaf"
        size="14" 
        class="text-gray-400 transition-transform duration-200"
        :class="{ 'transform rotate-90': expanded }"
      >
        <ArrowRight />
      </el-icon>
    </div>
    
    <!-- 节点图标 -->
    <el-icon 
      size="16" 
      class="text-gray-600 dark:text-gray-400 mr-2 flex-shrink-0"
    >
      <component :is="node.icon" />
    </el-icon>
    
    <!-- 节点内容 -->
    <div class="flex-1 flex items-center gap-2 min-w-0">
      <!-- 节点标签 -->
      <span 
        class="text-sm truncate"
        :class="{
          'font-medium': node.type === 'cluster' || node.type === 'node',
          'text-gray-500 dark:text-gray-400 italic': node.label === '无实体'
        }"
      >
        {{ node.label }}
      </span>
      
      <!-- 状态指示器（仅节点类型显示） -->
      <div 
        v-if="node.type === 'node' && node.nodeData"
        :class="`w-2 h-2 rounded-full flex-shrink-0 ${getStatusColor(node.nodeData.status)}`"
      />
      
      <!-- 标识徽章 -->
      <div v-if="node.badges && node.badges.length > 0" class="flex gap-1 flex-shrink-0">
        <el-tag 
          v-for="badge in node.badges" 
          :key="badge"
          :type="badge === 'Leader' ? 'warning' : 'success'"
          size="small"
          class="text-xs h-4"
        >
          {{ badge }}
        </el-tag>
      </div>
    </div>
    
    
    <!-- Actor路径（实体类型显示） -->
    <div 
      v-if="node.type === 'entity' && node.nodeData?.actorPath" 
      class="text-xs text-gray-500 dark:text-gray-400 font-mono ml-2 truncate flex-shrink-0 max-w-xs"
    >
      {{ node.nodeData.actorPath }}
    </div>
    </div>
    
    <!-- 分片概览卡片 -->
    <div 
      v-if="node.type === 'shard-heatmap' && node.nodeData" 
      class="mt-1 mb-1 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
      :style="{ marginLeft: `${level * 20 + 12}px`, marginRight: '20px' }"
    >
      <div class="flex flex-wrap gap-2.5">
        <div 
          v-for="(shard, index) in node.nodeData.shards" 
          :key="index"
          class="w-16 h-16 rounded-lg border border-gray-300 dark:border-gray-500 flex flex-col items-center justify-center font-mono cursor-pointer hover:border-primary transition-all hover:scale-105 relative group flex-shrink-0 shadow-sm hover:shadow-md"
          :style="{
            backgroundColor: getShardHeatColor(shard.entityCount, node.nodeData.shards)
          }"
          @click="handleHeatmapShardClick(node.nodeData.hostname, node.nodeData.port, shard.shardId)"
        >
          <div class="text-[10px] text-gray-600 dark:text-gray-400 leading-none">分片ID</div>
          <div class="font-bold text-gray-900 text-sm leading-none mt-0.5">{{ shard.shardId }}</div>
          <div class="text-[10px] text-gray-600 dark:text-gray-400 leading-none mt-1">实体数</div>
          <div class="font-semibold text-gray-800 dark:text-gray-200 text-xs leading-none">{{ shard.entityCount }}</div>
          
          <!-- Tooltip -->
          <el-tooltip 
            effect="dark" 
            placement="top"
            :content="`分片 ${shard.shardId}: ${shard.entityCount} 个实体`"
          >
            <div class="absolute inset-0" />
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowRight } from "@element-plus/icons-vue";

interface Props {
  node: any;
  level: number;
  expanded: boolean;
}

interface Emits {
  (e: 'toggle', key: string): void;
  (e: 'shardClick', nodeId: string, shardId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleClick = () => {
  if (!props.node.isLeaf) {
    emit('toggle', props.node.key);
  }
};

// 删除单个分片节点的点击处理，因为不再需要

const handleHeatmapShardClick = (hostname: string, port: number, shardId: string) => {
  const nodeId = `${hostname}:${port}`;
  emit('shardClick', nodeId, shardId);
};

// 分片热力图颜色计算
const getShardHeatColor = (entityCount: number, allShards: any[]) => {
  if (!allShards.length) return 'rgba(34, 197, 94, 0.1)';
  
  const maxEntities = Math.max(...allShards.map((s: any) => s.entityCount));
  const intensity = maxEntities > 0 ? entityCount / maxEntities : 0;
  
  // 使用绿色渐变色彩
  return `rgba(34, 197, 94, ${intensity * 0.8 + 0.1})`;
};

// 状态颜色函数
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'up':
      return 'bg-green-500';
    case 'down':
      return 'bg-red-500';
    case 'joining':
      return 'bg-yellow-500';
    default:
      return 'bg-gray-500';
  }
};
</script>

<style scoped>
.font-mono {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Fira Mono', 'Droid Sans Mono', 'Consolas', monospace;
}
</style>