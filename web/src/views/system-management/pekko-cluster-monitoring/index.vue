<template>
  <div class="main bg-gray-50 dark:bg-gray-900 p-4 flex flex-col">
    <div class="w-full flex flex-col h-full">
      <!-- 页面头部 -->
      <div class="flex items-center justify-between mb-4 flex-shrink-0">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            Pekko 运行监控
          </h1>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            最后更新: {{ formatTimestamp(clusterData?.timestamp) }}
          </span>
          <div class="flex items-center gap-2">
            <div 
              :class="`w-3 h-3 rounded-full ${clusterData?.state ? 'bg-green-500' : 'bg-red-500'}`"
            />
            <span class="text-sm font-medium">
              {{ clusterData?.state ? '集群正常' : '集群异常' }}
            </span>
          </div>
          <el-button 
            type="primary" 
            size="small" 
            :loading="loading"
            @click="refreshData"
          >
            <el-icon class="mr-1"><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 概览卡片 -->
      <div class="grid grid-cols-4 gap-4 mb-4 flex-shrink-0">
        <!-- 节点总数 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-row items-center justify-between pb-1">
            <h3 class="text-xs font-medium text-gray-600 dark:text-gray-400">节点总数</h3>
            <el-icon size="14" class="text-gray-400"><Monitor /></el-icon>
          </div>
          <div class="text-xl font-bold text-gray-900 dark:text-white">{{ totalNodes }}</div>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            {{ upNodes }} 个在线
          </p>
        </div>

        <!-- 分片总数 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-row items-center justify-between pb-1">
            <h3 class="text-xs font-medium text-gray-600 dark:text-gray-400">分片总数</h3>
            <el-icon size="14" class="text-gray-400"><Collection /></el-icon>
          </div>
          <div class="text-xl font-bold text-gray-900 dark:text-white">{{ totalShards }}</div>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            {{ totalNodes }} 个节点
          </p>
        </div>

        <!-- 实体总数 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-row items-center justify-between pb-1">
            <h3 class="text-xs font-medium text-gray-600 dark:text-gray-400">实体总数</h3>
            <el-icon size="14" class="text-gray-400"><User /></el-icon>
          </div>
          <div class="text-xl font-bold text-gray-900 dark:text-white">{{ totalEntities }}</div>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            活跃实体
          </p>
        </div>

        <!-- 集群状态 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-row items-center justify-between pb-1">
            <h3 class="text-xs font-medium text-gray-600 dark:text-gray-400">集群状态</h3>
            <el-icon size="14" class="text-gray-400"><CircleCheck /></el-icon>
          </div>
          <div :class="`text-xl font-bold ${clusterData?.state ? 'text-green-600' : 'text-red-600'}`">
            {{ clusterData?.state ? '健康' : '异常' }}
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            {{ clusterData?.state ? '正常运行' : '存在异常' }}
          </p>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex-1 min-h-0 flex flex-col">
        <el-tabs v-model="activeTab" class="h-full flex flex-col px-6">
          <!-- 集群总览 -->
          <el-tab-pane label="集群总览" name="tree" class="h-full flex flex-col">
            <div class="flex items-center justify-between mb-4 flex-shrink-0">
              <div class="flex-1 max-w-md">
                <el-input
                  v-model="treeSearchQuery"
                  placeholder="搜索节点、角色、分片或实体..."
                  clearable
                  :prefix-icon="Search"
                  size="small"
                />
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400 ml-4">
                {{ treeSearchQuery ? '匹配结果' : '树形结构展示' }}
              </div>
            </div>
            
            <div class="flex-1 pb-4 min-h-0">
              <VirtualTreeView 
                :data="filteredTreeData" 
                :search-query="treeSearchQuery"
                @shard-click="navigateToShard"
              />
            </div>
          </el-tab-pane>

          <!-- 节点 -->
          <el-tab-pane label="节点" name="nodes" class="h-full flex flex-col">
            <div class="flex items-center justify-between mb-4 flex-shrink-0">
              <div class="flex-1 max-w-md">
                <el-input
                  v-model="nodeSearchQuery"
                  placeholder="搜索节点地址、状态或角色..."
                  clearable
                  :prefix-icon="Search"
                  size="small"
                />
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400 ml-4">
                {{ filteredNodes.length }} / {{ clusterData?.data?.length || 0 }} 个节点
              </div>
            </div>
            
            <div class="flex-1 pb-4 min-h-0">
              <el-table 
                :data="filteredNodes" 
                stripe 
                size="small"
                height="100%"
                style="width: 100%"
              >
                <el-table-column prop="hostname" label="地址" min-width="180">
                  <template #default="{ row }">
                    <span class="font-mono text-sm">{{ row.hostname }}:{{ row.port }}</span>
                  </template>
                </el-table-column>
                
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <div class="flex items-center gap-2">
                      <div :class="`w-2 h-2 rounded-full ${getStatusColor(row.status)}`" />
                      {{ row.status }}
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="roles" label="角色" min-width="200">
                  <template #default="{ row }">
                    <div class="flex gap-1 flex-wrap">
                      <el-tag 
                        v-for="(role, index) in row.roles" 
                        :key="index"
                        type="info"
                        size="small"
                      >
                        {{ role }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="shardTypes" label="分片类型" min-width="150">
                  <template #default="{ row }">
                    {{ row.shardTypes.map(st => st.typeName).join(', ') }}
                  </template>
                </el-table-column>
                
                <el-table-column prop="entities" label="实体数" width="80" align="center">
                  <template #default="{ row }">
                    {{ row.shardTypes.reduce((sum, st) => sum + st.totalEntities, 0) }}
                  </template>
                </el-table-column>
                
                <el-table-column prop="flags" label="标识" width="120">
                  <template #default="{ row }">
                    <div class="flex gap-1">
                      <el-tag v-if="row.leader" type="warning" size="small">
                        Leader
                      </el-tag>
                      <el-tag v-if="row.self" type="success" size="small">
                        Self
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <!-- 分片分布 -->
          <el-tab-pane label="分片" name="shards" class="h-full flex flex-col">
            <div class="flex items-center justify-between mb-4 flex-shrink-0">
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">分片分布概览</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">显示各节点分片的实体数量分布，点击分片可查看详细实体列表</p>
              </div>
            </div>
            
            <div class="flex-1 pb-4 min-h-0 overflow-y-auto">
              <div class="space-y-6">
                <div v-for="(node, nodeIndex) in clusterData?.data" :key="nodeIndex" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                  <div class="flex items-center justify-between mb-4">
                    <h4 class="font-semibold flex items-center gap-2 text-gray-900 dark:text-white">
                      <el-icon size="16"><Monitor /></el-icon>
                      {{ node.hostname }}:{{ node.port }}
                      <el-tag v-if="node.leader" type="warning" size="small">Leader</el-tag>
                      <el-tag v-if="node.self" type="success" size="small">Self</el-tag>
                    </h4>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                      总计: {{ node.shardTypes[0]?.totalShards || 0 }} 分片, {{ (node.shardTypes[0]?.totalEntities || 0).toLocaleString() }} 实体
                    </div>
                  </div>
                  
                  <div class="flex flex-wrap gap-4 mb-3">
                    <div 
                      v-for="(shard, shardIndex) in node.shardTypes[0]?.shards || []" 
                      :key="shardIndex"
                      class="w-20 h-20 rounded-lg border border-gray-300 dark:border-gray-500 flex flex-col items-center justify-center font-mono cursor-pointer hover:border-primary transition-all hover:scale-105 relative group flex-shrink-0 shadow-sm hover:shadow-md"
                      :style="{
                        backgroundColor: getShardHeatColor(shard.entityCount, node.shardTypes[0]?.shards || [])
                      }"
                      @click="navigateToShard(`${node.hostname}:${node.port}`, shard.shardId)"
                    >
                      <div class="text-[11px] text-gray-600 dark:text-gray-400 leading-none">分片ID</div>
                      <div class="font-bold text-gray-900 text-base leading-none mt-1">{{ shard.shardId }}</div>
                      <div class="text-[11px] text-gray-600 dark:text-gray-400 leading-none mt-2">实体数</div>
                      <div class="font-semibold text-gray-800 dark:text-gray-200 text-sm leading-none mt-0.5">{{ shard.entityCount }}</div>
                      
                      <!-- Tooltip -->
                      <el-tooltip 
                        effect="dark" 
                        placement="top"
                        :content="`分片 ${shard.shardId}: ${shard.entityCount} 个实体`"
                      >
                        <div class="absolute inset-0" />
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 分片详情 -->
          <!-- <el-tab-pane label="分片详情" name="shards-detail" class="h-full flex flex-col">
            <div class="flex items-center justify-between mb-4 flex-shrink-0">
              <div class="flex-1 max-w-md">
                <el-input
                  v-model="shardSearchQuery"
                  placeholder="搜索节点、分片类型或分片ID..."
                  clearable
                  :prefix-icon="Search"
                  size="small"
                />
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400 ml-4">
                {{ filteredShards.length }} 个分片匹配
              </div>
            </div>
            
            <div class="flex-1 pb-4 min-h-0 overflow-y-auto">
              <div class="space-y-4">
                <div v-if="filteredShards.length === 0 && shardSearchQuery" class="text-center py-8">
                  <div class="text-gray-500 dark:text-gray-400">未找到匹配的分片信息</div>
                </div>
                <template v-for="shardGroup in groupedFilteredShards" :key="shardGroup.nodeKey">
                  <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="flex items-center gap-2 mb-4">
                      <el-icon size="18" class="text-primary"><Collection /></el-icon>
                      <h4 class="text-base font-semibold text-gray-900 dark:text-white">
                        节点 {{ shardGroup.hostname }}:{{ shardGroup.port }} 的分片信息
                      </h4>
                    </div>
                    
                    <div v-for="(shardType, stIndex) in shardGroup.shardTypes" :key="stIndex" class="mb-4">
                      <div class="flex items-center justify-between mb-3">
                        <h5 class="font-medium text-gray-800 dark:text-gray-200">{{ shardType.typeName }}</h5>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                          匹配分片: {{ shardType.matchedShards }} / {{ shardType.totalShards }}
                        </div>
                      </div>
                      
                      <el-table :data="shardType.shards" size="small" max-height="300">
                        <el-table-column prop="shardId" label="分片ID" width="100">
                          <template #default="{ row }">
                            <span class="font-mono text-sm">{{ row.shardId }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="entityCount" label="实体数量" width="90" align="center">
                          <template #default="{ row }">
                            <el-tag :type="row.entityCount > 0 ? 'success' : 'info'" size="small">
                              {{ row.entityCount }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column prop="actorPath" label="Actor路径" min-width="200">
                          <template #default="{ row }">
                            <span class="font-mono text-xs text-gray-600 dark:text-gray-400 break-all">{{ row.actorPath }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </el-tab-pane> -->

          

          <!-- 实体列表 -->
          <el-tab-pane label="实体" name="entities" class="h-full flex flex-col">
            <div class="mb-4 flex-shrink-0 space-y-3">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">实体查询与管理</h3>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  共 {{ filteredEntities.length.toLocaleString() }} 个实体
                  <span v-if="selectedShardFilter !== 'all'" class="ml-2 text-primary">
                    - 当前过滤: 分片 {{ selectedShardFilter }}
                  </span>
                </div>
              </div>
              
              <!-- 搜索和过滤控件 -->
              <div class="flex gap-4 items-center flex-wrap">
                <div class="flex-1 min-w-64 relative">
                  <el-input
                    v-model="entitySearchQuery"
                    placeholder="搜索实体ID或节点地址..."
                    clearable
                    :prefix-icon="Search"
                    size="small"
                  />
                </div>
                <el-select 
                  v-model="selectedNodeFilter" 
                  placeholder="选择节点"
                  size="small"
                  style="width: 200px"
                  @change="handleNodeFilterChange"
                >
                  <el-option label="所有节点" value="all" />
                  <el-option 
                    v-for="node in clusterData?.data || []" 
                    :key="`${node.hostname}:${node.port}`"
                    :label="`${node.hostname}:${node.port}`"
                    :value="`${node.hostname}:${node.port}`"
                  />
                </el-select>
                <el-select 
                  v-model="selectedShardFilter" 
                  placeholder="选择分片"
                  size="small"
                  style="width: 150px"
                  @change="handleShardFilterChange"
                >
                  <el-option label="所有分片" value="all" />
                  <el-option 
                    v-for="shardId in availableShards" 
                    :key="shardId"
                    :label="`分片 ${shardId}`"
                    :value="shardId"
                  />
                </el-select>
                <el-select 
                  v-model="pageSize" 
                  size="small"
                  style="width: 120px"
                  @change="handlePageSizeChange"
                >
                  <el-option label="10 条/页" :value="10" />
                  <el-option label="20 条/页" :value="20" />
                  <el-option label="50 条/页" :value="50" />
                  <el-option label="100 条/页" :value="100" />
                </el-select>
                <el-button 
                  v-if="selectedNodeFilter !== 'all' || selectedShardFilter !== 'all' || entitySearchQuery"
                  size="small"
                  @click="clearAllFilters"
                >
                  清除过滤
                </el-button>
              </div>
            </div>
            
            <div class="flex-1 pb-4 min-h-0 flex flex-col">
              <div class="flex-1 min-h-0">
                <el-table 
                  :data="paginatedEntities" 
                  stripe 
                  size="small"
                  height="100%"
                  style="width: 100%"
                >
                  <el-table-column prop="entityId" label="实体ID" min-width="200">
                    <template #default="{ row }">
                      <span class="font-mono text-sm">{{ row.entityId }}</span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="shardId" label="分片ID" width="120">
                    <template #default="{ row }">
                      <el-tag type="info" size="small" class="font-mono">
                        {{ row.shardId }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="node" label="节点" width="180">
                    <template #default="{ row }">
                      <span class="font-mono text-sm">{{ row.hostname }}:{{ row.port }}</span>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="actorPath" label="Actor路径" min-width="300">
                    <template #default="{ row }">
                      <span class="font-mono text-xs text-gray-600 dark:text-gray-400 break-all">
                        {{ row.actorPath }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              
              <!-- 分页控件 -->
              <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-600 flex-shrink-0">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  显示 {{ ((currentPage - 1) * pageSize + 1).toLocaleString() }} - {{ Math.min(currentPage * pageSize, filteredEntities.length).toLocaleString() }} 条，
                  共 {{ filteredEntities.length.toLocaleString() }} 条记录
                </div>
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="filteredEntities.length"
                  layout="prev, pager, next, jumper"
                  size="small"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Refresh,
  Monitor,
  Collection,
  User,
  CircleCheck,
  Operation,
  Search,
  Loading,
  TrendCharts,
  Histogram,
  View
} from "@element-plus/icons-vue";
import { 
  getClusterTree, 
  getMockClusterData,
  type ClusterResponse, 
  type ClusterMember,
  type EntityInfo
} from "@/api/cluster";
import VirtualTreeView from "./components/VirtualTreeView.vue";
import { h } from "vue";

// 响应式数据
const loading = ref(false);
const activeTab = ref("tree");
const clusterData = ref<ClusterResponse | null>(null);

// 跨标签页状态管理
const selectedNodeFilter = ref("all");
const selectedShardFilter = ref("all");
const currentPage = ref(1);
const pageSize = ref(20);

// 搜索查询
const nodeSearchQuery = ref("");
const shardSearchQuery = ref("");
const entitySearchQuery = ref("");
const treeSearchQuery = ref("");


// 计算属性
const totalNodes = computed(() => clusterData.value?.data?.length || 0);

const upNodes = computed(() => 
  clusterData.value?.data?.filter(node => node.status === 'Up').length || 0
);

const totalEntities = computed(() =>
  clusterData.value?.data?.reduce((sum, node) =>
    sum + node.shardTypes.reduce((nodeSum, shardType) => nodeSum + shardType.totalEntities, 0), 0
  ) || 0
);

const totalShards = computed(() =>
  clusterData.value?.data?.reduce((sum, node) =>
    sum + node.shardTypes.reduce((nodeSum, shardType) => nodeSum + shardType.totalShards, 0), 0
  ) || 0
);

// 聚合统计数据
const aggregatedStats = computed(() => {
  if (!clusterData.value?.data) {
    return { nodeStats: [], shardDistribution: [] };
  }

  const nodeStats = clusterData.value.data.map(node => ({
    hostname: node.hostname,
    port: node.port,
    status: node.status,
    totalShards: node.shardTypes.reduce((sum, st) => sum + st.totalShards, 0),
    totalEntities: node.shardTypes.reduce((sum, st) => sum + st.totalEntities, 0),
    avgEntitiesPerShard: node.shardTypes.reduce((sum, st) => sum + st.totalEntities, 0) / 
                        Math.max(1, node.shardTypes.reduce((sum, st) => sum + st.totalShards, 0)),
    leader: node.leader,
    self: node.self,
    roles: node.roles
  }));

  const shardDistribution = clusterData.value.data.flatMap(node => 
    node.shardTypes.flatMap(st => 
      st.shards.map(shard => ({
        nodeId: `${node.hostname}:${node.port}`,
        shardId: shard.shardId,
        entityCount: shard.entityCount,
        typeName: shard.typeName
      }))
    )
  );

  return { nodeStats, shardDistribution };
});

// 负载均衡评估
const loadBalanceMetrics = computed(() => {
  const stats = aggregatedStats.value.nodeStats;
  if (stats.length === 0) {
    return {
      status: '未知',
      variance: 0,
      coefficient: 0,
      recommendation: '无数据'
    };
  }

  const entityCounts = stats.map(s => s.totalEntities);
  const avg = entityCounts.reduce((sum, count) => sum + count, 0) / entityCounts.length;
  const variance = entityCounts.reduce((sum, count) => sum + Math.pow(count - avg, 2), 0) / entityCounts.length;
  const coefficient = avg > 0 ? Math.sqrt(variance) / avg : 0;

  let status = '优秀';
  let recommendation = '负载分布均匀';
  
  if (coefficient > 0.3) {
    status = '需要优化';
    recommendation = '建议重新分布分片';
  } else if (coefficient > 0.15) {
    status = '良好';
    recommendation = '负载基本均衡';
  }

  return {
    status,
    variance: Math.round(variance),
    coefficient: Math.round(coefficient * 100) / 100,
    recommendation
  };
});

// 节点间跳转功能
const navigateToShard = (nodeId: string, shardId: string) => {
  selectedNodeFilter.value = nodeId;
  selectedShardFilter.value = shardId;
  entitySearchQuery.value = '';
  currentPage.value = 1;
  activeTab.value = 'entities';
  ElMessage.success(`已跳转到节点 ${nodeId} 的分片 ${shardId}`);
};

// 展平的实体列表
const flattenedEntities = computed(() => {
  const entities: (EntityInfo & { hostname: string; port: number })[] = [];
  clusterData.value?.data?.forEach(node => {
    node.shardTypes.forEach(shardType => {
      shardType.shards.forEach(shard => {
        shard.entities.forEach(entity => {
          entities.push({
            ...entity,
            hostname: node.hostname,
            port: node.port
          });
        });
      });
    });
  });
  return entities;
});

// 搜索过滤计算属性
const filteredNodes = computed(() => {
  if (!clusterData.value?.data || !nodeSearchQuery.value.trim()) {
    return clusterData.value?.data || [];
  }
  
  const query = nodeSearchQuery.value.toLowerCase().trim();
  return clusterData.value.data.filter(node => {
    // 搜索节点地址
    const address = `${node.hostname}:${node.port}`.toLowerCase();
    if (address.includes(query)) return true;
    
    // 搜索状态
    if (node.status.toLowerCase().includes(query)) return true;
    
    // 搜索角色
    if (node.roles.some(role => role.toLowerCase().includes(query))) return true;
    
    return false;
  });
});

const filteredShards = computed(() => {
  if (!clusterData.value?.data || !shardSearchQuery.value.trim()) {
    return [];
  }
  
  const query = shardSearchQuery.value.toLowerCase().trim();
  const shards = [];
  
  clusterData.value.data.forEach(node => {
    node.shardTypes.forEach(shardType => {
      shardType.shards.forEach(shard => {
        const nodeAddress = `${node.hostname}:${node.port}`.toLowerCase();
        const typeName = shardType.typeName.toLowerCase();
        const shardId = shard.shardId.toLowerCase();
        const actorPath = shard.actorPath.toLowerCase();
        
        if (nodeAddress.includes(query) || 
            typeName.includes(query) || 
            shardId.includes(query) ||
            actorPath.includes(query)) {
          shards.push({
            ...shard,
            nodeHostname: node.hostname,
            nodePort: node.port,
            typeName: shardType.typeName
          });
        }
      });
    });
  });
  
  return shards;
});

const groupedFilteredShards = computed(() => {
  if (!shardSearchQuery.value.trim()) {
    return clusterData.value?.data?.map(node => ({
      nodeKey: `${node.hostname}:${node.port}`,
      hostname: node.hostname,
      port: node.port,
      shardTypes: node.shardTypes.map(st => ({
        ...st,
        matchedShards: st.shards.length
      }))
    })) || [];
  }
  
  const groups = new Map();
  
  filteredShards.value.forEach(shard => {
    const nodeKey = `${shard.nodeHostname}:${shard.nodePort}`;
    if (!groups.has(nodeKey)) {
      groups.set(nodeKey, {
        nodeKey,
        hostname: shard.nodeHostname,
        port: shard.nodePort,
        shardTypes: new Map()
      });
    }
    
    const group = groups.get(nodeKey);
    if (!group.shardTypes.has(shard.typeName)) {
      group.shardTypes.set(shard.typeName, {
        typeName: shard.typeName,
        shards: [],
        matchedShards: 0,
        totalShards: 0
      });
    }
    
    const shardType = group.shardTypes.get(shard.typeName);
    shardType.shards.push(shard);
    shardType.matchedShards++;
    
    // 计算总分片数
    const originalNode = clusterData.value?.data?.find(n => n.hostname === shard.nodeHostname && n.port === shard.nodePort);
    const originalShardType = originalNode?.shardTypes.find(st => st.typeName === shard.typeName);
    shardType.totalShards = originalShardType?.totalShards || 0;
  });
  
  return Array.from(groups.values()).map(group => ({
    ...group,
    shardTypes: Array.from(group.shardTypes.values())
  }));
});

const filteredEntities = computed(() => {
  let entities = flattenedEntities.value;

  // 搜索过滤
  if (entitySearchQuery.value.trim()) {
    const query = entitySearchQuery.value.toLowerCase().trim();
    entities = entities.filter(entity => {
      const entityId = entity.entityId.toLowerCase();
      const nodeAddress = `${entity.hostname}:${entity.port}`.toLowerCase();
      return entityId.includes(query) || nodeAddress.includes(query);
    });
  }

  // 节点过滤
  if (selectedNodeFilter.value !== 'all') {
    entities = entities.filter(entity => 
      `${entity.hostname}:${entity.port}` === selectedNodeFilter.value
    );
  }

  // 分片过滤
  if (selectedShardFilter.value !== 'all') {
    entities = entities.filter(entity => entity.shardId === selectedShardFilter.value);
  }

  return entities;
});

// 分页数据
const paginatedEntities = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  return filteredEntities.value.slice(startIndex, startIndex + pageSize.value);
});

const totalPages = computed(() => Math.ceil(filteredEntities.value.length / pageSize.value));

// 可用分片选项
const availableShards = computed(() => {
  if (selectedNodeFilter.value === 'all') {
    return Array.from(new Set(
      (clusterData.value?.data || []).flatMap(node =>
        node.shardTypes.flatMap(st => st.shards.map(shard => shard.shardId))
      )
    )).sort((a, b) => {
      const numA = parseInt(a);
      const numB = parseInt(b);
      if (!isNaN(numA) && !isNaN(numB)) return numA - numB;
      return a.localeCompare(b);
    });
  } else {
    const node = clusterData.value?.data?.find(n => `${n.hostname}:${n.port}` === selectedNodeFilter.value);
    return node ? node.shardTypes.flatMap(st => st.shards.map(s => s.shardId)) : [];
  }
});

// 事件处理
const handleNodeFilterChange = () => {
  selectedShardFilter.value = 'all'; // 重置分片选择
  currentPage.value = 1;
};

const handleShardFilterChange = () => {
  currentPage.value = 1;
};

const handlePageSizeChange = () => {
  currentPage.value = 1;
};

const clearAllFilters = () => {
  selectedNodeFilter.value = 'all';
  selectedShardFilter.value = 'all';
  entitySearchQuery.value = '';
  currentPage.value = 1;
};

const filteredTreeData = computed(() => {
  if (!treeSearchQuery.value.trim()) {
    return clusterData.value;
  }
  
  // 集群总览的搜索将由VirtualTreeView组件内部处理
  return clusterData.value;
});



// 虚拟表格列定义
const entityColumns = [
  {
    key: 'entityId',
    title: '实体ID',
    dataKey: 'entityId',
    width: 200,
    cellRenderer: ({ rowData }) => h('span', { class: 'font-mono text-sm' }, rowData.entityId)
  },
  {
    key: 'shardId',
    title: '分片ID',
    dataKey: 'shardId',
    width: 120,
    cellRenderer: ({ rowData }) => h('span', { class: 'font-mono text-sm' }, rowData.shardId)
  },
  {
    key: 'node',
    title: '节点',
    dataKey: 'hostname',
    width: 180,
    cellRenderer: ({ rowData }) => h('span', { class: 'font-mono text-sm' }, `${rowData.hostname}:${rowData.port}`)
  },
  {
    key: 'actorPath',
    title: 'Actor路径',
    dataKey: 'actorPath',
    width: 400,
    cellRenderer: ({ rowData }) => h('span', { 
      class: 'font-mono text-xs text-gray-600 dark:text-gray-400 break-all' 
    }, rowData.actorPath)
  }
];

// 工具函数
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'up':
      return 'bg-green-500';
    case 'down':
      return 'bg-red-500';
    case 'joining':
      return 'bg-yellow-500';
    default:
      return 'bg-gray-500';
  }
};

// 分片热力图颜色计算
const getShardHeatColor = (entityCount: number, allShards: any[]) => {
  if (!allShards.length) return 'rgba(34, 197, 94, 0.1)';
  
  const maxEntities = Math.max(...allShards.map(s => s.entityCount));
  const intensity = maxEntities > 0 ? entityCount / maxEntities : 0;
  
  // 使用绿色渐变色彩
  return `rgba(34, 197, 94, ${intensity * 0.8 + 0.1})`;
};

const formatTimestamp = (timestamp?: number) => {
  if (!timestamp) return '';
  return new Date(timestamp).toLocaleString('zh-CN');
};





// 数据加载
const loadClusterData = async () => {
  try {
    loading.value = true;
    
    // 首先尝试真实API，失败则使用模拟数据
    try {
      const response = await getClusterTree();
      clusterData.value = response;
    } catch (apiError) {
      console.warn('API请求失败，使用模拟数据:', apiError);
      clusterData.value = getMockClusterData();
    }
  } catch (error) {
    console.error('获取集群数据失败:', error);
    ElMessage.error('获取集群数据失败');
  } finally {
    loading.value = false;
  }
};



const refreshData = () => {
  loadClusterData();
};

// 生命周期
onMounted(() => {
  loadClusterData();
});
</script>

<style scoped>
.font-mono {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Fira Mono', 'Droid Sans Mono', 'Consolas', monospace;
}

.main {
  height: 100%;
}

</style>