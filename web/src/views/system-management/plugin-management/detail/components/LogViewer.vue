<template>
  <div class="log-viewer p-6">
    <!-- 工具栏 -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-4 w-80">
        <el-select
          v-model="selectedLevel"
          placeholder="选择日志级别"
          clearable
          class="w-32"
          @change="onLevelChange"
        >
          <el-option label="全部" value="" />
          <el-option label="ERROR" value="ERROR" />
          <el-option label="WARN" value="WARN" />
          <el-option label="INFO" value="INFO" />
          <el-option label="DEBUG" value="DEBUG" />
        </el-select>

        <el-switch
          v-model="realTimeMode"
          active-text="实时日志"
          class="w-64"
          @change="toggleRealTime"
        />
      </div>

      <div class="flex items-center space-x-2">
        <el-button
          :icon="Refresh"
          :loading="connecting"
          type="primary"
          text
          @click="reconnectStream"
        >
          重连
        </el-button>
        <el-button :icon="Download" type="primary" text @click="downloadLogs">
          下载
        </el-button>
        <el-button :icon="Delete" type="danger" text @click="clearLogs">
          清空
        </el-button>
      </div>
    </div>

    <!-- 日志内容 -->
    <div
      ref="logContainer"
      class="log-container bg-white dark:bg-gray-950 border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-sm font-mono overflow-auto"
      style="height: 500px"
    >
      <div v-if="connecting && logs.length === 0" class="text-center py-8">
        <el-icon size="24" class="text-gray-400 animate-spin mb-2">
          <Loading />
        </el-icon>
        <p class="text-gray-400">正在连接日志流...</p>
      </div>

      <div v-else-if="logs.length === 0" class="text-center py-8">
        <el-icon size="24" class="text-gray-400 mb-2">
          <Document />
        </el-icon>
        <p class="text-gray-400">暂无日志数据</p>
      </div>

      <div v-else>
        <div
          v-for="(log, index) in logs"
          :key="index"
          class="log-line flex items-start space-x-3 py-1 px-2 rounded mb-1 hover:bg-gray-50 dark:hover:bg-gray-800"
        >
          <span
            class="text-gray-500 dark:text-gray-400 text-xs whitespace-nowrap flex-shrink-0"
          >
            {{ formatTime(log.timeStamp) }}
          </span>
          <span
            :class="[
              'text-xs font-bold px-2 py-1 rounded whitespace-nowrap flex-shrink-0',
              getLogLevelBadgeClass(log.level)
            ]"
          >
            {{ log.level }}
          </span>
          <span
            v-if="log.thread"
            class="text-gray-500 dark:text-gray-400 text-xs whitespace-nowrap flex-shrink-0"
          >
            [{{ log.thread }}]
          </span>
          <span
            v-if="log.loggerName"
            class="text-gray-500 dark:text-gray-400 text-xs whitespace-nowrap flex-shrink-0"
          >
            {{ log.loggerName }}
          </span>
          <span class="text-gray-900 dark:text-gray-100 flex-1 break-all">
            {{ log.message }}
          </span>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div
      class="flex items-center justify-between mt-4 text-sm text-gray-500 dark:text-gray-400"
    >
      <div class="flex items-center space-x-4">
        <span>共 {{ logs.length }} 条日志</span>
        <span
          v-if="realTimeMode && connectionStatus === 'connected'"
          class="flex items-center"
        >
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2" />
          实时更新中
        </span>
        <span
          v-else-if="connectionStatus === 'connecting'"
          class="flex items-center"
        >
          <div class="w-2 h-2 bg-yellow-500 rounded-full animate-pulse mr-2" />
          连接中...
        </span>
        <span
          v-else-if="connectionStatus === 'disconnected'"
          class="flex items-center"
        >
          <div class="w-2 h-2 bg-red-500 rounded-full mr-2" />
          连接断开
        </span>
      </div>
      <div v-if="lastUpdateTime">
        最后更新: {{ formatTime(lastUpdateTime) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  Refresh,
  Download,
  Delete,
  Loading,
  Document
} from "@element-plus/icons-vue";
import { setPluginLogLevel } from "@/api/plugin";

// 日志信息接口
interface LogInfo {
  timeStamp: string;
  level: string;
  thread: string;
  loggerName: string;
  message: string;
}

interface Props {
  pluginId: string;
}

const props = defineProps<Props>();

// 状态管理
const connecting = ref(false);
const logs = ref<LogInfo[]>([]);
const selectedLevel = ref<string>("");
const maxLines = ref(200);
const realTimeMode = ref(true);
const lastUpdateTime = ref<string>("");
const logContainer = ref<HTMLElement>();
const connectionStatus = ref<"disconnected" | "connecting" | "connected">(
  "disconnected"
);

// EventSource 相关
let eventSource: EventSource | null = null;

// 监听 pluginId 变化
watch(
  () => props.pluginId,
  newId => {
    if (newId) {
      reconnectStream();
    }
  }
);

// 初始化 EventSource 连接
const initEventSource = () => {
  if (!props.pluginId) return;

  closeStream();
  connectionStatus.value = "connecting";
  connecting.value = true;

  // 构建流URL，添加查询参数
  const params = new URLSearchParams({
    pluginId: props.pluginId
  });

  if (selectedLevel.value) {
    params.append("level", selectedLevel.value);
  }

  const streamUrl = `/api/thing/plugins/logs-stream?${params.toString()}`;
  eventSource = new EventSource(streamUrl);

  // 连接建立时的历史日志
  eventSource.addEventListener("connected", event => {
    try {
      const historicalLogs = JSON.parse(event.data) as LogInfo[];
      logs.value = historicalLogs;
      lastUpdateTime.value = new Date().toISOString();
      connectionStatus.value = "connected";
      connecting.value = false;
      scrollToBottom();
      checkLimit();
    } catch (error) {
      console.error("解析历史日志失败:", error);
    }
  });

  // 实时追加新日志
  eventSource.addEventListener("append", event => {
    try {
      const newLog = JSON.parse(event.data) as LogInfo;
      const shouldAutoScroll = isScrolledToBottom();

      logs.value.push(newLog);
      lastUpdateTime.value = new Date().toISOString();

      nextTick(() => {
        checkLimit();
        if (shouldAutoScroll) {
          scrollToBottom();
        }
      });
    } catch (error) {
      console.error("解析新日志失败:", error);
    }
  });

  // 清空日志
  eventSource.addEventListener("clean", () => {
    logs.value = [];
    lastUpdateTime.value = new Date().toISOString();
  });

  // 连接错误处理
  eventSource.onerror = () => {
    console.error("EventSource 连接错误");
    connectionStatus.value = "disconnected";
    connecting.value = false;

    if (eventSource) {
      eventSource.close();
      eventSource = null;
    }

    // 自动重连（如果是实时模式）
    if (realTimeMode.value) {
      setTimeout(() => {
        initEventSource();
      }, 5000);
    }
  };

  // 连接打开
  eventSource.onopen = () => {
    console.log("EventSource 连接已建立");
  };
};

// 关闭流连接
const closeStream = () => {
  if (eventSource) {
    eventSource.close();
    eventSource = null;
  }
  connectionStatus.value = "disconnected";
  connecting.value = false;
};

// 检查日志行数限制
const checkLimit = () => {
  if (logs.value.length > maxLines.value) {
    logs.value = logs.value.slice(-maxLines.value);
  }
};

// 判断是否滚动到底部
const isScrolledToBottom = (): boolean => {
  if (!logContainer.value) return false;
  const { scrollTop, scrollHeight, clientHeight } = logContainer.value;
  return Math.abs(scrollHeight - scrollTop - clientHeight) < 5;
};

// 滚动到底部
const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight;
  }
};

// 重连流
const reconnectStream = () => {
  if (realTimeMode.value) {
    initEventSource();
  }
};

// 切换实时模式
const toggleRealTime = (enabled: boolean) => {
  if (enabled) {
    initEventSource();
  } else {
    closeStream();
  }
};

// 日志级别变化处理
const onLevelChange = async () => {
  // 设置插件日志级别
  if (selectedLevel.value) {
    try {
      await setPluginLogLevel(props.pluginId, selectedLevel.value);
      ElMessage.success("日志级别设置成功");
    } catch (error) {
      ElMessage.error("日志级别设置失败");
    }
  }

  // 重新连接流以应用新的过滤条件
  if (realTimeMode.value) {
    reconnectStream();
  }
};

// 最大行数变化处理
const onMaxLinesChange = () => {
  checkLimit();
};

// 下载日志
const downloadLogs = () => {
  if (logs.value.length === 0) {
    ElMessage.warning("暂无日志可下载");
    return;
  }

  const logText = logs.value
    .map(log => {
      const parts = [formatTime(log.timeStamp), `[${log.level}]`];
      if (log.thread) parts.push(`[${log.thread}]`);
      if (log.loggerName) parts.push(log.loggerName);
      parts.push(log.message);
      return parts.join(" ");
    })
    .join("\n");

  const blob = new Blob([logText], { type: "text/plain;charset=utf-8" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = `plugin-${props.pluginId}-logs-${new Date().toISOString().slice(0, 10)}.txt`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  ElMessage.success("日志下载成功");
};

// 清空日志
const clearLogs = () => {
  logs.value = [];
  ElMessage.success("日志已清空");
};

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};

// 获取日志级别徽章样式类
const getLogLevelBadgeClass = (level: string) => {
  switch (level?.toUpperCase()) {
    case "ERROR":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
    case "WARN":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
    case "INFO":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    case "DEBUG":
      return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
  }
};

// 页面挂载
onMounted(() => {
  if (props.pluginId) {
    initEventSource();
  }
});

// 页面卸载
onUnmounted(() => {
  closeStream();
});
</script>

<style scoped>
.log-container {
  background: #fff;
}

.dark .log-container {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.log-line {
  border-left: 2px solid transparent;
  transition: all 0.2s ease;
}

.log-line:hover {
  border-left-color: var(--el-color-primary);
}

.log-container::-webkit-scrollbar {
  width: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: rgb(0 0 0 / 10%);
  border-radius: 4px;
}

.dark .log-container::-webkit-scrollbar-track {
  background: rgb(255 255 255 / 10%);
}

.log-container::-webkit-scrollbar-thumb {
  background: rgb(0 0 0 / 30%);
  border-radius: 4px;
}

.dark .log-container::-webkit-scrollbar-thumb {
  background: rgb(255 255 255 / 30%);
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: rgb(0 0 0 / 50%);
}

.dark .log-container::-webkit-scrollbar-thumb:hover {
  background: rgb(255 255 255 / 50%);
}
</style>
