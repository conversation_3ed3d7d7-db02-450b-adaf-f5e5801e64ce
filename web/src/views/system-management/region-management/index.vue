<template>
  <div class="main">
    <div class="w-full p-4">
      <!-- 工具栏 -->
      <div class="flex justify-between items-center mb-4">
        <div class="flex gap-4 items-center">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索区域名称"
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="handleCollapseAll">
            <el-icon><Fold /></el-icon>
            收起全部
          </el-button>
        </div>
        <div class="flex gap-2">
          <Auth value="system:region:add">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增区域
            </el-button>
          </Auth>
          <Auth value="system:region:sync">
            <el-button type="info" @click="handleSyncFromS6" :loading="syncLoading" disabled>
              <el-icon><Refresh /></el-icon>
              S6同步
            </el-button>
          </Auth>
        </div>
      </div>

      <!-- 提示信息 -->
      <div class="mb-2 flex items-center gap-2">
        <el-tag type="warning" size="small" effect="plain" :bordered="false">
          <el-icon class="mr-1"><InfoFilled /></el-icon>
          右键点击节点可进行编辑、删除等操作
        </el-tag>
      </div>

      <!-- 区域虚拟树 -->
      <div class="tree-container" ref="treeContainer">
        <el-tree-v2
          ref="treeRef"
          v-loading="treeLoading"
          :data="treeData"
          :height="treeHeight"
          :item-size="36"
          :props="treeProps"
          node-key="regionId"
          :default-expanded-keys="expandedKeys"
          :filter-method="filterMethod"
          @node-click="handleNodeClick"
          @node-contextmenu="handleContextMenu"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="node-content">
                <span class="node-label" v-html="highlightSearchText(node.label)"></span>
                <div class="node-tags">
                  <el-tag 
                    v-if="data.children && data.children.length > 0" 
                    size="small" 
                    type="success"
                    :title="`子区域: ${data.children.length}个`"
                  >
                    {{ data.children.length }}
                  </el-tag>
                  <el-tag 
                    v-if="data.items && data.items.length > 0" 
                    size="small" 
                    type="info"
                    :title="`关联项目: ${data.items.length}个`"
                  >
                    {{ data.items.length }}
                  </el-tag>
                </div>
              </div>
              <div class="node-description" v-if="data.description">
                <span class="description-text">{{ data.description }}</span>
              </div>
            </div>
          </template>
        </el-tree-v2>
        
        <!-- 空状态 -->
        <div v-if="!treeLoading && treeData.length === 0" class="empty-state">
          <el-empty description="暂无区域数据" />
        </div>
      </div>
    </div>

    <!-- 区域表单对话框 -->
    <RegionFormDialog
      v-model:visible="formDialogVisible"
      :region-data="currentRegion"
      :parent-region="parentRegion"
      :is-edit="isEdit"
      @refresh="handleRefresh"
    />

    <!-- 暂时隐藏区域项目管理对话框 -->
    <!-- <RegionItemDialog
      v-model:visible="itemDialogVisible"
      :region-data="currentRegion"
      @refresh="handleRefresh"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch, onUnmounted } from "vue";
import { ElMessage, ElMessageBox, ElTreeV2 } from "element-plus";
import { Search, Refresh, Plus, Expand, Fold, InfoFilled } from "@element-plus/icons-vue";
import ContextMenu from "@imengyu/vue3-context-menu";
import { 
  getRegionTree, 
  deleteRegion, 
  syncRegionFromS6,
  type RegionTreeInfo 
} from "@/api/region";
import RegionFormDialog from "./components/RegionFormDialog.vue";
// import RegionItemDialog from "./components/RegionItemDialog.vue";

defineOptions({
  name: "RegionManagement"
});

// 响应式数据
const treeRef = ref<InstanceType<typeof ElTreeV2>>();
const treeContainer = ref<HTMLElement>();
const treeLoading = ref(false);
const syncLoading = ref(false);
const treeData = ref<RegionTreeInfo[]>([]);
const originalData = ref<RegionTreeInfo[]>([]);
const formDialogVisible = ref(false);
const itemDialogVisible = ref(false);
const currentRegion = ref<RegionTreeInfo | null>(null);
const parentRegion = ref<RegionTreeInfo | null>(null);
const isEdit = ref(false);
const searchKeyword = ref("");
const expandedKeys = ref<string[]>([]);
const selectedRegion = ref<RegionTreeInfo | null>(null);
const treeHeight = ref(600);

// 树形配置
const treeProps = {
  children: "children",
  label: "regionName",
  value: "regionId"
};

// 获取区域树数据
const getTreeData = async () => {
  try {
    treeLoading.value = true;
    const response = await getRegionTree();
    if (response.state) {
      originalData.value = response.data || [];
      treeData.value = [...originalData.value];
      // 默认展开第一层
      expandedKeys.value = originalData.value.map(item => item.regionId.toString());
    } else {
      ElMessage.error(response.err_msg || "获取区域列表失败");
      treeData.value = [];
    }
  } catch (error) {
    console.error("获取区域列表失败:", error);
    ElMessage.error("获取区域列表失败");
    treeData.value = [];
  } finally {
    treeLoading.value = false;
  }
};

// 过滤方法
const filterMethod = (query: string, node: any) => {
  if (!query) return true;
  const searchText = query.toLowerCase();
  return (
    node.regionName.toLowerCase().includes(searchText) ||
    (node.description && node.description.toLowerCase().includes(searchText))
  );
};

// 搜索高亮
const highlightSearchText = (text: string) => {
  if (!searchKeyword.value) return text;
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi');
  return text.replace(regex, '<span class="highlight-text">$1</span>');
};

// 搜索处理
const handleSearch = () => {
  if (treeRef.value) {
    treeRef.value.filter(searchKeyword.value);
  }
};

// 监听搜索关键词变化
watch(searchKeyword, (newValue) => {
  if (treeRef.value) {
    treeRef.value.filter(newValue);
  }
});

// 收起全部
const handleCollapseAll = () => {
  expandedKeys.value = [];
  if (treeRef.value) {
    treeRef.value.setExpandedKeys([]);
  }
};

// 节点点击事件
const handleNodeClick = (data: RegionTreeInfo) => {
  selectedRegion.value = data;
};

// 节点展开事件
const handleNodeExpand = (data: RegionTreeInfo) => {
  const key = data.regionId.toString();
  if (!expandedKeys.value.includes(key)) {
    expandedKeys.value.push(key);
  }
};

// 节点收起事件
const handleNodeCollapse = (data: RegionTreeInfo) => {
  const key = data.regionId.toString();
  const index = expandedKeys.value.indexOf(key);
  if (index > -1) {
    expandedKeys.value.splice(index, 1);
  }
};

// 右键菜单事件
const handleContextMenu = (event: Event, data: RegionTreeInfo, node: any) => {
  if (event instanceof MouseEvent) {
    event.preventDefault();
    
    ContextMenu.showContextMenu({
      x: event.x,
      y: event.y,
      items: [
        {
          label: "编辑区域",
          icon: "h:pencil-alt",
          onClick: () => handleEdit(data)
        },
        {
          label: "新增子区域",
          icon: "h:plus",
          onClick: () => handleAddChild(data)
        },
        // 暂时隐藏管理关联功能
        // {
        //   label: "管理关联",
        //   icon: "h:link",
        //   onClick: () => handleManageItems(data)
        // },
        {
          label: "删除区域",
          icon: "h:trash",
          onClick: () => handleDelete(data)
        }
      ]
    });
  }
};

// 新增区域
const handleAdd = () => {
  currentRegion.value = null;
  parentRegion.value = null;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 添加子区域
const handleAddChild = (parent: RegionTreeInfo) => {
  currentRegion.value = null;
  parentRegion.value = parent;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 编辑区域
const handleEdit = (region: RegionTreeInfo) => {
  currentRegion.value = { ...region };
  parentRegion.value = null;
  isEdit.value = true;
  formDialogVisible.value = true;
};

// 管理区域项目关联
const handleManageItems = (region: RegionTreeInfo) => {
  currentRegion.value = { ...region };
  itemDialogVisible.value = true;
};

// 删除区域
const handleDelete = async (region: RegionTreeInfo) => {
  try {
    await ElMessageBox.confirm(
      `确认删除区域 "${region.regionName}" 吗？删除后子区域和关联项目也会被删除！`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    const response = await deleteRegion(region.regionId);
    if (response.state) {
      ElMessage.success("删除区域成功");
      getTreeData();
    } else {
      ElMessage.error(response.err_msg || "删除区域失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除区域失败:", error);
      ElMessage.error("删除区域失败");
    }
  }
};

// 从S6同步数据
const handleSyncFromS6 = async () => {
  try {
    syncLoading.value = true;
    const response = await syncRegionFromS6();
    if (response.state) {
      ElMessage.success("同步成功");
      getTreeData();
    } else {
      ElMessage.error(response.err_msg || "同步失败");
    }
  } catch (error) {
    console.error("同步失败:", error);
    ElMessage.error("同步失败");
  } finally {
    syncLoading.value = false;
  }
};

// 刷新数据
const handleRefresh = () => {
  getTreeData();
};

// 计算树的高度
const calculateTreeHeight = () => {
  if (treeContainer.value) {
    const containerRect = treeContainer.value.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const availableHeight = viewportHeight - containerRect.top - 20; // 减去一些底部边距
    treeHeight.value = Math.max(300, availableHeight); // 最小高度300px
  }
};

// 监听窗口大小变化
const handleResize = () => {
  calculateTreeHeight();
};

// 页面挂载时获取数据
onMounted(() => {
  getTreeData();
  nextTick(() => {
    calculateTreeHeight();
  });
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.main {
  background: var(--el-bg-color);
  height: 100%;
}

.tree-container {
  background: var(--el-bg-color);
  overflow: hidden;
}

.custom-tree-node {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 4px 12px;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-label {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
  margin-right: 8px;
}

.node-tags {
  display: flex;
  align-items: center;
  gap: 6px;
}

.node-description {
  margin-top: 2px;
}

.description-text {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 搜索高亮样式 */
:deep(.highlight-text) {
  background-color: var(--el-color-warning-light-8);
  color: var(--el-color-warning-dark-2);
  font-weight: bold;
  padding: 1px 2px;
  border-radius: 2px;
}
</style>