<template>
  <div class="main">
    <div class="w-full p-4">
      <!-- 搜索工具栏 -->
      <div class="flex justify-between items-center mb-4">
        <div class="flex gap-4 items-center">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名或登录账号"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select
            v-model="searchForm.enable"
            placeholder="启用状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="true" />
            <el-option label="停用" :value="false" />
          </el-select>
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="treeProps"
            placeholder="筛选部门"
            clearable
            filterable
            style="width: 200px"
            check-strictly
            :render-after-expand="false"
          />
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        <div class="flex gap-2">
          <Auth value="system:user:add">
            <el-button
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              新增用户
            </el-button>
          </Auth>
          <Auth value="system:user:batch-delete">
            <el-button
              type="danger"
              :disabled="!hasSelection"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
          </Auth>
        </div>
      </div>

      <!-- 用户表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        border
        size="default"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="userId" label="用户ID" width="80" />
        <el-table-column prop="userName" label="用户名称" min-width="120" />
        <el-table-column prop="loginId" label="登录账号" min-width="120" />
        <el-table-column label="所属部门" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.departmentPath" class="text-gray-600">
              {{ row.departmentPath }}
            </span>
            <span v-else class="text-gray-400">
              暂无部门
            </span>
          </template>
        </el-table-column>
        <el-table-column label="角色" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag
              v-for="role in getRoleNames(row.roleIds)"
              :key="role"
              size="small"
              class="mr-1"
            >
              {{ role }}
            </el-tag>
            <span v-if="!row.roleIds || row.roleIds.trim() === ''" class="text-gray-500">
              暂无角色
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column label="启用状态" width="100" align="center">
          <template #default="{ row }">
            <Auth value="system:user:toggle-enable">
              <el-switch
                v-model="row.enable"
                @change="handleToggleEnable(row)"
                :loading="row._switching"
              />
            </Auth>
          </template>
        </el-table-column>
        <el-table-column label="锁定状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.locked ? 'danger' : 'success'">
              {{ row.locked ? '锁定' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="maxError" label="最大错误次数" width="130" align="center" />
        <el-table-column prop="validTime" label="账号有效期" width="180" show-overflow-tooltip />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <Auth value="system:user:edit">
              <el-button
                link
                type="primary"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
            </Auth>
            <Auth value="system:user:reset-pwd">
              <el-button
                link
                type="warning"
                @click="handleResetPassword(row)"
              >
                重置密码
              </el-button>
            </Auth>
            <Auth value="system:user:assign-role">
              <el-button
                link
                type="info"
                @click="handleAssignRole(row)"
              >
                分配角色
              </el-button>
            </Auth>
            <Auth value="system:user:delete">
              <el-button
                link
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </Auth>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 用户表单对话框 -->
    <UserFormDialog
      v-model:visible="formDialogVisible"
      :user-data="currentUser"
      :is-edit="isEdit"
      @refresh="handleRefresh"
    />

    <!-- 密码管理对话框 -->
    <PasswordDialog
      v-model:visible="passwordDialogVisible"
      :user-data="currentUser"
      @refresh="handleRefresh"
    />

    <!-- 角色分配对话框 -->
    <RoleAssignDialog
      v-model:visible="roleDialogVisible"
      :user-data="currentUser"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Plus, Delete } from "@element-plus/icons-vue";
import { getAllAccounts, editEnable, deleteAccount, type AccountInfo } from "@/api/account";
import { getAllRoles, type RoleInfo } from "@/api/role";
import { getDepartmentTree, type DepartmentInfo } from "@/api/department";
import UserFormDialog from "./components/UserFormDialog.vue";
import PasswordDialog from "./components/PasswordDialog.vue";
import RoleAssignDialog from "./components/RoleAssignDialog.vue";

defineOptions({
  name: "UserManagement"
});

// 响应式数据
const tableLoading = ref(false);
const tableData = ref<AccountInfo[]>([]);
const selectedUsers = ref<AccountInfo[]>([]);
const formDialogVisible = ref(false);
const passwordDialogVisible = ref(false);
const roleDialogVisible = ref(false);
const currentUser = ref<AccountInfo | null>(null);
const isEdit = ref(false);
const allRoles = ref<RoleInfo[]>([]);
const departmentTree = ref<DepartmentInfo[]>([]);

// 搜索表单
const searchForm = reactive({
  keyword: "",
  enable: "",
  departmentId: null as number | null
});

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// 计算属性
const hasSelection = computed(() => selectedUsers.value.length > 0);

// 部门树配置
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
};

// 根据角色ID获取角色名称
const getRoleNames = (roleIds: string | undefined): string[] => {
  if (!roleIds || roleIds.trim() === '') return [];
  
  const ids = roleIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
  return ids.map(id => {
    const role = allRoles.value.find(r => r.roleId === id);
    return role ? role.roleName : `未知角色(${id})`;
  });
};

// 加载角色列表
const loadRoles = async () => {
  try {
    const response = await getAllRoles();
    if (response.state) {
      allRoles.value = response.data || [];
    } else {
      console.error("获取角色列表失败:", response.err_msg);
    }
  } catch (error) {
    console.error("获取角色列表失败:", error);
  }
};

// 获取用户列表
const getTableData = async () => {
  try {
    tableLoading.value = true;
    const response = await getAllAccounts(searchForm.departmentId || undefined);
    if (response.state) {
      let data = response.data || [];
      
      // 搜索过滤
      if (searchForm.keyword) {
        data = data.filter(user => 
          user.userName?.includes(searchForm.keyword) || 
          user.loginId?.includes(searchForm.keyword)
        );
      }
      
      if (searchForm.enable !== "") {
        data = data.filter(user => user.enable === searchForm.enable);
      }
      
      pagination.total = data.length;
      
      // 分页
      const start = (pagination.page - 1) * pagination.size;
      const end = start + pagination.size;
      tableData.value = data.slice(start, end);
    } else {
      ElMessage.error(response.err_msg || "获取用户列表失败");
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
    ElMessage.error("获取用户列表失败");
    tableData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.page = 1;
  getTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.keyword = "";
  searchForm.enable = "";
  searchForm.departmentId = null;
  pagination.page = 1;
  getTableData();
};

// 新增用户
const handleAdd = () => {
  currentUser.value = null;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 编辑用户
const handleEdit = (user: AccountInfo) => {
  currentUser.value = { ...user };
  isEdit.value = true;
  formDialogVisible.value = true;
};

// 启用/停用用户
const handleToggleEnable = async (user: AccountInfo) => {
  try {
    user._switching = true;
    const response = await editEnable(user.userId, user.enable);
    if (response.state) {
      ElMessage.success(`${user.enable ? '启用' : '停用'}用户成功`);
    } else {
      user.enable = !user.enable; // 回滚状态
      ElMessage.error(response.err_msg || `${user.enable ? '启用' : '停用'}用户失败`);
    }
  } catch (error) {
    user.enable = !user.enable; // 回滚状态
    console.error("切换用户状态失败:", error);
    ElMessage.error("操作失败");
  } finally {
    user._switching = false;
  }
};

// 删除用户
const handleDelete = async (user: AccountInfo) => {
  try {
    await ElMessageBox.confirm(
      `确认删除用户 "${user.userName}" 吗？此操作不可逆！`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    const response = await deleteAccount(user.userId);
    if (response.state) {
      ElMessage.success("删除用户成功");
      getTableData();
    } else {
      ElMessage.error(response.err_msg || "删除用户失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除用户失败:", error);
      ElMessage.error("删除用户失败");
    }
  }
};

// 批量删除用户
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可逆！`,
      "确认批量删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    const promises = selectedUsers.value.map(user => deleteAccount(user.userId));
    const results = await Promise.allSettled(promises);
    
    const successCount = results.filter(result => result.status === "fulfilled").length;
    const failCount = results.length - successCount;
    
    if (successCount > 0) {
      ElMessage.success(`成功删除 ${successCount} 个用户${failCount > 0 ? `，${failCount} 个失败` : ''}`);
      getTableData();
    } else {
      ElMessage.error("批量删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("批量删除用户失败:", error);
      ElMessage.error("批量删除失败");
    }
  }
};

// 重置密码
const handleResetPassword = (user: AccountInfo) => {
  currentUser.value = { ...user };
  passwordDialogVisible.value = true;
};

// 分配角色
const handleAssignRole = (user: AccountInfo) => {
  currentUser.value = { ...user };
  roleDialogVisible.value = true;
};

// 表格选择变化
const handleSelectionChange = (selection: AccountInfo[]) => {
  selectedUsers.value = selection;
};

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.page = 1;
  getTableData();
};

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getTableData();
};

// 刷新数据
const handleRefresh = () => {
  getTableData();
};

// 加载部门树
const loadDepartmentTree = async () => {
  try {
    const response = await getDepartmentTree();
    if (response.state) {
      departmentTree.value = response.data || [];
    } else {
      console.error("获取部门树失败:", response.err_msg);
    }
  } catch (error) {
    console.error("获取部门树失败:", error);
  }
};


// 页面挂载时获取数据
onMounted(() => {
  loadRoles();
  loadDepartmentTree();
  getTableData();
});
</script>

<style scoped>
.main {
  background: var(--el-bg-color);
  height: 100%;
}

.text-gray-500 {
  color: var(--el-text-color-placeholder);
}

.text-gray-400 {
  color: var(--el-text-color-disabled);
}

.text-gray-600 {
  color: var(--el-text-color-regular);
}

.text-gray-700 {
  color: var(--el-text-color-primary);
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}
</style>